git submodule update --init --recursive --remote --force

# 清空 dist 文件夹，如果存在则删除，然后新建空文件夹
if (Test-Path dist) {
    Remove-Item dist -Recurse -Force
}
New-Item -ItemType Directory -Path dist | Out-Null

# 执行 cargo build --release 编译项目
cargo build --release

# 将 target\release 文件夹下所有 .exe 文件复制到 dist 文件夹
Get-ChildItem -Path target\release -Filter *.exe | Copy-Item -Destination dist

# 使用 git archive 命令复制 .runtime 文件夹（git 可以无视 .gitignore 文件）
git archive HEAD .runtime | tar -xf - -C dist

# 手动提取子模块内容
Push-Location .runtime/tools
git archive HEAD | tar -xf - -C ../../dist/.runtime/tools
Pop-Location

# 获取当前日期，格式为 yymmdd，并创建压缩包 dist-yymmdd.zip
$date = (Get-Date).ToString("yyMMdd")
Compress-Archive -Path dist\* -DestinationPath "dist-$date.zip"
