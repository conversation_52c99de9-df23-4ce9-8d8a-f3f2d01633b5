/// Handle signal listening for graceful service shutdown
#[tracing::instrument]
pub async fn shutdown() {
    let ctrl_c = async {
        tokio::signal::ctrl_c()
            .await
            .inspect_err(|e| tracing::debug!("Failed to install Ctrl+C handler: {:?}", e))
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
            .inspect_err(|e| tracing::debug!("Failed to install signal handler: {:?}", e))
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }
}
