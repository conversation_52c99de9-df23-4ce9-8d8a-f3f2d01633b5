//! 配置管理模块
//! 负责加载系统配置、能力组和模块定义

mod manager;
mod nacos;
pub mod templates;

use std::{collections::HashMap, sync::Arc};

use serde::{Deserialize, Serialize};

pub use manager::*;
pub use script::config::ScriptFile;

// 配置根目录
pub const CONFIG_ROOT: &str = ".runtime";

/// 服务器配置
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ServerConfig {
    pub listen: String,
    pub chat_api: String,
    pub arguments_api: String,
    pub explore_api: String,
    pub search_source_api: String,
    pub ai_api_base: String,
    pub ai_api_key: Option<String>,
    pub ai_api_model: Option<String>,
    pub ai_api_version: Option<String>,
    pub redis_url: String,
    pub redis_cluster: bool,
    pub minio_endpoint: String,
    pub minio_access_key: String,
    pub minio_secret_key: String,
    pub integral_api: String,
    pub calculate_integral: bool,
    pub gateway_public_key: Option<String>,
    pub mongodb_url: String,
}

/// 提示词配置
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct PromptsConfig {
    pub converter_system_prompt: String,
    pub extractor_system_prompt: String,
    pub tool_summary_system_prompt: String,
    pub tool_summary_hint_non_verbose: String,
    pub tool_summary_hint_verbose: String,
    pub flow_summary_system_prompt: String,
}

/// 模板配置
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct TemplatesConfig {
    pub templates: HashMap<String, String>,
}

/// 应用配置
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub mcp: McpConfig,
    pub prompts: PromptsConfig,
    pub templates: TemplatesConfig,
    pub modules: Vec<ModuleConfig>,
}

/// MCP服务器配置
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Hash)]
pub struct McpServerConfig {
    pub name: String,
    #[serde(flatten)]
    pub kind: McpServerKind,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Hash)]
#[serde(untagged)]
pub enum McpServerKind {
    Streamable { url: String },
    ChildProcess { command: String, args: Vec<String> },
}

/// MCP配置
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Hash)]
pub struct McpConfig {
    pub servers: Vec<McpServerConfig>,
}

/// 应用配置
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Hash)]
pub struct ModuleConfig {
    pub name: String,
    pub base: String,
    pub alias: Option<Vec<String>>,
    pub description: Option<String>,
    pub version: Option<String>,
    pub config: Option<Arc<serde_json::Value>>,
}
