use std::{collections::HashMap, path::PathBuf, sync::Arc};

use config::{
    Config, ConfigError, Environment, File, FileFormat, FileSource, FileSourceString,
    FileStoredFormat, Source,
};
use futures::StreamExt;
use script::config::{ScriptFile, ScriptModuleDefinition, ScriptToolDefinition};
use semver::{Version, VersionReq};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;

use crate::config::{
    AppConfig, CONFIG_ROOT, McpConfig, ModuleConfig, PromptsConfig, ServerConfig, TemplatesConfig,
    nacos::{NACOS_DATA_ID, NACOS_DATA_ID_OVERRIDES, NACOS_GROUP_NAME},
    templates,
};

/// 配置管理器
#[derive(Clone)]
pub struct ConfigManager {
    pub(super) app: Arc<RwLock<AppConfig>>,
    #[allow(dead_code)]
    pub(super) nacos: Option<nacos_sdk::api::config::ConfigService>,
}

impl ConfigManager {
    pub fn root_dir() -> PathBuf {
        PathBuf::from(CONFIG_ROOT)
    }

    pub fn modules_dir() -> PathBuf {
        Self::root_dir().join("tools").join("tools")
    }

    pub fn config_file() -> PathBuf {
        Self::root_dir().join("config.yaml")
    }

    pub fn config_file_modules() -> PathBuf {
        Self::root_dir().join("tools").join("modules.json")
    }

    pub fn config_overrides_file() -> PathBuf {
        Self::root_dir().join("config-overrides.json")
    }

    /// 加载配置
    pub fn load() -> error::Result<Arc<Self>> {
        // 构建配置
        let config = Self::build::<OverrideFile<FileSourceString, FileFormat>>(None)?;

        Ok(Arc::new(Self {
            app: Arc::new(RwLock::new(config)),
            nacos: None,
        }))
    }

    /// 加载配置
    pub async fn load_with_nacos(
        nacos: nacos_sdk::api::config::ConfigService,
    ) -> error::Result<Arc<Self>> {
        let config = nacos
            .get_config(NACOS_DATA_ID.to_owned(), NACOS_GROUP_NAME.to_owned())
            .await?;

        let config_overrides = nacos
            .get_config(
                NACOS_DATA_ID_OVERRIDES.to_owned(),
                NACOS_GROUP_NAME.to_owned(),
            )
            .await;

        let mut configs = vec![(config.content_type().as_str(), config.content().as_str())];

        if let Ok(config_overrides) = &config_overrides {
            configs.push((
                config_overrides.content_type().as_str(),
                config_overrides.content().as_str(),
            ));
        }

        let manager = Arc::new(Self {
            app: Arc::new(RwLock::new(Self::load_config_with_extra_data(
                configs.as_slice(),
            )?)),
            nacos: Some(nacos.clone()),
        });

        nacos
            .add_listener(
                NACOS_DATA_ID.to_owned(),
                NACOS_GROUP_NAME.to_owned(),
                manager.clone(),
            )
            .await?;

        Ok(manager)
    }

    // &[(format: &str, data: &str)]
    pub fn load_config_with_extra_data(data: &[(&str, &str)]) -> error::Result<AppConfig> {
        let source = data
            .iter()
            .map(|(format, data)| {
                OverrideFile(File::from_str(
                    data,
                    match *format {
                        "json" => FileFormat::Json,
                        "yaml" | "yml" => FileFormat::Yaml,
                        _ => FileFormat::Json,
                    },
                ))
            })
            .collect::<Vec<_>>();

        Self::build(Some(source))
    }

    fn build<T: Source + Send + Sync + 'static>(
        source: Option<Vec<T>>,
    ) -> error::Result<AppConfig> {
        let mut config = Config::builder()
            .add_source(config::Config::try_from(&Self::default_config())?)
            // 添加配置文件
            .add_source(File::from(Self::config_file()).required(false))
            .add_source(File::from(Self::config_file_modules()).required(true))
            .add_source(OverrideFile(
                File::from(Self::config_overrides_file()).required(false),
            ))
            .add_source(
                Environment::with_prefix("tools")
                    .separator(".")
                    .prefix_separator("."),
            );

        if let Some(source) = source {
            for s in source {
                config = config.add_source(s);
            }
        }

        let config = config.build()?;

        let config = config.try_deserialize()?;

        tracing::info!("current config: {config:?}");

        Ok(config)
    }
}

impl ConfigManager {
    pub async fn update_module(&self, module: ModuleConfig) -> error::Result<()> {
        let mut config = self.app.write().await;

        // 移除同名模块
        config.modules.retain(|m| m.name != module.name);

        // 添加新模块
        config.modules.push(module);

        drop(config);

        self.save_overrides().await?;

        Ok(())
    }

    pub async fn update_modules(&self, modules: Vec<ModuleConfig>) -> error::Result<()> {
        let mut config = self.app.write().await;

        let new_module_names = modules.iter().map(|m| m.name.clone()).collect::<Vec<_>>();

        // 移除同名模块
        config
            .modules
            .retain(|m| !new_module_names.contains(&m.name));

        // 添加新模块
        config.modules.extend(modules);

        drop(config);

        self.save_overrides().await?;

        Ok(())
    }

    pub async fn delete_module(&self, name: &str) -> error::Result<()> {
        let mut config = self.app.write().await;

        config.modules.retain(|m| m.name != name);

        drop(config);

        self.save_overrides().await?;

        Ok(())
    }

    pub async fn save_overrides(&self) -> error::Result<()> {
        let config = self.app_config().await;
        let mut config_overrides = serde_json::to_value(&config)?;
        // 这里只保存config.modules
        let config_overrides = config_overrides.get_mut("modules").unwrap().take();
        let config_overrides = serde_json::to_string_pretty(&serde_json::json!({
            "modules": config_overrides
        }))?;

        if let Some(nacos) = &self.nacos {
            nacos
                .publish_config(
                    NACOS_DATA_ID_OVERRIDES.to_owned(),
                    NACOS_GROUP_NAME.to_owned(),
                    config_overrides,
                    Some("json".to_string()),
                )
                .await?;
        } else {
            tokio::fs::write(Self::config_overrides_file(), config_overrides).await?;
        }

        Ok(())
    }

    /// 获取应用配置
    pub async fn app_config(&self) -> AppConfig {
        self.app.read().await.clone()
    }

    pub async fn load_script_modules(
        &self,
    ) -> error::Result<(Vec<Arc<ScriptModuleDefinition>>, Vec<String>)> {
        #[derive(Serialize, Deserialize)]
        pub struct ToolsDefinition {
            pub tools: Vec<Arc<ScriptToolDefinition>>,
        }

        let mut errors: Vec<String> = vec![];

        let mut modules = vec![];

        let modules_dir = Self::modules_dir();

        for module_config in self.app.read().await.modules.iter() {
            let module_dir = modules_dir.join(module_config.base.as_str());
            let module_name = module_config.name.as_str();

            let requested_version = module_config
                .version
                .as_deref()
                .unwrap_or("*")
                .trim_start_matches(['v', 'V']);
            let requested_version = match VersionReq::parse(requested_version) {
                Ok(v) => v,
                Err(err) => {
                    tracing::error!("Failed to parse version request: {:?}", requested_version);
                    errors.push(format!(
                        "[{module_name}] failed to parse version {requested_version}: {err}"
                    ));
                    continue;
                }
            };

            let versions = match tokio::fs::read_dir(module_dir).await {
                Ok(versions) => versions,
                Err(err) => {
                    tracing::error!("Failed to read directory: {}", err);
                    errors.push(format!("[{module_name}] failed to read directory: {err}"));
                    continue;
                }
            };

            let versions = tokio_stream::wrappers::ReadDirStream::new(versions);

            // 获取versions所有的子文件夹，并解析文件夹名为具体的版本
            let (mut versions, version_errors) = versions
                .fold(
                    (vec![], vec![]),
                    |(mut versions, mut errors), v| async move {
                        let v = match v {
                            Ok(v) => v,
                            Err(err) => {
                                tracing::error!("Failed to read directory: {}", err);
                                errors.push(format!(
                                    "[{module_name}] failed to read directory {err}"
                                ));
                                return (versions, errors);
                            }
                        };

                        // 类型为文件夹
                        if !v.path().is_dir() {
                            errors.push(format!(
                                "[{}] required directory: {:?}",
                                module_name,
                                v.path()
                            ));
                            return (versions, errors);
                        }

                        let path = v.path();
                        let version = match path.file_name() {
                            Some(v) => match v.to_str() {
                                Some(v) => v,
                                None => {
                                    tracing::error!("Failed to get file name: {:?}", path);
                                    errors.push(format!(
                                        "[{module_name}] failed to get file name: {path:?}"
                                    ));
                                    return (versions, errors);
                                }
                            },
                            None => {
                                tracing::error!("Failed to get file name: {:?}", path);
                                errors.push(format!(
                                    "[{module_name}] failed to get file name: {path:?}"
                                ));
                                return (versions, errors);
                            }
                        };

                        // 移除开始的 v 前缀
                        let version = version.trim_start_matches(['v', 'V']);
                        let version = match VersionReq::parse(version) {
                            Ok(v) => {
                                let c = match v.comparators.first() {
                                    Some(c) => c,
                                    None => {
                                        tracing::error!("Failed to parse version {}", version);
                                        errors.push(format!(
                                            "[{module_name}] failed to parse version {version}"
                                        ));
                                        return (versions, errors);
                                    }
                                };

                                Version::new(
                                    c.major,
                                    c.minor.unwrap_or_default(),
                                    c.patch.unwrap_or_default(),
                                )
                            }
                            Err(err) => {
                                tracing::error!("Failed to parse version {}: {:?}", version, err);
                                errors.push(format!(
                                    "[{module_name}] failed to parse version {version}: {err}"
                                ));
                                return (versions, errors);
                            }
                        };

                        versions.push((path, version));

                        (versions, errors)
                    },
                )
                .await;

            if !version_errors.is_empty() {
                errors.extend(version_errors);
            }

            versions.sort_by(|a, b| b.1.cmp(&a.1));

            let version = versions
                .into_iter()
                .find(|(_, v)| requested_version.matches(v));

            let (versioned_module_dir, version) = match version {
                Some(v) => v,
                None => {
                    tracing::error!("Failed to find matched version: {:?}", requested_version);
                    errors.push(format!(
                        "[{module_name}] failed to find matched version: {requested_version}"
                    ));

                    continue;
                }
            };

            let tools_definition_file = versioned_module_dir.join("tools.json");
            let tools_definition = match tokio::fs::read_to_string(tools_definition_file).await {
                Ok(tools_definition) => {
                    match serde_json::from_str::<ToolsDefinition>(&tools_definition) {
                        Ok(tools_definition) => tools_definition,
                        Err(err) => {
                            tracing::error!("Failed to parse tools definition: {:?}", err);
                            errors.push(format!(
                                "[{module_name}] failed to parse tools definition: {err}"
                            ));
                            continue;
                        }
                    }
                }
                Err(err) => {
                    tracing::error!("Failed to read tools definition: {:?}", err);
                    errors.push(format!(
                        "[{module_name}] failed to read tools definition: {err}"
                    ));
                    continue;
                }
            };

            for tool in tools_definition.tools.iter() {
                let tool_name = tool.name.as_str();
                let tool_params = tool.params.clone();
                let tool_result = tool.result.clone();

                if !tool_params.is_null() {
                    if let Err(err) = jsonschema::validator_for(tool_params.as_ref()) {
                        tracing::error!("Failed to validate tool params schema: {:?}", err);
                        errors.push(format!(
                            "[{module_name}/{tool_name}] failed to validate tool params schema: {err}"
                        ));
                    }
                }

                if !tool_result.is_null() {
                    if let Err(err) = jsonschema::validator_for(tool_result.as_ref()) {
                        tracing::error!("Failed to validate tool result schema: {:?}", err);
                        errors.push(format!(
                            "[{module_name}/{tool_name}] failed to validate tool result schema: {err}"
                        ));
                    }
                }
            }

            let files = match tokio::fs::read_dir(versioned_module_dir).await {
                Ok(files) => files,
                Err(err) => {
                    tracing::error!("Failed to read files: {:?}", err);
                    errors.push(format!("[{module_name}] failed to read files: {err}"));
                    continue;
                }
            };

            let files = tokio_stream::wrappers::ReadDirStream::new(files);

            let (files, file_errors) = files
                .fold((vec![], vec![]), |(mut files, mut errors), f| async move {
                    let f = match f {
                        Ok(f) => f,
                        Err(_) => {
                            tracing::error!("Failed to read file: {:?}", f);
                            errors.push(format!("[{module_name}] failed to read file: {f:?}"));
                            return (files, errors);
                        }
                    };

                    let path = f.path();

                    // 类型为文件
                    if !path.is_file() {
                        errors.push(format!("[{module_name}] required file: {path:?}"));
                        return (files, errors);
                    }

                    // 过滤类型为 py 的文件
                    if !path
                        .extension()
                        .and_then(|ext| ext.to_str())
                        .map(|ext| ext.eq_ignore_ascii_case("py"))
                        .unwrap_or_default()
                    {
                        return (files, errors);
                    }

                    let file = ScriptFile {
                        path: path.clone(),
                        code: match tokio::fs::read(&path).await {
                            Ok(code) => code,
                            Err(_) => {
                                tracing::error!("Failed to read file: {:?}", path);
                                errors
                                    .push(format!("[{module_name}] failed to read file: {path:?}"));
                                return (files, errors);
                            }
                        },
                    };

                    files.push(Arc::new(file));

                    (files, errors)
                })
                .await;

            if !file_errors.is_empty() {
                errors.extend(file_errors);
            }

            let module_definition = ScriptModuleDefinition {
                name: module_config.name.clone(),
                version: version.clone(),
                tools_definition: tools_definition.tools,
                base: module_config.base.clone(),
                alias: module_config.alias.clone(),
                description: module_config.description.clone().unwrap_or_default(),
                config: module_config.config.clone().unwrap_or_default(),
                files,
            };

            modules.push(Arc::new(module_definition));
        }

        Ok((modules, errors))
    }

    /// 获取提示词配置
    pub async fn prompts(&self) -> PromptsConfig {
        self.app.read().await.prompts.clone()
    }

    /// 获取模板配置
    pub async fn templates(&self) -> TemplatesConfig {
        self.app.read().await.templates.clone()
    }

    /// 获取模板内容
    pub async fn get_template(&self, name: &str) -> Option<String> {
        self.templates().await.templates.get(name).cloned()
    }

    /// 创建默认配置
    fn default_config() -> AppConfig {
        AppConfig {
            server: ServerConfig {
                listen: "127.0.0.1:15336".to_string(),
                chat_api: "http://127.0.0.1:15337".to_string(),
                explore_api: "ws://127.0.0.1:15337".to_string(),
                arguments_api: "http://127.0.0.1:15337".to_string(),
                search_source_api: "http://127.0.0.1:15337".to_string(),
                ai_api_base: "http://127.0.0.1:15337".to_string(),
                ai_api_key: None,
                ai_api_model: None,
                ai_api_version: None,
                redis_url: "redis://127.0.0.1:6379".to_string(),
                redis_cluster: false,
                minio_endpoint: "http://127.0.0.1:9000".to_string(),
                minio_access_key: "minioadmin".to_string(),
                minio_secret_key: "minioadmin".to_string(),
                gateway_public_key: None,
                calculate_integral: false,
                integral_api: "http://127.0.0.1:1234/api/points-transaction/changePoints"
                    .to_string(),
                mongodb_url: "*************************************".to_string(),
            },
            mcp: McpConfig { servers: vec![] },
            prompts: PromptsConfig {
                converter_system_prompt: templates::CONVERTER_SYSTEM_PROMPT.to_string(),
                extractor_system_prompt: templates::EXTRACTOR_SYSTEM_PROMPT.to_string(),
                tool_summary_system_prompt: templates::TOOL_SUMMARY_SYSTEM_PROMPT.to_string(),
                tool_summary_hint_non_verbose: templates::TOOL_SUMMARY_HINT_NON_VERBOSE.to_string(),
                tool_summary_hint_verbose: templates::TOOL_SUMMARY_HINT_VERBOSE.to_string(),
                flow_summary_system_prompt: templates::FLOW_SUMMARY_SYSTEM_PROMPT.to_string(),
            },
            templates: TemplatesConfig {
                templates: HashMap::new(),
            },
            modules: vec![],
        }
    }
}

#[derive(Clone, Debug)]
struct OverrideFile<T, F>(File<T, F>);

impl<T, F> Source for OverrideFile<T, F>
where
    F: FileStoredFormat + std::fmt::Debug + Clone + Send + Sync + 'static,
    T: Sync + Send + FileSource<F> + 'static,
{
    fn clone_into_box(&self) -> Box<dyn Source + Send + Sync> {
        Box::new((*self).clone())
    }

    fn collect(&self) -> Result<HashMap<String, config::Value>, ConfigError> {
        match self.0.collect() {
            Ok(v) => Ok(v),
            Err(e) => {
                tracing::error!("Failed to load override file: {:?}", e);
                Ok(HashMap::new())
            }
        }
    }
}
