use nacos_sdk::api::config::{Config<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ConfigResponse};

use crate::config::ConfigManager;

pub const NACOS_GROUP_NAME: &str = "v1";
pub const NACOS_DATA_ID: &str = "tools_server";
pub const NACOS_DATA_ID_OVERRIDES: &str = "tools_server_overrides";

impl ConfigManager {
    #[tracing::instrument(skip(self))]
    async fn handle_nacos_config(&self, _: ConfigResponse) -> error::Result<()> {
        tracing::info!("nacos config changed");

        let new = Self::load_with_nacos(self.nacos.clone().unwrap())
            .await?
            .app_config()
            .await;

        let mut current = self.app.write().await;
        let mut changed = false;

        // 使用 Hash 比较来检查 modules 是否发生变化
        use std::hash::{Hash, Hasher};

        let current_modules_hash = {
            let mut hasher = std::collections::hash_map::DefaultHasher::new();
            current.modules.hash(&mut hasher);
            hasher.finish()
        };

        let new_modules_hash = {
            let mut hasher = std::collections::hash_map::DefaultHasher::new();
            new.modules.hash(&mut hasher);
            hasher.finish()
        };

        if current_modules_hash != new_modules_hash {
            changed = true;
            current.modules = new.modules;
        }

        drop(current);

        if changed {
            tracing::info!("nacos modules changed, reloading script modules");

            self.load_script_modules().await?;
        }

        Ok(())
    }
}

impl ConfigChangeListener for ConfigManager {
    fn notify(&self, config: ConfigResponse) {
        // 使用 tokio::spawn 在后台异步处理配置变更
        // 避免在同步回调中阻塞线程
        let manager = self.clone();
        tokio::spawn(async move {
            if let Err(e) = manager.handle_nacos_config(config).await {
                tracing::error!("Failed to handle nacos config: {:?}", e);
            }
        });
    }
}
