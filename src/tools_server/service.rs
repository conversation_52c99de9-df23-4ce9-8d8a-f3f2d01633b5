//! Service Routing and API Module
//! Provides HTTP interfaces and route handling for the system

use std::sync::Arc;

use axum::http::StatusCode;
use axum::middleware::from_fn_with_state;
use axum::{
    Json, Router,
    response::{Sse, sse::Event},
    routing::get,
};
use futures::Stream;
use resource::ResourceManager;
use serde::Serialize;
use shared_core::{config::ConfigManager, conversation::manager::ConversationManager};
use tokio_stream::StreamExt as _;
use tool::global::GlobalToolRepository;

use crate::task::TaskManager;
use crate::{
    auth, background_agent, conversation, file, health, mcp, playback, rpc, task, tool_service,
    workflow,
};

pub type Response =
    Result<Json<serde_json::Value>, (axum::http::StatusCode, Json<serde_json::Value>)>;
pub type Error = (axum::http::StatusCode, Json<serde_json::Value>);

pub trait ResponseExt {
    fn success() -> Response {
        Ok(Json(serde_json::json!({
            "status": "success",
        })))
    }

    fn body(body: impl Serialize) -> Response {
        let value =
            serde_json::to_value(body).map_err(|e| Error::bad_request_error(format!("{e:?}")))?;
        Ok(Json(value))
    }

    fn error(code: StatusCode, error: String) -> Response {
        Err((code, Json(serde_json::json!({ "error": error}))))
    }

    fn bad_request(error: String) -> Response {
        Self::error(StatusCode::BAD_REQUEST, error)
    }

    fn not_found(error: String) -> Response {
        Self::error(StatusCode::NOT_FOUND, error)
    }
}

pub trait ErrExt {
    fn error(e: error::Error) -> Error {
        Self::bad_request_error(format!("{e:?}"))
    }

    fn bad_request_error(error: String) -> Error {
        (
            StatusCode::BAD_REQUEST,
            Json(serde_json::json!({"error":error})),
        )
    }

    fn not_found_error(error: String) -> Error {
        (
            StatusCode::NOT_FOUND,
            Json(serde_json::json!({"error":error})),
        )
    }

    fn cast_error(e: error::Error) -> Error {
        Self::bad_request_error(format!("转换错误: {e:?}"))
    }

    fn internal_server_error(error: String) -> Error {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(serde_json::json!({"error":error})),
        )
    }
}

impl ResponseExt for Response {}

impl ErrExt for Error {}

/// Create system API routes
///
/// Includes the following endpoints:
/// - /api/rpc: Direct capability invocation
/// - /api/mcp: MCP service interface
#[tracing::instrument(skip_all)]
pub async fn create_root_router(
    global_repository: Arc<GlobalToolRepository>,
    conversation_manager: Arc<ConversationManager>,
    resource_manager: Arc<ResourceManager>,
    config_manager: Arc<ConfigManager>,
    document_repository: Arc<entity::DocumentRepository>,
    task_manager: Arc<TaskManager>,
) -> Router {
    let app_config = config_manager.app_config().await;
    let public_key = Arc::new(
        app_config
            .server
            .gateway_public_key
            .expect("gateway_public_key is required for JWT validation"),
    );

    let api_router = Router::new()
        .nest("/file", file::router(resource_manager.clone()))
        .nest("/rpc", rpc::router(global_repository.clone()))
        .nest("/mcp", mcp::router(global_repository.clone()))
        .nest(
            "/workflow",
            workflow::router(document_repository.clone(), conversation_manager.clone()),
        )
        .nest(
            "/conversation",
            conversation::router(conversation_manager.clone()),
        )
        .nest(
            "/test",
            Router::new().route("/sse", get(test_sse).post(test_sse)),
        )
        .nest(
            "/playback",
            playback::create_router(document_repository.clone(), resource_manager.clone()),
        )
        .nest(
            "/background_agent",
            background_agent::router(document_repository.clone()),
        )
        .layer(from_fn_with_state(public_key, auth::jwt_auth));

    let open_api_router = Router::new()
        .nest(
            "/tool",
            tool_service::open_router(
                global_repository.clone(),
                config_manager.clone(),
                resource_manager.clone(),
            ),
        )
        .nest(
            "/conversation",
            conversation::open_router(conversation_manager),
        )
        .nest("/task", task::open_router(task_manager.clone()))
        .nest("/health", health::router());

    Router::new()
        .nest("/api", api_router.merge(open_api_router.clone()))
        .nest("/open-api", open_api_router)
}

#[tracing::instrument]
async fn test_sse() -> Sse<impl Stream<Item = error::Result<Event>> + Send + 'static> {
    // every 5 seconds send a incrementing number
    let stream = tokio_stream::wrappers::IntervalStream::new(tokio::time::interval(
        std::time::Duration::from_secs(5),
    ))
    .map(|i| {
        let event = Event::default()
            .event("data")
            .data(format!("{:#?}", i.elapsed()));
        error::Result::Ok(event)
    });

    Sse::new(stream)
}
