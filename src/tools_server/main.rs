//! Supcon-AI-Flow System Entry
//! AI process system based on modular design, supporting Python/Lua script extensions and MCP protocol

use std::{collections::HashMap, sync::Arc};

use axum::extract::DefaultBodyLimit;
use integral::IntegralManager;
use reqwest::Url;
use resource::ResourceManager;
use script::python::PythonScriptManager;
use shared_core::{
    config::{ConfigManager, McpServerKind},
    conversation::{
        manager::ConversationManager,
        runner::{ConversationRunner, ConversationScriptRunner, ConversationWorkflowRunner},
    },
    signal::shutdown,
    trace::start_tracing,
    *,
};
use tokio::sync::RwLock;
use tool::{
    builtin::BuiltinToolRepository,
    global::GlobalToolRepository,
    mcp::{McpClientBuilder, McpToolRepository},
    script::ScriptToolRepository,
};
use tower_http::{cors::Co<PERSON><PERSON><PERSON><PERSON>, trace::<PERSON><PERSON>ayer};

use crate::task::TaskManager;

mod auth;
mod background_agent;
mod conversation;
mod file;
mod health;
mod mcp;
mod playback;
mod rpc;
mod service;
mod task;
mod tool_service;
mod workflow;

fn main() {
    // Build async runtime with support for large concurrent execution capacity
    let mut builder = tokio::runtime::Builder::new_multi_thread();
    builder
        .enable_all()
        .max_blocking_threads(512)
        .thread_keep_alive(std::time::Duration::from_secs(u64::MAX))
        .on_thread_stop(script::on_thread_stop);

    script::runtime::set_runtime_builder(builder);

    let _guard = script::runtime::get_runtime().enter();

    let prevent_log_files = std::env::var("TOOLS_PREVENT_LOG_FILES")
        .map(|v| v == "true")
        .unwrap_or_default();

    // let _guard = init_remote_tracing();
    let _guard = start_tracing(!prevent_log_files);

    tracing::debug!("Hello, tools!");

    // Initialize script engine environment
    script::on_initialize();

    #[tracing::instrument]
    async fn main() -> error::Result<()> {
        // 加载配置系统

        let use_nacos = std::env::var("TOOLS_USE_NACOS").unwrap_or_default() == "true";

        let load_minio_tools =
            std::env::var("TOOLS_LOAD_MINIO_TOOLS").unwrap_or_default() == "true";

        let config_manager = if use_nacos {
            let nacos = nacos_sdk::api::config::ConfigServiceBuilder::default()
                .enable_auth_plugin_http()
                .build()?;

            config::ConfigManager::load_with_nacos(nacos)
                .await
                .inspect_err(|e| tracing::error!("Failed to load configuration: {:?}", e))?
        } else {
            config::ConfigManager::load()
                .inspect_err(|e| tracing::error!("Failed to load configuration: {:?}", e))?
        };

        // 获取应用配置和旧版配置
        let app_config = config_manager.app_config().await;

        let mongodb_url = app_config.server.mongodb_url.clone();
        let repository = Arc::new(entity::DocumentRepository::init(mongodb_url).await?);

        let resource_manager = Arc::new(ResourceManager::new(
            resource::Redis::build(
                app_config.server.redis_url.clone(),
                app_config.server.redis_cluster,
            )
            .await?,
            minio::s3::Client::new(
                app_config.server.minio_endpoint.parse().unwrap(),
                Some(Box::new(minio::s3::creds::StaticProvider::new(
                    &app_config.server.minio_access_key,
                    &app_config.server.minio_secret_key,
                    None,
                ))),
                None,
                None,
            )?,
        ));

        for bucket_name in resource::BUCKETS {
            resource_manager
                .create_bucket_if_not_exists(bucket_name.to_string())
                .await?;
        }

        if load_minio_tools {
            match resource_manager
                .read_file(resource::RUNTIME_BUCKET_NAME, "tools.zip", None)
                .await
            {
                Ok(bytes) => match zip::ZipArchive::new(std::io::Cursor::new(&bytes)) {
                    Ok(mut zip) => {
                        if let Err(e) = zip.extract_unwrapped_root_dir(
                            ConfigManager::modules_dir(),
                            zip::read::root_dir_common_filter,
                        ) {
                            tracing::error!("failed to extract tools.zip: {:?}", e);
                        }
                    }
                    Err(e) => {
                        tracing::error!("failed to extract tools.zip: {:?}", e);
                    }
                },
                Err(e) => {
                    tracing::info!("tools.zip not found: {:?}", e);
                }
            }
        }

        // 创建脚本引擎
        let (scripts, _) = config_manager.load_script_modules().await?;

        let conversation_script_runner = Arc::new(ConversationScriptRunner {
            resource_manager: resource_manager.clone(),
        });

        let script_manager = Arc::new(PythonScriptManager::new(conversation_script_runner.clone()));

        let mut script_repository = ScriptToolRepository::new(script_manager.clone());

        let _ = script_repository.load(scripts).await?;

        let global_tool_repository = Arc::new(GlobalToolRepository {
            builtin: BuiltinToolRepository::new(),
            script: RwLock::new(Arc::new(script_repository)),
            mcp: app_config
                .mcp
                .servers
                .iter()
                .map(|mcp| {
                    Arc::new(McpToolRepository::new(
                        mcp.name.clone(),
                        match &mcp.kind {
                            McpServerKind::Streamable { url } => McpClientBuilder::Streamable {
                                url: Url::parse(url).unwrap(),
                            },
                            McpServerKind::ChildProcess { command, args } => {
                                McpClientBuilder::ChildProcess {
                                    command: command.clone(),
                                    args: args.clone(),
                                }
                            }
                        },
                    ))
                })
                .collect(),
        });

        let model = Arc::new(actor::InternalModel {
            chat_api: app_config.server.chat_api.clone(),
            arguments_api: app_config.server.arguments_api.clone(),
            explore_api: app_config.server.explore_api.clone(),
            search_source_api: app_config.server.search_source_api.clone(),
            ai_api_base: app_config.server.ai_api_base.clone(),
            ai_api_key: app_config.server.ai_api_key.clone(),
            ai_api_model: app_config.server.ai_api_model.clone(),
            ai_api_version: app_config.server.ai_api_version.clone(),
        });

        let conversation_workflow_runner = Arc::new(ConversationWorkflowRunner {
            max_retry_count: 5,
            resource_manager: resource_manager.clone(),
            repository: global_tool_repository.clone(),
            script: script_manager.clone(),
            model: model.clone(),
            converter_system_prompt: app_config.prompts.converter_system_prompt.clone(),
            summary_system_prompt: app_config.prompts.tool_summary_system_prompt.clone(),
            extractor_system_prompt: app_config.prompts.extractor_system_prompt.clone(),
            final_summary_system_prompt: app_config.prompts.flow_summary_system_prompt.clone(),
        });

        let integral_manager = IntegralManager {
            integral_api: app_config.server.integral_api.clone(),
            calculate_integral: app_config.server.calculate_integral,
        };
        let conversation_runner = Arc::new(ConversationRunner {
            resource_manager: resource_manager.clone(),
            repository: global_tool_repository.clone(),
            model,
            script: script_manager.clone(),
            workflow: conversation_workflow_runner.clone(),
            document_repository: repository.clone(),
            integral_manager: Arc::new(integral_manager),
        });

        let conversation_manager = Arc::new(ConversationManager::new(conversation_runner.clone())?);

        let conversation_manager_clone = conversation_manager.clone();

        tokio::spawn(async move {
            let _ = conversation_manager_clone
                .reset_all_conversation_status()
                .await;
        });

        let task_manager = Arc::new(TaskManager {
            workflow_runner: conversation_workflow_runner.clone(),
            actors: RwLock::new(HashMap::new()),
            resource_manager: resource_manager.clone(),
            repository: repository.clone(),
        });

        // Create API routes and start service
        let root_router = service::create_root_router(
            global_tool_repository,
            conversation_manager,
            resource_manager,
            config_manager,
            repository,
            task_manager,
        )
        .await;

        let root_router = root_router
            .layer(CorsLayer::permissive())
            .layer(TraceLayer::new_for_http())
            .layer(DefaultBodyLimit::max(128 * 1024 * 1024));

        let listener = tokio::net::TcpListener::bind(&app_config.server.listen)
            .await
            .inspect_err(|e| tracing::debug!("Failed to bind port: {:?}", e))?;

        axum::serve(
            listener,
            root_router.into_make_service_with_connect_info::<std::net::SocketAddr>(),
        )
        .with_graceful_shutdown(shutdown())
        .await
        .inspect_err(|e| tracing::debug!("Service execution failed: {:?}", e))?;

        Ok(())
    }

    script::runtime::block_on(main).unwrap();

    // Clean up resources when system shuts down
    script::on_finalize();
}
