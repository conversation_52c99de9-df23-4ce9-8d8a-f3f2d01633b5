use std::collections::{HashMap, HashSet};
use std::str::FromStr;
use std::sync::Arc;

use ai::SimpleWorkflow;
use axum::extract::{Path, Query, State};
use axum::routing::{delete, get, put};
use axum::{Extension, Json, Router};
use bson::doc;
use content::{ContentCard, ContentItemInner, ContentView, ContentViewOrText};
use entity::{Document as _, DocumentRepository, LOOKUP, MATCH, OPTIONS, REGEX, UNWIND};
use entity::{ID, SET};
use expression::{CompOp, Expr, Index, Variable};
use mongodb::options::{UpdateOneModel, WriteModel};
use serde::{Deserialize, Deserializer, Serialize};
use serde_json::Value;
use shared_core::conversation::{Author, Conversation, manager::ConversationManager};
use time::OffsetDateTime;
use uuid::Uuid;
use workflow::{
    Edge, EdgeConfig, Node, NodePort, NodePortConfig, NodePortType, ScriptConverter, Workflow,
};

use crate::auth::Claims;
use crate::service::{ErrExt, Error, Response, ResponseExt};

#[derive(Clone)]
struct RouterState {
    repository: Arc<DocumentRepository>,
    _conversation_manager: Arc<ConversationManager>,
}

pub fn router(
    repository: Arc<DocumentRepository>,
    conversation_manager: Arc<ConversationManager>,
) -> Router {
    Router::new()
        .route(
            "/{id}",
            delete(delete_workflow)
                .get(get_workflow)
                .put(update_workflow),
        )
        .nest(
            "/favorite",
            Router::new()
                .route("/", get(get_workflow_favorite).post(add_workflow_favorite))
                .route(
                    "/{id}",
                    put(update_workflow_favorite).delete(delete_workflow_favorite),
                )
                .route("/tag", put(update_tag))
                .nest(
                    "/group",
                    Router::new()
                        .route(
                            "/",
                            get(get_workflow_favorite_group).post(add_workflow_favorite_group),
                        )
                        .route(
                            "/{id}",
                            put(update_workflow_favorite_group)
                                .delete(delete_workflow_favorite_group),
                        ),
                ),
        )
        .with_state(RouterState {
            repository,
            _conversation_manager: conversation_manager,
        })
}

async fn get_workflow(
    State(RouterState { repository, .. }): State<RouterState>,
    Path(id): Path<Uuid>,
) -> Response {
    let option: Option<entity::document::Workflow> = repository
        .find_one(doc! {ID: id})
        .await
        .map_err(|e| Error::bad_request_error(format!("{e:?}")))?;
    match option {
        Some(workflow) => {
            let workflow: Workflow = workflow.try_into().map_err(Error::cast_error)?;
            Response::body(workflow)
        }
        None => Response::not_found(format!("workflow {id:?} 不存在")),
    }
}

async fn update_workflow(
    State(RouterState { repository, .. }): State<RouterState>,
    Path(_id): Path<Uuid>,
    Json(workflow): Json<Workflow>,
) -> Response {
    let document: entity::document::Workflow = workflow.try_into().map_err(Error::cast_error)?;
    repository
        .replace_one(doc! {ID: document.id}, document)
        .await
        .map_err(Error::error)?;
    Response::success()
}

async fn delete_workflow(
    State(RouterState { repository, .. }): State<RouterState>,
    Path(id): Path<Uuid>,
) -> Response {
    use entity::document::*;

    repository
        .update_one::<entity::document::Workflow>(
            doc! {ID: id},
            doc! {SET: doc!{WorkflowField::Status.as_str(): WorkflowStatusField::Deleted.as_str()}},
        )
        .await
        .map_err(Error::error)?;
    Response::success()
}

#[derive(Clone, Debug, Serialize, Deserialize, Default)]
struct WorkflowFavoriteGroupResponse {
    pub id: Uuid,
    pub parent_id: Option<Uuid>,
    pub name: String,
    pub description: Option<String>,
    pub r#type: WorkflowFavoriteType,
    pub workflow_id: Option<Uuid>,
    #[serde(default)]
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub children: Vec<WorkflowFavoriteGroupResponse>,
    #[serde(
        default,
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub create_time: Option<OffsetDateTime>,
    #[serde(
        default,
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub update_time: Option<OffsetDateTime>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub agent_type: Option<String>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub tag: Option<String>,
}

#[derive(Clone, Debug, Serialize, Deserialize, Default)]
pub enum WorkflowFavoriteType {
    #[default]
    #[serde(rename = "folder")]
    Folder,
    #[serde(rename = "workflow")]
    Workflow,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
struct WorkflowFavoriteGroupRequest {
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub id: Option<Uuid>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(default, deserialize_with = "deserialize_from_str")]
    pub parent_id: Option<Uuid>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub user_id: Option<String>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub tenant_id: Option<String>,
    #[serde(
        default,
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub create_time: Option<OffsetDateTime>,
    #[serde(
        default,
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub update_time: Option<OffsetDateTime>,
}

impl TryFrom<entity::document::Agent> for WorkflowFavoriteGroupRequest {
    type Error = error::Error;

    fn try_from(value: entity::document::Agent) -> Result<Self, Self::Error> {
        Ok(Self {
            id: Some(value.id),
            parent_id: value.parent_id,
            name: Some(value.name),
            description: value.description,
            user_id: Some(value.user_id),
            tenant_id: Some(value.tenant_id),
            create_time: Some(value.create_time),
            update_time: value.update_time,
        })
    }
}

impl TryFrom<WorkflowFavoriteGroupRequest> for entity::document::Agent {
    type Error = error::Error;

    fn try_from(value: WorkflowFavoriteGroupRequest) -> Result<Self, Self::Error> {
        Ok(Self {
            id: value.id.unwrap_or_else(Uuid::now_v7),
            parent_id: value.parent_id,
            name: value.name.unwrap_or_else(|| "default_name".to_string()),
            r#type: entity::document::AgentType::Folder,
            description: value.description,
            workflow_id: None,
            workflow: None,
            user_id: value.user_id.unwrap_or_default(),
            tenant_id: value.tenant_id.unwrap_or_default(),
            create_time: value.create_time.unwrap_or_else(OffsetDateTime::now_utc),
            update_time: value.update_time,
            agent_type: None,
            tag: None,
        })
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
struct WorkflowFavorite {
    pub id: Option<Uuid>,
    pub name: Option<String>,
    pub description: Option<String>,
    #[serde(default, deserialize_with = "deserialize_from_str")]
    pub workflow_id: Option<Uuid>,
    /// workflow json
    pub workflow: Option<String>,
    /// 分组id
    #[serde(default, deserialize_with = "deserialize_from_str")]
    pub group_id: Option<Uuid>,
    pub user_id: Option<String>,
    pub tenant_id: Option<String>,
    #[serde(
        default,
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub create_time: Option<OffsetDateTime>,
    #[serde(
        default,
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub update_time: Option<OffsetDateTime>,
    pub agent_type: Option<String>,
    pub tag: Option<String>,
}

fn deserialize_from_str<'de, D>(deserializer: D) -> Result<Option<Uuid>, D::Error>
where
    D: Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    if s.is_empty() {
        Ok(None)
    } else {
        let uuid = Uuid::from_str(&s).map_err(serde::de::Error::custom)?;
        Ok(Some(uuid))
    }
}

impl TryFrom<entity::document::Agent> for WorkflowFavorite {
    type Error = error::Error;

    fn try_from(value: entity::document::Agent) -> Result<Self, Self::Error> {
        Ok(Self {
            id: Some(value.id),
            name: Some(value.name),
            description: value.description,
            workflow_id: value.workflow_id,
            workflow: None,
            group_id: value.parent_id,
            user_id: Some(value.user_id),
            tenant_id: Some(value.tenant_id),
            create_time: Some(value.create_time),
            update_time: value.update_time,
            agent_type: value.agent_type,
            tag: value.tag,
        })
    }
}

impl TryFrom<WorkflowFavorite> for entity::document::Agent {
    type Error = error::Error;

    fn try_from(value: WorkflowFavorite) -> Result<Self, Self::Error> {
        Ok(Self {
            id: match value.id {
                Some(id) => id,
                None => return Err(error::AgentError::IdCanNotBeEmpty.into()),
            },
            parent_id: value.group_id,
            name: match value.name.filter(|name| !name.trim().is_empty()) {
                Some(name) => name,
                None => return Err(error::AgentError::NameCanNotBeEmpty.into()),
            },
            r#type: entity::document::AgentType::Workflow,
            description: value.description,
            workflow_id: value.workflow_id,
            workflow: None,
            user_id: match value.user_id {
                Some(user_id) => user_id,
                None => {
                    tracing::warn!("user_id is none");
                    "".to_string()
                }
            },
            tenant_id: match value.tenant_id {
                Some(tenant_id) => tenant_id,
                None => {
                    tracing::warn!("tenant_id is none");
                    "".to_string()
                }
            },
            create_time: value.create_time.unwrap_or_else(OffsetDateTime::now_utc),
            update_time: value.update_time,
            agent_type: value.agent_type,
            tag: value.tag,
        })
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
struct UpdateTagRequest {
    pub id: Uuid,
    pub tag: Option<String>,
}

#[derive(Clone, Debug, Deserialize)]
struct AddWorkflowFavoriteRequest {
    #[serde(flatten)]
    workflow_favorite: WorkflowFavorite,
    conversation_id: Option<uuid::Uuid>,
}

async fn add_workflow_favorite(
    State(RouterState { repository, .. }): State<RouterState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<AddWorkflowFavoriteRequest>,
) -> Response {
    let name = request.workflow_favorite.name.clone();
    if name.is_none() || name.as_ref().unwrap().trim().is_empty() {
        return Response::bad_request("名称不能为空".to_string());
    }
    let count = repository
        .count::<entity::document::Agent>(doc! {
            entity::document::AgentField::Name.as_str(): name.unwrap(),
            entity::document::AgentField::UserId.as_str(): claims.user_id(),
            entity::document::AgentField::TenantId.as_str(): claims.tenant_id(),
        })
        .await
        .map_err(Error::error)?;
    if count > 0 {
        return Response::bad_request("Agent名称重复".to_string());
    }

    let AddWorkflowFavoriteRequest {
        mut workflow_favorite,
        conversation_id,
    } = request;

    workflow_favorite.id = Some(Uuid::now_v7());
    workflow_favorite.create_time = Some(OffsetDateTime::now_utc());
    workflow_favorite.user_id = Some(claims.user_id());
    workflow_favorite.tenant_id = Some(claims.tenant_id());
    //新建一个工作流类型的收藏节点，先存一个空的workflow_id，修改工作流时用这个workflow_id update到workflow里
    if workflow_favorite.workflow_id.is_none() {
        let new_workflow_id = Uuid::now_v7();
        workflow_favorite.workflow_id = Some(new_workflow_id);
        // 插入这个空的workflow
        repository
            .insert_one(entity::document::Workflow {
                id: new_workflow_id,
                start_id: None,
                edges: bson::to_bson(&Vec::<Edge>::new())
                    .map_err(|e| Error::bad_request_error(format!("转换错误: {e}")))?,
                nodes: bson::to_bson(&Vec::<Node>::new())
                    .map_err(|e| Error::bad_request_error(format!("转换错误: {e}")))?,
                metadata: bson::Bson::Null,
                config: bson::Bson::Null,
                diagram: bson::Bson::Null,
                status: entity::document::WorkflowStatus::Normal,
                user_id: claims.user_id(),
                tenant_id: claims.tenant_id(),
                runtime: bson::Bson::Null,
            })
            .await
            .map_err(|e| Error::bad_request_error(format!("保存wworkflow失败: {e:?}")))?;
    } else {
        // 从对话中收藏工作流时，复制一个相同的工作流实例，避免多个收藏使用同一个工作流实例
        let new_id = Uuid::now_v7();
        match repository
            .find_one::<entity::document::Workflow>(doc! {
                ID: workflow_favorite.workflow_id.unwrap()
            })
            .await
        {
            Ok(option) => {
                if let Some(workflow) = option {
                    let mut document: entity::document::Workflow = if let Some(conversation_id) =
                        conversation_id
                    {
                        let mut workflow: workflow::Workflow = workflow.clone().try_into().map_err(|e|{
                            (
                                axum::http::StatusCode::BAD_REQUEST,
                                Json(serde_json::json!({ "error":format!("转换workflow失败:{:?}",e) })),
                            )
                        })?;

                        let workflow_runtime = workflow.runtime.get_or_insert_default();

                        let conversation_document =
                            repository.load_conversation_by_id(conversation_id).await.map_err(|e|{
                                (
                                    axum::http::StatusCode::BAD_REQUEST,
                                Json(
                                    serde_json::json!({ "error": format!("加载对话失败: {}", e) })
                                )
                                )
                            })?;
                        let conversation: Conversation = match Conversation::try_from(
                            conversation_document,
                        ) {
                            Ok(conversation) => conversation,
                            Err(e) => {
                                tracing::error!("转换对话失败: {:?}", e);
                                return Err((
                                    axum::http::StatusCode::BAD_REQUEST,
                                    Json(
                                        serde_json::json!({ "error": format!("转换对话失败: {}", e) }),
                                    ),
                                ));
                            }
                        };

                        let context = conversation.get_context_with_workflow(workflow.id);

                        // 添加转换函数
                        context.into_iter().for_each(|m| {
                            if let Author::Tool {
                                step_id: Some(step_id),
                                ..
                            } = m.author
                            {
                                let node = match workflow.nodes.iter_mut().find(|n| n.id == step_id)
                                {
                                    Some(node) => node,
                                    None => return,
                                };

                                let mut node_clone = node.as_ref().clone();

                                if workflow.start_id.as_ref().is_some_and(|id| *id == node.id) {
                                    let input = m.content.iter().find_map(|c| match &c.content {
                                        ContentItemInner::View {
                                            view:
                                                ContentView::Card(ContentCard {
                                                    r#type,
                                                    details: Some(ContentViewOrText::Text(text)),
                                                    ..
                                                }),
                                            ..
                                        } if r#type == "input" => Some(text),
                                        _ => None,
                                    });

                                    if let Some(input) = input {
                                        let input = input
                                            .trim_start_matches("```json")
                                            .trim_end_matches("```")
                                            .trim();
                                        let config = node_clone.config.get_or_insert_default();
                                        if let Ok(default_value) = serde_json::from_str(input) {
                                            config.default_value = Some(Arc::new(default_value));
                                        }
                                    }
                                }

                                let converter = m.content.iter().find_map(|c| match &c.content {
                                    ContentItemInner::View {
                                        view:
                                            ContentView::Card(ContentCard {
                                                r#type,
                                                details: Some(ContentViewOrText::Text(text)),
                                                ..
                                            }),
                                        ..
                                    } if r#type == "code" => Some(text),
                                    _ => None,
                                });

                                if let Some(converter) = converter {
                                    let converter = converter
                                        .trim_start_matches("```python\n")
                                        .trim_end_matches("\n```");
                                    let config = node_clone.config.get_or_insert_default();
                                    config.input_converter =
                                        Some(Arc::new(ScriptConverter::Python {
                                            code: Arc::new(converter.to_owned()),
                                        }));
                                }

                                *node = Arc::new(node_clone);

                                m.content
                                    .iter()
                                    .filter_map(|c| {
                                        if let ContentItemInner::Interaction { interaction } =
                                            &c.content
                                        {
                                            Some(interaction)
                                        } else {
                                            None
                                        }
                                    })
                                    .for_each(|i| {
                                        if let Some(result) = i.content.get_result() {
                                            workflow_runtime
                                                .interactions
                                                .insert(i.id.clone(), result);
                                        }
                                    });
                            }
                        });

                        workflow.try_into().map_err(Error::cast_error)?
                    } else {
                        return Response::not_found(format!(
                            "conversation_id 不存在: {conversation_id:?}"
                        ));
                    };

                    // TODO 直接把workflow给agent
                    document.id = new_id;
                    repository
                        .insert_one(document)
                        .await
                        .map_err(|e| Error::bad_request_error(format!("保存工作流失败: {e:?}")))?;
                } else {
                    return Response::not_found("工作流id不存在".to_string());
                }
            }
            Err(e) => {
                return Response::bad_request(format!("{e:?}"));
            }
        }
        workflow_favorite.workflow_id = Some(new_id);
    }
    let workflow_tree_user: entity::document::Agent = workflow_favorite
        .clone()
        .try_into()
        .map_err(Error::cast_error)?;
    repository
        .insert_one(workflow_tree_user)
        .await
        .map_err(|e| Error::bad_request_error(format!("保存angent失败: {e:?}")))?;
    Response::body(workflow_favorite)
}

async fn get_workflow_favorite(
    State(RouterState { repository, .. }): State<RouterState>,
    Query(workflow_favorite): Query<WorkflowFavorite>,
    Extension(claims): Extension<Claims>,
) -> Response {
    use entity::document::*;

    let mut filter = doc! {
        AgentField::Type.as_str(): AgentTypeField::Workflow.as_str(),
        AgentField::UserId.as_str(): claims.user_id(),
        AgentField::TenantId.as_str(): claims.tenant_id(),
    };
    match workflow_favorite.group_id {
        Some(group_id) => filter.insert(AgentField::ParentId.as_str(), group_id),
        _ => filter.insert(AgentField::ParentId.as_str(), bson::Bson::Null),
    };

    workflow_favorite
        .name
        .as_ref()
        .filter(|name| !name.is_empty())
        .map(|name| {
            filter.insert(
                AgentField::Name.as_str(),
                doc! {
                    REGEX: format!("{}", name),
                    OPTIONS: "i",
                },
            )
        });

    workflow_favorite
        .agent_type
        .as_ref()
        .filter(|agent_type| !agent_type.is_empty())
        .map(|agent_type| {
            filter.insert(
                AgentField::AgentType.as_str(),
                doc! {
                    REGEX: format!("{}", agent_type),
                    OPTIONS: "i",
                },
            )
        });

    let result = repository
        .aggregate::<entity::document::Agent>(vec![
            doc! {
                MATCH:filter
            },
            doc! {
                LOOKUP: {
                        "from": "workflow",
                        "localField": "workflow_id",
                        "foreignField": ID,
                        "as": "workflow",
                    }
            },
            doc! {
                    UNWIND: {
                        "path": "$workflow",
                        "preserveNullAndEmptyArrays": true
                    }
            },
        ])
        .await
        .map_err(|e| {
            tracing::error!("查询agent失败: {:?}", e);
            Error::error(e)
        })?;

    let result: Vec<WorkflowFavorite> = result
        .into_iter()
        .filter_map(|document| {
            let agent: entity::document::Agent = match bson::from_document(document) {
                Ok(agent) => agent,
                Err(e) => {
                    tracing::error!("转换agent失败: {:?}", e);
                    return None;
                }
            };
            let option = agent.workflow.clone();
            match WorkflowFavorite::try_from(agent) {
                Ok(mut w) => {
                    if let Some(workflow_document) = option {
                        let workflow: workflow::Workflow = match workflow_document.try_into() {
                            Ok(w) => w,
                            Err(e) => {
                                tracing::error!("转换workflow失败: {:?}", e);
                                return None;
                            }
                        };
                        let workflow_string = match SimpleWorkflow::build_string(&workflow) {
                            Ok(s) => s.0,
                            Err(e) => {
                                tracing::error!("转换workflow string失败: {:?}", e);
                                return None;
                            }
                        };
                        w.workflow = Some(workflow_string);
                    }
                    Some(w)
                }
                Err(e) => {
                    tracing::error!("{:?}", e);
                    None
                }
            }
        })
        .collect();
    Response::body(result)
}

async fn update_workflow_favorite(
    State(RouterState { repository, .. }): State<RouterState>,
    Path(_id): Path<Uuid>,
    Json(mut workflow_favorite): Json<WorkflowFavorite>,
) -> Response {
    workflow_favorite.update_time = Some(OffsetDateTime::now_utc());
    let workflow_tree_user: entity::document::Agent =
        workflow_favorite.try_into().map_err(|e| {
            tracing::error!("转换错误: {:?}", e);
            Error::cast_error(e)
        })?;
    repository
        .replace_one(doc! {ID: workflow_tree_user.id}, workflow_tree_user)
        .await
        .map_err(|e| {
            tracing::error!("更新Agent失败: {e:?}");
            Error::bad_request_error(format!("更新Agent失败: {e:?}"))
        })?;
    Response::success()
}

async fn delete_workflow_favorite(
    State(RouterState { repository, .. }): State<RouterState>,
    Path(id): Path<Uuid>,
    Query(_workflow_favorite): Query<WorkflowFavorite>,
) -> Response {
    repository
        .delete_by_id::<entity::document::Agent>(id)
        .await
        .map_err(|e| {
            tracing::error!("删除agent失败: {e:?}");
            Error::bad_request_error(format!("删除Angent失败: {e:?}"))
        })?;
    Response::success()
}

async fn add_workflow_favorite_group(
    State(RouterState { repository, .. }): State<RouterState>,
    Extension(claims): Extension<Claims>,
    Json(mut workflow_favorite_group): Json<WorkflowFavoriteGroupRequest>,
) -> Response {
    let id = Uuid::now_v7();
    workflow_favorite_group.id = Some(id);
    workflow_favorite_group.create_time = Some(OffsetDateTime::now_utc());
    workflow_favorite_group.user_id = Some(claims.user_id());
    workflow_favorite_group.tenant_id = Some(claims.tenant_id());
    let workflow_tree_user: entity::document::Agent = workflow_favorite_group
        .try_into()
        .map_err(Error::cast_error)?;
    repository
        .insert_one(workflow_tree_user)
        .await
        .map_err(Error::error)?;
    Response::success()
}

async fn get_workflow_favorite_group(
    State(RouterState { repository, .. }): State<RouterState>,
    Query(workflow_favorite_group): Query<WorkflowFavoriteGroupRequest>,
    Extension(claims): Extension<Claims>,
) -> Response {
    use entity::document::*;
    let mut filter = doc! {
        AgentField::UserId.as_str(): claims.user_id(),
        AgentField::TenantId.as_str(): claims.tenant_id(),
    };
    workflow_favorite_group
        .name
        .as_ref()
        .filter(|name| !name.is_empty())
        .map(|name| {
            filter.insert(
                AgentField::Name.as_str(),
                doc! {
                    REGEX: format!("{}", name),
                    OPTIONS: "i",
                },
            )
        });

    match repository.find::<entity::document::Agent>(filter).await {
        Ok(agents) => {
            let response = model_to_response(agents);
            Response::body(response)
        }
        Err(e) => {
            tracing::error!("查询Agent失败: {:?}", e);
            Response::bad_request(format!("查询Agent失败: {e:?}"))
        }
    }
}

fn model_to_response(models: Vec<entity::document::Agent>) -> Vec<WorkflowFavoriteGroupResponse> {
    use entity::document::*;

    if models.is_empty() {
        return Vec::new();
    }

    // 1. 将所有节点转换为 Response 类型并按 parent_id 分组
    let mut children_map: HashMap<Option<Uuid>, Vec<WorkflowFavoriteGroupResponse>> =
        HashMap::new();
    for model in models {
        let node = WorkflowFavoriteGroupResponse {
            id: model.id,
            parent_id: model.parent_id,
            name: model.name,
            description: model.description,
            workflow_id: model.workflow_id,
            r#type: match model.r#type {
                AgentType::Folder => WorkflowFavoriteType::Folder,
                AgentType::Workflow => WorkflowFavoriteType::Workflow,
            },
            children: Vec::new(),
            create_time: Some(model.create_time),
            update_time: model.update_time,
            agent_type: model.agent_type,
            tag: model.tag,
        };
        children_map.entry(model.parent_id).or_default().push(node);
    }

    // 2. 提取根节点
    let mut roots = children_map.remove(&None).unwrap_or_default();
    if roots.is_empty() && !children_map.is_empty() {
        tracing::warn!(
            "No root nodes found, but other nodes exist. Possible circular dependencies or orphan nodes."
        );
    }

    // 3. 为每个根节点构建子树
    for root in &mut roots {
        let mut path = HashSet::new();
        path.insert(root.id); // 将根节点ID加入路径
        build_tree_recursive(root, &mut children_map, &mut path);
    }

    // 如果 children_map 中还有剩余的节点，说明它们是孤儿节点或循环引用的一部分
    if !children_map.is_empty() {
        tracing::debug!(
            "{} nodes could not be placed in the tree. They might be orphans or part of a circular dependency.",
            children_map.values().map(Vec::len).sum::<usize>()
        );
    }

    // 4. (可选) 排序
    roots.sort_by(|a, b| a.name.cmp(&b.name));
    for root in &mut roots {
        sort_children_recursively(root);
    }

    roots
}

/// 带递归保护的树构建辅助函数（重构版）
fn build_tree_recursive(
    parent: &mut WorkflowFavoriteGroupResponse,
    children_map: &mut HashMap<Option<Uuid>, Vec<WorkflowFavoriteGroupResponse>>,
    path: &mut HashSet<Uuid>, // 当前从根到父节点的路径
) {
    // 关键：直接 remove 子节点列表，获取其所有权。这样后续操作不会与 map 的借用冲突。
    if let Some(mut children) = children_map.remove(&Some(parent.id)) {
        for child in &mut children {
            // --- 递归保护检查 ---
            if path.contains(&child.id) {
                tracing::debug!(
                    "Circular dependency detected: Node '{}' ({}) is an ancestor of itself. Aborting this branch.",
                    child.name,
                    child.id
                );
                // 标记这个节点为问题节点，或者直接跳过
                // 不把它加入树中
                continue;
            }

            // --- 递归调用 ---
            // 1. 将子节点ID加入路径
            path.insert(child.id);

            // 2. 对子节点进行递归构建
            build_tree_recursive(child, children_map, path);

            // 3. 回溯：将子节点ID从路径中移除
            path.remove(&child.id);
            // --- 结束递归 ---
        }
        // 将处理完的子节点列表（可能有些已被跳过）赋给父节点
        parent.children = children;
    }
}

/// 递归地对节点的子节点按名称排序（与之前完全相同）。
fn sort_children_recursively(node: &mut WorkflowFavoriteGroupResponse) {
    if !node.children.is_empty() {
        node.children.sort_by(|a, b| a.name.cmp(&b.name));
        for child in &mut node.children {
            sort_children_recursively(child);
        }
    }
}

async fn update_workflow_favorite_group(
    State(RouterState { repository, .. }): State<RouterState>,
    Path(id): Path<uuid::Uuid>,
    Json(workflow_favorite_group): Json<WorkflowFavoriteGroupRequest>,
) -> Response {
    let mut agent = repository
        .find_one::<entity::document::Agent>(doc! {
            ID:id
        })
        .await
        .map_err(|e| {
            tracing::error!("查询Agent失败: {:?}", e);
            Error::error(e)
        })?
        .ok_or_else(|| Error::not_found_error(format!("Agent不存在: {id:?}")))?;
    agent.update_time = Some(time::OffsetDateTime::now_utc());
    agent.name = workflow_favorite_group
        .name
        .filter(|name| !name.trim().is_empty())
        .ok_or_else(|| Error::bad_request_error("名称不能为空".to_string()))?;
    agent.description = workflow_favorite_group.description;
    repository
        .update_one::<entity::document::Agent>(
            doc! {ID: id},
            doc! {SET: agent.to_document().map_err(
                |e| {
                    tracing::error!("转换错误: {:?}", e);
                    Error::cast_error(e)
                }
            )?},
        )
        .await
        .map_err(|e| {
            tracing::error!("更新agent失败: {e:?}");
            Error::bad_request_error(format!("更新Agent失败: {e:?}"))
        })?;
    Response::success()
}

async fn delete_workflow_favorite_group(
    State(RouterState { repository, .. }): State<RouterState>,
    Path(id): Path<uuid::Uuid>,
) -> Response {
    repository
        .delete_by_id::<entity::document::Agent>(id)
        .await
        .map_err(|e| {
            tracing::error!("删除agent失败: {e:?}");
            Error::bad_request_error(format!("删除Agent失败: {e:?}"))
        })?;
    Response::success()
}

async fn update_tag(
    State(RouterState { repository, .. }): State<RouterState>,
    Json(requests): Json<Vec<UpdateTagRequest>>,
) -> Response {
    //清空所有tag
    repository
        .update_many::<entity::document::Agent>(
            doc! {},
            doc! {
                "$unset":{"tag":""}
            },
        )
        .await
        .map_err(Error::error)?;

    //更新新的tag
    let now = OffsetDateTime::now_utc();
    let namespace = repository
        .collection::<entity::document::Agent>()
        .await
        .namespace();

    let models = requests
        .into_iter()
        .map(|request| {
            WriteModel::UpdateOne(
                UpdateOneModel::builder()
                    .namespace(namespace.clone())
                    .filter(doc! { ID: request.id })
                    .update(doc! { SET: { "tag": request.tag,"update_time":now } })
                    .build(),
            )
        })
        .collect::<Vec<_>>();

    repository.bulk_write(models).await.map_err(|e| {
        tracing::error!("批量更新失败: {e:?}");
        Error::bad_request_error(format!("批量更新失败: {e:?}"))
    })?;

    Response::success()
}

#[allow(dead_code)]
fn demo() -> Workflow {
    Workflow {
        id: uuid::Uuid::now_v7(),
        start_id: Some("start".to_string()),
        edges: vec![
            Edge {
                source_node: "start".to_string(),
                target_node: "select".to_string(),
                ..Default::default()
            },
            Edge {
                source_node: "select".to_string(),
                source_port: Some("port1".to_string()),
                target_node: "end-port1".to_string(),
                ..Default::default()
            },
            Edge {
                source_node: "select".to_string(),
                source_port: Some("port2".to_string()),
                target_node: "end-port2".to_string(),
                ..Default::default()
            },
            Edge {
                source_node: "select".to_string(),
                target_node: "end-3".to_string(),
                config: Some(EdgeConfig {
                    expression: Some(Expr::Compare {
                        left: Box::new(Expr::Var(Variable(vec![
                            Index::String("start".to_string()),
                            Index::String("e3".to_string()),
                        ]))),
                        op: CompOp::Eq,
                        right: Box::new(Expr::Literal(Value::String("data3".to_string()))),
                    }),
                }),
                ..Default::default()
            },
        ]
        .into_iter()
        .map(Arc::new)
        .collect(),
        nodes: vec![
            Node {
                id: "start".to_string(),
                module: "builtin".to_string(),
                tool: "start".to_string(),
                ..Default::default()
            },
            Node {
                id: "select".to_string(),
                module: "builtin".to_string(),
                tool: "select".to_string(),
                ports: Some(vec![
                    NodePort {
                        id: "port1".to_string(),
                        r#type: NodePortType::Output,
                        config: Some(NodePortConfig {
                            expression: Some(Expr::Compare {
                                left: Box::new(Expr::Var(Variable(vec![
                                    Index::String("start".to_string()),
                                    Index::String("p1".to_string()),
                                ]))),
                                op: CompOp::Eq,
                                right: Box::new(Expr::Literal(Value::String("data1".to_string()))),
                            }),
                            ..Default::default()
                        }),
                        ..Default::default()
                    },
                    NodePort {
                        id: "port2".to_string(),
                        r#type: NodePortType::Output,
                        config: Some(NodePortConfig {
                            expression: Some(Expr::Compare {
                                left: Box::new(Expr::Var(Variable(vec![
                                    Index::String("start".to_string()),
                                    Index::String("p2".to_string()),
                                ]))),
                                op: CompOp::Eq,
                                right: Box::new(Expr::Literal(Value::String("data2".to_string()))),
                            }),
                            ..Default::default()
                        }),
                        ..Default::default()
                    },
                ]),
                ..Default::default()
            },
            Node {
                id: "end-port1".to_string(),
                module: "builtin".to_string(),
                tool: "end".to_string(),
                ..Default::default()
            },
            Node {
                id: "end-port2".to_string(),
                module: "builtin".to_string(),
                tool: "end".to_string(),
                ..Default::default()
            },
            Node {
                id: "end-3".to_string(),
                module: "builtin".to_string(),
                tool: "end".to_string(),
                ..Default::default()
            },
        ]
        .into_iter()
        .map(Arc::new)
        .collect(),
        ..Default::default()
    }
}
