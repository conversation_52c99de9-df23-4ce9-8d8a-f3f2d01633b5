use std::sync::Arc;

use axum::{
    Json, Router,
    extract::{Query, State},
    http::StatusCode,
    response::{IntoResponse, Response},
    routing::get,
};
use resource::ResourceManager;
use serde::{Deserialize, Serialize};

pub fn router(resource_manager: Arc<ResourceManager>) -> Router {
    Router::new()
        .route("/", get(read_file))
        .with_state(resource_manager.clone())
}

#[derive(Debug, Deserialize, Serialize)]
pub struct FileTarget {
    bucket: String,
    object: String,
    version_id: Option<String>,
}

async fn read_file(
    State(resource_manager): State<Arc<ResourceManager>>,
    Query(target): Query<FileTarget>,
) -> Result<Response, (StatusCode, Json<serde_json::Value>)> {
    match resource_manager
        .read_file_stream(&target.bucket, &target.object, target.version_id)
        .await
    {
        Ok((stream, headers)) => {
            let body = axum::body::Body::from_stream(stream);

            Ok((headers, body).into_response())
        }
        Err(e) => Err((
            StatusCode::BAD_REQUEST,
            Json(serde_json::json!({ "error": e.to_string() })),
        )),
    }
}
