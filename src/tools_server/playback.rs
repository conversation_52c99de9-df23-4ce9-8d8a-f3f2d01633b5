use std::sync::Arc;

use axum::{
    Extension, Json, Router,
    extract::{Multipart, Path, State},
    routing::{delete, get},
};
use bson::doc;
use bytes::Bytes;
use entity::{Document as _, DocumentRepository, ID, SET};
use futures::StreamExt;
use resource::ResourceManager;
use shared_core::conversation::{ConversationPlayback, ImageInfo};
use uuid::Uuid;

use crate::auth::Claims;
use crate::service::{ErrExt, Error, Response, ResponseExt};

#[derive(Clone)]
struct PlaybackState {
    repository: Arc<DocumentRepository>,
    resource_manager: Arc<ResourceManager>,
}

pub fn create_router(
    repository: Arc<DocumentRepository>,
    resource_manager: Arc<ResourceManager>,
) -> Router {
    Router::new()
        .route("/", get(list_playback).post(add_playback))
        .route("/{id}", delete(delete_playback).put(update_playback))
        .with_state(PlaybackState {
            repository,
            resource_manager,
        })
}

async fn list_playback(State(state): State<PlaybackState>) -> Response {
    let playbacks = state
        .repository
        .find::<entity::document::Playback>(doc! {})
        .await
        .map_err(|e| {
            tracing::error!("查询对话回放失败: {:?}", e);
            Error::bad_request_error(format!("查询对话回放失败: {e}"))
        })?;
    let playbacks = playbacks
        .into_iter()
        .flat_map(ConversationPlayback::try_from)
        .collect::<Vec<_>>();
    Response::body(playbacks)
}

async fn add_playback(
    State(state): State<PlaybackState>,
    Extension(claims): Extension<Claims>,
    mut multipart: Multipart,
) -> Response {
    let mut playback = ConversationPlayback {
        id: None,
        title: "".to_string(),
        description: None,
        conversation_id: Uuid::now_v7(),
        r#type: None,
        images: vec![],
        user_id: claims.user_id(),
        tenant_id: claims.tenant_id(),
    };
    let mut files: Vec<(String, Bytes)> = vec![];

    while let Ok(option) = multipart.next_field().await {
        if let Some(field) = option {
            let field_name = match field.name() {
                Some(field_name) => field_name,
                None => break,
            };

            match field_name {
                "id" => {
                    break;
                }
                "title" => {
                    if let Ok(title) = field.text().await {
                        if title.is_empty() || title.trim().is_empty() {
                            return Response::bad_request("名称不能为空".to_string());
                        }
                        let count = state.repository
                            .count::<entity::document::Playback>(doc! {
                                entity::document::PlaybackField::Title.as_str(): &title,
                                entity::document::PlaybackField::UserId.as_str(): claims.user_id(),
                                entity::document::PlaybackField::TenantId.as_str(): claims.tenant_id(),
                        })
                        .await
                        .map_err(Error::error)?;

                        if count > 0 {
                            return Response::bad_request("案例名称重复".to_string());
                        }

                        playback.title = title;
                    }
                }
                "description" => {
                    if let Ok(description) = field.text().await {
                        playback.description = Some(description);
                    }
                }
                "conversation_id" => {
                    if let Ok(conversation_id) = field.text().await {
                        match conversation_id.parse() {
                            Ok(conversation_id) => {
                                playback.conversation_id = conversation_id;
                            }
                            Err(e) => {
                                tracing::error!(
                                    "解析conversation_id:[{:?}]失败: {:?}",
                                    conversation_id,
                                    e
                                );
                            }
                        }
                    }
                }
                "type" => {
                    if let Ok(r#type) = field.text().await {
                        playback.r#type = Some(r#type);
                    }
                }
                "images" => {
                    files.push((
                        field.file_name().unwrap().to_string(),
                        match field.bytes().await {
                            Ok(bytes) => bytes,
                            Err(_) => {
                                tracing::error!("读取文件失败");
                                return Response::bad_request("读取文件失败".to_string());
                            }
                        },
                    ));
                }
                _ => {
                    tracing::error!("不支持的字段: {}", field_name);
                    break;
                }
            }
        } else {
            break;
        }
    }

    let images = futures::stream::iter(files)
        .map(|file| {
            let resource_manager = state.resource_manager.clone();
            async move {
                let info = resource_manager
                    .write_file(resource::PLAYBACK_BUCKET_NAME, &file.0, file.1)
                    .await
                    .map_err(|e| {
                        tracing::error!("写入文件失败: {:?}", e);
                        Error::bad_request_error(format!("写入文件失败: {e}"))
                    })
                    .unwrap();
                ImageInfo {
                    bucket: info.bucket,
                    object: info.object,
                    version_id: info.version_id,
                }
            }
        })
        .buffer_unordered(2)
        .collect::<Vec<_>>()
        .await;

    playback.images = images;

    let playback: entity::document::Playback = playback.try_into().map_err(|e| {
        tracing::error!("转换对话回放失败: {:?}", e);
        Error::bad_request_error(format!("转换案例失败: {e}"))
    })?;

    match state.repository.insert_one(playback).await {
        Ok(_) => Response::success(),
        Err(e) => {
            tracing::error!("保存对话回放失败: {:?}", e);
            Response::bad_request(format!("保存案例失败: {e}"))
        }
    }
}

async fn update_playback(
    State(state): State<PlaybackState>,
    Path(id): Path<Uuid>,
    Extension(claims): Extension<Claims>,
    mut multipart: Multipart,
) -> Response {
    let mut playback = match state
        .repository
        .find_one::<entity::document::Playback>(doc! {
            ID:id
        })
        .await
        .map_err(|e| {
            tracing::error!("查询对话回放失败: {:?}", e);
            Error::bad_request_error(format!("查询对话回放失败: {e}"))
        })? {
        Some(playback) => playback,
        None => {
            tracing::error!("查询对话回放失败: {:?}", id);
            return Response::not_found(format!("案例不存在: {id}"));
        }
    };
    let old_images: Vec<ImageInfo> = bson::from_bson(playback.images.clone()).map_err(|e| {
        tracing::error!("解析对话回放图片失败: {:?}", e);
        Error::bad_request_error(format!("解析对话回放图片失败: {e}"))
    })?;

    let mut files: Vec<(String, Bytes)> = vec![];

    while let Ok(option) = multipart.next_field().await {
        if let Some(field) = option {
            let field_name = match field.name() {
                Some(field_name) => field_name,
                None => break,
            };

            match field_name {
                "title" => {
                    if let Ok(title) = field.text().await {
                        if title.is_empty() || title.trim().is_empty() {
                            return Response::bad_request("名称不能为空".to_string());
                        }
                        let count = state.repository
                            .count::<entity::document::Playback>(doc! {
                                entity::document::PlaybackField::Title.as_str(): &title,
                                entity::document::PlaybackField::UserId.as_str(): claims.user_id(),
                                entity::document::PlaybackField::TenantId.as_str(): claims.tenant_id(),
                        })
                        .await
                        .map_err(Error::error)?;

                        if count > 0 {
                            return Response::bad_request("案例名称重复".to_string());
                        }

                        playback.title = title;
                    }
                }
                "description" => {
                    if let Ok(description) = field.text().await {
                        playback.description = Some(description);
                    }
                }
                "type" => {
                    if let Ok(r#type) = field.text().await {
                        playback.r#type = Some(r#type);
                    }
                }
                "images" => {
                    files.push((
                        field.file_name().unwrap().to_string(),
                        match field.bytes().await {
                            Ok(bytes) => bytes,
                            Err(_) => {
                                tracing::error!("读取文件失败");
                                return Response::bad_request("读取文件失败".to_string());
                            }
                        },
                    ));
                }
                _ => {
                    tracing::error!("不支持的字段: {}", field_name);
                    break;
                }
            }
        } else {
            break;
        }
    }

    if !files.is_empty() {
        //删除旧图片
        if !old_images.is_empty() {
            let objects_to_delete = old_images
                .into_iter()
                .map(|info| (info.object, info.version_id))
                .collect::<Vec<_>>();
            state
                .resource_manager
                .delete_files(
                    resource::PLAYBACK_BUCKET_NAME.to_string(),
                    objects_to_delete,
                )
                .await
                .map_err(|e| {
                    tracing::error!("删除对话回放图片失败: {:?}", e);
                    Error::bad_request_error(format!("删除对话回放图片失败: {e}"))
                })?;
        }

        let images = futures::stream::iter(files)
            .map(|file| {
                let resource_manager = state.resource_manager.clone();
                async move {
                    let info = resource_manager
                        .write_file(resource::PLAYBACK_BUCKET_NAME, &file.0, file.1)
                        .await
                        .map_err(|e| {
                            tracing::error!("写入文件失败: {:?}", e);
                            Error::bad_request_error("写入文件失败".to_string())
                        })
                        .unwrap();
                    ImageInfo {
                        bucket: info.bucket,
                        object: info.object,
                        version_id: info.version_id,
                    }
                }
            })
            .buffer_unordered(2)
            .collect::<Vec<_>>()
            .await;

        if !images.is_empty() {
            match bson::to_bson(&images) {
                Ok(images) => {
                    playback.images = images;
                }
                Err(e) => {
                    tracing::error!("转换对话回放图片失败: {:?}", e);
                    return Response::bad_request(format!("转换对话回放图片失败: {e}"));
                }
            };
        }
    }

    match state
        .repository
        .update_one::<entity::document::Playback>(
            doc! {
                ID:id
            },
            doc! {
                SET: playback.to_document().map_err(|e| {
                tracing::error!("转换对话回放失败: {:?}", e);
                Error::bad_request_error(format!("转换对话回放失败: {e}"))
            })?
            },
        )
        .await
    {
        Ok(_) => Ok(Json(serde_json::json!({ "status": "success" }))),
        Err(e) => {
            tracing::error!("更新对话回放失败: {:?}", e);
            Response::bad_request(format!("更新对话回放失败: {e}"))
        }
    }
}

async fn delete_playback(Path(id): Path<Uuid>, State(state): State<PlaybackState>) -> Response {
    match state
        .repository
        .find_one_and_delete::<entity::document::Playback>(doc! {
            ID:id
        })
        .await
        .map_err(|e| {
            tracing::error!("删除对话回放失败: {:?}", e);
            Error::bad_request_error(format!("删除对话回放失败: {e}"))
        })? {
        Some(playback) => {
            let deleted_images: Vec<ImageInfo> = bson::from_bson(playback.images).map_err(|e| {
                tracing::error!("解析对话回放图片失败: {:?}", e);
                Error::bad_request_error(format!("解析对话回放图片失败: {e}"))
            })?;

            if !deleted_images.is_empty() {
                let objects_to_delete = deleted_images
                    .into_iter()
                    .map(|info| (info.object, info.version_id))
                    .collect::<Vec<_>>();
                state
                    .resource_manager
                    .delete_files(
                        resource::PLAYBACK_BUCKET_NAME.to_string(),
                        objects_to_delete,
                    )
                    .await
                    .map_err(|e| {
                        tracing::error!("删除对话回放图片失败: {:?}", e);
                        Error::bad_request_error(format!("删除对话回放图片失败: {e}"))
                    })?;
            }

            Response::success()
        }
        None => {
            tracing::error!("对话回放不存在: {:?}", id);
            Response::bad_request(format!("对话回放不存在: {id:?}"))
        }
    }
}
