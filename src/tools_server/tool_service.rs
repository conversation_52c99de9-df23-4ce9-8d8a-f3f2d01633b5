use std::{error::Error as _, sync::Arc};

use axum::{
    <PERSON>son, Router,
    extract::{Multipart, Path, State},
    routing::{delete, get, post},
};
use rand::Rng as _;
use resource::ResourceManager;
use serde::{Deserialize, Serialize};
use shared_core::config::{ConfigManager, ModuleConfig};
use tokio::sync::{Mutex, MutexGuard};
use tool::{global::GlobalToolRepository, repository::ToolRepository};

use crate::service::{ErrExt, Error, Response, ResponseExt};

#[derive(Clone)]
struct RouterState {
    global_repository: Arc<GlobalToolRepository>,
    config_manager: Arc<ConfigManager>,
    resource_manager: Arc<ResourceManager>,
    config_lock: Arc<Mutex<()>>,
}

pub fn open_router(
    global_repository: Arc<GlobalToolRepository>,
    config_manager: Arc<ConfigManager>,
    resource_manager: Arc<ResourceManager>,
) -> Router {
    let state = RouterState {
        global_repository,
        config_manager,
        resource_manager,
        config_lock: Arc::new(Mutex::new(())),
    };

    Router::new()
        .route("/reload", post(reload))
        .route("/script", post(update_script))
        .route("/module", post(update_module))
        .route("/module/{name}", delete(delete_module))
        .route("/modules", get(list_modules).post(update_modules))
        .route("/modules/config", get(export_modules).post(update_modules))
        .with_state(state)
}

// timeout 10 seconds
async fn wait_config_lock<'a>(
    config_lock: &'a Arc<Mutex<()>>,
) -> error::Result<MutexGuard<'a, ()>> {
    // 使用 select! 等待 10 秒，如果超时则返回错误
    tokio::select! {
        _ = tokio::time::sleep(std::time::Duration::from_secs(10)) => {
            Err(error::ConfigError::Timeout.into())
        }
        lock = config_lock.lock() => {
            Ok(lock)
        }
    }
}

async fn reload_inner(state: &RouterState) -> error::Result<Json<serde_json::Value>> {
    let global_repository = state.global_repository.clone();
    let config_manager = state.config_manager.clone();

    match config_manager.load_script_modules().await {
        Ok((modules, module_errors)) => {
            let manager = global_repository.get_script().await.manager();
            let mut script = tool::script::ScriptToolRepository::new(manager);

            match script.load(modules).await {
                Ok(script_errors) => {
                    let modules = script.list_modules().await;

                    global_repository.set_script(Arc::new(script)).await;

                    Ok(Json(serde_json::json!({
                        "script_errors": script_errors,
                        "module_errors": module_errors,
                        "modules": modules.ok(),
                    })))
                }
                Err(e) => {
                    tracing::error!("Failed to load script modules: {:?}", e);
                    Err(error::ConfigError::LoadScriptModule(Box::new(e)).into())
                }
            }
        }
        Err(e) => {
            tracing::error!("Failed to load script modules: {:?}", e);
            Err(error::ConfigError::LoadScriptModule(Box::new(e)).into())
        }
    }
}

async fn reload(State(state): State<RouterState>) -> Response {
    let _lock = wait_config_lock(&state.config_lock)
        .await
        .map_err(Error::error)?;

    reload_inner(&state).await.map_err(Error::error)
}

async fn update_script(State(state): State<RouterState>, mut form: Multipart) -> Response {
    let _lock = wait_config_lock(&state.config_lock)
        .await
        .map_err(Error::error)?;

    let field = form
        .next_field()
        .await
        .map_err(|e| Error::bad_request_error(e.to_string()))?
        .ok_or_else(|| Error::bad_request_error("No field found".to_string()))?;

    // 必须为 zip 文件，允许 .zip application/zip, application/octet-stream, application/x-zip-compressed, multipart/x-zip
    // 没有 mime，则检查文件名后缀
    let is_zip = match field.content_type() {
        Some("application/zip")
        | Some("application/octet-stream")
        | Some("application/x-zip-compressed")
        | Some("multipart/x-zip") => true,
        None => field.file_name().unwrap_or_default().ends_with(".zip"),
        _ => false,
    };

    if !is_zip {
        return Response::bad_request("Not a zip file".to_string());
    }

    let bytes = field
        .bytes()
        .await
        .map_err(|e| Error::bad_request_error(e.to_string()))?;

    // 写入临时文件 scrips-<datetime>-<random>.zip
    let temp_path = ConfigManager::root_dir().join(format!(
        "scripts-{}.zip",
        rand::rng().random_range(100000..999999)
    ));

    tokio::fs::write(temp_path, &bytes)
        .await
        .map_err(|e| Error::bad_request_error(e.to_string()))?;

    // 解压 zip 文件
    let mut zip = zip::ZipArchive::new(std::io::Cursor::new(&bytes))
        .map_err(|e| Error::bad_request_error(format!("{}, {:?}", e, e.source())))?;

    zip.extract_unwrapped_root_dir(
        ConfigManager::modules_dir(),
        zip::read::root_dir_common_filter,
    )
    .map_err(|e| Error::bad_request_error(format!("{}, {:?}", e, e.source())))?;

    state
        .resource_manager
        .write_file(resource::RUNTIME_BUCKET_NAME, "tools.zip", bytes)
        .await
        .map_err(|e| Error::bad_request_error(e.to_string()))?;

    reload_inner(&state)
        .await
        .map_err(|e| Error::bad_request_error(e.to_string()))
}

async fn delete_module(State(state): State<RouterState>, Path(name): Path<String>) -> Response {
    let _lock = wait_config_lock(&state.config_lock)
        .await
        .map_err(Error::error)?;

    state
        .config_manager
        .delete_module(&name)
        .await
        .map_err(Error::error)?;

    reload_inner(&state).await.map_err(Error::error)
}

async fn update_module(
    State(state): State<RouterState>,
    Json(module): Json<ModuleConfig>,
) -> Response {
    let _lock = wait_config_lock(&state.config_lock)
        .await
        .map_err(Error::error)?;

    state
        .config_manager
        .update_module(module)
        .await
        .map_err(Error::error)?;

    reload_inner(&state).await.map_err(Error::error)
}

#[derive(Serialize, Deserialize)]
#[serde(untagged)]
enum FlexibleModulesConfig {
    Array(Vec<ModuleConfig>),
    Object { modules: Vec<ModuleConfig> },
}

impl From<FlexibleModulesConfig> for Vec<ModuleConfig> {
    fn from(value: FlexibleModulesConfig) -> Self {
        match value {
            FlexibleModulesConfig::Array(modules) => modules,
            FlexibleModulesConfig::Object { modules } => modules,
        }
    }
}

async fn update_modules(
    State(state): State<RouterState>,
    Json(modules): Json<FlexibleModulesConfig>,
) -> Response {
    let _lock = wait_config_lock(&state.config_lock)
        .await
        .map_err(Error::error)?;

    state
        .config_manager
        .update_modules(modules.into())
        .await
        .map_err(Error::error)?;

    reload_inner(&state).await.map_err(Error::error)
}

async fn list_modules(State(state): State<RouterState>) -> Response {
    let _lock = wait_config_lock(&state.config_lock)
        .await
        .map_err(Error::error)?;

    let modules = state
        .global_repository
        .list_modules()
        .await
        .map_err(Error::error)?;
    Response::body(modules)
}

async fn export_modules(State(state): State<RouterState>) -> Response {
    let _lock = wait_config_lock(&state.config_lock)
        .await
        .map_err(Error::error)?;
    let module_config = state.config_manager.app_config().await.modules;
    Response::body(module_config)
}
