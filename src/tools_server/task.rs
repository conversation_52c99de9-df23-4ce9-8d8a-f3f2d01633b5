use std::{
    collections::{HashMap, hash_map::Entry},
    sync::Arc,
};

use actor::{
    Task, TaskActor, TaskActorEvent, TaskKind, TaskRunner, TaskState, WorkflowActorOptions,
    WorkflowHistory, WorkflowRunner, WorkflowTask,
};
use axum::{
    Router,
    extract::{
        Path, State,
    },
    routing::{get, post},
};
use bson::doc;
use entity::{DocumentRepository, ID};
use ractor::{Actor, ActorRef, ActorStatus};
use resource::ResourceManager;
use tokio::sync::RwLock;

use crate::service::{ErrExt, Error, Response, ResponseExt};

pub fn open_router(task_manager: Arc<TaskManager>) -> Router {
    Router::new()
        .route("/{id}", post(start_task))
        .route("/{id}/start", post(start_task).get(start_task))
        .route("/{id}/kill", post(kill_task).get(kill_task))
        .route("/{id}/state", get(get_task_state))
        .route("/{id}/history", get(get_task_history))
        .with_state(task_manager.clone())
}

pub struct TaskManager {
    pub workflow_runner: Arc<dyn WorkflowRunner + Send + Sync + 'static>,
    pub actors: RwLock<HashMap<uuid::Uuid, ActorRef<TaskActorEvent>>>,
    pub resource_manager: Arc<ResourceManager>,
    pub repository: Arc<DocumentRepository>,
}

async fn start_task(
    State(task_manager): State<Arc<TaskManager>>,
    Path(task_id): Path<uuid::Uuid>,
) -> Response {
    let task = task_manager
        .repository
        .find_one::<entity::document::BackgroundAgent>(doc! {
            ID: task_id
        })
        .await
        .map_err(|e| {
            tracing::error!("查询task失败: {e:?}");
            Error::bad_request_error(format!("{e:?}"))
        })?
        .ok_or(Error::not_found_error("task不存在".to_string()))?;

    let workflow: workflow::Workflow = task_manager
        .repository
        .find_one::<entity::document::Workflow>(bson::doc! {
            ID: task.workflow_id,
        })
        .await
        .map_err(|e| {
            tracing::error!("查询工作流失败: {:?}", e);
            Error::internal_server_error(e.to_string())
        })?
        .ok_or_else(|| Error::not_found_error("工作流不存在".to_string()))?
        .try_into()
        .map_err(|err: error::Error| Error::internal_server_error(err.to_string()))?;

    let mut actors = task_manager.actors.write().await;

    let entry = match actors.entry(task_id) {
        Entry::Occupied(entry) => {
            if entry.get().get_status() == ActorStatus::Running {
                return Response::bad_request("任务正在运行".to_string());
            }

            Entry::Occupied(entry)
        }
        Entry::Vacant(entry) => Entry::Vacant(entry),
    };

    let (actor, _) = Actor::spawn(
        None,
        TaskActor {
            runner: task_manager.clone(),
            task: Arc::new(Task {
                id: task_id,
                tenant_id: task.tenant_id,
                user_id: task.user_id,
                created_at: time::OffsetDateTime::now_utc(),
                kind: TaskKind::Workflow(WorkflowTask {
                    workflow: Arc::new(workflow),
                    options: WorkflowActorOptions {
                        prevent_interaction: true,
                        skip_converter: true,
                        skip_extract_params: true,
                        skip_final_summary: true,
                        skip_summary: true,
                        ..Default::default()
                    },
                }),
            }),
        },
        (),
    )
    .await
    .map_err(|err| Error::bad_request_error(err.to_string()))?;

    actor
        .send_message(TaskActorEvent::Start)
        .map_err(|err| Error::bad_request_error(err.to_string()))?;

    entry.insert_entry(actor);

    Response::success()
}

async fn kill_task(
    State(task_manager): State<Arc<TaskManager>>,
    Path(task_id): Path<uuid::Uuid>,
) -> Response {
    let mut actors = task_manager.actors.write().await;

    if let Some(actor) = actors.remove(&task_id) {
        actor.kill();
    }

    Response::success()
}

async fn get_task_state(
    State(task_manager): State<Arc<TaskManager>>,
    Path(task_id): Path<uuid::Uuid>,
) -> Response {
    let state = task_manager
        .get_task_state(task_id)
        .await
        .map_err(|err| Error::internal_server_error(err.to_string()))?;

    Response::body(state)
}

async fn get_task_history(
    State(task_manager): State<Arc<TaskManager>>,
    Path(task_id): Path<uuid::Uuid>,
) -> Response {
    let history = task_manager
        .get_task_history(task_id)
        .await
        .map_err(|err| Error::internal_server_error(err.to_string()))?;

    Response::body(history)
}

#[async_trait::async_trait]
impl TaskRunner for TaskManager {
    fn workflow_runner(&self) -> Arc<dyn WorkflowRunner + Send + Sync + 'static> {
        self.workflow_runner.clone()
    }

    async fn update_task_state(&self, id: uuid::Uuid, state: TaskState) -> error::Result<()> {
        self.resource_manager
            .update_task_state(id.to_string().as_str(), state)
            .await
    }

    async fn get_task_state(&self, id: uuid::Uuid) -> error::Result<TaskState> {
        self.resource_manager
            .get_task_state(id.to_string().as_str())
            .await
            .and_then(|state| match state {
                Some(state) => Ok(state),
                None => Err(error::TaskError::NotFound(id).into()),
            })
    }

    async fn get_task_history(&self, id: uuid::Uuid) -> error::Result<WorkflowHistory> {
        self.resource_manager
            .get_task_history(id.to_string().as_str())
            .await
            .and_then(|history| match history {
                Some(history) => Ok(history),
                None => Err(error::TaskError::NotFound(id).into()),
            })
    }

    async fn update_task_history(
        &self,
        id: uuid::Uuid,
        history: WorkflowHistory,
    ) -> error::Result<()> {
        self.resource_manager
            .update_task_history(id.to_string().as_str(), history)
            .await
    }
}
