use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;

use axum::{
    Extension, Json, Router,
    extract::{Path, Query, State},
    routing::{delete, get, post},
};
use bson::doc;
use entity::{COUNT, DocumentRepository, FACET, ID, LIMIT, MATCH, PROJECT, SET, SKIP, SORT};
use entity::{OPTIONS, REGEX, document::*};
use futures::{TryFutureExt, TryStreamExt};
use mongodb::options::{FindOneOptions, UpdateOneModel, WriteModel};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{
    auth::Claims,
    service::{ErrExt, Error, Response, ResponseExt},
};

const STEP: i64 = 1 << 32;

pub fn router(repository: Arc<DocumentRepository>) -> Router {
    Router::new()
        .route("/", get(get_background_agent).post(add_background_agent))
        .route(
            "/{id}",
            delete(delete_background_agent).put(update_background_agent),
        )
        .route("/order", post(order))
        .with_state(repository)
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct BackgroundAgentRequest {
    pub id: Option<Uuid>,
    pub name: Option<String>,
    pub description: Option<String>,
    pub r#type: Option<String>,
    pub dynamic: Option<serde_json::Value>,
    pub user_id: Option<String>,
    pub tenant_id: Option<String>,
    pub order: Option<i64>,
    pub workflow_id: Uuid,
}

impl TryFrom<BackgroundAgentRequest> for entity::document::BackgroundAgent {
    type Error = error::Error;

    fn try_from(value: BackgroundAgentRequest) -> Result<Self, Self::Error> {
        Ok(Self {
            id: match value.id {
                Some(id) => id,
                None => return Err(error::AgentError::IdCanNotBeEmpty.into()),
            },
            name: match value.name {
                Some(name) => name,
                None => return Err(error::AgentError::NameCanNotBeEmpty.into()),
            },
            description: value.description,
            r#type: value.r#type,
            dynamic: match value.dynamic {
                Some(dynamic) => {
                    bson::to_bson(&dynamic).map_err(error::MongoError::BsonSerError)?
                }
                None => bson::Bson::Null,
            },
            user_id: match value.user_id {
                Some(user_id) => user_id,
                None => "".to_string(),
            },
            tenant_id: match value.tenant_id {
                Some(tenant_id) => tenant_id,
                None => "".to_string(),
            },
            order: value.order.unwrap_or(0),
            workflow_id: value.workflow_id,
        })
    }
}

impl TryFrom<entity::document::BackgroundAgent> for BackgroundAgentRequest {
    type Error = error::Error;

    fn try_from(value: entity::document::BackgroundAgent) -> Result<Self, Self::Error> {
        Ok(Self {
            id: Some(value.id),
            name: Some(value.name),
            description: value.description,
            r#type: value.r#type,
            dynamic: Some(bson::from_bson(value.dynamic).map_err(error::MongoError::BsonDeError)?),
            user_id: Some(value.user_id),
            tenant_id: Some(value.tenant_id),
            order: Some(value.order),
            workflow_id: value.workflow_id,
        })
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct BackgroundAgentListRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub r#type: Option<String>,
    pub dynamic: Option<serde_json::Value>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackgroundAgentListResponse {
    pub agents: Vec<entity::document::BackgroundAgent>,
    pub limit: usize,
    pub offset: usize,
    pub total: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct OrderRequest {
    pub preceding: Option<Uuid>,
    pub _self: Uuid,
    pub succeeding: Option<Uuid>,
}

async fn add_background_agent(
    State(repository): State<Arc<DocumentRepository>>,
    Extension(claims): Extension<Claims>,
    Json(mut request): Json<BackgroundAgentRequest>,
) -> Response {
    request.id = Some(Uuid::now_v7());
    request.user_id = Some(claims.user_id());
    request.tenant_id = Some(claims.tenant_id());

    let last_order = find_first_or_last_order(&repository.clone(), -1)
        .await
        .map_err(|e| {
            tracing::error!("查询前一个order失败: {e:?}");
            Error::bad_request_error(format!("查询前一个order失败: {e:?}"))
        })?;

    request.order = Some(last_order + STEP);

    let document: entity::document::BackgroundAgent = request
        .try_into()
        .map_err(|e| Error::bad_request_error(format!("转换失败: {e}")))?;

    repository
        .insert_one(document)
        .map_err(Error::error)
        .await?;

    Response::success()
}

async fn get_background_agent(
    Query(request): Query<BackgroundAgentListRequest>,
    State(repository): State<Arc<DocumentRepository>>,
    Extension(claims): Extension<Claims>,
) -> Response {
    let name = request.name;
    let limit = request.limit.unwrap_or(100);
    let offset = (request.offset.unwrap_or(1) - 1) / limit;

    let mut r#match = doc! {
        entity::document::BackgroundAgentField::UserId.as_str(): claims.user_id(),
        entity::document::BackgroundAgentField::TenantId.as_str(): claims.tenant_id(),
    };

    if name.is_some() && !name.as_ref().unwrap().is_empty() {
        r#match.insert(
            entity::document::BackgroundAgentField::Name.as_str(),
            doc! {
                REGEX: format!("{}", name.clone().unwrap()),
                OPTIONS: "i",
            },
        );
    }

    let r#match = doc! {
        MATCH: r#match,
    };

    let facet = doc! {
        FACET:{
            "metadata":[
                { COUNT: "total" }
            ],
            "data":[
                { SORT: { BackgroundAgentField::Order.as_str(): 1 } },
                { SKIP: offset as i64},
                { LIMIT: limit as i64},
            ]
        }
    };

    let project = doc! {
        PROJECT:{
            "data": "$data",
            "total": { "$arrayElemAt": ["$metadata.total", 0] }
        }
    };

    let pipeline = [r#match, facet, project];

    let result = repository
        .aggregate::<entity::document::BackgroundAgent>(pipeline)
        .await
        .map_err(|e| {
            tracing::error!("获取service_agent失败: {:?}", e);
            Error::bad_request_error(format!("获取service_agent失败: {e}"))
        })?;

    if let Some(data) = result.first() {
        match data.get("data") {
            Some(bson::Bson::Array(arr)) => {
                let agents = arr
                    .iter()
                    .map(|v| {
                        let background_agent: entity::document::BackgroundAgent =
                            bson::from_bson(v.clone()).map_err(error::MongoError::BsonDeError)?;
                        Ok(background_agent)
                    })
                    .collect::<error::Result<Vec<_>>>()
                    .map_err(|e| {
                        tracing::error!("转换service_agent失败: {:?}", e);
                        Error::bad_request_error(format!("转换service_agent失败: {e}"))
                    })?;
                let total = match data.get("total") {
                    Some(bson::Bson::Int32(i)) => *i as usize,
                    _ => {
                        // tracing::error!("转换对话列表总数失败");
                        0
                    }
                };
                let response = BackgroundAgentListResponse {
                    agents,
                    limit,
                    offset,
                    total,
                };
                Response::body(response)
            }
            _ => Response::bad_request("转换service_agent失败".to_string()),
        }
    } else {
        Response::bad_request("查询失败".to_string())
    }
}

async fn update_background_agent(
    Path(id): Path<Uuid>,
    State(repository): State<Arc<DocumentRepository>>,
    Json(request): Json<BackgroundAgentRequest>,
) -> Response {
    let mut update = doc! {};

    if let Some(name) = request.name {
        update.insert(BackgroundAgentField::Name.as_str(), name);
    }

    if let Some(description) = request.description {
        update.insert(BackgroundAgentField::Description.as_str(), description);
    }

    if let Some(r#type) = request.r#type {
        update.insert(BackgroundAgentField::Type.as_str(), r#type);
    }

    if let Some(dynamic) = request.dynamic {
        update.insert(
            BackgroundAgentField::Dynamic.as_str(),
            bson::to_document(&dynamic).map_err(|e| {
                tracing::error!("转换错误");
                Error::bad_request_error(format!("转换错误: {e}"))
            })?,
        );
    }

    update.insert(
        BackgroundAgentField::WorkflowId.as_str(),
        bson::Uuid::from(request.workflow_id),
    );

    if update.is_empty() {
        return Response::success();
    }

    match repository
        .update_one::<entity::document::BackgroundAgent>(
            doc! {ID: id},
            doc! {
                SET: update
            },
        )
        .await
    {
        Ok(_) => Response::success(),
        Err(e) => {
            tracing::error!("更新service_agent {id} 失败: {e}");
            Response::bad_request(format!("更新service_agent {id} 失败: {e}"))
        }
    }
}

async fn delete_background_agent(
    Path(id): Path<Uuid>,
    State(repository): State<Arc<DocumentRepository>>,
) -> Response {
    match repository
        .delete_by_id::<entity::document::BackgroundAgent>(id)
        .await
    {
        Ok(_) => Response::success(),
        Err(e) => Response::bad_request(format!("删除service_agent失败: {e}")),
    }
}

async fn order(
    State(repository): State<Arc<DocumentRepository>>,
    Json(order_request): Json<OrderRequest>,
) -> Response {
    let self_order = calculate_self_order(&repository, &order_request)
        .await
        .map_err(|e| Error::bad_request_error(format!("{e:?}")))?;

    repository
        .update_one::<BackgroundAgent>(
            doc! {ID: order_request._self},
            doc! {
                SET: {
                    BackgroundAgentField::Order.as_str(): self_order
                }
            },
        )
        .await
        .map_err(|e| Error::bad_request_error(format!("{e:?}")))?;

    Response::success()
}

async fn find_first_or_last_order(
    repository: &DocumentRepository,
    order: i32,
) -> error::Result<i64> {
    let sort = doc! {
        SORT: {BackgroundAgentField::Order.as_str(): order},
    };
    let limit = doc! {

        LIMIT: 1,
    };

    let project = doc! {
        PROJECT: {
            ID: -1,
            BackgroundAgentField::Order.as_str(): 1,
        }
    };

    let mut cursor = repository
        .collection::<BackgroundAgent>()
        .await
        .aggregate(vec![sort, limit, project])
        .await
        .map_err(error::MongoError::SeverError)?;

    #[derive(Clone, Debug, Serialize, Deserialize)]
    struct Order {
        pub order: i64,
    }

    match cursor
        .try_next()
        .await
        .map_err(error::MongoError::SeverError)?
    {
        Some(document) => {
            let order: Order =
                bson::from_document(document).map_err(error::MongoError::BsonDeError)?;
            Ok(order.order)
        }
        None => {
            tracing::warn!("目前还没有记录");
            Ok(0)
        }
    }
}

fn calculate_self_order<'a>(
    repository: &'a DocumentRepository,
    order_request: &'a OrderRequest,
) -> Pin<Box<dyn Future<Output = error::Result<i64>> + Send + 'a>> {
    Box::pin(async move {
        match order_request {
            OrderRequest {
                preceding: None,
                _self,
                succeeding: Some(_succeeding),
            } => {
                //放到第一个
                let first_order = find_first_or_last_order(repository, 1).await.map_err(|e| {
                    tracing::error!("查询前一个order失败: {e:?}");
                    e
                })?;

                if first_order <= 1 {
                    reorder(repository).await?;
                    return calculate_self_order(repository, order_request).await;
                }

                Ok(first_order / 2)
            }
            OrderRequest {
                preceding: Some(_preceding),
                _self,
                succeeding: None,
            } => {
                //放到最后一个
                let last_order = find_first_or_last_order(&repository.clone(), -1)
                    .await
                    .map_err(|e| {
                        tracing::error!("查询前一个order失败: {e:?}");
                        e
                    })?;

                Ok(last_order + STEP)
            }
            OrderRequest {
                preceding: Some(preceding),
                _self,
                succeeding: Some(succeeding),
            } => {
                //中间插入
                let preceding_order = repository
                    .collection::<BackgroundAgent>()
                    .await
                    .find_one(doc! {
                        ID: preceding
                    })
                    .with_options(
                        FindOneOptions::builder()
                            .projection(doc! {
                                ID: 0,
                                BackgroundAgentField::Order.as_str(): 1
                            })
                            .build(),
                    )
                    .await
                    .map_err(error::MongoError::SeverError)?
                    .ok_or(error::AgentError::BackgroundAgentError(
                        "数据库中还没有数据".to_string(),
                    ))?
                    .order;

                let succeeding_order = repository
                    .collection::<BackgroundAgent>()
                    .await
                    .find_one(doc! {
                        ID: succeeding
                    })
                    .with_options(
                        FindOneOptions::builder()
                            .projection(doc! {
                                ID: 0,
                                BackgroundAgentField::Order.as_str(): 1
                            })
                            .build(),
                    )
                    .await
                    .map_err(error::MongoError::SeverError)?
                    .ok_or(error::AgentError::BackgroundAgentError(
                        "数据库中还没有数据".to_string(),
                    ))?
                    .order;

                if succeeding_order - preceding_order <= 1 {
                    reorder(repository).await?;
                    return calculate_self_order(repository, order_request).await;
                }

                Ok(preceding_order + (succeeding_order - preceding_order) / 2)
            }
            OrderRequest {
                preceding: None,
                _self,
                succeeding: None,
            } => Err(
                error::AgentError::BackgroundAgentError("前后节点id不能同时为空".to_string())
                    .into(),
            ),
        }
    })
}

async fn reorder(repository: &DocumentRepository) -> error::Result<()> {
    let collection = repository.collection::<BackgroundAgent>().await;
    let namespace = collection.namespace();

    let mut session = repository
        .client()
        .await
        .start_session()
        .await
        .map_err(|e| {
            tracing::error!("{:?}", e);
            error::MongoError::SeverError(e)
        })?;

    session.start_transaction().await.map_err(|e| {
        tracing::error!("{:?}", e);
        error::MongoError::SeverError(e)
    })?;

    let mut cursor = collection
        .find(doc! {})
        .sort(doc! {BackgroundAgentField::Order.as_str(): 1})
        .session(&mut session)
        .await
        .map_err(|e| {
            tracing::error!("{:?}", e);
            error::MongoError::SeverError(e)
        })?;

    let mut write_models = Vec::new();
    let mut index = 0;
    while let Some(result) = cursor.next(&mut session).await {
        let document = result.map_err(|e| {
            tracing::error!("{:?}", e);
            error::MongoError::SeverError(e)
        })?;
        let new_order = (index + 1) * STEP;

        let model = WriteModel::UpdateOne(
            UpdateOneModel::builder()
                .filter(doc! {
                    ID: document.id,
                })
                .update(doc! {
                    SET:{
                        BackgroundAgentField::Order.as_str(): new_order,
                    }
                })
                .namespace(namespace.clone())
                .build(),
        );

        write_models.push(model);

        index += 1;
    }

    repository.bulk_write(write_models).await?;

    session.commit_transaction().await.map_err(|e| {
        tracing::error!("{:?}", e);
        error::MongoError::SeverError(e)
    })?;

    Ok(())
}
