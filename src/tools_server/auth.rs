//! JWT Authentication Middleware
use std::sync::Arc;

use axum::{
    Json,
    extract::{
        FromRequest, FromRequestParts, Request, State, WebSocketUpgrade,
        ws::{CloseFrame, Message},
    },
    http::{StatusCode, request::Parts},
    middleware::Next,
    response::{IntoResponse, Response},
};
use axum_extra::{
    TypedHeader,
    extract::CookieJar,
    headers::{Authorization, authorization::Bearer},
    typed_header::TypedHeaderRejection,
};
use jsonwebtoken::{DecodingKey, Validation, decode};
use serde::{Deserialize, Serialize};

/// Deserialized user object from the `userInfo` string in the JWT payload.
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub user: User,
    #[serde(rename = "parentOrg")]
    pub parent_org: ParentOrg,
}

/// User details from within the `UserInfo` object.
#[derive(<PERSON>lone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct User {
    pub id: i64,
    // 疑似乱码，先不使用
    #[serde(rename = "username")]
    pub user_name: String,
    // pub nick_name: String,
    #[serde(rename = "type")]
    pub r#type: i64,
    pub client_id: String,
}

/// Parent organization details from within the `UserInfo` object.
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ParentOrg {
    pub id: i64,
    pub code: String,
    pub name: String,
}

/// A custom deserializer to parse the `userInfo` field, which is a JSON string.
mod de_user_info_from_str {
    use super::UserInfo;
    use serde::{Deserialize, Deserializer, de};

    pub fn deserialize<'de, D>(deserializer: D) -> Result<UserInfo, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        serde_json::from_str(&s).map_err(de::Error::custom)
    }
}

/// Represents the claims of the JWT token.
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Claims {
    #[serde(
        rename = "userInfo",
        deserialize_with = "de_user_info_from_str::deserialize"
    )]
    pub user_info: UserInfo,
    // 疑似乱码，先不使用
    // pub user_name: String,
    // pub nick_name: String,
    pub scope: Vec<String>,
    pub id: i64,
    pub exp: usize,
    // #[serde(rename = "type")]
    // pub r#type: i64,
    pub jti: String,
    // pub client_id: String,
    pub tenant_id: Option<String>,
}

impl Claims {
    pub fn user_id(&self) -> String {
        self.user_info.user.id.to_string()
    }

    // pub fn user_name(&self) -> String {
    //     self.user_info.user.user_name.clone()
    // }

    pub fn tenant_id(&self) -> String {
        self.tenant_id.clone().unwrap_or_default()
    }
}

impl<S> FromRequestParts<S> for Claims
where
    S: Send + Sync,
{
    type Rejection = AuthError;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        let claims = parts
            .extensions
            .get::<Claims>()
            .ok_or(AuthError::MissingCredentials)?;
        Ok(claims.clone())
    }
}

#[axum::debug_middleware]
pub async fn jwt_auth(
    State(public_key): State<Arc<String>>,
    auth: Result<TypedHeader<Authorization<Bearer>>, TypedHeaderRejection>,
    jar: CookieJar,
    mut req: Request,
    next: Next,
) -> Result<Response, AuthError> {
    let handle_token = move || -> Result<Claims, AuthError> {
        let token = auth
            .map(|auth| auth.token().to_string())
            .or_else(|e| {
                jar.get("tpt-token")
                    .map_or_else(|| Err(e), |c| Ok(c.value().to_string()))
            })
            .map_err(|_| AuthError::InvalidToken)?;

        let tenant_id = jar.get("tenant-id").map(|c| c.value().to_string());

        let decoding_key = DecodingKey::from_rsa_pem(public_key.as_bytes())
            .map_err(|_| AuthError::InvalidToken)?;

        let mut validation = Validation::new(jsonwebtoken::Algorithm::RS256);
        validation.validate_exp = false;

        let mut claims = decode::<Claims>(&token, &decoding_key, &validation)
            .inspect_err(|e| tracing::debug!("Failed to decode JWT token: {}", e))
            .map_err(|_| AuthError::InvalidToken)?
            .claims;

        if let Some(tenant_id) = tenant_id {
            claims.tenant_id = Some(tenant_id);
        } else {
            claims.tenant_id = Some("".to_string())
        }

        Ok(claims)
    };

    match handle_token() {
        Ok(claims) => {
            req.extensions_mut().insert(claims);
        }
        Err(e) => match WebSocketUpgrade::from_request(req, &()).await {
            Ok(upgrade) => {
                return Ok(upgrade.on_upgrade(async |mut socket| {
                    let _ = socket
                        .send(Message::Close(Some(CloseFrame {
                            code: 4001,
                            reason: "invalid token".into(),
                        })))
                        .await;
                }));
            }
            Err(_) => return Err(e),
        },
    };

    Ok(next.run(req).await)
}

pub enum AuthError {
    InvalidToken,
    MissingCredentials,
}

impl IntoResponse for AuthError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AuthError::InvalidToken => (StatusCode::UNAUTHORIZED, "Invalid token"),
            AuthError::MissingCredentials => (StatusCode::UNAUTHORIZED, "Missing credentials"),
        };
        (status, Json(serde_json::json!({ "error": error_message }))).into_response()
    }
}
