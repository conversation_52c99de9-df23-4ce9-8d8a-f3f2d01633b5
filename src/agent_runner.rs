// filepath: d:\Project\supcon-ai-flow\src\agent_runner.rs
use std::{fs, io::Write, path::Path, sync::Arc};

use colored::*;
use interaction::InteractionContent;
use ractor::{ActorRef, call};
use shared_core::{
    conversation::{
        Conversation, InteractionRequest, InteractionRequestKind,
        actor::{ConversationActor, ConversationActorEvent},
    },
    trace::start_tracing,
    *,
};
use tokio::{
    io::AsyncBufReadExt as _,
    sync::{RwLock, broadcast::Receiver},
};
use uuid::Uuid;

use crate::conversation::{
    Author, ContentItem, ConversationStatus, StreamEvent, StreamRequest, StreamRequestMessage,
    ai::{GeneralModel, InternalModel, Model},
    runner::Runner,
};

#[tokio::main]
async fn main() -> error::Result<()> {
    let _guard = start_tracing(false);

    script::ScriptEngine::on_initialize();

    let config_manager = config::ConfigManager::load()
        .inspect_err(|e| tracing::debug!("Failed to load configuration: {:?}", e))?;

    let script_engine = script::ScriptEngine::load(config_manager.script_config())
        .await
        .inspect_err(|e| tracing::debug!("Failed to load script engine: {:?}", e))?;

    // 创建模型实例
    // 加载agent配置文件
    let agent_config_path = Path::new(".runtime").join("agent.json");
    let agent_config_content = fs::read_to_string(&agent_config_path)
        .map_err(|e| anyhow::anyhow!("无法读取agent配置文件: {}", e))?;

    let agent_config: serde_json::Value = serde_json::from_str(&agent_config_content)
        .map_err(|e| anyhow::anyhow!("解析agent配置文件失败: {}", e))?;

    // 从配置文件中获取模型配置
    let model_config = agent_config
        .get("model")
        .ok_or_else(|| anyhow::anyhow!("agent.json 中缺少 model 配置"))?;

    let model_type = model_config
        .get("type")
        .and_then(|v| v.as_str())
        .unwrap_or("internal");

    // 根据配置类型创建对应的模型实例
    let model = if model_type == "internal" {
        let model_name = model_config
            .get("name")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let api_base_url = model_config
            .get("api_base")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let ai_api_base_url = model_config
            .get("ai_api_base")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        ai::Internal(InternalModel {
            model_name,
            api_base_url,
            ai_api_base_url,
        })
    } else {
        // 默认使用通用模型
        ai::General(GeneralModel {})
    };

    // 创建Runner实例
    let runner = Arc::new(Runner {
        script_engine: script_engine.clone(),
        model,
    });

    // 创建一个新对话
    let conversation_id = Uuid::now_v7();

    // 创建对话实例
    let conversation = Conversation {
        id: conversation_id,
        title: "TEST".to_string(),
        create_time: time::OffsetDateTime::now_utc(),
        update_time: None,
        current_message_id: None,
        status: ConversationStatus::Ready,
        metadata: serde_json::Value::Object(serde_json::Map::new()),
        messages: std::collections::HashMap::new(),
    };

    // 将对话包装在Arc<RwLock>中
    let conversation_arc = Arc::new(RwLock::new(conversation));

    // 直接创建对话actor
    let (conversation_actor, _) = ractor::Actor::spawn(
        None,
        ConversationActor {
            conversation: Arc::clone(&conversation_arc),
            runner: runner.clone(),
        },
        (),
    )
    .await
    .unwrap();

    print!("\n输入:");
    std::io::stdout().flush()?;

    // 从标准输入获取用户输入
    let input = tokio::io::stdin();
    let mut reader = tokio::io::BufReader::new(input).lines();

    // 读取单行输入
    let line = reader
        .next_line()
        .await
        .inspect_err(|e| tracing::debug!("读取输入失败: {:?}", e))?
        .ok_or_else(|| anyhow::anyhow!("读取输入失败: EOF"))?;

    // 创建用户消息请求
    let user_message = StreamRequestMessage {
        content: vec![ContentItem::Text { text: line }],
    };

    let user_message_request = StreamRequest {
        parent_id: None,
        message: user_message,
    };
    println!("发送用户消息...");
    // 发送用户消息
    let receiver = call!(
        conversation_actor,
        ConversationActorEvent::UserInput,
        user_message_request
    )
    .map_err(|e| anyhow::anyhow!("发送用户消息失败: {:?}", e))?;

    let cc = conversation_arc.clone();
    let ca = conversation_actor.clone();

    // 处理用户消息产生的事件
    let user_message_handle = tokio::spawn(async move {
        process_events(receiver, cc, ca).await;
    });

    tokio::time::sleep(std::time::Duration::from_secs(1)).await;

    user_message_handle.await?;

    // 对话完成后，获取所有消息并按顺序打印
    println!("\n\n===== 对话完整记录 =====");

    // 获取对话实例
    let conversation = conversation_arc.read().await;

    // 获取当前消息ID
    if let Some(current_id) = conversation.current_message_id {
        // 收集所有消息ID，按照对话顺序
        let mut message_ids = Vec::new();
        let mut current = current_id;

        // 向上查找所有消息
        loop {
            let node = conversation.messages.get(&current);
            if let Some(node) = node {
                message_ids.push(current);

                if let Some(parent_id) = node.parent_id {
                    current = parent_id;
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        // 反转消息ID列表，使其按照时间顺序排列
        message_ids.reverse();

        // 按顺序打印所有消息
        for id in message_ids {
            if let Some(node) = conversation.messages.get(&id) {
                let content = content_to_string(&node.message.content);
                let author = &node.message.author;

                // 根据作者类型设置不同的颜色
                match author {
                    Author::User { .. } => {
                        println!("\n{}: \n{}", "用户".green(), content);
                    }
                    Author::Assistant { .. } => {
                        println!("\n{}: \n{}", "助手".blue(), content);
                    }
                    Author::System { .. } => {
                        println!("\n{}: \n{}", "系统".yellow(), content);
                    }
                    Author::Tool {
                        name,
                        module,
                        step_id,
                    } => {
                        println!(
                            "\n{} ({}/{}/{:?}): \n{}",
                            "工具".cyan(),
                            module,
                            name,
                            step_id,
                            content
                        );
                    }
                }

                let error = node.message.metadata.get("error").and_then(|e| e.as_str());
                if let Some(error) = error {
                    println!("消息出现错误: {}", error);
                }
            }
        }
    }

    Ok(())
}

// 处理事件的函数，根据修改后的StreamEvent结构调整
async fn process_events(
    mut receiver: Receiver<StreamEvent>,
    conversation: Arc<RwLock<Conversation>>,
    actor: ActorRef<ConversationActorEvent>,
) {
    'event_loop: loop {
        match receiver.recv().await {
            Ok(event) => {
                match event {
                    StreamEvent::NewMessage(_) => {}
                    StreamEvent::MessageContentDelta(_) => {}
                    StreamEvent::MessageDetailsDelta(_) => {}
                    StreamEvent::ConversationStatusChanged(status_change) => {
                        match status_change.status {
                            ConversationStatus::Chat => {
                                // 检查是否有错误 - 只有这里需要检查metadata
                                if let Some(ref metadata) = status_change.metadata {
                                    if let Some(error) =
                                        metadata.get("error").and_then(|e| e.as_str())
                                    {
                                        println!("【状态变更】对话中出现异常: {}", error);
                                        // 不立即结束，等待Ready状态
                                        continue;
                                    }
                                }
                                println!("【状态变更】正在对话中...");
                            }
                            ConversationStatus::Execute => {
                                // 检查是否有错误 - 只有这里需要检查metadata
                                if let Some(ref metadata) = status_change.metadata {
                                    if let Some(error) =
                                        metadata.get("error").and_then(|e| e.as_str())
                                    {
                                        println!("【状态变更】执行工作流出现异常: {}", error);
                                        // 不立即结束，等待Ready状态
                                        continue;
                                    }
                                }
                                println!("【状态变更】正在执行工作流...");
                            }
                            ConversationStatus::Interaction => {
                                println!("【状态变更】进入交互模式");
                                let conversation = conversation.read().await;
                                let message_id = conversation.current_message_id.unwrap();
                                let current_message = conversation
                                    .messages
                                    .get(&conversation.current_message_id.unwrap())
                                    .unwrap();

                                let content_index = current_message.message.content.len() - 1;
                                let content = current_message.message.content.last().unwrap();

                                match content {
                                    ContentItem::Interaction { interaction } => {
                                        println!("【交互】{}", interaction);
                                        match &interaction.content {
                                            InteractionContent::Confirm { .. } => {
                                                let _ = actor.send_message(
                                                    ConversationActorEvent::Interaction(
                                                        InteractionRequest {
                                                            message_id,
                                                            content_index,
                                                            result:
                                                                InteractionRequestKind::Confirm {
                                                                    confirm: true,
                                                                },
                                                        },
                                                    ),
                                                );
                                            }
                                            InteractionContent::PreStartConfirm { .. } => {
                                                let _ = actor.send_message(
                                                    ConversationActorEvent::Interaction(
                                                        InteractionRequest {
                                                            message_id,
                                                            content_index,
                                                            result:
                                                                InteractionRequestKind::PreStartConfirm{
                                                                    pre_start_confirm: true.into(),
                                                                },
                                                        },
                                                    ),
                                                );
                                            }
                                            InteractionContent::Select { .. } => {
                                                let _ = actor.send_message(
                                                    ConversationActorEvent::Interaction(
                                                        InteractionRequest {
                                                            message_id,
                                                            content_index,
                                                            result:
                                                                InteractionRequestKind::Select {
                                                                    select: vec![0],
                                                                },
                                                        },
                                                    ),
                                                );
                                            }
                                            InteractionContent::OpenPage { .. } => {
                                                let _ = actor.send_message(
                                                    ConversationActorEvent::Interaction(
                                                        InteractionRequest {
                                                            message_id,
                                                            content_index,
                                                            result:
                                                                InteractionRequestKind::OpenPage {
                                                                    open_page:
                                                                        serde_json::json!({}),
                                                                },
                                                        },
                                                    ),
                                                );
                                            }
                                            InteractionContent::Form { .. } => {
                                                let _ = actor.send_message(
                                                    ConversationActorEvent::Interaction(
                                                        InteractionRequest {
                                                            message_id,
                                                            content_index,
                                                            result: InteractionRequestKind::Form {
                                                                form: serde_json::Value::Null,
                                                            },
                                                        },
                                                    ),
                                                );
                                            }
                                        }
                                    }
                                    ContentItem::RequireConfirm { .. } => {
                                        println!("【交互】 执行前确认");
                                        let _ = actor.send_message(
                                            ConversationActorEvent::Interaction(
                                                InteractionRequest {
                                                    message_id,
                                                    content_index,
                                                    result: InteractionRequestKind::Confirm {
                                                        confirm: true,
                                                    },
                                                },
                                            ),
                                        );
                                    }
                                    _ => {
                                        println!(
                                            "【状态变更】对话中出现异常，无效的交互内容：{:#?}",
                                            current_message
                                        );
                                        break 'event_loop;
                                    }
                                }
                            }
                            ConversationStatus::Ready => {
                                println!("【状态变更】对话已准备就绪");
                                break 'event_loop;
                            }
                        }
                    }
                    StreamEvent::MessageStatusChanged(payload) => {
                        // 检查是否有错误 - 只有这里需要检查metadata
                        if let Some(ref metadata) = payload.metadata {
                            if let Some(error) = metadata.get("error").and_then(|e| e.as_str()) {
                                println!("【状态变更】对话中出现异常: {}", error);
                                // 不立即结束，等待Ready状态
                                continue;
                            }
                        }

                        println!("【消息状态变更】消息状态已变更: {:?}", payload.status);
                    }
                }
            }
            Err(e) => {
                println!("接收事件错误: {:?}", e);
            }
        }
    }
}

// 辅助函数，将ContentItem数组转换为字符串
fn content_to_string(content: &[ContentItem]) -> String {
    content
        .iter()
        .map(|item| match item {
            ContentItem::Metadata { metadata } => format!("元数据：{}", metadata),
            ContentItem::Text { text } => format!("文本：{}", text),
            ContentItem::Json { json } => format!("JSON：{}", json),
            ContentItem::Branch { branch } => format!("分支：{}", branch),
            ContentItem::Confidence { confidence } => format!("置信度：{}", confidence),
            ContentItem::Think { think } => format!("思考：{}", think),
            ContentItem::Verify { verify } => format!("验证：{}", verify),
            ContentItem::RequireConfirm { require_confirm } => format!("确认：{}", require_confirm),
            ContentItem::Interaction { interaction } => format!("交互：{}", interaction),
            ContentItem::Tool {
                input,
                output,
                converter,
                summary,
                converter_think,
                summary_think,
            } => {
                format!(
                    "工具：\n输入={:#?}\n输出={:#?}\n转换撕开代码={}\n转换代码={}\n总结思考={}\n总结={}",
                    input,
                    output,
                    converter_think.clone().unwrap_or("None".to_string()),
                    converter.clone().unwrap_or("None".to_string()),
                    summary_think.clone().unwrap_or("None".to_string()),
                    summary.clone().unwrap_or("None".to_string())
                )
            }
            ContentItem::Title { title } => format!("标题：{}", title),
            ContentItem::Details { details } => format!("详情：{}", details),
        })
        .collect::<Vec<String>>()
        .join("\n")
}
