{"cSpell.words": ["ahooks", "aiofiles", "aiohttp", "amap", "anyio", "appender", "asyncio", "automl", "Avenir", "byot", "classmethod", "creds", "debugpy", "depythonize", "<PERSON><PERSON>", "Dismissable", "dotenv", "eventsource", "functools", "getattr", "gmtime", "grayscale", "has<PERSON>r", "<PERSON><PERSON>", "HTTPGET", "<PERSON><PERSON>", "inittab", "iscoroutinefunction", "isinstance", "Jrpc", "jsonschema", "jsonwebtoken", "kwargs", "logicflow", "lucide", "memchr", "metatable", "mlua", "msecs", "mypy", "nacos", "nohighlight", "noto", "oneshot", "OPENAI", "parentid", "pycache", "pyclass", "pydantic", "pyfunction", "pymethods", "pymodule", "pyobject", "pytest", "pythonize", "qwen", "ractor", "remotefile", "reqwest", "rmcp", "<PERSON><PERSON>", "rposition", "rustls", "Sakal", "semconv", "serde", "setattr", "sqlx", "sseclient", "staticmethod", "stepid", "Streamable", "supcon", "thiserror", "todolist", "uninit", "unocss", "unpark", "venv", "vxdirect"], "cSpell.spellCheckOnlyWorkspaceFiles": true, "files.associations": {"*.css": "tailwindcss"}, "github.copilot.chat.localeOverride": "zh-CN", "markdown-preview-enhanced.mermaidTheme": "neutral", "rust-analyzer.debug.engineSettings": {"cppvsdbg": {"console": "externalTerminal"}}, "rust-analyzer.testExplorer": true, "zig.version": "0.14.1"}