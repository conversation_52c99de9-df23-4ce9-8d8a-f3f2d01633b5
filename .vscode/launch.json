{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            // "type": "lldb",
            "type": "cppvsdbg",
            "request": "launch",
            "name": "Launch",
            "program": "${workspaceRoot}/target/debug/tools-server.exe",
            // "cargo": {
            //     "args": [
            //         "build",
            //         "--bin=<your_executable_name>",
            //         "--package=<your_package_name>"
            //     ]
            // },
            "args": [],
            "cwd": "${workspaceFolder}",
            "preLaunchTask": "rust: cargo build",
            // "sourceLanguages": [
            //     "rust"
            // ],
            // "env": {
            //     "RUST_BACKTRACE": "full"
            // },
            // "console": "internalConsole"
            "console": "integratedTerminal",
            // "externalConsole": false,
        },
        // {
        //     "type": "lldb-dap",
        //     "request": "launch",
        //     "name": "Debug",
        //     "program": "${workspaceRoot}/target/debug/tools-server",
        //     "args": [],
        //     "env": [],
        //     "cwd": "${workspaceRoot}",
        //     "preLaunchTask": "rust: cargo build",
        //     "initCommands": [
        //         // 1. 导入 Rust 的格式化脚本
        //         //    将 <SYSROOT_PATH> 替换为你上一步找到的真实路径
        //         "command script import \"/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/etc/lldb_lookup.py\"",
        //         // 2. 告诉 LLDB 使用这个脚本来格式化所有类型
        //         //    -e, --expand: 展开聚合类型（比如 Vec, Struct）
        //         //    -x ".*":      匹配所有类型名称（使用正则表达式）
        //         //    -F lldb_lookup.summary_lookup: 使用脚本中的 summary_lookup 函数
        //         // "type summary add --expand -x \".*\" -F lldb_lookup.summary_lookup",
        //         // 3. 启用 "rust" 这个类型分类
        //         // "type category enable rust"
        //     ]
        // },
        // {
        //     "name": "(Windows) Attach to Dump",
        //     "type": "cppvsdbg", // 必须是 cppvsdbg
        //     "request": "launch", // 即使是分析 dump，request 也是 "launch"
        //     "program": "${workspaceFolder}/target/release/my_app.exe", // 指定与 dump 匹配的 .exe 文件路径
        //     "dumpPath": "${workspaceFolder}/dumps/crash.dmp", // 指定 .dmp 文件的路径
        //     "symbolSearchPath": "${workspaceFolder}/target/release", // 指定符号 (.pdb) 文件的搜索路径
        //     "console": "internalConsole"
        // }
        {
            "type": "cppvsdbg",
            "name": "zig",
            "request": "launch",
            "program": "${workspaceRoot}/zig-out/bin/debug-hello",
            "args": [],
            "cwd": "${workspaceRoot}/zig-out/bin",
            "symbolSearchPath": "${workspaceRoot}/zig-out/bin",
            "environment": [],
            "console": "integratedTerminal",
        }
    ]
}