FROM harbor.supcon5t.com/indu/tpt-app-run-base:v2507-an8-slim-x64

# 设置工作目录
WORKDIR /app

# 创建用户
RUN groupadd -r agentrunner -g 1001 && useradd -r -u 1001 -g agentrunner agentrunner

RUN chown 1001:1001 /app 

# 复制二进制文件
COPY --chown=1001:1001 target/x86_64-unknown-linux-gnu/release/tools-server /app/tools-server

# 复制运行时配置目录
COPY --chown=1001:1001 .runtime /app/.runtime

# 复制 python 依赖
COPY .dep/usr/local/lib/ /usr/lib/
COPY .dep/usr/local/bin/ /usr/bin/

RUN chmod -R u+rwX,go+rX /app 

ENV PYTHONPATH=/usr/lib/python3.13:/usr/lib/python3.13/lib-dynload:/usr/lib/python3.13/site-packages

# 写入 ld.so.conf.d/*
RUN echo "/usr/lib" > /etc/ld.so.conf.d/usr.conf

RUN ldconfig

# 切换到非特权用户
USER 1001

# 暴露端口
EXPOSE 15336

# 设置环境变量
ENV PATH="/usr/bin:$PATH"

ENV TOOLS_USE_NACOS=true
ENV TOOLS_LOAD_MINIO_TOOLS=true
ENV TOOLS_PREVENT_LOG_FILES=true
ENV TOOLS_SERVER_LISTEN=0.0.0.0:15336

# nacos
ENV NACOS_CLIENT_SERVER_ADDRESS=nacos-cs.devops-dev:8848
ENV NACOS_CLIENT_NAMESPACE=0405ff0d-4a5a-48aa-abec-4db98933b2f0
ENV NACOS_CLIENT_APP_NAME=tools_server

# 启动命令
CMD ["/app/tools-server"] 