# Agent-Runner Tools-Server 部署指南

## 概述

这个目录包含了 agent-runner 项目中 tools-server 服务的 CI/CD 配置文件，用于自动化构建、Docker 镜像打包和 Kubernetes 部署。

## 文件说明

### k8s-deploy.yml
Kubernetes 部署配置文件，包含：
- **Deployment**: 部署 tools-server 服务
- **Service**: 暴露服务端口（15336）
- **Ingress**: 提供外部访问入口

### Dockerfile.x64
Docker 镜像构建文件，用于：
- 基于 Debian Bookworm Slim 构建轻量级镜像
- 复制编译好的 tools-server 二进制文件
- 包含运行时配置目录 (.runtime)
- 设置适当的用户权限和环境变量

### .gitlab-ci.yml (项目根目录)
GitLab CI/CD 流水线配置，包含以下阶段：
1. **s1-*********************bin**: 编译 Rust 代码
2. **s2-Agent-Run-docker-buldpush**: 构建并推送 Docker 镜像
3. **s3-Agent-Run-k8s-deploy**: 部署到 Kubernetes

## 部署流程

### 开发环境部署
当推送代码到分支时：
1. 自动编译 tools-server 二进制文件
2. 构建 Docker 镜像并推送到 Harbor 仓库
3. 部署到 `indu-dev` 命名空间

### 生产环境部署
当创建 Git Tag 时：
1. 编译并构建带版本标签的 Docker 镜像
2. 部署到 `indu` 命名空间

## 配置参数

### 应用配置
- **应用名称**: agent-runner-tools-server
- **监听端口**: 15336
- **镜像仓库**: harbor.supcon5t.com/indu/
- **健康检查路径**: /api/test/sse

### 环境变量
- `RUST_LOG=info`: 设置日志级别
- `TOOLS_SERVER_LISTEN=0.0.0.0:15336`: 设置监听地址
- `TZ=Asia/Shanghai`: 设置时区

### 资源配置
- **CPU 请求**: 200m
- **CPU 限制**: 2000m
- **内存请求**: 512Mi
- **内存限制**: 2Gi

## 访问方式

### 开发环境
- **内部访问**: http://agent-runner-tools-server.indu-dev.svc.cluster.local:15336
- **外部访问**: http://agent-runner-tools-server-indu-dev.supcon5t.com

### 生产环境
- **内部访问**: http://agent-runner-tools-server.indu.svc.cluster.local:15336
- **外部访问**: http://agent-runner-tools-server-indu.supcon5t.com

## 注意事项

1. 确保 `.runtime` 目录包含必要的配置文件
2. 二进制文件路径为 `target/x86_64-unknown-linux-gnu/release/tools-server`
3. 服务运行在非特权用户 `agentrunner` 下
4. 健康检查和就绪检查都使用 `/api/test/sse` 端点
5. 部署时会自动创建必要的存储卷用于日志和配置

## 故障排除

### 常见问题
1. **健康检查失败**: 检查 `/api/test/sse` 端点是否正常响应
2. **配置文件缺失**: 确保 `.runtime` 目录存在且包含必要配置
3. **权限问题**: 检查容器内文件权限是否正确设置

### 查看日志
```bash
# 查看 Pod 日志
kubectl logs -n <namespace> -l app=agent-runner-tools-server

# 查看 Pod 状态
kubectl get pods -n <namespace> -l app=agent-runner-tools-server
``` 