###>src@https://gitlab.supcon5t.com/5tpd/private/cicd/glab-ci-template/-/blob/master/template/deploy-template.yml
###>Comme: the template for most suition k8s workload deploy, updated @240116
--- ###>Begin add workload deploy
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-runner-tools-server-v1
  namespace: K8S_NAMESPACE
  labels:
    app: agent-runner-tools-server
    appver: v1
    env: MY_NS
spec:
  replicas: 1
  template:
    metadata:
      name: agent-runner-tools-server
      labels:
        app: agent-runner-tools-server
        appver: v1
        env: MY_NS
    spec:
      ###deploy most app to k8s node with tag devops for central run it, if need not pls remove follow two line
      nodeSelector:
        kubernetes: devops
      ####init container use busybox to fix obp-log write permision for unpriviege user sc5trun used by most obp bigdata app
      # initContainers:
      #    - name: obplog-unroot-tune
      #      imagePullPolicy: IfNotPresent
      #      image: 'harbor.supcon5t.com/devops/busybox:stable-glibc-x64'
      #      command:
      #        - chown
      #        - -R
      #        - 1001:1001
      #        - /data/
      #      volumeMounts:
      #        - name: obp-logs
      #          mountPath: /data/
      ###End init container env
      containers:
        - name: agent-runner-tools-server
          image: IMGNAME
          imagePullPolicy: Always
          command: ["/app/tools-server"]
          workingDir: /app
          securityContext:
            runAsUser: 1001
            runAsGroup: 1001
            allowPrivilegeEscalation: false
          livenessProbe:
            failureThreshold: 10
            tcpSocket:
              port: 15336
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          readinessProbe:
            failureThreshold: 3
            tcpSocket:
              port: 15336
            initialDelaySeconds: 10
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 8000m
              memory: 16Gi
            requests:
              cpu: 200m
              memory: 512Mi
          ###Care tune follow env to call from nacos cfg centra
          env:
            - name: TZ
              value: Asia/Shanghai
            ###>Atten:use CI_PIPELINE_ID env to trigger every kubectl apply get affect.
            ###>need coworks with gitlab-ci.yaml with sed -i "s/CI_PIPELINE_ID/${CI_PIPELINE_ID}/g" xxx
            - name: info.version
              value: "CI_PIPELINE_ID"
            # nacos
            - name: NACOS_CLIENT_SERVER_ADDRESS
              value: "NACOS_ADDRESS"
            - name: NACOS_CLIENT_NAMESPACE
              value: "fbb63d08-c2ea-48dd-b52b-187e6585f33d"
            - name: NACOS_CLIENT_APP_NAME
              value: "tools_server"
            - name: NACOS_CLIENT_USERNAME
              value: "nacos"
            - name: NACOS_CLIENT_PASSWORD
              value: "nacos"
          ports:
            - containerPort: 15336
              name: http
              protocol: TCP
      ###>default obp app need obp-logs pvc to get analysis log,2505 change to emptyDir
      # volumeMounts:
      #   - mountPath: /data/
      #     name: obp-logs
      #   - mountPath: /app/.runtime
      #     name: runtime-config
      # volumes:
      # - name: obp-logs
      #   emptyDir:
      #     sizeLimit: 512Mi
      # - name: runtime-config
      #   emptyDir:
      #     sizeLimit: 1Gi
      restartPolicy: Always
  selector:
    matchLabels:
      app: agent-runner-tools-server
      appver: v1
      env: MY_NS
...
###>add def k8s service for our app common with v1 tag,if U are v2/v3/vX,pls must remove next section legacy add svc
---
apiVersion: v1
kind: Service
metadata:
  name: agent-runner-tools-server-v1
  namespace: K8S_NAMESPACE
spec:
  selector:
    app: agent-runner-tools-server
    appver: v1
    env: MY_NS
  ports:
    - port: 15336
      targetPort: 15336
      name: http
  type: NodePort
...
###>legacy add svc to direct our most  app without v1 tag ,to coexist with v1 tag
---
apiVersion: v1
kind: Service
metadata:
  name: agent-runner-tools-server
  namespace: K8S_NAMESPACE
spec:
  selector:
    app: agent-runner-tools-server
    appver: v1
    env: MY_NS
  ports:
    - port: 15336
      targetPort: 15336
      name: http
  type: NodePort
###>add  http ingress for most app without v1 tag coexist with v1 tag
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-runner-tools-server
  namespace: MY_NS
spec:
  ingressClassName: nginx
  rules:
    - host: agent-runner-tools-server-MY_NS.supcon5t.com
      http:
        paths:
          - backend:
              service:
                name: agent-runner-tools-server
                port:
                  number: 15336
            path: /
            pathType: Prefix
---
#End
