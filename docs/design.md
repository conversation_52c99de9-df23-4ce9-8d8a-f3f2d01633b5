# ToolHub 软件详细设计说明书

项目阶段/评审点
**概念 *√*计划 **开发 **验证 **发布 __生命周期

## 目 录

1. [系统组件结构设计](#1系统组件结构设计)
   1.1. [组件结构图](#11组件结构图)
   1.2. [组件汇总表](#12组件汇总表)
   1.3. [组件复用分析](#13组件复用分析)
   1.4. [组件对外接口](#14组件对外接口)
2. [系统组件功能详细设计](#2系统组件功能详细设计)
   2.1. [脚本引擎组件](#21脚本引擎组件)
   2.2. [解析器组件](#22解析器组件)
   2.3. [会话管理组件](#23会话管理组件)
   2.4. [MCP服务组件](#24mcp服务组件)
   2.5. [服务路由组件](#25服务路由组件)
3. [非功能详细设计](#3非功能详细设计)
   3.1. [性能详细设计](#31性能详细设计)
   3.2. [实施运维效率详设](#32实施运维效率详设)
   3.3. [安全性详细设计](#33安全性详细设计)
   3.4. [易用性详细设计](#34易用性详细设计)
   3.5. [可靠性详细设计](#35可靠性详细设计)
4. [附录](#4附录)

## 1.系统组件结构设计

### 1.1.组件结构图

以下是ToolHub系统的高层结构图，展示了主要组件之间的交互关系：

```mermaid
graph TD
    Client[客户端应用] --> Router[服务路由组件]
    Router --> MCP[MCP服务组件]
    Router --> Resource[资源管理组件]
    MCP --> Resolver[解析器组件]
    MCP --> Resource
    Resolver --> ScriptEngine[脚本引擎组件]

    classDef component fill:#f9f,stroke:#333,stroke-width:2px;
    class Client,Router,MCP,Resource,Resolver,ScriptEngine component;
```

在这个图中，每个箭头代表接口的使用，从接口的提供者到接口的使用者。系统中的主要接口调用如下：

| 提供者 | 调用者 | 接口名称 |
| ------ | ------ | -------- |
| 服务路由组件 | 客户端应用 | HTTP API接口 |
| MCP服务组件 | 服务路由组件 | McpServer接口 |
| 解析器组件 | MCP服务组件 | Resolver接口 |
| 脚本引擎组件 | MCP服务组件 | ScriptEngine接口 |

| 资源管理组件 | 服务路由组件 | ResourceManager接口 |
| 脚本引擎组件 | 解析器组件 | SourceTool接口 |

### 1.2.组件汇总表

以下是系统各组件的功能汇总表：

| 名称 | 功能 | 实现文件 | 是否为重要组件 |
| ---- | ---- | -------- | -------------- |
| 脚本引擎组件 | 管理Python和Lua脚本的加载、执行和资源管理 | script/mod.rs, script/python.rs, script/lua.rs | 是 |
| 解析器组件 | 处理能力版本解析、模块和能力依赖关系、路由查找 | resolver.rs | 是 |
| MCP服务组件 | 实现MCP协议，支持与外部系统的工具集成 | mcp.rs | 是 |

| 服务路由组件 | 提供HTTP接口和路由处理 | service.rs | 是 |
| 资源管理组件 | 管理系统资源 | resources.rs | 否 |
| 配置组件 | 管理系统配置 | config.rs | 否 |
| 会话管理组件 | 管理用户会话和交互 | conversation/mod.rs, conversation/*.rs | 否 |
| 工具定义组件 | 定义系统中能力的核心数据结构 | tool.rs | 是 |
| 连接池组件 | 管理资源池或连接池 | pool.rs | 否 |
| 追踪组件 | 提供系统日志和追踪功能 | trace.rs | 否 |

### 1.3.组件复用分析

以下是系统组件的复用性分析：

| 组件 | 版本 | 结果（开发、复用、购买、开源） | 理由 |
| ---- | ---- | ---------------------------- | ---- |
| 脚本引擎组件 | 1.0.0 | 开发 | 核心组件，需要定制化实现以支持Python和Lua脚本的统一管理 |
| 解析器组件 | 1.0.0 | 开发 | 核心组件，需要实现特定的版本解析和依赖管理逻辑 |
| MCP服务组件 | 1.0.0 | 开发 | 核心组件，实现MCP协议的服务端功能 |
| Axum Web框架 | 0.6.x | 开源 | 高性能的Rust Web框架，适合构建API服务 |
| Tokio异步运行时 | 1.x | 开源 | Rust生态中最成熟的异步运行时，提供高性能的异步IO支持 |
| Serde序列化库 | 1.x | 开源 | Rust生态中标准的序列化/反序列化库 |
| mlua | 0.8.x | 开源 | 提供Lua脚本与Rust的绑定 |
| pyo3 | 0.18.x | 开源 | 提供Python脚本与Rust的绑定 |
| semver | 1.x | 开源 | 提供语义化版本解析和比较功能 |

### 1.4.组件对外接口

以下是系统对外提供的主要接口：

| 接口名称 | 功能 | 提供者 | 接口规格描述 |
| -------- | ---- | ------ | ------------ |
| /api/rpc | 直接能力调用 | 服务路由组件 | JSON-RPC接口，用于直接调用系统中的能力 |
| /api/mcp | MCP服务接口 | MCP服务组件 | 实现MCP协议的HTTP接口，支持工具集成 |
| /api/flow | 流程定义和执行 | 流程管理组件 | REST API，用于获取和执行流程 |
| /api/view | 视图定义获取 | 服务路由组件 | REST API，用于获取视图定义 |
| /api/conversation | 会话管理接口 | 会话管理组件 | REST API，用于管理用户会话和交互 |
| ScriptEngine | 脚本执行接口 | 脚本引擎组件 | Rust API，用于加载和执行Python和Lua脚本 |
| Resolver | 解析器接口 | 解析器组件 | Rust API，用于解析能力和模块依赖关系 |

## 2.系统组件功能详细设计

### 2.1.脚本引擎组件

#### 2.1.1.功能描述

脚本引擎组件负责管理Python和Lua脚本的加载、执行和资源管理。它提供了统一的接口来处理不同类型的脚本，支持能力的动态加载和执行。脚本引擎是系统的核心组件，通过它可以将Python和Lua脚本作为系统能力进行统一管理和调用。

#### 2.1.2.数据结构

```mermaid
classDiagram
    class ScriptEngine {
        -lua_pool: Arc~Pool~mlua::Lua~~
        -resolver: Arc~Resolver~
        -resource_manager: Arc~RwLock~ResourceManager~~
        +on_initialize()
        +on_finalize()
        +on_thread_stop()
        +empty() Arc~ScriptEngine~
        +load(config) anyhow::Result~Arc~Self~~
        +resolver() Arc~Resolver~
        +resource_manager() Arc~RwLock~ResourceManager~~
        +run_python(code, function_name, argument) anyhow::Result~Value~
        +run_tool(tool, session_id, argument) anyhow::Result~Value~
    }
    
    class SourceTool {
        +source: PathBuf
        +tool_name: String
        +tool_kind: ToolKind
        +dependency_version: String
        +resolve_id() Option~String~
    }
    
    class Tool {
        +id: String
        +name: String
        +kind: ToolKind
        +description: String
        +params: Value
        +source: Option~PathBuf~
    }
    
    ScriptEngine --> SourceTool: 加载
    ScriptEngine --> Tool: 创建
```

#### 2.1.3.处理流程

```mermaid
flowchart TD
    A[初始化流程] --> A1[调用on_initialize初始化Python环境]
    A1 --> A2[设置信号处理和导入必要的Python模块]
    A2 --> A3[配置Python路径以加载脚本]
    
    B[加载流程] --> B1[从配置中读取所有Python和Lua脚本文件]
    B1 --> B2[加载Python脚本文件]
    B1 --> B3[加载Lua脚本文件]
    B2 --> B4[提取Python能力信息]
    B3 --> B5[创建Lua执行环境池]
    B5 --> B6[提取Lua能力信息]
    B4 --> B7[收集所有能力信息]
    B6 --> B7
    B7 --> B8[创建解析器]
    
    C[执行流程] --> C1{能力类型?}
    C1 -->|Python| C2[使用pyo3调用Python函数]
    C1 -->|Lua| C3[从池中获取Lua执行环境]
    C3 --> C4[调用Lua函数]
    C2 --> C5[处理返回结果]
    C4 --> C5
    C5 --> C6[转换为JSON格式返回]
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    class A,A1,A2,A3,B,B1,B2,B3,B4,B5,B6,B7,B8,C,C2,C3,C4,C5,C6 process;
    class C1 decision;
```

1. **初始化流程**：
   - 调用`on_initialize`初始化Python环境
   - 设置信号处理和导入必要的Python模块
   - 配置Python路径以加载脚本

2. **加载流程**：
   - 从配置中读取所有Python和Lua脚本文件
   - 分别加载Python和Lua脚本，提取能力信息
   - 创建Lua执行环境池
   - 收集所有能力信息并创建解析器

3. **执行流程**：
   - 根据能力类型选择相应的脚本引擎
   - 对于Lua脚本，从池中获取执行环境并调用相应函数
   - 对于Python脚本，使用pyo3调用Python函数
   - 处理返回结果并转换为JSON格式

#### 2.1.4.算法设计

**脚本加载算法**：
```mermaid
flowchart TD
    A[开始加载脚本] --> B[初始化空文件列表]
    B --> C[遍历配置中的工具组]
    C --> D[遍历工具组中的版本]
    D --> E[遍历版本中的代码文件]
    E --> F{文件扩展名?}
    F -->|lua| G[添加到Lua文件列表]
    F -->|py| H[添加到Python文件列表]
    G --> I[继续遍历]
    H --> I
    I --> J{遍历完成?}
    J -->|否| E
    J -->|是| K[加载Python文件]
    K --> L[创建Lua执行环境池]
    L --> M[获取Lua工具列表]
    M --> N[获取Python工具列表]
    N --> O[创建解析器]
    O --> P[创建脚本引擎实例]
    P --> Q[返回脚本引擎]
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,B,C,D,E,G,H,I,K,L,M,N,O,P,Q process;
    class F,J decision;
```

**能力执行算法**：
```mermaid
flowchart TD
    A[开始执行能力] --> B{能力类型?}
    B -->|Lua| C[获取Lua执行环境]
    B -->|Python| D[准备Python执行环境]
    C --> E[调用Lua能力函数]
    D --> F[调用Python能力函数]
    E --> G[处理返回结果]
    F --> G
    G --> H[返回JSON格式结果]
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,C,D,E,F,G,H process;
    class B decision;
```

#### 2.1.5.关键技术

1. 使用pyo3库实现Rust与Python的互操作，支持Python脚本的加载和执行
2. 使用mlua库实现Rust与Lua的互操作，支持Lua脚本的加载和执行
3. 采用池化技术管理脚本执行环境，提高性能和资源利用率
4. 实现异步执行模型，支持非阻塞的脚本调用
5. 使用Arc和RwLock实现线程安全的资源共享和并发控制

#### 2.1.6.提供的接口

| 接口名称 | 功能 | 详细描述 |
| -------- | ---- | -------- |
| ScriptEngine::on_initialize | 初始化脚本引擎环境 | 设置Python环境，配置信号处理和导入必要模块 |
| ScriptEngine::on_finalize | 清理脚本引擎资源 | 释放Python环境资源 |
| ScriptEngine::on_thread_stop | 清理线程相关资源 | 释放线程特定的Python资源 |
| ScriptEngine::empty | 创建空的脚本引擎实例 | 创建不包含任何脚本的引擎实例，用于测试 |
| ScriptEngine::load | 加载脚本文件 | 从配置中加载所有脚本文件，收集能力信息 |
| ScriptEngine::resolver | 获取解析器 | 返回解析器实例，用于查找和管理能力 |
| ScriptEngine::resource_manager | 获取资源管理器 | 返回资源管理器实例，用于管理系统资源 |
| ScriptEngine::run_python | 执行Python代码 | 直接执行Python代码字符串和指定函数 |
| ScriptEngine::run_tool | 执行能力 | 根据能力类型选择相应的脚本引擎执行能力函数 |

#### 2.1.7.需要的接口

| 接口名称 | 功能 | 提供者 |
| -------- | ---- | ------ |
| Resolver::resolve_tool | 解析能力 | 解析器组件 |
| Resolver::new | 创建解析器 | 解析器组件 |
| Pool::acquire | 获取执行环境 | 连接池组件 |
| ResourceManager::new | 创建资源管理器 | 资源管理组件 |
| python::load_files | 加载Python文件 | Python脚本支持模块 |
| python::get_tools | 获取Python能力 | Python脚本支持模块 |
| python::run_tool | 执行Python能力 | Python脚本支持模块 |
| lua::create | 创建Lua环境池 | Lua脚本支持模块 |
| lua::get_tools | 获取Lua能力 | Lua脚本支持模块 |
| lua::run_tool | 执行Lua能力 | Lua脚本支持模块 |

#### 2.1.8.异常处理

1. 脚本加载失败：记录错误日志，跳过该脚本，继续加载其他脚本
2. 脚本执行错误：捕获异常，转换为Rust错误类型，返回给调用者
3. 资源分配失败：返回资源不足错误，触发资源回收机制
4. Lua环境获取失败：记录详细错误日志，返回错误信息
5. Python工具获取失败：记录详细错误日志，返回错误信息

### 2.2.解析器组件

#### 2.2.1.功能描述

解析器组件负责处理能力版本解析、模块和能力依赖关系、路由查找。它是系统中能力管理的核心，确保能力的正确加载和版本匹配。

#### 2.2.2.数据结构

```mermaid
classDiagram
    class Resolver {
        -tools: HashMap~String, BTreeMap~semver::Version, Arc~Tool~~~
        -modules: HashMap~String, Arc~ToolModule~~
        +new(source_tools, config) Self
        +default() Self
        +resolve_tool(tool_id) Option~Arc~Tool~~
        +resolve_tool_with_module(module_name, tool_id) Option~Arc~Tool~~
        +resolve_tool_with_group_version(group, version, tool_id) Option~Arc~Tool~~
        +list_tools() Vec~Arc~Tool~~
        +modules() HashMap~String, Arc~ToolModule~~
    }

    class ToolModule {
        +name: String
        +description: String
        +version: semver::Version
        +tools: HashMap~String, Arc~Tool~~
    }

    class Tool {
        +id: String
        +name: String
        +kind: ToolKind
        +description: String
        +params: Value
        +source: Option~PathBuf~
    }

    Resolver --> "*" ToolModule: 管理
    ToolModule --> "*" Tool: 包含
```

#### 2.2.3.处理流程

1. **版本解析流程**：
   - 支持标准版本格式（如"1.0.0"）和前缀版本格式（如"v2"、"V1.2"）
   - 处理版本字符串前缀，转换为标准semver格式
   - 对于非标准格式，尝试解析为版本要求并提取主要版本信息

2. **能力解析流程**：
   - 从源能力信息和配置中创建能力对象
   - 根据能力组和版本组织能力
   - 支持按名称、模块或版本查找能力
   - 处理版本依赖关系，确保能力的正确加载

3. **模块管理流程**：
   - 从配置中加载模块定义
   - 根据模块版本要求匹配相应的能力
   - 创建模块对象，包含模块信息和可用能力

#### 2.2.4.算法设计

**版本解析算法**：
```mermaid
flowchart TD
    A[开始解析版本] --> B{以v或V开头?}
    B -->|是| C[移除前缀字符]
    B -->|否| D[保持原样]
    C --> E{可以解析为semver?}
    D --> E
    E -->|是| F[返回解析后的版本]
    E -->|否| G{尝试作为版本要求解析}
    G -->|有比较器| H[提取主要版本信息]
    G -->|无比较器| I[返回None]
    H --> J[创建新版本对象]
    J --> K[返回版本对象]
    F --> L[结束]
    I --> L
    K --> L
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,C,D,F,H,I,J,K process;
    class B,E,G decision;
    class L terminal;
```

**能力解析算法**：
```mermaid
flowchart TD
    A[开始解析能力] --> B{完整ID存在?}
    B -->|是| C[返回对应能力]
    B -->|否| D[提取无版本ID]
    D --> E{无版本ID存在?}
    E -->|是| F[获取版本列表]
    E -->|否| G[遍历模块]
    F --> H[返回最新版本能力]
    G --> I{模块中存在该能力?}
    I -->|是| J[返回模块中的能力]
    I -->|否| K[返回None]
    C --> L[结束]
    H --> L
    J --> L
    K --> L
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,D,F,G,C,H,J,K process;
    class B,E,I decision;
    class L terminal;
```

#### 2.2.5.关键技术

1. 使用语义化版本（SemVer）进行版本管理和比较，确保版本兼容性
2. 实现自定义版本解析逻辑，支持标准版本和前缀版本，增强系统兼容性
3. 采用模块化设计，支持能力的动态加载和版本管理
4. 使用HashMap和BTreeMap高效组织和查找能力，优化查询性能
5. 实现版本依赖关系解析，确保能力的正确加载和使用

#### 2.2.6.提供的接口

| 接口名称 | 功能 | 详细描述 |
| -------- | ---- | -------- |
| Resolver::new | 创建解析器实例 | 初始化解析器，从源能力和配置中创建能力和模块 |
| Resolver::default | 创建默认解析器 | 创建空的解析器实例，用于测试或初始化 |
| Resolver::resolve_tool | 解析能力 | 根据能力名称或ID查找匹配的能力实例 |
| Resolver::resolve_tool_with_module | 解析特定模块的能力 | 在指定模块中查找能力 |
| Resolver::resolve_tool_with_group_version | 解析特定版本的能力 | 在指定能力组和版本中查找能力 |
| Resolver::list_tools | 列出所有能力 | 返回系统中所有可用的能力列表 |
| Resolver::modules | 获取所有模块 | 返回系统中所有可用的模块 |

#### 2.2.7.需要的接口

| 接口名称 | 功能 | 提供者 |
| -------- | ---- | ------ |
| SourceTool::resolve_id | 解析源能力ID | 脚本引擎组件 |
| config::Config::tool_groups | 获取能力组配置 | 配置组件 |
| parse_custom_version | 解析自定义版本 | 解析器内部函数 |
| create_tool_id | 创建能力ID | 解析器内部函数 |
| create_unversioned_tool_id | 创建无版本能力ID | 解析器内部函数 |

#### 2.2.8.异常处理

1. 版本解析错误：记录警告日志，使用默认版本或跳过该能力
2. 模块配置错误：记录错误日志，返回配置错误信息
3. 能力不存在：返回找不到能力的错误信息
4. 依赖版本解析失败：记录详细错误日志，跳过该能力
5. 无效的能力ID：记录错误日志，返回错误信息

### 2.3.会话管理组件

#### 2.3.1.功能描述

会话管理组件负责管理系统中的所有对话，提供对话的创建、获取、更新等功能。它是系统与用户交互的核心组件，支持多轮对话、工作流执行和用户交互等功能。

#### 2.3.2.数据结构

```mermaid
classDiagram
    class ConversationManager {
        -conversations: RwLock<HashMap<Uuid, Arc<RwLock<Conversation>>>>
        -conversation_ids: RwLock<Vec<Uuid>>
        -actors: RwLock<HashMap<Uuid, ActorRef<ConversationActorEvent>>>
        -runner: Arc<Runner>
        +new(runner) Self
        +create_conversation(title) Arc<RwLock<Conversation>>
        +create_actor(conversation) ActorRef<ConversationActorEvent>
        +get_conversation(id) Option<Arc<RwLock<Conversation>>>
        +list_conversations() ConversationListResponse
        +delete_conversation(id) bool
    }
    
    class Conversation {
        +id: Uuid
        +title: String
        +create_time: OffsetDateTime
        +update_time: Option<OffsetDateTime>
        +current_message_id: Option<Uuid>
        +status: ConversationStatus
        +metadata: Value
        +messages: HashMap<Uuid, Message>
    }
    
    class ConversationActor {
        +conversation: Arc<RwLock<Conversation>>
        +runner: Arc<Runner>
    }
    
    class Runner {
        +script_engine: Arc<ScriptEngine>
        +model: Model
    }
    
    ConversationManager --> Conversation: 管理
    ConversationManager --> ConversationActor: 创建
    ConversationActor --> Conversation: 操作
    ConversationActor --> Runner: 使用
    Runner --> ScriptEngine: 使用
```

#### 2.3.3.处理流程

```mermaid
stateDiagram-v2
    [*] --> Ready: 创建对话
    Ready --> Chat: 用户输入
    Chat --> Ready: 对话完成
    Chat --> Execute: 生成工作流
    Execute --> Execute: 执行下一步骤
    Execute --> Ready: 执行完成
    Execute --> Interaction: 需要用户交互
    Interaction --> Execute: 交互完成
    Interaction --> Ready: 交互取消
```

1. **对话创建流程**：
   - 生成唯一的对话ID
   - 创建对话实例并存储
   - 创建对话Actor处理对话事件

2. **对话处理流程**：
   - 接收用户输入，创建用户消息
   - 调用模型生成助手回复
   - 解析回复内容，可能生成工作流
   - 执行工作流中的工具调用
   - 处理工具执行结果，可能需要用户交互

3. **交互处理流程**：
   - 生成交互请求并等待用户响应
   - 接收用户交互结果
   - 继续执行工作流或结束对话

#### 2.3.4.算法设计

**对话状态转换算法**：
```mermaid
flowchart TD
    A[开始处理事件] --> B{当前状态和事件类型}
    
    B -->|Ready & UserInput| C[创建用户消息]
    C --> D[创建助手消息]
    D --> E[启动模型运行器]
    E --> F[返回Chat状态]
    
    B -->|Chat & ChatDeltaReceived| G[更新助手消息]
    G --> H[返回Chat状态]
    
    B -->|Chat & ChatDone| I{消息中存在工作流?}
    I -->|是| J[提取工作流]
    J --> K[返回Execute状态]
    I -->|否| L[返回Ready状态]
    
    B -->|Execute & ExecuteNext| M{存在下一节点?}
    M -->|是| N[执行节点中的工具]
    N --> O[添加结果到工具结果]
    O --> P[确定下一节点]
    P --> Q[返回Execute状态]
    M -->|否| R[返回Ready状态]
    
    B -->|Execute & Interaction| S[创建交互请求]
    S --> T[返回Interaction状态]
    
    B -->|Interaction & InteractionResponse| U[处理交互响应]
    U --> V[返回Execute状态]
    
    F --> W[结束]
    H --> W
    K --> W
    L --> W
    Q --> W
    R --> W
    T --> W
    V --> W
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,C,D,E,F,G,H,J,K,L,N,O,P,Q,R,S,T,U,V process;
    class B,I,M decision;
    class W terminal;
```

**对话有限状态机设计**：
```mermaid
stateDiagram-v2
    [*] --> Ready: 初始化
    
    Ready --> Chat: UserInput事件
    Chat --> Chat: ChatDeltaReceived事件
    Chat --> Ready: ChatDone事件(无工作流)
    Chat --> Execute: ChatDone事件(有工作流)
    
    Execute --> Execute: ExecuteNext事件(有下一节点)
    Execute --> Ready: ExecuteNext事件(无下一节点)
    Execute --> Interaction: Interaction事件
    
    Interaction --> Execute: InteractionResponse事件
    Interaction --> Ready: InteractionCancel事件
    
    state Ready {
        [*] --> Idle
        Idle --> WaitingForInput: 等待用户输入
        WaitingForInput --> Idle: 收到用户输入
    }
    
    state Chat {
        [*] --> Generating
        Generating --> Processing: 生成完成
        Processing --> [*]: 处理完成
    }
    
    state Execute {
        [*] --> ToolExecution
        ToolExecution --> ResultProcessing: 工具执行完成
        ResultProcessing --> NextNodeSelection: 结果处理完成
        NextNodeSelection --> [*]: 选择下一节点
    }
    
    state Interaction {
        [*] --> WaitingForResponse
        WaitingForResponse --> ProcessingResponse: 收到响应
        ProcessingResponse --> [*]: 处理完成
    }
```

**对话流解析有限状态机**：

对话流解析器（ChatParser）是一个基于有限状态机的解析器，用于从流式输入中解析特定格式的标签内容。解析器支持多种标签类型，包括think、json、branch、verify、confidence、require-confirm、title、details和metadata。

```mermaid
stateDiagram-v2
    [*] --> Outside: 初始化
    
    Outside --> TagOpen: 遇到标签开始符'<'
    Outside --> Outside: 普通文本
    
    TagOpen --> TagContent: 标签名匹配成功
    TagOpen --> Outside: 标签名匹配失败
    
    TagContent --> TagClose: 遇到标签开始符'<'
    TagContent --> TagContent: 标签内容
    
    TagClose --> Outside: 关闭标签匹配成功
    TagClose --> TagContent: 关闭标签匹配失败
    
    state Outside {
        [*] --> TextProcessing
        TextProcessing --> TagDetection: 处理普通文本
        TagDetection --> [*]: 查找标签开始符
    }
    
    state TagOpen {
        [*] --> CandidateCollection
        CandidateCollection --> TagMatching: 收集候选标签
        TagMatching --> [*]: 匹配标签名
    }
    
    state TagContent {
        [*] --> ContentCollection
        ContentCollection --> EndTagDetection: 收集标签内容
        EndTagDetection --> [*]: 查找结束标签
    }
    
    state TagClose {
        [*] --> CloseHintCheck
        CloseHintCheck --> TagNameCheck: 检查关闭提示符'/'
        TagNameCheck --> EndBracketCheck: 检查标签名
        EndBracketCheck --> [*]: 检查结束符'>'
    }
```

解析器的核心数据结构包括：

1. **ChatDelta枚举**：表示解析出的不同类型的内容片段
   - Text：普通文本内容
   - Think：思考内容
   - Json：JSON格式内容
   - Branch：分支内容
   - Verify：验证内容
   - Confidence：置信度内容
   - RequireConfirm：需要确认的内容
   - Title：标题内容
   - Details：详细内容
   - Metadata：元数据内容

2. **ParseState枚举**：表示解析器的当前状态
   - Outside：在标签外部
   - TagOpen：正在解析开始标签
   - TagContent：正在解析标签内容
   - TagClose：正在解析结束标签

3. **ChatParser结构**：实现Stream特性，提供流式解析功能
   - state：当前解析状态
   - input_stream：输入流
   - pending_deltas：待处理的解析结果

```mermaid
stateDiagram-v2
        StructureBuilding --> [*]: 构建结构
    }
    
    state FlowExtraction {
        [*] --> ToolIdentification
        ToolIdentification --> ArgumentExtraction: 识别工具
        ArgumentExtraction --> DependencyAnalysis: 提取参数
        DependencyAnalysis --> [*]: 分析依赖
    }
    
    state FlowValidation {
        [*] --> SyntaxCheck
        SyntaxCheck --> SemanticCheck: 语法检查
        SemanticCheck --> CycleDetection: 语义检查
        CycleDetection --> [*]: 循环检测
    }
```

#### 2.3.5.关键技术

1. 使用Actor模型管理对话状态和事件处理，提高系统并发性能
2. 实现状态机处理对话的不同状态转换，使流程清晰可控
3. 采用异步编程模型，支持长时间运行的对话和工具执行
4. 使用广播通道实现对话事件的实时推送
5. 实现工作流解析和执行引擎，支持复杂的工具调用序列

#### 2.3.6.提供的接口

| 接口名称 | 功能 | 详细描述 |
| -------- | ---- | -------- |
| ConversationManager::new | 创建对话管理器 | 初始化对话管理器实例 |
| ConversationManager::create_conversation | 创建对话 | 创建新的对话实例并返回 |
| ConversationManager::get_conversation | 获取对话 | 根据ID获取对话实例 |
| ConversationManager::list_conversations | 列出对话 | 返回所有对话的列表 |
| ConversationManager::delete_conversation | 删除对话 | 根据ID删除对话 |
| ConversationActor::handle | 处理对话事件 | 处理各种对话事件并更新状态 |

#### 2.3.7.需要的接口

| 接口名称 | 功能 | 提供者 |
| -------- | ---- | ------ |
| ScriptEngine::run_tool | 执行能力 | 脚本引擎组件 |
| Model::chat | 生成对话回复 | 模型组件 |
| Model::summarize_tool | 总结工具结果 | 模型组件 |
| Model::convert | 转换数据 | 模型组件 |

### 2.4.MCP服务组件

#### 2.4.1.功能描述

MCP服务组件实现了Model Context Protocol (MCP)协议，支持与外部系统的工具集成。它提供了标准化的接口，使外部系统能够调用ToolHub中的能力。

#### 2.4.2.数据结构

```mermaid
classDiagram
    class McpServer {
        -txs: Arc~RwLock~HashMap~SessionId, Sender~ClientJsonRpcMessage~~~~
        -transport_tx: UnboundedSender~SseServerTransport~
        +new(transport_tx) Self
        +handle_connection(session_id, rx, tx) Future~Result~()~~
        +send_message(session_id, message) Future~Result~()~~
        +broadcast(message) Future~()~
    }
    
    class ServerKind {
        <<enumeration>>
        Global
        Module
    }
    
    class SessionModuleRouter {
        -session_id: SessionId
        -inner: ServerKind
        +new(session_id, inner) Self
        +handle_request(request) Future~Result~Response~~
        +resolve_tool(tool_id) Option~Arc~Tool~~
    }
    
    McpServer --> "*" SessionModuleRouter: 管理
    SessionModuleRouter --> "1" ServerKind: 包含
    ServerKind ..> ScriptEngine: 使用
    ServerKind ..> ToolModule: 可能包含
```

#### 2.4.3.处理流程

1. **服务初始化流程**：
   - 创建传输通道和会话管理结构
   - 启动后台任务处理传输请求
   - 创建全局路由和模块特定路由

2. **请求处理流程**：
   - 接收客户端请求（HTTP POST或SSE连接）
   - 根据请求路径确定使用全局路由还是模块特定路由
   - 创建会话并处理请求
   - 执行相应的能力并返回结果

3. **能力调用流程**：
   - 解析客户端请求中的能力名称和参数
   - 在全局范围或指定模块中查找能力
   - 调用脚本引擎执行能力
   - 将执行结果返回给客户端

#### 2.4.4.算法设计

**路由创建算法**：
```mermaid
flowchart TD
    A[开始创建路由] --> B[创建传输通道]
    B --> C[启动后台任务处理传输请求]
    C --> D[创建服务器实例]
    D --> E[创建基础路由]
    E --> F[添加全局路由]
    F --> G[获取所有模块]
    G --> H[遍历模块列表]
    H --> I{还有模块?}
    I -->|是| J[为模块创建特定路由]
    J --> H
    I -->|否| K[返回完整路由]
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,B,C,D,E,F,G,H,J,K process;
    class I decision;
```

**能力列表获取算法**：
```mermaid
flowchart TD
    A[开始获取能力列表] --> B{服务器类型?}
    B -->|全局| C[获取所有能力]
    B -->|模块| D[获取指定模块的能力]
    C --> E[过滤和排序能力]
    D --> E
    E --> F[转换为客户端格式]
    F --> G[返回能力列表]
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,C,D,E,F,G process;
    class B decision;
```mermaid
flowchart TD
    A[开始获取工具列表] --> B[创建空工具集合]
    B --> C{服务器类型?}
    C -->|全局| D[获取所有模块]
    C -->|模块| E[使用指定模块]
    D --> F[遍历所有模块]
    F --> G[遍历模块中的工具]
    G --> H[添加工具到集合<br>使用模块名前缀]
    H --> I{还有更多工具?}
    I -->|是| G
    I -->|否| J{还有更多模块?}
    J -->|是| F
    J -->|否| M[返回工具集合]
    E --> K[遍历模块中的工具]
    K --> L[添加工具到集合<br>不使用模块名前缀]
    L --> N{还有更多工具?}
    N -->|是| K
    N -->|否| M
    
    classDef process fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef decision fill:#fbb,stroke:#f33,stroke-width:2px;
    classDef terminal fill:#bfb,stroke:#3f3,stroke-width:2px;
    class A,B,D,E,F,G,H,K,L,M process;
    class C,I,J,N decision;
    class M terminal;
```

#### 2.4.5.关键技术

1. 实现MCP协议规范，支持工具发现和调用，提供标准化接口
2. 使用HTTP和Server-Sent Events (SSE)提供同步和异步的通信方式
3. 支持全局能力路由和模块特定路由，实现灵活的能力访问控制
4. 使用Tokio异步运行时处理并发请求，提高系统性能
5. 实现会话管理机制，支持长连接和状态维护

#### 2.4.6.提供的接口

| 接口名称 | 功能 | 详细描述 |
| -------- | ---- | -------- |
| McpServer::router | 创建MCP路由 | 创建处理MCP请求的HTTP路由，包括全局和模块特定路由 |
| /api/mcp/ | 全局能力路由 | 处理全局范围内的能力调用，支持GET(SSE)和POST请求 |
| /api/mcp/{module} | 模块特定路由 | 处理特定模块内的能力调用，支持GET(SSE)和POST请求 |
| ServerHandler::get_info | 获取服务器信息 | 返回服务器能力和实现信息 |
| ServerHandler::list_tools | 列出可用工具 | 返回可用的能力列表，支持分页 |
| ServerHandler::call_tool | 调用工具 | 执行指定的能力并返回结果 |

#### 2.4.7.需要的接口

| 接口名称 | 功能 | 提供者 |
| -------- | ---- | ------ |
| ScriptEngine::run_tool | 执行能力 | 脚本引擎组件 |
| Resolver::resolve_tool | 解析能力 | 解析器组件 |
| Resolver::modules | 获取所有模块 | 解析器组件 |
| create_unversioned_tool_id | 创建无版本能力ID | 解析器组件 |
| Tool::params | 获取能力参数定义 | 工具定义组件 |

#### 2.4.8.异常处理

1. 请求格式错误：返回400错误，附带详细的错误信息
2. 能力不存在：返回404错误，指明找不到请求的能力
3. 能力执行错误：返回500错误，包含能力执行过程中的错误信息
4. 会话创建失败：记录错误日志，返回服务器错误
5. 传输通道错误：记录错误日志，关闭连接


### 2.4.服务路由组件

#### 2.4.1.功能描述

服务路由组件提供HTTP接口和路由处理，是系统对外的主要入口。它负责接收客户端请求，路由到相应的处理组件，并返回处理结果。

#### 2.4.5.关键技术

1. 使用Axum Web框架实现HTTP路由和请求处理
2. 支持JSON-RPC和REST API两种接口风格
3. 实现中间件机制，处理认证、日志等横切关注点

#### 2.4.6.提供的接口

| 接口名称 | 功能 | 详细描述 |
| -------- | ---- | -------- |
| create_root_router | 创建根路由 | 创建系统的所有HTTP路由 |
| /api/rpc | RPC接口 | 处理JSON-RPC请求，直接调用能力 |


#### 2.4.7.需要的接口

| 接口名称 | 功能 | 提供者 |
| -------- | ---- | ------ |
| McpServer::router | MCP路由 | MCP服务组件 |
| ScriptEngine::run_tool | 执行能力 | 脚本引擎组件 |


#### 2.4.8.异常处理

1. 路由不存在：返回404错误
2. 请求格式错误：返回400错误，附带详细的错误信息
3. 内部处理错误：返回500错误，记录详细的错误日志

## 3.非功能详细设计

### 3.1.性能详细设计

系统设计中采取了以下措施来满足性能要求：

1. **异步处理模型**：使用Tokio异步运行时，支持高并发的非阻塞IO操作
2. **资源池化**：实现脚本执行环境的池化管理，减少创建和销毁的开销
3. **延迟加载**：能力和资源采用延迟加载策略，减少启动时间和内存占用
4. **缓存机制**：对频繁访问的数据（如能力定义、流程定义）实现缓存
5. **批处理优化**：支持批量处理请求，减少通信开销

系统性能指标：
- 响应时间：普通能力调用延迟增量 < 100ms，主要取决于能力执行时间
- 并发用户数：支持100+并发连接
- 吞吐量：每秒处理1000+能力调用

### 3.2.实施运维效率详设

为提高实施和运维效率，系统提供了以下工具和方法：

1. **配置自动校验**：使用JSON Schema对配置文件进行校验，减少配置错误
2. **热加载支持**：支持能力和流程的热加载，无需重启服务
3. **监控接口**：提供系统状态和性能监控的接口
4. **日志追踪**：实现结构化日志和分布式追踪，便于问题定位
5. **测试工具**：提供能力测试和MCP客户端测试工具，简化测试过程

### 3.3.安全性详细设计

系统在安全性方面采取了以下措施：

1. **输入验证**：对所有外部输入进行严格验证，防止注入攻击
2. **权限控制**：实现基于角色的访问控制，限制对敏感操作的访问
3. **资源隔离**：脚本执行在隔离的环境中，限制对系统资源的访问
4. **错误处理**：统一的错误处理机制，避免敏感信息泄露
5. **日志审计**：记录关键操作的日志，支持安全审计

### 3.4.易用性详细设计

系统在易用性方面的设计考虑：

1. **统一接口**：提供统一的HTTP API接口，便于集成和使用
2. **自动文档**：基于配置自动生成API文档和能力说明
3. **错误提示**：提供详细的错误信息和解决建议
4. **示例代码**：为常见操作提供示例代码和使用指南
5. **配置辅助**：支持配置文件的自动补全和校验

### 3.5.可靠性详细设计

系统在可靠性方面的设计考虑：

1. **错误恢复**：实现错误恢复机制，自动重试失败的操作
2. **超时控制**：对长时间运行的操作实施超时控制，防止资源耗尽
3. **熔断机制**：实现服务熔断，防止级联故障
4. **状态监控**：实时监控系统状态，及时发现和处理异常
5. **数据备份**：关键配置和数据支持自动备份和恢复

## 4.附录

### 4.1 版本管理说明

系统采用语义化版本（SemVer）进行版本管理，版本号格式为：主版本号.次版本号.修订号（例如1.0.0）。

- **主版本号**：当做了不兼容的API修改时递增
- **次版本号**：当做了向下兼容的功能性新增时递增
- **修订号**：当做了向下兼容的问题修正时递增

系统支持的版本格式：
- 标准版本：如"1.0.0"、"2.1.3"
- 前缀版本：如"v2"、"V1.2"

### 4.2 能力开发指南

开发新能力的基本步骤：

1. 在`.runtime/tools/{group}/{version}/`目录下创建脚本文件
2. 使用`@tool(version="*")`装饰器标记函数为能力
3. 在`.runtime/tools/{group}/{version}/tools.json`中定义能力的元数据
4. 在`.runtime/modules.json`中配置使用该能力的模块


### 4.3 相关文档引用

- [MCP协议规范](mcp_slides.md)
- [能力开发指南](mcp_tool_dev_guide.md)
- [API接口文档](api.md)