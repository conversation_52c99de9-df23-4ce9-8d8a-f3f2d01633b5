---
presentation:
  enableSpeakerNotes: false
  mouseWheel: true
---

<!-- slide -->

# MCP 能力开发指南

## 用能力赋能大型语言模型

<small>2025 年 3 月 26 日</small>

<!-- slide -->

## 演示：MCP Client

- 模型交互与调用能力
- 实机演示
  - 问好能力演示
  - 天气查询能力演示

<!-- slide -->

## 什么是能力(Tool)？

### 能力的本质

- **现有软件功能的标准化接口**：将已有软件系统的功能以统一方式暴露
- **异构系统的集成层**：屏蔽底层实现差异，提供一致的调用体验
- **大语言模型的执行能力扩展**：让 AI 能够通过标准方式访问企业现有软件资产

<!-- slide -->

## 能力的价值

- 让 AI 能够访问、调用和组合现有软件功能
- 无需重新开发，快速赋能大型语言模型

<!-- slide -->

## 能力的组织结构

```
.runtime/
  └── tools/
      └── [能力组名称]/
          └── [版本]/
              ├── tools.json     # 能力配置文件
              ├── [能力1].py      # Python 能力实现
              └── lib/           # 共享库目录
```

<div/>

<!-- slide -->

## 版本控制

- 语义化版本(Semantic Versioning): MAJOR.MINOR.PATCH
- 多版本并存，支持平滑升级

<!-- slide -->

## MCP (Model Context Protocol)

实现大型语言模型与外部能力的标准化集成

<!-- slide -->

## MCP 核心特点

- 标准化能力定义
- 统一的调用接口
- 自动参数验证
- 动态能力发现
- **解决的问题**：标准化集成、减少开发复杂度、提高安全性
- **与传统 API 的优势**：统一定义、自动验证、标准错误处理

<!-- slide -->

## MCP 架构图

![MCP架构图](https://norahsakal.com/assets/images/mcp_overview-641a298352ff835488af36be3d8eee52.png)

- MCP 实现模型与多种工具的统一交互
- 标准化的工具定义和调用机制

<!-- slide -->

## MCP vs 传统 API 集成

| 特性     | 传统 API 集成      | MCP 集成             |
| -------- | ------------------ | -------------------- |
| 工具定义 | 非标准化，依赖文档 | 标准化 JSON Schema   |
| 参数验证 | 需要手动实现       | 自动基于 Schema 验证 |
| 工具发现 | 需要硬编码         | 内置标准机制         |
| 错误处理 | 各 API 不一致      | 标准化错误格式       |
| 扩展性   | 需修改集成代码     | 可动态添加新工具     |

<!-- slide -->

## JSON RPC 请求格式

```json
{
  "jsonrpc": "2.0",
  "method": "demo/greet",
  "params": {
    "name": "ronbb"
  },
  "id": "request-123"
}
```

<div/>

<!-- slide -->

## JSON RPC 响应格式

```json
{
  "jsonrpc": "2.0",
  "result": {
    "output": "hello, ronbb"
  },
  "id": "request-123"
}
```

<div/>

<!-- slide -->

## 能力函数基本结构

```python
from __runner__ import tool, Context

@tool(version="*")
async def my_tool(context: Context, params: any):
    # 能力实现代码
    result = {"output": "处理结果"}
    return result
```

<div/>

- 使用装饰器标记能力，支持异步执行
- 通过 Context 访问配置与上下文信息

<!-- slide -->

## 能力定义(tools.json)

```json
{
  "tools": [
    {
      "name": "greet",
      "description": "问候能力",
      "params": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string",
            "description": "接收问候的人员姓名"
          }
        },
        "required": ["name"]
      },
      "result": {
        "type": "object",
        "properties": {
          "output": {
            "type": "string",
            "description": "问候消息"
          }
        },
        "required": ["output"]
      }
    }
  ]
}
```

<div/>

<!-- slide -->

## 参数处理最佳实践

```python
@tool(version="*")
async def greet(context: Context, params: any):
    # 必需参数 (已通过schema验证)
    name = params["name"]

    # 可选参数处理
    greeting = params.get("greeting", "hello")

    return {
        "output": f"{greeting}, {name}"
    }
```

<div/>

<!-- slide -->

## 上下文对象(Context)

```python
@tool(version="*")
async def weather(context: Context, params: any):
    # 访问配置信息
    api_key = context.config.get("api_key")
    base_url = context.config.get(
        "base_url",
        "https://api.default.com"
    )

    # 使用配置信息进行处理
    result = await fetch_weather(api_key, base_url, params["city"])

    return {"output": result}
```

<div/>

<!-- slide -->

## 异步能力实现

```python
@tool(version="*")
async def async_tool(context: Context, params: any):
    # 异步HTTP请求
    import aiohttp
    async with aiohttp.ClientSession() as session:
        url = f"https://api.example.com/data?q={params['query']}"
        async with session.get(url) as response:
            data = await response.json()

    # 并行处理多个异步任务
    import asyncio
    tasks = [process_item(item) for item in data]
    results = await asyncio.gather(*tasks)

    return {"output": results}
```

<div/>

<!-- slide -->

## 单元测试

```python
from __tester__ import load_module, run_async
from __runner__ import Context

# 加载能力模块
echo_module = load_module("demo/1.0.0/echo.py")

# 测试用例
context = Context()
params = {"input": "Hello, world!"}
expected_result = {"output": "Hello, world!"}

result = run_async(echo_module.echo, context, params)
assert result == expected_result, f"测试失败: {result}"
```

<div/>

<!-- slide -->

## MCP 客户端测试

### 环境变量设置

```bash
# 环境变量设置
MCP_SERVER_URL=http://127.0.0.1:15336/api/mcp
OPENAI_BASE_URL=https://api.deepseek.com
OPENAI_API_KEY=your-api-key-here
OPENAI_MODEL=deepseek-chat
```

<div/>

### 运行 MCP 客户端

```bash
# 运行MCP客户端
cd demo
python mcp_client.py
```

<div/>

<!-- slide -->

## 实际案例：PID 整定能力

### 业务需求

- 为工业控制系统提供 PID 参数整定能力
- 根据系统特性和目标响应计算最佳 PID 参数

### 实现思路

- 提供多种整定算法：Ziegler-Nichols, Cohen-Coon 等
- 支持仿真验证与参数优化

<!-- slide -->

## PID 整定能力参数设计

```json
{
  "name": "pid_tuning",
  "description": "PID控制器参数整定能力",
  "params": {
    "type": "object",
    "properties": {
      "system_type": {
        "type": "string",
        "description": "系统类型",
        "enum": ["first_order", "second_order", "integrating"]
      },
      "system_params": {
        "type": "object",
        "description": "系统参数"
      },
      "tuning_method": {
        "type": "string",
        "default": "ziegler_nichols"
      }
    },
    "required": ["system_type", "system_params"]
  },
  "result": {
    "type": "object",
    "properties": {
      "kp": { "type": "number" },
      "ki": { "type": "number" },
      "kd": { "type": "number" },
      "performance": { "type": "object" }
    }
  }
}
```

<div/>

<!-- slide -->

## PID 整定能力实现

> 演示

<div/>

<!-- slide -->

## ToolHub 服务架构

- **模块化设计**：灵活的模块化架构，便于扩展和定制
- **脚本扩展**：支持通过 Lua 与 Python 脚本扩展系统功能
- **MCP 协议实现**：原生支持 Model Context Protocol
- **异步执行支持**：针对长时间运行的任务提供异步机制

<!-- slide -->

## API 接口说明

- **/api/mcp**：全局能力路由，访问所有已注册的能力
- **/api/mcp/{module_name}**：模块特定路由
- **/api/rpc**：直接的 JSON-RPC 接口

<!-- slide -->

## 能力设计原则

- 单一职责：每个能力专注一项功能
- 明确接口：清晰的参数和返回值定义
- 健壮性：防御性编程，良好的错误处理
- 可测试性：方便单元测试和验证

<!-- slide -->

## 版本兼容性

- 语义化版本管理
- 向后兼容策略：保留旧参数支持
- 版本过渡：并行维护多个版本

<!-- slide -->

## Python vs Lua：选择考量

- **Python 优势**: 生态丰富、开发效率高、适合原型和一般应用
- **Lua 优势**: 性能远好于 Python，内存占用小，嵌入性强

<!-- slide -->

## 性能需求时考虑 Lua

- 目前 Lua 定义 tool 的 API 还未稳定
- 如有需求请联系开发团队获取最新支持

<!-- slide -->

## Q: 能力函数必须是异步的吗？

A: 推荐使用异步函数，但不是强制要求。异步能力可以提高系统整体性能。

<!-- slide -->

## Q: 如何优化能力性能？

A: 使用异步操作、并行处理、缓存策略、资源池化。对于性能关键场景，考虑使用 Lua 实现。

<!-- slide -->

## 更多常见问题

**Q: 如何在一个能力中调用另一个能力？**

```python
# 未来计划支持的功能
result = await context.call_tool(
    "module/another_tool",
    {"param1": "value1"}
)
```

<!-- slide -->

**Q: 能力函数抛出异常会怎样？**  
A: 框架会捕获异常并转换为标准的 JSON-RPC 错误响应。建议使用有意义的异常消息，便于调试。

<!-- slide -->

**Q: 如何防止能力被滥用？**  
A: 实施速率限制、添加访问控制、验证输入参数并限制处理范围、监控异常使用模式。

<!-- slide -->

## 外部系统连接配置

```json
{
  "modules": [
    {
      "name": "aas",
      "base": "aas",
      "description": "AAS模块",
      "version": "1.0.0",
      "config": {
        "aas_base_url": "https://example.com/api/v2",
        "aas_token": "encrypted:AES256:abc123...",
        "connection_timeout": 30
      }
    }
  ]
}
```

<div />

<!-- slide -->

# 谢谢

## 问题与讨论
