## 对话管理 API

### 术语说明

| 术语                            | 解释                                                                                                                                                                    |
| ------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| conversation/status/ready       | 对话就绪状态，可以进行用户输入                                                                                                                                          |
| conversation/status/chat        | 对话中状态，指发出对话后接收和解析的过程                                                                                                                                |
| conversation/status/execute     | 对话内的工作流执行中状态                                                                                                                                                |
| conversation/status/interaction | 对话等待用户交互完成的状态                                                                                                                                              |
| message/content/text            | 普通对话内容                                                                                                                                                            |
| message/content/think           | 思维链内容                                                                                                                                                              |
| message/content/json            | 工作流部分，不包含转换条件 `[{"id": "id1", "tool": "PID", "ability": "回路组态查询", "dep": null},{"id": "id2", "tool": "PID", "ability": "数据校验", "dep": ["id1"]}]` |
| message/content/branch          | 工作流内步骤的转换条件 `[{"from": "id1", "to": "id2", "expression": "${xx} == true"}]`                                                                                  |
| message/content/valid           | 工作流校验信息，暂无                                                                                                                                                    |

### 参考调用流程

以下是完整的对话交互流程示例，展示了从创建对话到接收实时响应的完整过程：

1. **创建新对话**

   - 发送 `POST /api/conversation`
   - 接收对话 ID 和基本信息

2. **发送用户输入并建立 SSE 连接**

   - 发送 `POST /api/conversation/{id}/stream`，包含用户消息
   - 连接自动建立 SSE 流，准备接收实时更新
   - 观察 SSE 中的状态变化：`ready` → `chat`

3. **接收助手响应**

   - 通过 SSE 接收 `new_message` 和 `message_content_delta` 事件
   - 根据 delta 增量更新消息内容
   - 监听对话状态变化

4. **工作流执行**

   - 当状态变为 `execute` 时，系统正在执行工作流
   - 通过 SSE 接收工作流执行状态和结果

5. **用户交互处理**

   - 当收到带有 `interaction` 类型的消息时，需要用户交互
   - 对话状态变为 `interaction`
   - 根据交互类型(`select`/`confirm`/`open_page`)执行相应操作
   - 交互完成后状态回到 `ready`

6. **中断处理**

   - 如需中断当前操作，发送 `POST /api/conversation/{id}/break`

7. **查询历史对话**
   - 使用 `GET /api/conversation` 获取对话列表
   - 使用 `GET /api/conversation/{id}` 获取完整对话历史

### 示例流程图

```mermaid
sequenceDiagram
    participant 客户端
    participant API服务器
    participant AI引擎

    客户端->>API服务器: 创建对话 (POST /api/conversation)
    API服务器-->>客户端: 返回对话ID

    客户端->>API服务器: 发送用户输入并建立SSE连接 (POST /api/conversation/{id}/stream)
    API服务器->>AI引擎: 处理用户输入

    API服务器-->>客户端: 状态更新 (ready → chat)
    AI引擎-->>API服务器: 返回助手响应
    API服务器-->>客户端: 增量响应消息 (message_content_delta)

    alt 执行工作流
        API服务器-->>客户端: 状态更新 (chat → execute)
        API服务器-->>客户端: 工作流执行结果
    end

    alt 需要用户交互
        API服务器-->>客户端: 状态更新 (execute → interaction)
        客户端->>API服务器: 提交交互结果 (/api/conversation/{id}/interaction)
        API服务器-->>客户端: 状态更新 (interaction → execute)
    end

    alt 中断操作
        客户端->>API服务器: 中断请求 (POST /api/conversation/{id}/break)
        API服务器-->>客户端: 确认中断
    end

    alt
        API服务器-->>客户端: 状态更新 (execute → ready)
    end
```

### 获取对话列表 GET /api/conversation

**Query 参数**

| 参数名   | 类型    | 默认值      | 描述                       |
| -------- | ------- | ----------- | -------------------------- |
| offset   | integer | 0           | 查询偏移                   |
| limit    | integer | 100         | 查询数量                   |
| order    | string  | update_time | 排序字段，可选 update_time |
| reversed | boolean | false       | 排序反向                   |

**200 响应**

```jsonc
{
  "conversations": [
    {
      "id": "[uuid]",
      "title": "[string]",
      "create_time": "[datetime/iso/utc]",
      "update_time": "[datetime/iso/utc or null]"
    }
  ],
  "limit": 100,
  "offset": 0,
  "total": 233
}
```

**400 响应**

```jsonc
{
  "error": "[string]"
}
```

### 获取对话内容 GET /api/conversation/{id}

**URL 参数**

| 参数名 | 类型 | 默认值 | 描述    |
| ------ | ---- | ------ | ------- |
| id     | uuid |        | 对话 ID |

**200 响应**

```jsonc
{
  "id": "[uuid]",
  "title": "[string]",
  "create_time": "[datetime/iso/utc]",
  "update_time": "[datetime/iso/utc or null]",
  "current_message_id": "[uuid]",
  "status": "[enum<ready/chat/execute/interaction>]",
  "metadata": {}, // TODO
  "messages": {
    "[message_id]": {
      "id": "[uuid]",
      "parent_id": "[uuid or null]",
      "children": "[Array<uuid>]",
      "message": {
        "id": "[uuid]",
        "author": {
          "role": "[enum<user/system/assistant/workflow/tool/interaction>]",
          "name": "[string or null]"
        },
        "create_time": "[datetime/iso/utc]",
        "update_time": "[datetime/iso/utc or null]",
        "metadata": {}, // TODO
        "status": "[enum<fetch/done/error>]",
        // [array]
        "content": [
          {
            "type": "[enum<think/text/json/branch/valid/interaction>]",
            "think": "[string, type=think]",
            "text": "[string, type=text]",
            "json": "[string, type=json]",
            "branch": "[string, type=branch]",
            "valid": "[string, type=valid]",
            // [type=interaction]
            "interaction": {
              "type": "enum<select/confirm/open_page/form>",
              // [array<string>, type=select]
              "select": [],
              "confirm": "[string, type=confirm]",
              "open_page": "[url, type=open_page]",
              "form": {
                "title": "[string]", // optional
                "schema": {}, // json schema
                "default": {} // any, optional
              },
              // [any or null]
              // e.g.
              // select => array<index>
              // confirm => true
              // open_page => any
              // form => any
              "result": null
            }
          }
        ]
      }
    }
  }
}
```

**400 响应**

```jsonc
{
  "error": "[string]"
}
```

### 创建新的对话 POST /api/conversation

**Body 参数**

```jsonc
{}
```

**200 响应**

```jsonc
{
  "id": "[uuid]",
  "title": "[string]",
  "create_time": "[datetime/iso/utc]",
  "update_time": "[null]"
}
```

**400 响应**

```jsonc
{
  "error": "[string]"
}
```

### 中断当前输出或执行 POST /api/conversation/{id}/break

**Body 参数**

```jsonc
{}
```

**200 响应**

```jsonc
{}
```

### 用户交互 POST /api/conversation/{id}/interaction

> 待定

**Body 参数**

```jsonc
// 选择 select
{
  "message_id": "[uuid]",
  "content_index": 0,
  "result": [] // array<index>
}
```

```jsonc
// 确认 confirm
{
  "message_id": "[uuid]",
  "content_index": 0,
  "result": true
}
```

```jsonc
// 打开页面
{
  "message_id": "[uuid]",
  "content_index": 0,
  "result": {} // any
}
```

```jsonc
// 表单提交
{
  "message_id": "[uuid]",
  "content_index": 0,
  "result": {} // any
}
```

**200 响应**

```jsonc
{}
```

### 实时对话内容 POST /api/conversation/{id}/stream

**Body 参数**

```jsonc
{
  "parent_id": "[uuid] or null",
  "message": {
    "content": [
      {
        "type": "text",
        "text": "[string]"
      }
    ]
  }
}
```

**接收 (SSE)**

```jsonc
// 新消息
{
  "type": "new_message",
  "parent_id": "[uuid]",
  "message": {
    "id": "[uuid]",
    "author": {
      "role": "[enum<user/system/assistant/tool/interaction>]",
      "name": "[string or null]"
    },
    "create_time": "[datetime/iso/utc]",
    "update_time": "[datetime/iso/utc or null]",
    "metadata": {},
    "status": "[enum<fetch/done/error>]",
    "content": [
      {
        "type": "[enum<think/text/json/branch/valid/interaction>]",
        "think": "[string, type=think]",
        "text": "[string, type=text]",
        "json": "[string, type=json]",
        "branch": "[string, type=branch]",
        "valid": "[string, type=valid]",
        // [type=interaction]
        "interaction": {
          "type": "enum<select/confirm/open_page/form>",
          // [array<string>, type=select]
          "select": [],
          "confirm": "[string, type=confirm]",
          "open_page": "[url, type=open_page]",
          "form": {
            "title": "[string]", // optional
            "schema": {}, // json schema
            "default": {} // any, optional
          },
          // [any or null]
          "result": null
        }
      }
    ]
  }
}
```

```jsonc
// 消息内容 delta
{
  "type": "message_content_delta",
  "payload": {
    "message_id": "[uuid]",
    "update_time": "[datetime/iso/utc or null]",
    "operation": "append", // append or update
    // [Array<number or string>]
    // e.g. [2, "think"] [3, "json"]
    "path": [],
    "content": ""
  }
}
```

```jsonc
// 对话状态变化
{
  "type": "conversation_status_changed",
  "payload": {
    "status": "[enum<ready/chat/execute/interaction>]"
  }
}
```

```jsonc
// 消息状态变化
{
  "type": "message_status_changed",
  "payload": {
    "message_id": "[uuid]",
    "status": "[enum<fetch/done/error>]"
  }
}
```

```mermaid
sequenceDiagram
  participant User
  participant CUI
  participant Agent
  participant Tool as Tool(Python)
  participant Server as Business Server
  participant GUI

  User->>CUI: 对话开始
  activate CUI
  CUI->>Agent: 对话开始
  activate Agent
  Agent->>Agent: 执行工作流
  Agent->>Tool: 执行能力
  activate Tool
  Tool->>Server: 请求
  activate Server
  Server->>Tool: 响应
  deactivate Server
  Tool->>Agent: 提示交互(open_page: url)
  deactivate Tool
  Agent->>CUI: 提示交互(open_page: url)
  deactivate Agent
  CUI->>GUI: 打开 Page
  activate GUI
  User->>GUI: 界面操作
  GUI->>Server: 界面请求
  activate Server
  Server->>GUI: 界面响应
  deactivate Server
  alt 完成方式二选一
  User->>CUI: 完成，无数据，只有一个空字典
  CUI->>Agent: 完成交互
  activate Agent
  else
  GUI->>Agent: 完成(Post Message)，附带数据
  end
  deactivate GUI

  Agent->>Tool: 继续执行同一能力
  activate Tool
  Tool->>Server: 请求
  activate Server
  Server->>Tool: 响应
  deactivate Server
  Tool->>Agent: 提示交互(open_page: url)
  deactivate Tool
  Agent->>Agent: 继续执行
  deactivate Agent
  deactivate CUI
```

## LLM 接口

大型语言模型（LLM）接口提供与 AI 模型的交互能力，用于生成对话内容、工作流和参数解析。

### 工作流生成 POST /llm/workflow

用于生成包含工作流的对话响应，支持流式输出。

**Body 参数**

```jsonc
{
  "messages": [
    {
      "role": "[enum<user/system/assistant>]",
      "content": "[string]"
    }
  ]
}
```

**接收 (SSE)**

服务器发送事件流，每个事件包含解析后的增量响应内容。

> \<!-- xxx --\> 为注释

```plaintext
<!-- 思维链 -->
<think>
xxxxx
</think>

<!-- 工作流 -->
<json>
[{"id": "id1", "tool": "PID", "ability": "回路组态查询", "dep": null},{"id": "id2", "tool": "PID", "ability": "数据校验", "dep": ["id1"]}]
</json>

<!-- 工作流分支信息 -->
<branch>
[{"from": "id1", "to": "id2", "expression": "${xx} == true"}]
</branch>

<!-- 结束标记 -->
[DONE]
```

### 参数生成 POST /llm/parameter

根据对话历史和 JSON Schema 生成符合指定格式的参数。

**Body 参数**

```jsonc
{
  "messages": [
    {
      "role": "[enum<user/system/assistant>]",
      "content": "[string]"
    }
  ],
  "schema": {
    // JSON Schema 定义
  }
}
```

**200 响应**

```jsonc
{
  "isSuccess": true,
  "param": {
    // 生成的参数，符合请求中提供的 schema
  }
}
```

**400 响应**

```jsonc
{
  "isSuccess": false,
  "param": null
}
```
