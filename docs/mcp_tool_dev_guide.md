# MCP 服务与能力（Tool）开发指南

## 0. Quick Start

### 创建能力

在 `.runtime/tools` 下创建能力组文件夹 `demo/v1`。在 `demo/v1` 下创建 `echo.py`。

```python
from __runner__ import tool, Context

@tool(version="*")
async def echo(context: Context, params: any):
    return params["input"]

```

在 `.runtime/tools/demo/v1` 下创建 `tools.json`。

```json
{
  "$schema": "../../../schema/tools.schema.json",
  "tools": [
    {
      "name": "echo",
      "description": "回声能力",
      "params": {
        "type": "object",
        "description": "回声能力的输入参数",
        "properties": {
          "input": {
            "type": "string",
            "description": "需要被回显的输入文本"
          }
        },
        "required": ["input"]
      },
      "result": {
        "type": "object",
        "description": "回声能力的输出结果",
        "properties": {
          "output": {
            "type": "string",
            "description": "原样返回的输入文本内容"
          }
        },
        "required": ["output"]
      }
    }
  ]
}
```

编辑 `.runtime/modules.json`，添加 `demo` 模块声明。

```jsonc
{
  "$schema": "./schema/modules.schema.json",
  "modules": [
    {
      "name": "demo-1",
      "base": "demo",
      "description": "描述用途",
      "version": "1.0.0",
      "config": {
        // "base_url": "http://********:9096/"
      }
    }
  ]
}
```

### 本地直接测试能力

测试能力。创建 `.runtime/tools/test_echo.py` 。

```python
from __tester__ import load_module, run_async
from __runner__ import Context

greet_module = load_module("demo/1.0.0/greet.py")

context = Context()
context.config = {"key": "value"}
params = {"name": "ronbb"}
expected_result = {"output": "hello, ronbb"}

result = run_async(greet_module.greet, context, params)
assert result == expected_result

# 测试不传context参数
params = {"name": "world"}
expected_result = {"output": "hello, world"}
result = run_async(greet_module.greet, params=params)
assert result == expected_result

print("测试通过!")
```

执行测试。

```bash
cd .runtime/tools
python ./test_echo.py
```

### 以 MCP 测试能力

启动服务，注意，配置文件将从工作路径的相对路径加载。

```bash
# 从源码启动
cargo run --release

# 从二进制启动
tool-hub
```

创建 `demo/.env` 填入 LLM 相关 API URL 与 API KEY。

```ini
OPENAI_BASE_URL=https://api.deepseek.com
OPENAI_API_KEY=sk-***
```

修改 `demo/mcp_client.py` 提示词。

```diff
- anyio.run(main, "hello 早上好呀, 我是 ronbb")
+ anyio.run(main, "<新的提示词>")
```

执行。

```bash
cd demo
python ./mcp_client.py
```

## 1. MCP (Model Context Protocol) 概述

### 1.0 为什么要使用 MCP

#### 1.0.1 解决的问题与带来的优势

MCP 协议解决了以下关键问题：

- **标准化集成**：提供统一的方式集成各种工具和服务
- **减少开发复杂度**：简化工具定义和调用过程
- **动态工具发现**：允许模型在运行时发现和使用新工具
- **增强模型能力**：使模型能够访问外部数据和功能，弥补其固有限制
- **安全性提升**：通过结构化调用和参数验证，减少安全风险
- **可扩展性**：易于添加新工具或扩展现有工具功能

特别是对于工具开发者，MCP 提供了：

- 清晰的工具定义框架
- 自动的参数验证机制
- 标准化的错误处理流程
- 与多种模型和服务的兼容性

**MCP 架构图**：

![MCP架构图](https://norahsakal.com/assets/images/mcp_overview-641a298352ff835488af36be3d8eee52.png)

_图示：MCP 通过规范化的工具定义和调用机制，实现模型与多种工具的统一交互_

#### 1.0.2 与传统 API 集成方式的对比

MCP 与传统 API 集成方式相比具有显著优势。以下是两种方式的对比：

**传统 API 集成流程**：

1. 为每个 API 编写特定代码
2. 手动解析和处理各种格式的响应
3. 需要为不同模型实现不同的集成逻辑
4. 缺乏统一的错误处理机制

![传统API架构图](https://norahsakal.com/assets/images/api_overview-0d9335920826e30bba0897997f599829.png)

_图示：传统 API 集成需要为每个 API 和模型编写特定代码，增加了维护和扩展的复杂度_

**MCP 集成流程**：

1. 使用标准 JSON Schema 定义工具
2. 一次定义，可被多个模型使用
3. 自动参数验证和错误处理
4. 统一的工具发现和调用机制

下面是两种方式的详细比较表格：

| 特性       | 传统 API 集成          | MCP 集成             |
| ---------- | ---------------------- | -------------------- |
| 工具定义   | 非标准化，依赖文档     | 标准化 JSON Schema   |
| 参数验证   | 需要手动实现           | 自动基于 Schema 验证 |
| 工具发现   | 需要硬编码或自定义实现 | 内置标准机制         |
| 错误处理   | 各 API 不一致          | 标准化错误格式       |
| 会话管理   | 需要自行实现           | 协议内置支持         |
| 模型兼容性 | 需为每个模型定制       | 一次实现，兼容多模型 |
| 扩展性     | 需修改模型集成代码     | 可动态添加新工具     |

> **参考资料**：关于更多 MCP 与传统 API 的对比分析，请参考 Norah Sakal 的文章《[MCP vs API: Model Context Protocol Explained](https://norahsakal.com/blog/mcp-vs-api-model-context-protocol-explained/)》。该文章提供了深入的分析和图示，详细解释了为什么 MCP 是大语言模型工具集成的更好选择。

### 1.1 MCP 定义与目的

MCP（Model Context Protocol）是一种标准化协议，旨在实现大型语言模型与外部工具和服务的无缝集成。它提供了一种结构化的方式，使模型能够以可预测和可控的方式与各种外部功能进行交互。

MCP 的核心目的在于：

- 定义模型与外部工具之间的标准通信接口
- 简化工具集成流程，减少开发复杂度
- 增强模型通过工具与外部世界交互的能力
- 提供一致的工具发现、调用和错误处理机制

### 1.2 MCP 核心功能与特点

MCP 协议具有以下关键功能与特点：

- **标准化工具定义**：使用 JSON Schema 定义工具的输入参数和返回结果
- **会话管理**：维护模型与工具之间的上下文会话状态
- **工具发现机制**：允许模型动态发现可用的工具及其功能
- **异步工具执行**：支持长时间运行的工具操作
- **多模态支持**：能够处理文本、图像等多种数据格式
- **错误处理**：标准化的错误报告机制
- **协议版本控制**：确保工具与模型版本兼容性

### 1.3 MCP 工具（Tools）机制详解

#### 1.3.1 工具定义方式

在 MCP 中，工具是通过明确的 JSON Schema 定义的，包含以下核心组件：

```json
{
  "name": "工具名称",
  "description": "工具功能描述",
  "inputSchema": {
    "type": "object",
    "properties": {
      "参数1": {
        "type": "string",
        "description": "参数1的说明"
      },
      "参数2": {
        "type": "number",
        "description": "参数2的说明"
      }
    },
    "required": ["参数1"]
  },
  "outputSchema": {
    "type": "object",
    "properties": {
      "结果字段1": {
        "type": "string",
        "description": "结果字段1的说明"
      }
    }
  }
}
```

每个工具必须有唯一的名称、清晰的描述，以及结构化的输入参数和输出结果定义。

#### 1.3.2 工具参数与结果规范

MCP 对工具参数和结果有严格的规范要求：

- **参数验证**：所有参数都会根据定义的 Schema 进行验证，确保类型和格式正确
- **必选参数**：通过 `required` 字段指定必须提供的参数
- **参数约束**：可以通过 Schema 定义参数的范围、格式和其他约束条件
- **结果一致性**：工具返回的结果必须符合预定义的输出 Schema
- **错误处理**：当参数不符合要求或执行出错时，使用标准格式报告错误

#### 1.3.3 工具调用流程

MCP 工具的典型调用流程如下：

```mermaid
sequenceDiagram
    participant Model as 大语言模型
    participant Client as MCP 客户端
    participant Server as MCP 服务器
    participant Tool as 工具实现

    Model->>Client: 1. 请求获取可用工具
    Client->>Server: 2. 发送工具列表请求
    Server-->>Client: 3. 返回可用工具列表
    Client-->>Model: 4. 提供可用工具信息

    Note over Model: 5. 选择工具并准备参数

    Model->>Client: 6. 发起工具调用请求
    Client->>Server: 7. 转发工具调用
    Server->>Server: 8. 参数验证

    alt 参数验证通过
        Server->>Tool: 9. 执行工具
        Tool-->>Server: 10. 返回执行结果
    else 参数验证失败
        Server-->>Server: 生成错误信息
    end

    Server-->>Client: 11. 响应工具执行结果
    Client-->>Model: 12. 提供工具执行结果
    Model->>Model: 13. 处理结果并生成回复
```

工具调用流程详解：

1. **获取可用工具**：模型请求 MCP 客户端获取可用工具列表
2. **工具列表请求**：MCP 客户端向 MCP 服务器请求可用工具
3. **返回工具列表**：服务器返回所有可用的工具定义
4. **提供工具信息**：客户端将工具信息传递给模型
5. **选择和准备**：模型选择合适的工具并准备符合 Schema 的参数
6. **发起调用请求**：模型发送工具调用请求到客户端
7. **转发工具调用**：客户端将请求转发到 MCP 服务器
8. **参数验证**：服务器根据工具定义验证参数
9. **工具执行**：参数验证通过后，服务器执行工具功能
10. **返回执行结果**：工具执行完成并返回结果
11. **响应结果**：服务器将结果返回给客户端
12. **提供执行结果**：客户端将结果提供给模型
13. **处理结果**：模型处理工具执行结果并生成相应回复

## 2. JSON RPC 基础

### 2.1 JSON RPC 协议简介

JSON-RPC 是一种轻量级的远程过程调用(RPC)协议，它使用 JSON 作为数据格式。MCP 协议在底层采用了 JSON-RPC 作为其通信基础。JSON-RPC 具有简单、易于实现和跨语言支持的特点，使其成为实现 MCP 的理想选择。

JSON-RPC 的主要特点：

- 使用 JSON 作为数据编码格式
- 支持通知（无需响应的请求）
- 支持批量请求和响应
- 简单明确的错误处理机制
- 可以通过 HTTP、WebSocket 等多种传输层实现

> [参考链接](https://www.jsonrpc.org/specification)

### 2.2 请求与响应格式

**JSON-RPC 请求格式**：

```json
{
  "jsonrpc": "2.0",
  "method": "方法名",
  "params": {
    "参数1": "值1",
    "参数2": "值2"
  },
  "id": 1
}
```

**JSON-RPC 响应格式**：

```json
{
  "jsonrpc": "2.0",
  "result": {
    "返回字段1": "值1",
    "返回字段2": "值2"
  },
  "id": 1
}
```

**错误响应格式**：

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32600,
    "message": "错误描述",
    "data": {
      "额外错误信息": "详细说明"
    }
  },
  "id": 1
}
```

### 2.3 MCP 中的 JSON RPC 实现特点

MCP 对 JSON-RPC 进行了一些特定扩展和调整：

- **工具命名空间**：使用特定格式的方法名来表示工具（如"`模块名/工具名`"或"`组名@版本/工具名`"）
- **标准化上下文**：在参数中包含会话和请求上下文信息
- **Schema 验证集成**：将 JSON Schema 验证机制与 RPC 调用紧密结合
- **异步工具支持**：扩展了标准 JSON-RPC 以支持长时间运行的异步工具
- **多模态数据处理**：增强了对二进制数据和多模态内容的支持

在 MCP 工具调用中，典型的 JSON-RPC 请求可能如下所示：

```json
{
  "jsonrpc": "2.0",
  "method": "demo/greet",
  "params": {
    "name": "ronbb"
  },
  "id": "request-123"
}
```

对应的响应：

```json
{
  "jsonrpc": "2.0",
  "result": {
    "output": "hello, ronbb"
  },
  "id": "request-123"
}
```

## 3. ToolHub 服务架构

[数据流](https://doc.weixin.qq.com/flowchart/f4_AVcAZQYaAHsQj4to0PMTHeQ8r1ouP)

### 3.1 服务概述与功能特点

ToolHub 是一个基于 Rust 构建的 AI 流程/工作流系统，它具备以下核心功能特点：

- **模块化设计**：采用灵活的模块化架构，便于扩展和定制
- **脚本扩展机制**：支持通过 Python（推荐） 与 Lua 脚本扩展系统功能
- **MCP 协议实现**：原生支持 Model Context Protocol，提供标准化工具接口
- **能力（Tool）管理**：强大的能力管理系统，支持版本控制和灵活组织
- **异步执行支持**：针对长时间运行的任务提供异步执行机制
- **JSON Schema 验证**：配置文件和能力定义均支持 Schema 验证

ToolHub 服务的主要用途是：

- 作为大语言模型的能力扩展平台
- 管理和调度各类功能组件（能力）
- 提供标准化的工具集成接口
- 支持复杂业务流程的编排和执行

### 3.2 API 接口说明

#### 3.2.1 MCP API (`/api/mcp`)

MCP API 是 ToolHub 实现 Model Context Protocol 的主要接口，用于处理大语言模型的工具调用请求。

**接口路径**：

- `/api/mcp`：全局能力路由，访问所有已注册的能力
- `/api/mcp/{module_name}`：模块特定路由，只访问指定模块的能力

#### 3.2.2 RPC API (`/api/rpc`)

RPC API 提供了直接的 JSON-RPC 接口，用于直接调用系统中的能力，而无需通过 MCP 协议的完整流程。

**接口路径**：

- `/api/rpc`：通用 RPC 接口

**支持的操作**：

- `POST`：发送 JSON-RPC 请求，调用系统能力

**示例请求**：

```http
POST /api/rpc HTTP/1.1
Host: localhost:15336
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "demo/greet",
  "params": {
    "name": "ronbb"
  },
  "id": 123
}
```

**示例响应**：

```json
{
  "jsonrpc": "2.0",
  "result": {
    "output": "hello, ronbb"
  },
  "id": 123
}
```

### 3.3 服务依赖与环境配置

#### 3.3.1 Python 环境要求

ToolHub 服务对于 Python 能力的支持，需要满足以下环境要求：

- **Python 版本**：3.10 或更高版本
- **包管理器**：pip 或 conda

#### 3.3.2 Lua 环境要求

ToolHub 服务内置了 Lua 5.4 运行时，因此无需单独安装 Lua 环境。并且我们提供了一些基础功能：

- `bridge.http`: 提供 `http` 请求能力

#### 3.3.3 其他依赖配置

除了语言环境外，ToolHub 服务可能还需要以下配置：

- **网络访问**：如果能力需要访问外部服务，确保网络连接正常
- **存储权限**：确保服务对 `.runtime` 目录有读写权限
- **内存要求**：建议至少 4GB 可用内存，以支持多个能力并发执行
- **环境变量**：某些能力可能需要特定的环境变量配置

**重要说明**：所有配置文件均存放在 `.runtime` 目录中，对此目录的权限设置至关重要，确保服务进程能够正确读取和写入配置文件。

## 4. 能力（Tool）开发完全指南

### 4.0 能力作为现有软件的拆解与封装

#### 4.0.1 能力的本质与定位

在 ToolHub 中，能力（Tool）不仅仅是简单的功能模块，更是对**现有软件系统**的功能拆解与封装。一个能力本质上是：

- **现有软件功能的标准化接口**：将已有软件系统的功能以统一的方式暴露出来
- **异构系统的集成层**：屏蔽底层实现差异，提供一致的调用体验
- **大语言模型的执行能力扩展**：让 AI 能够通过标准方式访问企业现有软件资产

#### 4.0.2 能力与被封装软件的关系

能力层与现有软件的关系是：

1. **适配层**：通过各种协议与现有软件通信，如 HTTP、WebSocket、GRPC、数据库连接等
2. **转换层**：将现有软件特定的数据格式转换为标准化的 JSON 结构
3. **抽象层**：将复杂的软件功能抽象为语义清晰的能力接口
4. **编排层**：可以组合多个底层软件功能，形成更高级的业务能力

#### 4.0.3 外部软件连接配置管理

为确保能力安全有效地连接到外部软件系统，应将连接信息存储在模块配置中，而非硬编码在能力实现中：

**模块配置示例** (.runtime/modules.json)：

```json
{
  "modules": [
    {
      "name": "aas",
      "base": "aas",
      "description": "AAS模块",
      "version": "1.0.0",
      "config": {
        "aas_base_url": "https://example.com/api/v2",
        "aas_token": "encrypted:AES256:abc123...",
        "connection_timeout": 30,
        "retry_attempts": 3
      }
    }
  ]
}
```

通过这种方式，实现了能力与外部系统连接配置的解耦，使得能力代码可以在不同环境中灵活部署，只需调整配置文件即可对接不同的外部软件实例。

### 4.1 能力定义与组织方式

#### 4.1.1 目录结构与命名规范

在 ToolHub 中，能力（Tool）按照特定的目录结构进行组织，便于管理和版本控制：

```
.runtime/
  └── tools/
      └── [能力组名称]/
          ├── [版本]/
          │   ├── tools.json      # 能力配置文件
          │   ├── [能力1].py       # Python 能力实现
          │   ├── [能力2].lua      # Lua 能力实现
          │   └── lib/            # 能力组共享库目录
          └── [另一个版本]/
              └── ...
```

**命名规范**：

- **能力组名称**：使用小写字母，可包含字母、数字和连字符，例如：`demo`、`text-processing`
- **版本目录**：遵循语义化版本命名（如 `1.0.0`）或前缀版本命名（如 `v2`）
- **能力文件**：使用小写字母，可包含字母、数字和下划线，文件名应与能力名称一致
- **配置文件**：每个版本目录必须包含一个 `tools.json` 文件，定义该版本所有能力的元数据

#### 4.1.2 能力组与版本控制

能力组是一组相关能力的集合，通过版本控制机制支持能力的演进：

- **能力组**：表示功能相关的一组能力，例如 `demo` 组可能包含演示用的各种简单能力
- **版本**：每个能力组可以有多个版本，版本之间的差异可以是：
  - 功能增强或修复
  - 参数调整
  - 新增或移除能力
  - 内部实现变更

**版本兼容性**：

- 在每个版本目录内的能力定义了自己的版本兼容范围
- 使用 semver（语义化版本）的比较规则确定兼容性

**版本选择机制**：

1. 模块在 `modules.json` 中声明其依赖的目标版本
2. 系统会按照版本号从高到低尝试匹配符合条件的能力组
3. 找到匹配的能力组后，只激活其中与模块版本兼容的能力

#### 4.1.3 模块与能力组与应用的关系

模块是能力组的实例化，是应用的抽象。在声明模块时需要定义应用的版本以及必要的配置信息。ToolHub 根据应用的版本匹配能力的实现。

### 4.2 Python 能力开发

#### 4.2.1 能力函数结构与装饰器

Python 能力是一个使用特定装饰器标记的 Python 函数。基本结构如下：

```python
from __runner__ import tool, Context

@tool(version="*")
async def my_tool(context: Context, params: any):
    # 能力实现代码
    result = {"output": "处理结果"}
    return result
```

**关键组件**：

- **`@tool` 装饰器**：将函数标记为一个能力，并指定版本兼容范围
- **`Context` 参数**：提供上下文信息和辅助功能
- **`params` 参数**：包含调用方传入的参数
- **返回值**：必须是一个符合输出 Schema 的字典

**装饰器选项**：

- `version`：指定能力的版本兼容范围（使用 semver 格式）
  - `*`：兼容任何版本
  - `^2.0.0`：兼容 2.x.x 的所有版本
  - `~1.2.0`：兼容 1.2.x 的所有版本
  - 还支持其他操作符：`=`、`>`、`>=`、`<`、`<=`
  - 使用 `,` 填写多个匹配规则 `>=1.2.3, <1.8` 表示同时生效
  - 四位版本需要转换，可以参考
    - `InPlant Direct V4.02.01.14` -> `4.2.1+14` （不会匹配 `+` 后的内容）
    - `InPlant Direct V4.02.01.14` -> `4.201.14`
    - `InPlant Direct V4.02.01.14` -> `4.2.114`
  - [参考链接](https://semver.org/lang/zh-CN/)

#### 4.2.2 参数定义与类型说明

Python 能力的参数和返回值应符合在 `tools.json` 中定义的 Schema：

```python
@tool(version="*")
async def greet(context: Context, params: any):
    # 参数验证已由框架自动处理
    name = params["name"]  # 必需参数，已在 schema 中定义

    # 可选参数处理示例
    greeting = params.get("greeting", "hello")

    # 返回符合 schema 的结果
    return {
        "output": f"{greeting}, {name}"
    }
```

**参数处理最佳实践**：

- 假设必需参数已存在（框架会根据 schema 进行验证）
- 使用 `get()` 方法获取可选参数并提供默认值
- 考虑参数类型和格式的一致性
- 如需额外验证，在函数开始处进行

#### 4.2.3 上下文对象（Context）的使用

`Context` 对象提供了能力运行时的上下文信息和功能。**目前在 ToolHub 中，Context 主要支持以下功能**：

```python
@tool(version="*")
async def basic_tool(context: Context, params: any):
    # 访问配置信息（当前主要支持的功能）
    api_key = context.config.get("api_key")
    base_url = context.config.get("base_url", "https://api.default.com")

    # 使用配置信息进行处理
    result = process_with_config(api_key, base_url)

    return {"output": f"处理完成: {result}"}
```

**注意**：目前 Context 对象主要用于访问模块配置，如果您需要更多功能（如调用其他能力、高级日志记录、会话管理等），请联系服务开发团队，他们可以根据您的实际需求添加相应功能支持。

**未来计划支持的功能**（如有需要可联系服务开发团队）：

- `context.module`：获取当前模块信息
- `context.call_tool()`：调用其他能力
- `context.log`：提供更丰富的日志记录
- `context.session_id`：获取和管理当前会话信息

#### 4.2.4 异步能力实现

建议所有 Python 能力都使用异步实现，以提高系统整体性能：

```python
@tool(version="*")
async def async_tool(context: Context, params: any):
    # 异步 HTTP 请求
    import aiohttp
    async with aiohttp.ClientSession() as session:
        async with session.get("https://api.example.com/data") as response:
            data = await response.json()

    # 异步文件操作
    import aiofiles
    async with aiofiles.open("result.txt", "w") as f:
        await f.write(str(data))

    # 并行处理多个异步任务
    import asyncio
    tasks = [process_item(item) for item in data]
    results = await asyncio.gather(*tasks)

    return {"output": results}
```

**异步编程提示**：

- 使用 `async/await` 语法进行异步操作
- 利用 `aiohttp`、`aiofiles` 等异步库
- 避免在异步函数中使用阻塞操作
- 使用 `asyncio.gather()` 并行处理多个异步任务

#### 4.2.5 返回值格式与规范

能力函数的返回值必须是一个字典，且符合在 `tools.json` 中定义的输出 Schema：

```python
@tool(version="*")
async def data_processor(context: Context, params: any):
    # 处理逻辑
    processed_data = process(params["input"])

    # 返回符合 schema 的结果
    return {
        "result": processed_data,
        "timestamp": time.time(),
        "status": "success"
    }
```

**返回值规范**：

- 确保所有必需字段都存在
- 遵循字段的类型和格式要求
- 不要包含 Schema 中未定义的额外字段
- 如有错误，抛出适当的异常，框架会转换为标准错误响应

### 4.3 Lua 能力开发

#### 4.3.1 Lua 能力基本结构

> 待完善

#### 4.3.2 Lua 特有的能力实现方式

> 待完善

### 4.4 能力配置文件编写

#### 4.4.1 tools.json 完整示例与字段说明

每个能力组版本目录都需要包含一个 `tools.json` 文件，定义该版本下所有能力的元数据：

```json
{
  "$schema": "../../../schema/tools.schema.json",
  "tools": [
    {
      "name": "greet",
      "description": "问候能力",
      "params": {
        "type": "object",
        "description": "问候能力的输入参数",
        "properties": {
          "name": {
            "type": "string",
            "description": "接收问候的人员姓名，将在问候消息中使用"
          },
          "greeting": {
            "type": "string",
            "description": "问候语，默认为 'hello'",
            "default": "hello"
          }
        },
        "required": ["name"]
      },
      "result": {
        "type": "object",
        "description": "问候能力的输出结果",
        "properties": {
          "output": {
            "type": "string",
            "description": "包含针对指定人员的问候消息"
          }
        },
        "required": ["output"]
      }
    },
    {
      "name": "echo",
      "description": "回声能力",
      "params": {
        "type": "object",
        "description": "回声能力的输入参数",
        "properties": {
          "input": {
            "type": "string",
            "description": "需要被回显的输入文本"
          }
        },
        "required": ["input"]
      },
      "result": {
        "type": "object",
        "description": "回声能力的输出结果",
        "properties": {
          "output": {
            "type": "string",
            "description": "原样返回的输入文本内容"
          }
        },
        "required": ["output"]
      }
    }
  ]
}
```

**主要字段说明**：

- **`$schema`**：指向 JSON Schema 文件，提供编辑时的自动补全和验证
- **`tools`**：包含所有能力定义的数组
  - **`name`**：能力的名称（必须与实现文件的能力函数名匹配）
  - **`description`**：能力的功能描述
  - **`params`**：定义输入参数的 JSON Schema
    - **`type`**：通常为 "object"
    - **`properties`**：定义各参数的类型和描述
    - **`required`**：指定必需的参数
  - **`result`**：定义返回结果的 JSON Schema
    - 结构与 `params` 类似，描述能力的输出格式

#### 4.4.2 JSON Schema 支持与验证

ToolHub 系统使用 JSON Schema 进行参数验证和文档生成：

- **参数验证**：系统会根据 `params` 定义验证输入参数
- **结果验证**：系统会根据 `result` 定义验证返回值
- **编辑器支持**：通过 `$schema` 引用获得编辑器的自动补全和验证

**JSON Schema 编写提示**：

- 使用描述性的属性名称
- 为每个属性提供清晰的描述
- 指定适当的数据类型
- 使用 `required` 标记必需属性
- 考虑添加 `default` 值，增强易用性
- 可以使用高级特性如 `pattern`、`minimum`/`maximum` 等进行更精确的约束

## 5. 能力测试与调试

### 5.1 单元测试

ToolHub 提供了一套完整的测试工具，允许开发者在不启动完整服务的情况下测试能力。这些工具模拟了服务环境，使得能力测试变得简单高效。

#### 5.1.1 `__tester__.py` 工具使用方法

`__tester__.py` 是一个专门用于测试能力的辅助工具，它提供了以下核心功能：

- **加载能力模块**：通过 `load_module()` 函数加载指定路径的能力文件
- **执行异步能力**：通过 `run_async()` 函数执行异步能力函数
- **执行同步能力**：通过 `run()` 函数执行同步能力函数
- **模拟上下文**：创建与服务环境相似的上下文对象

使用 `__tester__.py` 的基本步骤：

1. 导入测试工具和 Context 类
2. 加载要测试的能力模块
3. 准备测试数据和上下文
4. 执行能力函数
5. 验证返回结果

```python
# 导入测试工具
from __tester__ import load_module, run_async
from __runner__ import Context

# 加载能力模块
greet_module = load_module("demo/1.0.0/greet.py")

# 准备测试数据
context = Context()
context.config = {"key": "value"}
params = {"name": "ronbb"}

# 执行能力函数
result = run_async(greet_module.greet, context, params)

# 验证结果
assert result == {"output": "hello, ronbb"}
print("测试通过!")
```

#### 5.1.2 编写测试用例（参考 test_echo.py）

一个完整的测试用例应该包含多个测试场景，包括正常情况和异常情况。以 `test_echo.py` 为例：

```python
from __tester__ import load_module, run_async
from __runner__ import Context

# 加载要测试的能力模块
echo_module = load_module("demo/1.0.0/echo.py")

# 测试场景 1: 基本功能测试
context = Context()
params = {"input": "Hello, world!"}
expected_result = {"output": "Hello, world!"}

result = run_async(echo_module.echo, context, params)
assert result == expected_result, f"期望 {expected_result}，实际得到 {result}"

# 测试场景 2: 空输入测试
params = {"input": ""}
expected_result = {"output": ""}

result = run_async(echo_module.echo, context, params)
assert result == expected_result, f"期望 {expected_result}，实际得到 {result}"

# 测试场景 3: 特殊字符测试
params = {"input": "!@#$%^&*()_+"}
expected_result = {"output": "!@#$%^&*()_+"}

result = run_async(echo_module.echo, context, params)
assert result == expected_result, f"期望 {expected_result}，实际得到 {result}"

# 测试场景 4: 中文输入测试
params = {"input": "你好，世界！"}
expected_result = {"output": "你好，世界！"}

result = run_async(echo_module.echo, context, params)
assert result == expected_result, f"期望 {expected_result}，实际得到 {result}"

print("所有测试用例均通过!")
```

#### 5.1.3 模拟上下文与参数

在测试能力时，通常需要模拟服务环境中的上下文（Context）对象和参数。`__tester__.py` 提供了创建和配置模拟上下文的功能：

```python
from __tester__ import load_module, run_async
from __runner__ import Context

# 创建和配置模拟上下文
context = Context()

# 设置模块配置
context.config = {
    "api_key": "test-api-key",
    "base_url": "https://api.example.com"
}

# 设置模块信息
context.module = {
    "name": "test-module",
    "base": "demo",
    "version": "1.0.0"
}

# 设置会话ID
context.session_id = "test-session-123"

# 准备测试参数
params = {
    "name": "ronbb",
    "greeting": "welcome"
}

# 执行能力并验证结果
greet_module = load_module("demo/1.0.0/greet.py")
result = run_async(greet_module.greet, context, params)
assert result["output"] == "welcome, ronbb"
```

#### 5.1.4 断言与验证

测试用例中应该包含充分的断言和验证，以确保能力的行为符合预期：

```python
# 基本断言：验证完整结果
assert result == expected_result

# 部分字段验证
assert result["output"] == "hello, ronbb"
assert "timestamp" in result
assert isinstance(result["count"], int)

# 结构验证
assert isinstance(result, dict)
assert all(key in result for key in ["status", "data", "message"])

# 数值范围验证
assert 0 <= result["probability"] <= 1

# 异常处理测试
try:
    result = run_async(module.risky_function, context, {})
    assert False, "应该抛出异常但没有"
except ValueError:
    # 预期会抛出 ValueError
    pass
```

### 5.2 MCP 客户端测试

除了单元测试外，ToolHub 还提供了 MCP 客户端工具，用于测试能力在实际 MCP 环境中的表现。这种方式可以验证能力在与大语言模型交互时的行为。

#### 5.2.1 `mcp_client.py` 配置与使用

`mcp_client.py` 是一个用于连接 MCP 服务并测试能力的客户端工具。使用它需要以下步骤：

1. 确保 ToolHub 服务已启动
2. 配置环境变量或 `.env` 文件
3. 运行 `mcp_client.py` 脚本

```bash
# 在 demo 目录下运行
cd demo
python mcp_client.py
```

`mcp_client.py` 的核心功能：

- 连接到 ToolHub 的 MCP 服务
- 创建 MCP 客户端会话
- 获取可用工具列表
- 将可用工具提供给大语言模型
- 处理用户输入和模型响应
- 将工具调用请求转发到 MCP 服务
- 将工具执行结果返回给模型

#### 5.2.2 环境变量设置

使用 `mcp_client.py` 需要配置以下环境变量：

```properties
# MCP 服务配置
MCP_SERVER_URL=http://127.0.0.1:15336/api/mcp

# OpenAI API 配置（或兼容的替代服务）
OPENAI_BASE_URL=https://api.deepseek.com
OPENAI_API_KEY=your-api-key-here
OPENAI_MODEL=deepseek-chat

# 其他配置
OPENAI_AGENTS_DISABLE_TRACING=1
```

可以通过两种方式设置环境变量：

1. 在系统环境中直接设置
2. 在 `demo` 目录下创建 `.env` 文件（推荐方式）

#### 5.2.3 与大语言模型交互测试

`mcp_client.py` 提供了一个交互式环境，让你可以通过自然语言与大语言模型交流，测试模型使用你的能力的情况：

```python
# mcp_client.py 的核心流程示例
async def main(input: str):
    # 连接 MCP 服务
    async with sse_client("http://127.0.0.1:15336/api/mcp") as (read_stream, write_stream):
        async with ClientSession(read_stream, write_stream) as session:
            # 初始化会话并获取工具列表
            await session.initialize()
            tools = await session.list_tools()

            # 将工具转换为 OpenAI 格式
            openai_tools = convert_tools_to_openai_format(tools.tools)

            # 使用 OpenAI API 创建对话
            messages = [
                {"role": "system", "content": "你是一个助手，可以使用提供的工具来帮助用户。"},
                {"role": "user", "content": input},
            ]

            # 调用大语言模型
            async with AsyncOpenAI() as openai_client:
                response = await openai_client.chat.completions.create(
                    model=os.environ.get("OPENAI_MODEL", "gpt-3.5-turbo"),
                    messages=messages,
                    tools=openai_tools,
                    tool_choice="auto"
                )

                # 处理模型响应
                assistant_message = response.choices[0].message

                # 如果模型想要使用工具
                if assistant_message.tool_calls:
                    for tool_call in assistant_message.tool_calls:
                        # 调用 MCP 服务中的工具
                        tool_name = tool_call.function.name.replace("____", "/")
                        tool_params = json.loads(tool_call.function.arguments)

                        # 执行工具调用
                        tool_result = await session.call_tool(tool_name, tool_params)

                        # 将工具结果返回给模型
                        messages.append(assistant_message)
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "name": tool_name,
                            "content": json.dumps(tool_result)
                        })
```

**测试示例**：

1. 启动 ToolHub 服务：

   ```bash
   cargo run
   ```

2. 在另一个终端窗口中运行 `mcp_client.py`：

   ```bash
   cd demo
   python mcp_client.py "你好，请使用 greet 能力向 ronbb 问好"
   ```

3. 观察模型是否正确调用了 `greet` 能力，并返回了问候消息。

### 5.3 调试技巧与最佳实践

#### 5.3.1 日志记录与分析

在开发能力时，良好的日志记录对于调试至关重要：

- **使用 Context 提供的日志功能**：

  ```python
  context.log.debug("调试信息")
  context.log.info("一般信息")
  context.log.warning("警告信息")
  context.log.error("错误信息")  # 包含异常堆栈
  ```

- **记录关键数据**：

  ```python
  context.log.info(f"接收到的参数: {params}")
  context.log.info(f"API 响应: {response.status_code}")
  context.log.debug(f"处理结果: {result}")
  ```

- **结构化日志**：
  ```python
  context.log.info({
      "request_id": context.session_id,
      "params": params,
      "status": "success"
  })
  ```

#### 5.3.2 常见错误排查

开发能力时可能遇到的常见错误及其解决方法：

1. **能力未被注册或找不到**

   - 检查能力文件名和函数名是否一致
   - 验证 `tools.json` 中是否正确定义了能力
   - 确认版本声明与模块要求匹配

2. **参数验证失败**

   - 比对传入参数与 Schema 定义
   - 检查必需参数是否提供
   - 验证参数类型是否正确

3. **异步函数执行错误**

   - 确保所有 `await` 语句都在 `async` 函数内
   - 检查异步资源是否正确释放
   - 捕获并处理异步操作中的异常

4. **能力执行超时**

   - 检查是否存在长时间阻塞操作
   - 优化复杂计算或 IO 操作
   - 考虑使用更高效的算法或库

5. **返回值格式错误**
   - 确保返回值是一个字典
   - 验证返回的字段与 Schema 定义一致
   - 检查字段值的类型是否符合预期

#### 5.3.3 性能优化建议

提高能力性能的一些建议：

- **使用异步操作**：特别是对于 IO 密集型任务

  ```python
  # 优先使用异步库
  import aiohttp
  async with aiohttp.ClientSession() as session:
      async with session.get(url) as response:
          data = await response.json()
  ```

- **缓存频繁使用的数据**：

  ```python
  # 使用函数级缓存
  from functools import lru_cache

  @lru_cache(maxsize=100)
  def get_expensive_data(key):
      # 获取数据的代码
      return data
  ```

- **批量处理**：当需要处理多个项目时

  ```python
  # 并行处理多个任务
  import asyncio
  tasks = [process_item(item) for item in items]
  results = await asyncio.gather(*tasks)
  ```

- **减少不必要的计算**：

  ```python
  # 提前检查条件，避免不必要的工作
  if not params.get("enable_feature"):
      return {"output": "Feature disabled"}

  # 昂贵操作只在必要时执行
  if detailed_analysis_needed(params):
      result = await perform_detailed_analysis(data)
  else:
      result = perform_simple_analysis(data)
  ```

- **优化资源使用**：
  ```python
  # 合理设置连接池大小
  connector = aiohttp.TCPConnector(limit=30)  # 最多30个并发连接
  async with aiohttp.ClientSession(connector=connector) as session:
      # 使用会话
  ```

## 6. 进阶工具与资源

### 6.1 MCP Inspector 使用指南

MCP Inspector 是一个用于测试和调试 MCP 服务的图形界面工具，它能够帮助开发者可视化地检查和测试 MCP 工具。

#### 6.1.1 安装与配置

**安装步骤**：

1. 克隆 MCP Inspector 仓库：

   ```bash
   git clone https://github.com/modelcontextprotocol/inspector.git
   cd inspector
   ```

2. 安装依赖：

   ```bash
   npm install
   ```

3. 启动应用：

   ```bash
   # Linux/MacOS
   npm run dev

   # Windows
   npm run dev:windows
   ```

4. 访问 Web 界面（默认地址为 http://localhost:3000）

**配置 MCP Inspector**：

1. 在 Inspector 界面中，点击左侧的"设置"按钮
2. 在服务器 URL 字段中输入 ToolHub 的 MCP 服务地址：http://localhost:15336/api/mcp
3. 点击"保存"按钮应用设置

#### 6.1.2 通过 GUI 测试 MCP 服务

MCP Inspector 提供了直观的界面来测试 MCP 服务：

1. **浏览可用工具**：

   - 连接到 MCP 服务后，Inspector 会自动获取并显示所有可用工具
   - 工具按模块和名称组织，便于浏览和选择

2. **测试特定工具**：

   - 点击要测试的工具
   - 界面会显示该工具的详细信息，包括描述、参数结构和返回值格式
   - 在参数输入区填写测试数据
   - 点击"执行"按钮调用工具
   - 查看工具执行结果和响应时间

3. **保存和载入测试用例**：
   - 可以将测试参数保存为用例，便于重复测试
   - 支持导入和导出测试用例，便于团队共享

#### 6.1.3 分析请求与响应

MCP Inspector 提供了强大的请求和响应分析功能：

1. **请求详情**：

   - 查看完整的 JSON-RPC 请求内容
   - 检查 HTTP 头信息
   - 显示请求参数的验证结果

2. **响应详情**：

   - 展示格式化的响应结果
   - 提供 JSON 语法高亮和折叠功能
   - 显示响应时间和大小信息
   - 标记错误和警告

3. **历史记录**：
   - 自动保存最近的调用历史
   - 可以比较不同调用的结果
   - 支持筛选和搜索历史记录

### 6.2 开发工具推荐

#### 6.2.1 IDE 与插件

开发能力（Tool）时，推荐使用以下 IDE 和插件：

**Visual Studio Code**：

- **Python 扩展**：提供智能补全、代码分析和调试功能
- **JSON Schema Validator**：支持 JSON Schema 验证，便于编辑 tools.json 文件
- **REST Client**：用于测试 HTTP API
- **Lua 语言支持**：提供 Lua 语法高亮和补全

**PyCharm**：(付费警告)

- 对 Python 开发提供全面支持
- 支持远程调试
- 集成 HTTP 客户端工具

**其他工具**：

- **Insomnia/Postman**：用于测试 RPC 和 MCP API
- **JSON Schema 验证工具**：如 jsonschema.net，用于生成和验证 Schema

### 6.3 参考资源与文档

#### 6.3.1 MCP 规范文档链接

- [MCP 官方规范文档](https://github.com/modelcontextprotocol/specification/blob/main/docs/specification/2024-11-05/server/tools.md)：完整的协议规范和实现指南
- [MCP 示例与最佳实践](https://github.com/modelcontextprotocol/specification/tree/main/examples)：各种语言的实现示例
- [MCP Inspector 项目](https://github.com/modelcontextprotocol/inspector)：用于测试和调试 MCP 服务的工具

#### 6.3.2 相关项目与示例

- [Python MCP 客户端库](https://github.com/modelcontextprotocol/python-mcp)：用于开发 MCP 客户端的 Python 库
- [MCP 示例服务器](https://github.com/modelcontextprotocol/python-mcp-server)：Python 实现的示例 MCP 服务器
- [OpenAI Function Calling 文档](https://platform.openai.com/docs/guides/function-calling)：了解大语言模型如何使用工具

## 7. 最佳实践与案例研究

### 7.1 能力设计原则

设计高质量能力（Tool）时应遵循以下原则：

1. **单一职责**：每个能力应专注于完成一项特定任务

   - 做法：将复杂功能分解为多个专注的能力
   - 例如：将数据处理拆分为获取、转换、验证等独立能力

2. **明确接口**：提供清晰的参数和返回值定义

   - 做法：详尽的 JSON Schema 及注释
   - 例如：为每个参数提供类型、格式、默认值和约束条件

3. **健壮性**：防御性编程，处理各种异常情况

   - 做法：参数验证、异常捕获和恢复机制
   - 例如：检查外部 API 响应，提供优雅的错误处理

4. **可测试性**：设计便于测试的能力

   - 做法：避免硬编码依赖，支持注入测试上下文
   - 例如：使用配置项代替硬编码的 API 端点以及 BaseURL

5. **性能考量**：优化能力执行效率
   - 做法：异步操作、资源池化、缓存策略
   - 例如：使用连接池避免频繁创建连接

### 7.2 版本兼容性保障

确保能力版本平滑升级的最佳实践：

1. **语义化版本管理**：

   - 主版本（x.0.0）：不兼容的 API 变更
   - 次版本（0.x.0）：向后兼容的功能新增
   - 修订版本（0.0.x）：向后兼容的问题修复

2. **向后兼容策略**：

   - 保留旧参数的支持
   - 新参数设为可选
   - 扩展而非修改已有字段

3. **版本过渡**：

   - 并行维护多个版本
   - 提供版本迁移指南
   - 使用废弃（deprecation）警告

4. **文档更新**：
   - 明确标记版本变更
   - 提供升级指引
   - 记录不兼容变更

### 7.3 实际案例分析

**案例：开发文件处理能力组**

假设我们需要开发一组处理文件的能力，包括文件读取、写入和转换。

**1. 能力组织结构**：

```
.runtime/
  └── tools/
      └── file-processor/
          └── 1.0.0/
              ├── tools.json
              ├── read_file.py
              ├── write_file.py
              ├── convert_format.py
              └── lib/
                  └── file_utils.py
```

**2. 能力定义示例**：

```json
{
  "$schema": "../../../schema/tools.schema.json",
  "tools": [
    {
      "name": "read_file",
      "description": "读取文件内容",
      "params": {
        "type": "object",
        "properties": {
          "file_path": {
            "type": "string",
            "description": "要读取的文件路径"
          },
          "encoding": {
            "type": "string",
            "description": "文件编码",
            "default": "utf-8"
          }
        },
        "required": ["file_path"]
      },
      "result": {
        "type": "object",
        "properties": {
          "content": {
            "type": "string",
            "description": "文件内容"
          },
          "size": {
            "type": "number",
            "description": "文件大小（字节）"
          }
        },
        "required": ["content"]
      }
    }
  ]
}
```

**3. 能力实现示例**：

```python
from __runner__ import tool, Context
import os
from pathlib import Path

@tool(version="^1.0.0")
async def read_file(context: Context, params: any):
    file_path = params["file_path"]
    encoding = params.get("encoding", "utf-8")

    # 安全检查
    abs_path = Path(file_path).resolve()
    base_dir = Path(context.config.get("base_dir", "/")).resolve()
    if not str(abs_path).startswith(str(base_dir)):
        raise ValueError(f"访问拒绝: {file_path} 在允许的目录之外")

    try:
        # 异步读取文件
        import aiofiles
        async with aiofiles.open(abs_path, mode='r', encoding=encoding) as f:
            content = await f.read()

        # 获取文件信息
        stats = os.stat(abs_path)

        return {
            "content": content,
            "size": stats.st_size
        }
    except FileNotFoundError:
        raise ValueError(f"文件未找到: {file_path}")
    except PermissionError:
        raise ValueError(f"权限拒绝: {file_path}")
    except Exception as e:
        context.log.error(f"读取文件错误: {str(e)}")
        raise ValueError(f"文件读取失败: {str(e)}")
```

**4. 测试用例示例**：

```python
from __tester__ import load_module, run_async
from __runner__ import Context
import os
import tempfile

# 创建临时测试文件
test_content = "Hello, this is a test file.\n第二行是中文。"
temp_dir = tempfile.gettempdir()
test_file = os.path.join(temp_dir, "test_file.txt")

with open(test_file, "w", encoding="utf-8") as f:
    f.write(test_content)

# 加载能力模块
file_module = load_module("file-processor/1.0.0/read_file.py")

# 创建上下文
context = Context()
context.config = {"base_dir": temp_dir}

# 测试正常情况
params = {"file_path": test_file}
result = run_async(file_module.read_file, context, params)

assert result["content"] == test_content
assert result["size"] == len(test_content.encode("utf-8"))

# 测试错误情况：文件不存在
try:
    params = {"file_path": os.path.join(temp_dir, "non_existent_file.txt")}
    result = run_async(file_module.read_file, context, params)
    assert False, "应该抛出异常但没有"
except ValueError as e:
    assert "文件未找到" in str(e)

# 测试错误情况：路径越界
try:
    params = {"file_path": "/etc/passwd"}
    result = run_async(file_module.read_file, context, params)
    assert False, "应该抛出异常但没有"
except ValueError as e:
    assert "访问拒绝" in str(e)

# 清理测试文件
os.remove(test_file)

print("所有测试通过!")
```

### 7.4 扩展与集成建议

将 ToolHub 的能力集成到更大系统时的建议：

1. **模块化部署**：

   - 按业务域划分能力组
   - 考虑微服务架构
   - 使用容器化部署

2. **安全性考量**：

   - 实施访问控制
   - 添加请求验证层
   - 限制资源使用
   - 记录审计日志

3. **监控与可观测性**：

   - 部署健康检查端点
   - 集成监控系统
   - 设置关键指标告警
   - 实现分布式追踪

4. **扩展集成点**：
   - 与消息队列系统集成
   - 支持事件驱动架构
   - 实现 webhook 通知
   - 考虑批处理能力

## 8. 常见问题解答 (FAQ)

### 8.1 开发过程中的常见问题

**Q: 能力函数必须是异步的吗？**  
A: 强烈推荐使用异步函数，但不是强制要求。异步能力可以提高系统整体性能，特别是对于 IO 密集型操作。使用 `async def` 定义异步能力函数，内部使用 `await` 关键字等待异步操作完成。

**Q: 如何在一个能力中调用另一个能力？**  
A: 使用 Context 对象的 `call_tool` 方法：

```python
result = await context.call_tool("module/another_tool", {"param1": "value1"})
```

**Q: 能力函数抛出异常会怎样？**  
A: 框架会捕获异常并转换为标准的 JSON-RPC 错误响应返回给调用者。建议使用有意义的异常消息，便于调试和问题定位。

**Q: 为什么我的能力没有被激活？**  
A: 检查以下几点：

1. 确认能力文件名与函数名匹配
2. 验证 tools.json 中正确定义了能力
3. 检查能力声明的版本兼容范围（`@tool(version="x.y.z")`）与模块要求版本是否匹配
4. 检查日志中是否有相关错误信息

**Q: 如何添加自定义依赖库？**  
A: 可以将依赖库放在能力组的 `lib` 目录下，或者使用系统环境中已安装的库。确保在能力实现中正确导入所需库。

**Q: Python vs Lua：选择考量**
A: 区别在于

- **Python 优势**: 生态丰富、开发效率高、适合原型和一般应用
- **Lua 优势**: 性能远好于 Python，内存占用小，嵌入性强

### 8.2 部署与集成问题

**Q: 如何将 MCP 服务与现有系统集成？**  
A: 有多种集成方式：

1. 直接调用 MCP API 端点（`/api/mcp`）
2. 使用 RPC API 端点（`/api/rpc`）
3. 通过 MCP 客户端库进行连接
4. 在大语言模型中作为工具使用

**Q: 能力服务如何扩展以支持高并发？**  
A: 考虑以下几点：

1. 使用异步能力减少阻塞
2. 优化资源密集型操作
3. 部署多个服务实例并使用负载均衡
4. 对于计算密集型能力，考虑单独部署

**Q: 能力调用之间是否共享状态？**  
A: 默认情况下，每次能力调用都是独立的，不共享状态。如需在会话中保持状态，可以：

1. 使用数据库或缓存存储会话状态
2. 在每次调用中传递必要的状态信息
3. 利用 `context.session_id` 关联同一会话的多次调用

**Q: 如何监控能力的性能和错误率？**  
A: 可以通过以下方式实现监控：

1. 使用日志系统记录关键指标
2. 集成第三方监控工具
3. 实现健康检查端点
4. 添加性能计时器和错误计数器

### 8.3 性能与安全考量

**Q: 如何优化长时间运行的能力？**  
A: 考虑以下优化策略：

1. 实现分块处理，避免一次性处理大量数据
2. 使用异步操作和并行处理
3. 考虑实现进度报告机制
4. 对于特别耗时的操作，考虑任务队列或后台作业

**Q: 能力中如何安全地处理敏感信息？**  
A: 遵循以下安全实践：

1. 敏感信息（如 API 密钥）存储在配置中，通过 `context.config` 访问
2. 不要在日志中记录敏感数据
3. 实施适当的访问控制
4. 对敏感操作增加额外验证层

**Q: 如何防止能力被滥用？**  
A: 可采取以下措施：

1. 实施速率限制
2. 添加访问控制和认证
3. 验证输入参数并限制处理范围
4. 监控异常使用模式
5. 实现资源使用限制

**Q: 是否可以限制某个能力的资源使用？**  
A: 可以通过以下方式限制资源使用：

1. 设置超时限制
2. 限制处理数据量
3. 实现资源配额
4. 监控资源使用并中断过度消耗的操作

## 附录

### A. 术语表

- **能力（Tool）**：封装特定功能的代码单元，通过 MCP 协议暴露给调用者
- **能力组（Tool Group）**：功能相关的能力集合，共享相同版本
- **模块（Module）**：使用特定版本能力的配置单元
- **上下文（Context）**：包含当前执行环境信息的对象
- **JSON Schema**：描述 JSON 数据结构的规范，用于定义参数和返回值
- **语义化版本（Semantic Versioning）**：格式为 MAJOR.MINOR.PATCH 的版本号系统
- **MCP（Model Context Protocol）**：模型上下文协议，定义模型与工具交互的标准
- **JSON-RPC**：基于 JSON 的远程过程调用协议
- **工具（Tool）**：在 MCP 中，能被大语言模型调用的功能单元

### B. 示例代码库

#### Echo 能力示例（Python）

```python
from __runner__ import tool, Context

@tool(version="*")
async def echo(context: Context, params: any):
    """简单的回声能力，将输入原样返回"""
    input_text = params["input"]

    # 记录日志
    context.log.info(f"Echo received: {input_text}")

    # 返回结果
    return {
        "output": input_text
    }
```

#### 天气查询能力示例（Python）

```python
from __runner__ import tool, Context
import aiohttp
import json

@tool(version="^1.0.0")
async def weather(context: Context, params: any):
    """查询指定城市的天气信息"""
    city = params["city"]

    # 获取API密钥（从模块配置中）
    api_key = context.config.get("weather_api_key")
    if not api_key:
        raise ValueError("未配置天气API密钥")

    # 调用外部API
    async with aiohttp.ClientSession() as session:
        url = f"https://api.example.com/weather?city={city}&key={api_key}"
        async with session.get(url) as response:
            if response.status != 200:
                raise ValueError(f"天气API返回错误: {response.status}")

            data = await response.json()

    # 处理和转换数据
    result = {
        "city": city,
        "temperature": data["main"]["temp"],
        "humidity": data["main"]["humidity"],
        "description": data["weather"][0]["description"],
        "updated_at": data["dt"]
    }

    return result
```

#### 计算器能力示例（Lua）

> 待确认

### C. 配置文件模板

#### tools.json 模板

```json
{
  "$schema": "../../../schema/tools.schema.json",
  "tools": [
    {
      "name": "tool_name",
      "description": "能力的详细描述",
      "params": {
        "type": "object",
        "description": "输入参数说明",
        "properties": {
          "param1": {
            "type": "string",
            "description": "参数1的说明"
          },
          "param2": {
            "type": "number",
            "description": "参数2的说明"
          }
        },
        "required": ["param1"]
      },
      "result": {
        "type": "object",
        "description": "返回结果说明",
        "properties": {
          "output": {
            "type": "string",
            "description": "输出结果说明"
          }
        },
        "required": ["output"]
      }
    }
  ]
}
```

#### modules.json 模板

```json
{
  "$schema": "./schema/modules.schema.json",
  "modules": [
    {
      "name": "module-name",
      "base": "base-module",
      "description": "模块用途描述",
      "version": "1.0.0",
      "config": {
        "api_key": "your-api-key",
        "base_url": "https://api.example.com/"
      }
    }
  ]
}
```
