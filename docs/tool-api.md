# Tool Service API 文档

接口前缀 /api/tool

## 目录

- [POST /reload](#post-reload)
- [POST /script](#post-script)
- [POST /module](#post-module)
- [DELETE /module/{name}](#delete-modulename)
- [GET /modules](#get-modules)
- [POST /modules](#post-modules)

---

## POST /reload

**功能**：重新加载所有脚本模块。通常用于配置或脚本变更后，强制刷新内存中的模块列表。

**请求参数**：无

**响应示例**：

```json
{
  "script_errors": [ ... ],
  "module_errors": [ ... ],
  "modules": [ ... ]
}
```

**字段说明**：

- `script_errors`：数组，加载脚本时遇到的错误信息。
- `module_errors`：数组，加载模块配置时遇到的错误信息。
- `modules`：数组，当前已加载的所有模块对象，结构见下文 [模块对象结构](#模块对象结构)。

---

## POST /script

**功能**：上传并更新脚本模块（zip 文件），自动解压并重新加载。

**请求参数**：

- `multipart/form-data`，需包含一个 zip 文件字段，支持的 Content-Type:
  - `application/zip`
  - `application/octet-stream`
  - `application/x-zip-compressed`
  - `multipart/x-zip`
  - 或文件名以 `.zip` 结尾

**响应示例**：同 [POST /reload](#post-reload)

**错误返回**：

- 非 zip 文件会返回 400 错误，`{ "error": "Not a zip file" }`
- 其他错误会返回 400 错误，`{ "error": "详细错误信息" }`

---

## POST /module

**功能**：新增或更新单个模块配置。

**请求参数**：

- `application/json`，内容为单个模块配置对象（见 [模块配置结构](#模块配置结构)）

**响应示例**：同 [POST /reload](#post-reload)

**错误返回**：

- 配置错误或写入失败会返回 500 错误，`{ "error": "详细错误信息" }`

---

## DELETE /module/{name}

**功能**：删除指定名称的模块。

**路径参数**：

- `name`：要删除的模块名称（字符串）

**响应示例**：同 [POST /reload](#post-reload)

**错误返回**：

- 删除失败会返回 500 错误，`{ "error": "详细错误信息" }`

---

## GET /modules

**功能**：获取当前所有已加载的模块列表。

**请求参数**：无

**响应示例**：

```json
[
  {
    "name": "example",
    "version": "1.0.0",
    ...
  }
]
```

**字段说明**：返回为模块对象数组，结构见 [模块对象结构](#模块对象结构)。

---

## POST /modules

**功能**：批量新增或更新模块配置。

**请求参数**：

- `application/json`，内容为模块配置对象数组（见 [模块配置结构](#模块配置结构)）

**响应示例**：同 [POST /reload](#post-reload)

**错误返回**：

- 配置错误或写入失败会返回 500 错误，`{ "error": "详细错误信息" }`

---

## 数据结构详细说明

### 模块配置对象

```json
{
  "name": "string",                // 模块名称，唯一标识
  "base": "string",                // 模块基础类型（如脚本、内置等）
  "alias": ["string", ...],        // 模块别名列表，可选
  "description": "string",         // 模块描述，可选
  "version": "string",             // 模块版本号，可选
  "config": { ... }                 // 额外自定义配置，任意 JSON，可选
}
```
- `name`：模块名称，唯一标识。
- `base`：模块基础类型（如脚本类型、内置类型等，具体取决于实现）。
- `alias`：模块别名列表，可选。
- `description`：模块描述，可选。
- `version`：模块版本号，可选。
- `config`：额外的自定义配置，结构为任意 JSON，可选。

---

### 模块对象

```json
{
  "name": "string",                 // 模块名称，唯一标识
  "description": "string",          // 模块描述
  "config": { ... },                 // 该模块的完整配置，任意 JSON
  "tools": [ Tool, ... ]             // 该模块下所有工具的列表
}
```
- `name`：模块名称，唯一标识。
- `description`：模块描述。
- `config`：该模块的完整配置，结构为任意 JSON。
- `tools`：该模块下所有工具的列表，每个工具为 Tool 类型。

#### Tool 对象

```json
{
  "type": "script",                 // 工具类型: script/mcp/builtin
  "name": "string",                 // 工具名称
  "module_name": "string",          // 所属模块名
  "description": "string",          // 工具描述
  "params_schema": { ... },          // 工具参数 JSON Schema
  "result_schema": { ... },          // 工具返回值 JSON Schema
  "features": {
    "verbose_summary": true,         // 是否详细摘要
    "skip_summary": false,           // 是否跳过摘要
    "ignore_converter_cache": false  // 是否忽略转换器缓存
  }
}
```
- `type`：工具类型，可能值有 "script"、"mcp"、"builtin"。
- `name`：工具名称。
- `module_name`：所属模块名称。
- `description`：工具描述。
- `params_schema`：工具参数的 JSON Schema。
- `result_schema`：工具返回值的 JSON Schema。
- `features`：工具特性配置，常见字段有：
  - `verbose_summary`：是否详细摘要。
  - `skip_summary`：是否跳过摘要。
  - `ignore_converter_cache`：是否忽略转换器缓存。

---

如需更详细的 Tool 变体结构（如 ScriptTool、McpTool、BuiltinTool）字段说明，请告知，我可以进一步补充。

---

## 错误处理

所有接口在发生错误时，均返回如下结构：

```json
{
  "error": "错误描述"
}
```

- 400：请求参数错误、格式不符、超时等
- 500：内部处理错误、IO 错误等

---

## 线程安全与并发

所有涉及配置变更的接口（如 `/reload`、`/script`、`/module`、`/modules`、`/module/{name}`）均有全局锁保护，单次操作超时时间为 10 秒，超时会返回 `Config lock timeout` 错误。

---

## 备注

- 上传脚本时，系统会自动将 zip 文件解压到模块目录，并自动 reload。
- 所有变更操作（增、删、改）均会自动触发 reload，确保内存与磁盘配置一致。
