// 工作流主类型定义
/**
 * 工作流对象，描述整个流程的结构。
 * 包含节点、边、配置等完整的工作流信息。
 * @example
 * {
 *   "id": "uuid",
 *   "name": "示例工作流",
 *   "start_id": "node1",
 *   "edges": [...],
 *   "nodes": [...]
 * }
 */
export interface Workflow {
  /** 工作流唯一标识（UUID字符串） */
  id: string;
  /** 工作流名称，可选 */
  name?: string;
  /** 起始节点ID，可选 */
  start_id?: string;
  /** 边的数组，描述节点之间的连接关系 */
  edges: Edge[];
  /** 节点的数组，描述流程中的所有节点 */
  nodes: Node[];
  /** 额外元数据，任意JSON对象，可选 */
  metadata?: any;
  /** 工作流配置，可选 */
  config?: WorkflowConfig;
  /** 可视化信息，任意JSON对象，可选 */
  diagram?: any;
}

/**
 * 工作流配置，预留扩展，目前为空。
 */
export interface WorkflowConfig {
  // 预留扩展
}

/**
 * 边对象，描述节点之间的连接。
 * 可以包含条件表达式来控制流程的走向。
 * @example
 * {
 *   "source_node": "node1",
 *   "target_node": "node2",
 *   "config": {
 *     "expression": { "type": "literal", "content": true }
 *   }
 * }
 */
export interface Edge {
  /** 源节点ID */
  source_node: string;
  /** 源端口ID，可选 */
  source_port?: string;
  /** 目标节点ID */
  target_node: string;
  /** 目标端口ID，可选 */
  target_port?: string;
  /** 元数据，任意JSON对象，可选 */
  metadata?: any;
  /** 边的配置，可选 */
  config?: EdgeConfig;
  /** 可视化信息，任意JSON对象，可选 */
  diagram?: any;
}

/**
 * 边的配置。
 */
export interface EdgeConfig {
  /** 条件表达式，可选 */
  expression?: Expr;
}

/**
 * 节点对象，描述流程中的一个节点。
 * 包含模块、工具、端口等节点信息。
 * @example
 * {
 *   "id": "node1",
 *   "module": "modA",
 *   "tool": "toolX",
 *   "ports": [
 *     { "id": "out", "type": "output" }
 *   ]
 * }
 */
export interface Node {
  /** 节点唯一标识 */
  id: string;
  /** 所属模块 */
  module: string;
  /** 工具名 */
  tool: string;
  /** 端口数组，描述节点的输入输出，可选 */
  ports?: NodePort[];
  /** 元数据，任意JSON对象，可选 */
  metadata?: any;
  /** 节点配置，可选 */
  config?: NodeConfig;
  /** 可视化信息，任意JSON对象，可选 */
  diagram?: any;
}

/**
 * 节点端口类型。
 */
export enum NodePortType {
  /** 输入端口 */
  Input = "input",
  /** 输出端口 */
  Output = "output"
}

/**
 * 节点端口，描述节点的输入或输出。
 */
export interface NodePort {
  /** 端口ID */
  id: string;
  /** 端口类型 */
  type: NodePortType;
  /** 元数据，任意JSON对象，可选 */
  metadata?: any;
  /** 端口配置，可选 */
  config?: NodePortConfig;
  /** 可视化信息，任意JSON对象，可选 */
  diagram?: any;
}

/**
 * 节点端口配置。
 */
export interface NodePortConfig {
  /** 标题，可选 */
  title?: string;
  /** 描述，可选 */
  description?: string;
  /** 表达式，可选 */
  expression?: Expr;
}

/**
 * 节点配置。
 */
export interface NodeConfig {
  /** 标题，可选 */
  title?: string;
  /** 描述，可选 */
  description?: string;
  /** 属性数组，可选 */
  properties?: NodeProperty[];
}

/**
 * 节点属性。
 */
export interface NodeProperty {
  /** 属性路径 */
  key_path: NodePropertyKeyPath;
  /** 属性值 */
  value: NodePropertyValue;
}

/**
 * 节点属性路径。
 * 用于定位节点属性在 JSON 对象中的位置。
 * 可以是字符串（对象键）或数字（数组索引）。
 */
export type NodePropertyKeyPath = (string | number)[];

/**
 * 节点属性映射路径。
 * 用于描述属性值映射到其他节点的路径。
 */
export interface NodePropertyPath {
  /** 节点ID */
  node_id: string;
  /** 属性路径（字符串或数字数组） */
  property_path: NodePropertyKeyPath;
}

/**
 * 节点属性值，可以是字面量或映射。
 */
export type NodePropertyValue =
  /** 字面量值 */
  | { type: 'literal'; value: any }
  /** 映射到其他节点属性 */
  | { type: 'mapping'; path: NodePropertyPath };

/**
 * 表达式类型定义（与 expression::Expr 兼容）。
 * 用于条件判断、端口表达式等。
 * @example
 * {
 *   "type": "compare",
 *   "content": {
 *     "left": { "type": "variable", "content": { "name": "a", "indices": [] } },
 *     "op": "==",
 *     "right": { "type": "literal", "content": true }
 *   }
 * }
 */
export type Expr =
  /** 变量表达式 */
  | { type: 'variable'; content: Variable }
  /** 字面量表达式 */
  | { type: 'literal'; content: any }
  /** 比较表达式 */
  | { type: 'compare'; content: { left: Expr; op: CompOp; right: Expr } }
  /** 逻辑表达式 */
  | { type: 'logical'; content: { left: Expr; op: LogicalOp; right: Expr } };

/**
 * 变量结构，支持多级索引。
 */
export interface Variable {
  /** 变量名 */
  name: string;
  /** 索引数组（如 arr[0]、obj.key） */
  indices: Index[];
}

/**
 * 索引类型，可以为数字或字符串。
 */
export type Index = number | string;

/**
 * 比较运算符。
 * '==' | '!=' | '<' | '>' | '<=' | '>='
 */
export type CompOp = '==' | '!=' | '<' | '>' | '<=' | '>=';

/**
 * 逻辑运算符。
 * 'and' | 'or'
 */
export type LogicalOp = 'and' | 'or';
