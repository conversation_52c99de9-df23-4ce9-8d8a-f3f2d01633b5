# Workflow API 文档

本文档详细介绍 `crates/workflow` 内的主要类型结构及其字段说明，适用于开发者理解和对接工作流相关数据结构。

---

## 1. Workflow

工作流是整个流程的核心对象，它描述了完整的流程结构，包括节点、边、配置等信息。

```jsonc
{
  "id": "uuid", // 工作流唯一标识
  "name": "string?", // 工作流名称
  "start_id": "string?", // 起始节点ID
  "edges": [/* Edge */], // 边集合
  "nodes": [/* Node */], // 节点集合
  "metadata": {}, // 额外元数据，可选
  "config": {/* WorkflowConfig */}, // 工作流配置，可选
  "diagram": {} // 可视化信息，可选
}
```

### 字段说明
- `id`：工作流唯一标识（UUID字符串）。
- `name`：可选，工作流名称。
- `start_id`：可选，起始节点ID。
- `edges`：边的数组，见 [Edge](#2-edge)。
- `nodes`：节点的数组，见 [Node](#4-node)。
- `metadata`：可选，任意JSON对象。
- `config`：可选，工作流配置，见 [WorkflowConfig](#3-workflowconfig)。
- `diagram`：可选，前端可视化信息。

### 使用示例
```jsonc
{
  "id": "b7e6c7e2-1c2a-4e2a-8e2a-123456789abc",
  "name": "数据处理工作流",
  "start_id": "start_node",
  "edges": [
    {
      "source_node": "start_node",
      "target_node": "process_node",
      "config": {
        "expression": {
          "type": "literal",
          "content": true
        }
      }
    }
  ],
  "nodes": [
    {
      "id": "start_node",
      "module": "data",
      "tool": "source",
      "ports": [
        {
          "id": "output",
          "type": "output",
          "config": {
            "title": "数据输出",
            "description": "输出处理后的数据"
          }
        }
      ]
    }
  ]
}
```

---

## 2. Edge

边对象描述节点之间的连接关系，可以包含条件表达式来控制流程的走向。

```jsonc
{
  "source_node": "string", // 源节点ID
  "source_port": "string?", // 源端口ID，可选
  "target_node": "string", // 目标节点ID
  "target_port": "string?", // 目标端口ID，可选
  "metadata": {}, // 元数据，可选
  "config": {/* EdgeConfig */}, // 边配置，可选
  "diagram": {} // 可视化信息，可选
}
```

### 字段说明
- `source_node`：源节点ID。
- `source_port`：可选，源端口ID。
- `target_node`：目标节点ID。
- `target_port`：可选，目标端口ID。
- `metadata`：可选，任意JSON对象。
- `config`：可选，边的配置，见 [EdgeConfig](#21-edgeconfig)。
- `diagram`：可选，前端可视化信息。

### 2.1 EdgeConfig
```jsonc
{
  "expression": {/* Expr */} // 条件表达式，可选
}
```
- `expression`：可选，条件表达式，见 [表达式语法](#5-表达式语法)。

---

## 3. WorkflowConfig

```jsonc
{
  // 预留扩展，目前为空
}
```
- 预留扩展，目前为空。

---

## 4. Node

节点对象描述流程中的一个节点，包含模块、工具、端口等信息。

```jsonc
{
  "id": "string", // 节点ID
  "module": "string", // 所属模块
  "tool": "string", // 工具名
  "ports": [/* NodePort */], // 端口数组，可选
  "metadata": {}, // 元数据，可选
  "config": {/* NodeConfig */}, // 节点配置，可选
  "diagram": {} // 可视化信息，可选
}
```

### 字段说明
- `id`：节点唯一标识。
- `module`：所属模块。
- `tool`：工具名。
- `ports`：可选，端口数组，见 [NodePort](#41-nodeport)。
- `metadata`：可选，任意JSON对象。
- `config`：可选，节点配置，见 [NodeConfig](#43-nodeconfig)。
- `diagram`：可选，前端可视化信息。

#### 4.1 NodePort
```jsonc
{
  "id": "string", // 端口ID
  "type": "input" | "output", // 端口类型
  "metadata": {}, // 元数据，可选
  "config": {/* NodePortConfig */}, // 端口配置，可选
  "diagram": {} // 可视化信息，可选
}
```
- `id`：端口ID。
- `type`：端口类型，可选值为 "input" 或 "output"。
- `metadata`：可选，任意JSON对象。
- `config`：可选，端口配置，见 [NodePortConfig](#42-nodeportconfig)。
- `diagram`：可选，前端可视化信息。

#### 4.2 NodePortConfig
```jsonc
{
  "title": "string?", // 标题
  "description": "string?", // 描述
  "expression": {/* Expr */} // 表达式，可选
}
```
- `title`：可选，标题。
- `description`：可选，描述。
- `expression`：可选，表达式，见 [表达式语法](#5-表达式语法)。

#### 4.3 NodeConfig
```jsonc
{
  "title": "string?", // 标题
  "description": "string?", // 描述
  "properties": [/* NodeProperty */] // 属性，可选
}
```
- `title`：可选，标题。
- `description`：可选，描述。
- `properties`：可选，属性数组，见 [NodeProperty](#44-nodeproperty)。

#### 4.4 NodeProperty
```jsonc
{
  "key_path": [/* string | number */], // 属性路径
  "value": {/* NodePropertyValue */} // 属性值
}
```
- `key_path`：属性路径，用于定位节点属性在 JSON 对象中的位置，可以是字符串（对象键）或数字（数组索引）。
- `value`：属性值，见 [NodePropertyValue](#45-nodepropertyvalue)。

#### 4.5 NodePropertyValue
```jsonc
// 字面量
{
  "type": "literal",
  "value": any
}
// 或映射
{
  "type": "mapping",
  "path": {/* NodePropertyPath */}
}
```
- `literal`：字面量值。
- `mapping`：引用其他节点属性。

#### 4.6 NodePropertyPath
```jsonc
{
  "node_id": "string", // 节点ID
  "property_path": [/* string | number */] // 属性路径
}
```
- `node_id`：节点ID。
- `property_path`：属性路径，用于定位节点属性在 JSON 对象中的位置，可以是字符串（对象键）或数字（数组索引）。

---

## 5. 表达式语法（Expr）

表达式用于条件判断、端口表达式等，支持变量、字面量、比较和逻辑运算。

### 5.1 变量
- 语法：`${变量名}` 或 `${对象.属性}` 或 `${数组[0]}`
- 示例：
  ```jsonc
  {
    "type": "variable",
    "content": {
      "name": "data",
      "indices": ["result", "status"]
    }
  }
  ```

### 5.2 字面量
- 支持：布尔（true/false）、数字、字符串、null
- 示例：
  ```jsonc
  {
    "type": "literal",
    "content": "success"
  }
  ```

### 5.3 比较运算符
- `==`, `!=`, `<`, `>`, `<=`, `>=`
- 示例：
  ```jsonc
  {
    "type": "compare",
    "content": {
      "left": {
        "type": "variable",
        "content": {
          "name": "status",
          "indices": []
        }
      },
      "op": "==",
      "right": {
        "type": "literal",
        "content": "success"
      }
    }
  }
  ```

### 5.4 逻辑运算符
- `and`, `or`
- 示例：
  ```jsonc
  {
    "type": "logical",
    "content": {
      "left": {
        "type": "compare",
        "content": {
          "left": {
            "type": "variable",
            "content": {
              "name": "status",
              "indices": []
            }
          },
          "op": "==",
          "right": {
            "type": "literal",
            "content": "success"
          }
        }
      },
      "op": "and",
      "right": {
        "type": "compare",
        "content": {
          "left": {
            "type": "variable",
            "content": {
              "name": "code",
              "indices": []
            }
          },
          "op": "==",
          "right": {
            "type": "literal",
            "content": 200
          }
        }
      }
    }
  }
  ```

---

## 6. 示例 JSON

```jsonc
{
  "id": "b7e6c7e2-1c2a-4e2a-8e2a-123456789abc",
  "name": "示例工作流",
  "start_id": "node1",
  "edges": [
    {
      "source_node": "node1",
      "source_port": "out",
      "target_node": "node2",
      "target_port": "in",
      "config": {
        "expression": {"type": "literal", "content": true}
      }
    }
  ],
  "nodes": [
    {
      "id": "node1",
      "module": "modA",
      "tool": "toolX",
      "ports": [
        {"id": "out", "type": "output"}
      ]
    },
    {
      "id": "node2",
      "module": "modB",
      "tool": "toolY",
      "ports": [
        {"id": "in", "type": "input"}
      ]
    }
  ]
}
```

---

## 7. 其他说明
- 所有 `metadata`、`diagram` 字段均为可选，类型为任意 JSON。
- 所有 `config` 字段均为可选，结构见对应类型。
- 所有 ID 字段均为字符串。

---

## 8. API 接口说明

### 8.1 工作流管理接口

`base_url` 为 `/api`。

1. **获取工作流列表**
- **方法**：GET
- **路径**：`/workflow`
- **请求体**：无
- **响应体**：`Workflow[]` 数组

2. **创建工作流**
- **方法**：POST 
- **路径**：`/workflow`
- **请求体**：`Workflow` 对象
- **响应体**：创建的 `Workflow` 对象

3. **获取指定工作流**
- **方法**：GET
- **路径**：`/workflow/{id}`
- **参数**：
  - `id`: UUID 格式的工作流ID
- **响应体**：`Workflow` 对象

4. **更新指定工作流**
- **方法**：PUT
- **路径**：`/workflow/{id}`
- **参数**：
  - `id`: UUID 格式的工作流ID
- **请求体**：`Workflow` 对象
- **响应体**：更新后的 `Workflow` 对象

5. **删除指定工作流**
- **方法**：DELETE
- **路径**：`/workflow/{id}`
- **参数**：
  - `id`: UUID 格式的工作流ID
- **响应体**：空的 `Workflow` 对象

### 8.2 会话工作流接口

1. **获取当前会话工作流**
- **方法**：GET
- **路径**：`/conversation/{id}/workflow/current`
- **参数**：
  - `id`: 会话ID
- **响应体**：当前会话的 `Workflow` 对象

2. **设置当前会话工作流**
- **方法**：POST
- **路径**：`/conversation/{id}/workflow/current`
- **参数**：
  - `id`: 会话ID
- **请求体**：`Workflow` 对象
- **响应体**：设置后的 `Workflow` 对象

### 示例请求

1. 创建工作流：
```bash
POST /workflow
Content-Type: application/json

{
  "id": "b7e6c7e2-1c2a-4e2a-8e2a-123456789abc",
  "name": "示例工作流",
  "start_id": "start",
  "nodes": [...],
  "edges": [...]
}
```

2. 获取特定工作流：
```bash
GET /workflow/b7e6c7e2-1c2a-4e2a-8e2a-123456789abc
```

3. 设置当前会话工作流：
```bash
POST /conversation/conversation-123/workflow/current
Content-Type: application/json

{
  "id": "b7e6c7e2-1c2a-4e2a-8e2a-123456789abc",
  "name": "当前会话工作流",
  ...
}
```

### 注意事项

1. 所有请求和响应的 Content-Type 都应该是 `application/json`
2. 工作流 ID 使用 UUID v7 格式
3. 接口返回的错误处理尚未在代码中明确定义，建议实现时添加适当的错误处理
4. 会话相关的工作流接口需要确保会话ID的有效性 