{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"$schema": {"type": "string"}, "modules": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "模块的名称"}, "base": {"type": "string", "description": "模块的基础名称，如果未指定则使用模块名"}, "description": {"type": "string", "description": "模块的描述"}, "version": {"type": "string", "description": "模块的版本号"}, "config": {"type": "object", "description": "模块的配置选项", "additionalProperties": true}}, "required": ["name", "description", "version", "base"], "additionalProperties": false}}}, "required": ["modules"]}