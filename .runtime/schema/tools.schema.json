{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {"tool": {"type": "object", "properties": {"name": {"type": "string"}, "alias": {"type": "array", "items": {"type": "string"}}, "description": {"type": "string"}, "params": {"$ref": "http://json-schema.org/draft-07/schema#"}, "result": {"$ref": "http://json-schema.org/draft-07/schema#"}}, "required": ["name", "description", "params", "result"]}}, "properties": {"tools": {"type": "array", "items": {"$ref": "#/definitions/tool"}}}, "required": ["tools"]}