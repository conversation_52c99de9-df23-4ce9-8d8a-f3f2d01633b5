# ToolHub - Tools Library

这是一个包含各种 AI 工具能力的仓库，用于提供给 ToolHub 系统调用。此仓库内的工具采用 Python 实现，通过 `@tool` 装饰器定义，可被主系统动态加载并执行。

## 快速开始

1. 安装 Python 环境，仅支持 3.12 及以上版本；
2. 创建能力文件夹：`demo/v1`
3. 添加能力脚本：`demo/v1/greet.py`
4. 添加能力接口说明：`demo/v1/tools.json`
5. 本地测试通过：`test_echo.py`
6. 提交 `gitlab` 代码，触发 `pipeline` 流水线会自动更新脚本，并完成脚本热重载
7. 联系大语言模型组 @fanshenghang，添加测试用的工作流和思维链
8. 在 CUI 集成环境中测试工作流

## 仓库结构

当前仓库包含以下主要内容：

- **支持文件**

  - **`__runner__.py`**: 实现了 Context 类和 tool 装饰器的核心功能。
  - **`__tester__.py`**: 提供测试辅助函数，用于加载模块和执行能力。

- **能力模块**
  - **demo**: 基础示例能力
    - **1.0.0**: 版本 1.0.0 的能力组
    - **v2**: 版本 2 的能力组
  - **pid**: 设备自控率相关能力
    - **v1**: 版本 1 的能力组

## 能力使用说明

### 版本管理说明

- 能力（tool）在代码中通过 `@tool(version="*")` 装饰器声明自己的**版本兼容范围**，这个声明采用语义化版本比较器（semver）格式。
  - 例如，`"*"` 表示兼容任何版本
  - `"^2.0.0"` 表示兼容 2.x.x 的所有版本
  - `"~1.2.0"` 表示兼容 1.2.x 的所有版本

### 如何定义一个能力

**示例：定义问候能力**

1. **编写能力脚本文件**  
   参考示例文件 `demo/v2/greet.py`，内容如下：

   ```python
   from __runner__ import tool, Context

   @tool(version="*")
   async def greet(context: Context, params: any):
       return {
           "output": "hello, " + params["name"]
       }
   ```

2. **更新能力配置**  
   参考示例文件 `demo/v2/tools.json`，其中关于问候能力的配置如下：

   ```json
   {
     "$schema": "../../../schema/tools.schema.json",
     "tools": [
       {
         "name": "greet",
         "description": "问候能力",
         "params": {
           "type": "object",
           "description": "问候能力的输入参数",
           "properties": {
             "name": {
               "type": "string",
               "description": "接收问候的人员姓名，将在问候消息中使用"
             }
           },
           "required": ["name"]
         },
         "result": {
           "type": "object",
           "description": "问候能力的输出结果",
           "properties": {
             "output": {
               "type": "string",
               "description": "包含针对指定人员的问候消息"
             }
           },
           "required": ["output"]
         }
       }
     ]
   }
   ```

## 测试工具使用方法

### 测试工具

- **`__runner__.py`**: 实现了 Context 类和 tool 装饰器的核心功能。
- **`__tester__.py`**: 提供测试辅助函数：
  - `load_module(module_path, module_name=None)`: 加载指定路径的能力模块
  - `run_async(func, context=None, params=None)`: 执行异步能力函数
  - `run(func, context=None, params=None)`: 执行同步能力函数

### 编写测试脚本

可以创建以 `test_` 为前缀的测试脚本，如 `test_echo.py`。

**示例: 测试问候能力**

```python
from __tester__ import load_module, run_async
from __runner__ import Context

# 加载要测试的能力模块
greet_module = load_module("demo/1.0.0/greet.py")

# 准备测试数据
context = Context()
context.config = {"key": "value"}
params = {"name": "ronbb"}
expected_result = {"output": "hello, ronbb"}

# 执行能力函数
result = run_async(greet_module.greet, context, params)
assert result == expected_result

# 测试不传context参数的情况
params = {"name": "world"}
expected_result = {"output": "hello, world"}
result = run_async(greet_module.greet, params=params)
assert result == expected_result

print("测试通过!")
```

### 运行测试

使用 Python 解释器运行测试脚本：

```bash
python test_echo.py
```

## 配置文件与 JSON Schema

每个能力组目录下的 `tools.json` 文件定义了能力的名称、描述、参数和返回值。推荐使用支持 JSON Schema 校验的编辑器（如 VSCode、JetBrains IDE）来编辑这些配置文件，以获得自动补全和校验功能。

---

## 注意事项

- 每个能力组（tool_group）的版本由文件夹路径决定，例如 `demo/1.0.0` 与 `demo/v2`，组内所有能力共享该版本。
- 使用 `@tool(version="*")` 装饰器明确定义能力的版本兼容范围。
- 确保能力的参数和返回值符合 `tools.json` 中的定义。
- 写好测试用例，确保能力在集成到系统前已经过充分验证。
