import json.tool
from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# APEX_inner_token = os.getenv('APEX_inner_token')  # 读取env文件中的key值为Inner_token的值
APEX_URL = os.getenv("APEX_URL")  # 读取网址信息
IP_FLAG = os.getenv("IPFLAG")  #... 暂时在env中写死，后续需要从queryProject中获取并存放到字典中

# dict_project_to_ipFlag = {}

# 主要实现的功能是 APEX的工程打开和关闭、元变量的查询和更新、运行
# add by yingzb 2025-04-18
@tool(version="*")
async def query_project(context: Context, params: any):
    userName = params['userName']  # 输入用户名
    # print('check params:')
    # print(userName)
    params = {'userName': userName}
    response = requests.post(url=APEX_URL + '/handle/project/queryProject'
                            , data=json.dumps(params), headers={
            'Content-Type': 'application/json',
            # 'Authorization': APEX_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj

@tool(version="*")
async def refresh(context: Context, params: any):
    userName = params['userName']  # 输入用户名
    password = params['password']  # 输入密码
    # print('check params:')
    # print(userName)
    # print(password)
    params = {'userName': userName, 'password': password}
    response = requests.post(url=APEX_URL + '/handle/project/refresh'
                            , data=json.dumps(params), headers={
            'Content-Type': 'application/json',
            # 'Authorization': APEX_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj

@tool(version="*")
async def init_project(context: Context, params: any):
    userName = params['userName']  # 输入用户名
    projectName = params['projectName']  # 输入工程名
    # print('check params:')
    # print(userName)
    # print(projectName)
    params = {'userName': userName, 'projectName': projectName}
    response = requests.post(url=APEX_URL + '/handle/project/initProject'
                            , data=json.dumps(params), headers={
            'Content-Type': 'application/json',
            # 'Authorization': APEX_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj

@tool(version="*")
async def get_var(context: Context, params: any):
    userName = params['userName']  # 输入用户名
    projectName = params['projectName']  # 输入工程名
    varNames = params['varName']  # 输入用户名
    # print('check params:')
    # print(userName)
    # print(projectName)
    conditions = []
    for varName in varNames:
        conditions.append({'logic':'or', 'op':'=', 'name':'strName', 'input':varName})
    params = {'userName': userName, 'projectName': projectName, 'ipFlag':IP_FLAG, 'conditions': conditions}
    response = requests.post(url=APEX_URL + '/handle/project/getVar'
                            , data=json.dumps(params), headers={
            'Content-Type': 'application/json',
            #'Authorization': APEX_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj

@tool(version="*")
async def run_project(context: Context, params: any):
    userName = params['userName']  # 输入用户名
    projectName = params['projectName']  # 输入工程名
    flag = params['flag'] # 输入运行模式
    # print('check params:')
    # print(userName)
    # print(projectName)
    params = {'userName': userName, 'projectName': projectName, 'flag': flag}
    response = requests.post(url=APEX_URL + '/handle/project/runProject'
                            , data=json.dumps(params), headers={
            'Content-Type': 'application/json',
            # 'Authorization': APEX_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj

@tool(version="*")
async def update_var(context: Context, params: any):
    userName = params['userName']  # 输入用户名
    projectName = params['projectName']  # 输入工程名
    varNames = params['varName'] # 输入元变量名
    varValues = params['varValue'] # 输入元变量值
    varlist = []
    while varNames and varValues:
        varName = varNames.pop()
        varValue = varValues.pop()
        varlist.append({'strAsName':varName, 'dbIterationValue':varValue})
    # print('check params:')
    # print(userName)
    # print(projectName)
    # print(varlist)
    params = {'userName': userName, 'projectName': projectName, 'ipFlag':IP_FLAG, 'eoUpdateBeanList': varlist}
    response = requests.put(url=APEX_URL + '/handle/chatProject/updateVar'
                            , data=json.dumps(params), headers={
            'Content-Type': 'application/json',
            # 'Authorization': APEX_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj

@tool(version="*")
async def close_project(context: Context, params: any):
    userName = params['userName']  # 输入用户名
    projectName = params['projectName']  # 输入工程名
    # print('check params:')
    # print(userName)
    # print(projectName)
    params = {'userName': userName, 'projectName': projectName}
    response = requests.post(url=APEX_URL + '/handle/project/closeProject'
                            , data=json.dumps(params), headers={
            'Content-Type': 'application/json',
            # 'Authorization': APEX_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj
