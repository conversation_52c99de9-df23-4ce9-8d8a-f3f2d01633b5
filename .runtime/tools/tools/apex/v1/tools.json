{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "query_project", "description": "罗列当前机理模型的工程", "params": {"type": "object", "description": "罗列当前机理模型的工程", "properties": {"userName": {"type": "string", "description": "用户名"}}, "required": ["userName"]}, "result": {"type": "object", "description": "罗列当前机理模型的工程", "properties": {"message": {"type": "string", "description": "结果数据"}, "list": {"type": "array", "description": "工程名列表"}}, "required": ["message", "list"]}, "alias": [""], "catalog": "simulation"}, {"name": "refresh", "description": "验证并刷新用户登录许可", "params": {"type": "object", "description": "验证并刷新用户登录许可", "properties": {"userName": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}}, "required": ["userName", "password"]}, "result": {"type": "object", "description": "返回是否验证通过", "properties": {"message": {"type": "string", "description": "结果数据"}}, "required": ["message"]}, "alias": [""], "catalog": "simulation"}, {"name": "init_project", "description": "初始化工程", "params": {"type": "object", "description": "根据工程名和用户名，加载对应的工程到缓存中", "properties": {"projectName": {"type": "string", "description": "工程名"}, "userName": {"type": "string", "description": "用户名"}}, "required": ["projectName", "userName"]}, "result": {"type": "object", "description": "返回是否初始化成功", "properties": {"message": {"type": "string", "description": "结果数据"}}, "required": ["message"]}, "alias": [""], "catalog": "simulation"}, {"name": "get_var", "description": "用户可以根据元变量名称获取变量的值和属性", "params": {"type": "object", "description": "用户可以根据元变量名称获取变量的值和属性", "properties": {"varName": {"type": "array", "description": "元变量名列表"}}, "required": ["varName"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"message": {"type": "string", "description": "结果数据"}, "list": {"type": "object", "description": "结果数据输出"}}, "required": ["message", "list"]}, "alias": [""], "catalog": "simulation"}, {"name": "run_project", "description": "按照不同的运行模式来运行工程", "params": {"type": "object", "description": "按照不同的运行模式来运行工程", "properties": {"flag": {"type": "integer", "description": "运行模式 1：模拟 2：参数矫正 3：数据调和 4：优化", "enum": [1, 2, 3, 4]}, "projectName": {"type": "string", "description": "工程名"}, "userName": {"type": "string", "description": "用户名"}}, "required": ["flag", "projectName", "userName"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"message": {"type": "string", "description": "结果数据"}}, "required": ["message"]}, "alias": [""], "catalog": "simulation"}, {"name": "update_var", "description": "更新元变量值和属性", "params": {"type": "object", "description": "更新元变量值和属性", "properties": {"varName": {"type": "array", "description": "元变量名的数组"}, "varValue": {"type": "array", "description": "元变量值的数组"}}, "required": ["varName", "varValue"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"message": {"type": "string", "description": "结果数据"}}, "required": ["message"]}, "alias": [""], "catalog": "simulation"}, {"name": "close_project", "description": "关闭工程", "params": {"type": "object", "description": "根据工程名和用户名，关闭工程并释放对应的缓存", "properties": {"projectName": {"type": "string", "description": "工程名"}, "userName": {"type": "string", "description": "用户名"}}, "required": ["projectName", "userName"]}, "result": {"type": "object", "description": "返回是否关闭成功", "properties": {"message": {"type": "string", "description": "结果数据"}}, "required": ["message"]}, "alias": [""], "catalog": "simulation"}]}