import io
import json
import urllib

from __runner__ import tool, Context
import requests
import base64
import os
import time
import redis



HEAT_API_HOST = "http://***********:31501"
# AUTONOMOUS_SPC_URL = "https://obp-dev.supcon5t.com/operate-optimization-web"  # os.getenv("AUTONOMOUS_OPT_URL")  # 读取网址信息
RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_select_feature_py&built_in=1&time_out=6000"
class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 3000  # 算法执行超时时间

    # changed
    SPC_SELECT_FEATURE_TOPIC = "single_python_train_spc_select_feature"  # 发送预测算法执行topic
    SPC_SELECT_FEATURE_TOPIC_RES = "single_python_train_spc_select_feature_res"  # 预测算法响应topic
    SPC_SELECT_FEATURE_ALG_NAME = "spc_select_feature_train"  # 与算法文件名对应
    SPC_SELECT_FEATURE_ALG_FULL_NAME = "spc_select_feature_train.zip"  # 与算法文件名对应
    TASK_NAME = "SPC:SELECT:FEATURE"

@tool(version="*")
async def select_feature(context: Context, params: any):
    clientId = context.session_id
    appId='123456'
    pv_target='HGLZM01.AIC2104.DACA.PV'
    mv_control='HGLZM01.AIC2104.PIDA.OP'
    time_interval=10
    mv_pv_correlation=1
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": appId
        },
        {
            "defaultValue": "",
            "name": "pv_target",
            "type": 1,
            "typeName": "str",  # list
            "userInput": 1,
            "value": pv_target  # json
        },
        {
            "defaultValue": "",
            "name": "mv_control",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": mv_control
        },
        {
            "defaultValue": "",
            "name": "time_interval",
            "type": 1,
            "typeName": "float",
            "userInput": 1,
            "value": str(time_interval)
        },
        {
            "defaultValue": "",
            "name": "mv_pv_correlation",
            "type": 1,
            "typeName": "float",
            "userInput": 1,
            "value": str(mv_pv_correlation)
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_SELECT_FEATURE_ALG_NAME,
            "sourcePath": Config.SPC_SELECT_FEATURE_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_SELECT_FEATURE_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_SELECT_FEATURE_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_SELECT_FEATURE_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break

    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    #print("算法执行响应结果:" + str(res))
    await context.log_info(f"res = {res}")
    
    await context.add_view(result['markdown_output'])
    await context.add_view({
            "format":"card",
            "content": {
                "type": "string",
                "title": "特征筛选结果",
                "details": result['markdown_output']
            }
        })

    return {'markdown_output':result['markdown_output'],
            'feature_num':result['feature_num'],
            'feature_num_check':result['feature_num_check']}

