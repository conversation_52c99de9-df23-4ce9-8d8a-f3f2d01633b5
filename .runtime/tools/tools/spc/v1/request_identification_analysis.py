import io
import json
import urllib

from __runner__ import tool, Context
import requests
import base64
import os
import redis
import time
from docx import Document
from redis import Redis
from redis.cluster import RedisCluster


def get_redis_client(host, port, is_cluster=False, **kwargs):
    if is_cluster:
        #集群模式:只磊一个节点即可自动发现全集群
        return RedisCluster(host=host, port=port, decode_responses=True, **kwargs)
    else:
        # 单鼠模式
        return Redis(host=host, port=port, decode_responses=True, **kwargs)



HEAT_API_HOST = "http://***********:31501"
# AUTONOMOUS_SPC_URL = "https://obp-dev.supcon5t.com/operate-optimization-web"  # os.getenv("AUTONOMOUS_OPT_URL")  # 读取网址信息
# RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_identification_analysis_py&built_in=1&time_out=6000"
# RUNTIME_OPT_EXEC_URL_PID = "http://***********:31668/call/app?name=spc_pid_identification_analysis_py&built_in=1&time_out=6000"


class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 3000  # 算法执行超时时间

    # changed
    SPC_IDENTIFICATION_TOPIC = "single_python_train_spc_identification"  # 发送预测算法执行topic
    SPC_IDENTIFICATION_TOPIC_RES = "single_python_train_spc_identification_res"  # 预测算法响应topic
    SPC_IDENTIFICATION_ALG_NAME = "spc_identification_analysis_train"  # 与算法文件名对应
    SPC_IDENTIFICATION_ALG_FULL_NAME = "spc_identification_analysis_train.py"  # 与算法文件名对应

    SPC_PID_IDENTIFICATION_TOPIC = "single_python_train_spc_pid_identification"  # 发送预测算法执行topic
    SPC_PID_IDENTIFICATION_TOPIC_RES = "single_python_train_spc_pid_identification_res"  # 预测算法响应topic
    SPC_PID_IDENTIFICATION_ALG_NAME = "spc_pid_identification_analysis_train"  # 与算法文件名对应
    SPC_PID_IDENTIFICATION_ALG_FULL_NAME = "spc_pid_identification_analysis_train.py"  # 与算法文件名对应
    TASK_IDENTIFICATION_NAME = "SPC:IDENTIFICATION"
    TASK_PID_IDENTIFICATION_NAME = "SPC:PID:IDENTIFICATION"

@tool(version="*")
async def identification_analysis(context: Context, params: any):
    clientId = context.session_id
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "time_interval",
            "type": 1,
            "typeName": "float",  # list
            "userInput": 1,
            "value": str(10)  # json
        },
        {
            "defaultValue": "",
            "name": "mv",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": 'HGLZM01.AIC2104.PIDA.OP'
        },
        {
            "defaultValue": "",
            "name": "pv",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": 'HGLZM01.AIC2104.DACA.PV'
        },
        {
            "defaultValue": "",
            "name": "mv_pv_correlation",
            "type": 1,
            "typeName": "float",
            "userInput": 1,
            "value": str(-1)
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_IDENTIFICATION_ALG_NAME,
            "sourcePath": Config.SPC_IDENTIFICATION_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_IDENTIFICATION_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_IDENTIFICATION_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_IDENTIFICATION_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            await context.log_info(f"{json_array}")
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_IDENTIFICATION_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_IDENTIFICATION_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break
    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    await context.log_info(f"res = {res}")

    await context.add_view("""
            | Key                 | Value            |
            |---------------------|------------------|
            | scene_judgment      | {scene_judgment} |
            """.format(
        scene_judgment=result['scene_judgment'],
    ))
    await context.add_view({
            "format":"card",
            "content": {
                "type": "string",
                "title": "场景判断结果",
                "details": result['markdown_table']
            }
        })

    
    return {'scene_judgment':result['scene_judgment'],
            'drift_data_check':result['drift_data_check'],
            'drift_feature_check':result['drift_feature_check']}


@tool(version="*")
async def PID_identification_analysis(context: Context, params: any):
    clientId = str(context.session_id)
    pv_target = params.get('PV')
    mv_control = params.get('MV')
    sv = params.get('SV')
    
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": '123456'
        },
        {
            "defaultValue": "",
            "name": "time_interval",
            "type": 1,
            "typeName": "float",  # list
            "userInput": 1,
            "value": str(10)  # json
        },
        {
            "defaultValue": "",
            "name": "mv",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": mv_control
        },
        {
            "defaultValue": "",
            "name": "pv",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": pv_target
        },
        {
            "defaultValue": "",
            "name": "sv",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": sv
        },
        {
            "defaultValue": "",
            "name": "mv_pv_correlation",
            "type": 1,
            "typeName": "float",
            "userInput": 1,
            "value": str(1)
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_PID_IDENTIFICATION_ALG_NAME,
            "sourcePath": Config.SPC_PID_IDENTIFICATION_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    # r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r = get_redis_client(context.config["redis_host"], context.config["redis_port"], is_cluster = context.config["redis_cluster"],redis_db=context.config["redis_db"])
    
    r.xadd(
        "runtime_python_stream:" + Config.SPC_PID_IDENTIFICATION_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_PID_IDENTIFICATION_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_PID_IDENTIFICATION_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            #json_array = json.loads(execute_res.decode('utf-8'))
            json_array = json.loads(execute_res)
            await context.log_info(f"json_array = {json_array}")
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_PID_IDENTIFICATION_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_PID_IDENTIFICATION_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break

    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    await context.log_info(f"res = {res}")

    await context.add_view(result['markdown_output'])


    # try:
    #     llm_params = {
    #         #"model":context.config["llm_model_file"],
    #         "model":"qwen3",
    #         "messages":[{
    #             "role":"user",
    #             "content":f"根据以下化工数据，生成一份标题为《数据辨识与统计分析》的专业报告。数据内容：{data_dict['markdown_output']}，其中cycle是数据的周期，Range是数据的频率，Volatility_assessment是数据的波动性，不需要参考文献和撰写日期等额外信息"
    #         }],
    #         "temperature": 0.3,
    #         "top_p": 0.5,
    #         "seed": 42
    #     }
    #     response = requests.post(
    #         #url= context.config["llm_api_url"],
    #         url = "http://nlb-2weu6cb4a97uoz9cqu39kvmj.nlb.cn-beijing.volces.com:32004/v1/chat/completions",
    #         data=json.dumps(llm_params, indent=2, ensure_ascii=False),
    #         headers={
    #             'Content-Type': 'application/json',
    #             'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; X64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
    #         })
    #     if response.status_code == 200:
    #         llm_resp = response.json().get("choices")[0].get("message").get("content")
    # except Exception as e:
    #     llm_resp ="调用LLM总结出现异常:"+ response.text
    #     raise RuntimeError(f"调用算法函数失败: {str(e)} ")
    
    # def text_to_docx(text):
    #     doc = Document()
    #     doc.add_heading('输出报告', level=1)
    #     doc.add_paragraph(text)  
    #     buffer = io.BytesIO()
    #     doc.save(buffer)  
    #     return buffer.getvalue() 
    # docx_binary = text_to_docx(llm_resp)

    # object_name = f"reports/spc/{int(time.time())}.docx"
    # file_info = await context.add_file(f"{object_name}", docx_binary)
    # minio_obj = {
    #     "bucket":file_info['bucket'],
    #     "object":file_info['object']
    # }

    # await context.add_view({
    #         "format": "card",
    #         "content": {
    #             "type": 'summary_file',
    #             "title": 'PID自整定报告',
    #             "details": minio_obj,
    #             "description": "输出报告.docx"
    #         }
    #     })

    
    return {'markdown_output':result['markdown_output']}

   


