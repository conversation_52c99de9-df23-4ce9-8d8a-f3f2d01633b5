from __runner__ import tool, Context
import os
import json
import requests
import base64
import pandas as pd

SPC_API_HOST = os.getenv("HEAT_API_HOST", "http://localhost:8080")
SPC_API_HOST1 = os.getenv("HEAT_API_HOST", "http://localhost:8080")
SPC_API_HOST2 = os.getenv("HEAT_API_HOST", "http://localhost:8080")

"""
1. 读取csv文件保存为sql
2. 周期辨识与场景判断
3. 数据处理与质量判断
3. 特征筛选
4. PID离线参数推荐正确✔
5. 预测模型训练与验证          需要模型超参数、取数据相关的入参、预测图的起止点； 返回mse、acc、一张图片、
6. 模型轻量化✔               需要模型地址、模型超参数、取数据相关的入参； 返回提速率、衰减率组成的一个makedown
7. 特征提取模型训练优化与验证
"""
# 1. 测试读取csv保存为sql

def get_db_connection_string():
    conn = {
        'driver': 'pymysql',
        "host": '***********',
        "username": 'root',
        "password": 'Supcon1304',
        "database": 'liusuan_model',
        "port": 30001
    }
    url = f"mysql+{conn['driver']}://{conn['username']}:{conn['password']}@{conn['host']}:{conn['port']}/{conn['database']}"
    return url  # 示例：PostgreSQL


# async def spc_csv_upload(context: Context, params: any):
#     formResult = await context.get_interaction("spc_up_test")
#     if formResult is None:
#         context.require_interaction({
#             "id": "tuning_file_form",
#             "title": "请上传位号历史数据文件",
#             "type": "form",
#             "form": {
#                 "schema": {
#                     "type": "object",
#                     "description": "请上传位号历史数据文件",
#                     "properties": {
#                         "file": {
#                             "title": "位号历史数据文件(.csv)",
#                             "type": "string",
#                             "format": "data-url"
#                         }
#                     },
#                     "required": ["file"]
#                 },
#                 "default": {}
#             }
#         })
#         return {}
#     else:
#         if 'file' not in formResult:
#             return {
#                 'fileId': -1
#             }

#         # 解析上传的文件
#         file_data = formResult['file'].split(',')[1]  # 分离出base64编码部分
#         decoded_data = base64.b64decode(file_data)
#         df = pd.read_csv(pd.compat.StringIO(decoded_data.decode('utf-8')))

#         # 连接到数据库
#         engine = create_engine(get_db_connection_string())

#         # 插入数据到指定表
#         try:
#             df.to_sql('spc_process_data_raw', con=engine, if_exists='replace', index=False)
#         except Exception as e:
#             print(f"Error inserting data into database: {e}")


#         return {
#             'file_path': 'spc_process_data_raw'
#         }

# 4.
def PID_offline_tune(context: Context, params: any):
    if not params.get('PV'):
        raise ValueError('PV is required')
    if not params.get('MV'):
        raise ValueError('MV is required')
    if not params.get('SV'):
        raise ValueError('SV is required')
    if not params.get('PB'):
        raise ValueError('PB is required')
    if not params.get('Ti'):
        raise ValueError('Ti is required')
    if not params.get('Td'):
        raise ValueError('Td is required')
    if not params.get('end_time'):
        raise ValueError('end_time is required')
    try:
        url = f"{SPC_API_HOST}/PID_offline_tune"
        headers = {"Content-Type": "application/json"}  # 申请请求为json格式
        response = requests.post(url, json=params, headers=headers, timeout=120)
        if response.status_code != 200:
            raise ValueError(response.text)
        try:
            result = response.json()  # 尝试解析 JSON
        except json.JSONDecodeError:
            result = response.text()  # 如果失败，回退为文本
        return result
    except Exception as e:
        raise RuntimeError(f"PID强化学习在线自整定失败: {str(e)}")

def PID_para_check(context: Context, params: any):
    if not params.get('generic_type'):
        raise ValueError('generic_type is required')
    try:
        url = f"{SPC_API_HOST}/PID_para_check"
        headers = {"Content-Type": "application/json"}  # 申请请求为json格式
        response = requests.post(url, json=params, headers=headers, timeout=120)
        if response.status_code != 200:
            raise ValueError(response.text)
        try:
            result = response.json()  # 尝试解析 JSON
        except json.JSONDecodeError:
            result = response.text()  # 如果失败，回退为文本
        return result
    except Exception as e:
        raise RuntimeError(f"PID参数验证失败: {str(e)}")
# 5.


# 6. 模型轻量化
def Quant_model(context: Context, params: any):
    if not params.get('saved_file'):
        raise ValueError('saved_file is required')
    try:
        url = f"{SPC_API_HOST}/quant_model"
        headers = {"Content-Type": "application/json"}  # 申请请求为json格式
        response = requests.post(url, json=params, headers=headers, timeout=120)
        if response.status_code != 200:
            raise ValueError(response.text)
        try:
            result = response.json()  # 尝试解析 JSON
        except json.JSONDecodeError:
            result = response.text()  # 如果失败，回退为文本
        return result
    except Exception as e:
        raise RuntimeError(f"模型量化失败: {str(e)}")





