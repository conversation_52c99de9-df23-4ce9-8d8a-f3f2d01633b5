{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "spc_csv_upload", "alias": ["数据处理与质量判断", "数据质量分析"], "catalog": "spc", "description": "上传功能需要的位号的历史数据文件,csv格式", "params": {"type": "object", "description": "上传数据", "properties": {}}, "result": {"type": "object", "description": "上传成功后返回的文件路径", "properties": {"pv_target": {"type": "string", "description": "目标位号"}, "mv_control": {"type": "string", "description": "控制位号"}, "data_score": {"type": "number", "description": "数据质量打分(最低0,最高100),分数的高低跟与'data_tag'的数值关系极大"}, "data_num_nan": {"type": "string", "description": "数据集中nan值数量,数量越多表示数据集越差"}, "mv_pv_correlation": {"type": "number", "description": "操作变量(MV)与被控变量(PV)的相关性:操纵变量变大,过程变量变大;操纵变量变大,过程变量变小."}}, "required": ["pv_target", "mv_control", "data_score", "data_num_nan", "mv_pv_correlation"]}}, {"name": "identification_analysis", "alias": ["数据辨识及统计分析", "可行性评估"], "catalog": "spc", "description": "数据辨识及统计分析", "params": {"type": "object", "description": "数据辨识及统计分析的输入参数", "properties": {"pv_target": {"type": "string", "description": "目标位号"}, "mv_control": {"type": "string", "description": "控制位号"}, "mv_pv_correlation": {"type": "number", "description": "目标位号与控制位号的正负相关性，正为1，负为-1"}}, "required": ["pv_target", "mv_control", "mv_pv_correlation"]}, "result": {"type": "object", "description": "场景判断与适用方法", "properties": {"scene_judgment": {"type": "string", "description": "场景判断与适用方法"}}, "required": ["drift_data_check", "drift_feature_check", "scene_judgment"]}}, {"name": "spc_PID_csv_upload", "alias": ["PID数据处理与质量判断", "PID数据质量分析"], "catalog": "spc", "description": "上传功能需要的位号的历史数据文件,csv格式", "params": {"type": "object", "description": "上传数据", "properties": {}, "required": []}, "result": {"type": "object", "description": "PID数据处理与质量判断能力返回的内容", "properties": {"markdown_table": {"type": "string", "description": "PID数据处理与质量判断返回的markdown表格"}, "data_score": {"type": "number", "description": "下游入参data_score"}, "PV": {"type": "string", "description": "下游入参PV"}, "MV": {"type": "string", "description": "下游入参MV"}, "SV": {"type": "string", "description": "下游入参SV"}, "PB": {"type": "string", "description": "下游入参PB"}, "Ti": {"type": "string", "description": "下游入参Ti"}, "Td": {"type": "string", "description": "下游入参Td"}, "short_time_length": {"type": "string", "description": "输入数据的时间长度是否符合数据大于20分钟,时间间隔小于10秒的要求"}, "timestamp_gap_tag": {"type": "number", "description": "下游入参timestamp_gap_tag"}, "timestamp_length_tag": {"type": "number", "description": "下游入参timestamp_length_tag"}}, "required": ["markdown_table", "PV", "MV", "SV", "PB", "Ti", "Td", "short_time_length", "timestamp_gap_tag", "timestamp_length_tag"]}}, {"name": "PID_identification_analysis", "alias": ["PID数据辨识及统计分析", "PID回路运行情况分析"], "catalog": "spc", "description": "PID数据辨识及统计分析", "params": {"type": "object", "description": "PID数据辨识及统计分析的输入参数", "properties": {"PV": {"type": "string", "description": "下游入参PV"}, "MV": {"type": "string", "description": "下游入参MV"}, "SV": {"type": "string", "description": "下游入参SV"}}, "required": ["PV", "MV", "SV"]}, "result": {"type": "object", "description": "周期与波动性", "properties": {"markdown_output": {"type": "string", "description": "周期与波动性"}}, "required": ["markdown_output"]}}, {"name": "select_feature", "alias": ["特征筛选", "关键变量挖掘"], "catalog": "spc", "description": "特征筛选", "params": {"type": "object", "description": "特征筛选的输入参数", "properties": {"pv_target": {"type": "string", "description": "目标位号"}, "mv_control": {"type": "string", "description": "控制位号"}, "mv_pv_correlation": {"type": "number", "description": "操作变量(MV)与被控变量(PV)的相关性:操纵变量变大,过程变量变大;操纵变量变大,过程变量变小."}}, "required": ["pv_target", "mv_control", "mv_pv_correlation"]}, "result": {"type": "object", "description": "特征筛选后的位号结果", "properties": {"markdown_output": {"type": "string", "description": "特征筛选后的位号结果"}}, "required": ["markdown_output", "feature_num", "feature_num_check"]}}, {"name": "spc_PID_auto_tune_tool", "alias": ["PID强化学习在线自整定"], "catalog": "spc", "description": "PID强化学习在线自整定（开环）", "params": {"type": "object", "description": "PID强化学习在线自整定能力的输入参数", "properties": {"PV": {"type": "string", "description": "待整定回路的PV变量全称"}, "MV": {"type": "string", "description": "待整定回路的MV变量全称"}, "SV": {"type": "string", "description": "待整定回路的SV变量全称"}, "PB": {"type": "string", "description": "待整定回路的PID控制器的比例作用参数全称"}, "Ti": {"type": "string", "description": "待整定回路的PID控制器的积分作用参数全称"}, "Td": {"type": "string", "description": "待整定回路的PID控制器的微分作用参数全称"}}, "required": ["PV", "MV", "SV", "PB", "Ti", "Td"]}, "result": {"type": "object", "description": "PID在线自整定结果", "properties": {"fig_html": {"type": "string", "description": "交互式图像链接"}}, "required": ["fig_html"]}}, {"name": "spc_para_check_tool", "alias": ["PID参数验证"], "catalog": "spc", "description": "利用专家参数经验库验证PID参数推荐值的正确性", "params": {"type": "object", "description": "PID参数验证能力的输入参数", "properties": {}, "required": []}, "result": {"type": "object", "description": "PID参数验证能力返回的内容", "properties": {"markdown_table": {"type": "string", "description": "PID参数验证能力返回的markdown表格"}}, "required": ["markdown_table"]}}, {"name": "spc_classification", "alias": ["特征提取", "智能工况识别"], "catalog": "spc", "description": "特征提取", "params": {"type": "object", "description": "特征提取的输入参数", "properties": {"pv_target": {"type": "string", "description": "目标位号"}, "mv_control": {"type": "string", "description": "控制位号"}, "mv_pv_correlation": {"type": "number", "description": "操作变量(MV)与被控变量(PV)的相关性:操纵变量变大,过程变量变大;操纵变量变大,过程变量变小."}}, "required": ["pv_target", "mv_control", "mv_pv_correlation"]}, "result": {"type": "object", "description": "特征提取运行结果", "properties": {"markdown_output": {"type": "string", "description": "特征提取运行结果"}, "train_data_time": {"type": "string", "description": "训练集数据的时间"}, "valid_data_time": {"type": "string", "description": "验证集数据的时间"}, "samples_count_score": {"type": "number", "description": "该参数为模型训练的样本多样性打分(最低0,最高100)。分数低于50表明样本多样性太少，分数在50到85之间表明样本多样性一般，分数低于85分时需要提示用户上传更多数据以增加样本多样性和类别均衡度。"}, "unique_label_count": {"type": "number", "description": "整体数据分类的类别数"}, "unique_val_label_count": {"type": "number", "description": "验证集上分类的类别数"}, "accuracy": {"type": "number", "description": "验证集上分类的准确率"}, "recall": {"type": "number", "description": "验证集上分类的召回率"}, "precision": {"type": "number", "description": "验证集上分类的准确度"}}, "required": ["markdown_output", "train_data_time", "valid_data_time", "samples_count_score", "unique_label_count", "unique_val_label_count", "accuracy", "recall", "precision"]}}, {"name": "spc_forecast", "alias": ["预测模型训练与验证", "预测模型构建"], "catalog": "spc", "description": "预测模型训练与验证", "params": {"type": "object", "description": "特征提取的输入参数", "properties": {"samples_count_score": {"type": "number", "description": "该参数为模型训练的样本数量打分(最低0,最高100)。分数低于50表明样本量太少，分数在50到85之间表明样本数量一般，分数低于85分时需要提示用户上传更多数据以增加样本量和类别均衡度。"}}, "required": ["samples_count_score"]}, "result": {"type": "object", "description": "特征提取运行结果", "properties": {"mse": {"type": "number", "description": "mse 越小越好"}, "plt_end_time": {"type": "string", "description": "画图开始时间"}, "plt_begin_time": {"type": "string", "description": "画图结束时间"}, "forecast_result": {"type": "boolean", "description": "是否可以进行闭环控制,如果可以进行闭环控制则进入到下一环节.如果不可以,考虑是否需要补充数据或重新寻优"}, "forecast_result_reson": {"type": "string", "description": "模型预测得到的mse是否符合标准,及其不符合预期的原因是什么导致的"}}, "required": ["mse", "plt_end_time", "plt_begin_time", "forecast_result", "forecast_result_reson"]}}, {"name": "spc_quantation", "alias": ["模型轻量化", "模型轻量化加速"], "catalog": "spc", "description": "模型压缩加速", "params": {"type": "object", "description": "特征提取的输入参数", "properties": {}, "required": []}, "result": {"type": "object", "description": "特征提取运行结果", "properties": {"is_require_quantation": {"type": "string", "description": "是否可以使用提升速度后的模型,如果可以则说明mse上升在可接受范围内,且期望获得速度提升"}, "speed_up": {"type": "string", "description": "提升速度后的模型是原模型速度的倍数,如1.11表示现在模型的推理速度是原模型的1.11倍"}, "mse_down": {"type": "string", "description": "提升速度后的模型较原模型mse损失率(%),如内容为18,则表示mse较原本提高了18%"}}, "required": ["is_require_quantation", "speed_up", "mse_down"]}}, {"name": "spc_long_control_tool", "alias": ["控制模型", "AI控制器搭建"], "catalog": "spc", "description": "基于AI的智能控制", "params": {"type": "object", "properties": {"mv_pv_correlation": {"type": "number", "description": "操作变量(MV)与被控变量(PV)的相关性:操纵变量变大,过程变量变大;操纵变量变大,过程变量变小."}, "plt_begin_time": {"type": "string", "format": "date-time", "description": "数据展示起始时间（ISO 8601格式，如 '2023-09-01T00:00:00'）"}, "plt_end_time": {"type": "string", "format": "date-time", "description": "数据展示结束时间（ISO 8601格式，如 '2023-09-02T23:59:59'）"}}, "required": ["mv_pv_correlation", "plt_begin_time", "plt_end_time"]}, "result": {"type": "object", "description": "SPC智能控制计算结果", "properties": {"is_reasonable": {"type": "object", "description": "控制量合理性的多维度判断", "properties": {"magnitude_valid": {"type": "boolean", "description": "控制量的数量级是否在合理范围内"}, "direction_valid": {"type": "string", "description": "控制量的方向性是否正确"}, "d_pv_std": {"type": "string", "description": "被控变量平稳度提升的预计效果"}}, "required": ["magnitude_valid", "direction_valid", "d_pv_std"]}}, "required": ["is_reasonable"]}}]}