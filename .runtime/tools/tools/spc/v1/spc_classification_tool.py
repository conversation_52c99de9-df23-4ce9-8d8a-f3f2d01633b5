import io
import json
import time
import urllib

import redis
from __runner__ import tool, Context
import requests
import base64
import os

HEAT_API_HOST = "http://***********:31501"
# AUTONOMOUS_SPC_URL = "https://obp-dev.supcon5t.com/operate-optimization-web"  # os.getenv("AUTONOMOUS_OPT_URL")  # 读取网址信息
RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_classification_py&built_in=1&time_out=6000"
class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 3000  # 算法执行超时时间

    # changed
    SPC_CLASSIFICATION_TOPIC = "single_python_train_spc_classification"  # 发送预测算法执行topic
    SPC_CLASSIFICATION_TOPIC_RES = "single_python_train_spc_classification_res"  # 预测算法响应topic
    SPC_CLASSIFICATION_ALG_NAME = "spc_classification_train"  # 与算法文件名对应
    SPC_CLASSIFICATION_ALG_FULL_NAME = "spc_classification_train.py"  # 与算法文件名对应
    TASK_NAME = "SPC:CLASSIFICATION"

@tool(version="*")
async def spc_classification(context: Context, params: any):
    clientId = str(context.session_id)

    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "pv_target",
            "type": 1,
            "typeName": "str",  # list
            "userInput": 1,
            "value": 'G_LT_13001.PV'  # json
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": '123456'
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_CLASSIFICATION_ALG_NAME,
            "sourcePath": Config.SPC_CLASSIFICATION_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_CLASSIFICATION_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_CLASSIFICATION_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_CLASSIFICATION_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break
    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    await context.log_info(f"res = {res}")

    # 展示多个 HTML 内容
    # await context.add_view(result['markdown_table'])
    await context.add_view({
        "format": "card",
        "content": {
            "type": "html",
            "title": "特征提取训练集结果",
            "details": result['train_data_plot_html']
        }
    })
    await context.add_view({
        "format": "card",
        "content": {
            "type": "html",
            "title": "特征提取验证集结果",
            "details": result['test_data_plot_html']
        }
    })
    # await context.add_view({
    #         "format":"card",
    #         "content": {
    #             "type": "html",
    #             "title": "混淆矩阵",
    #             "details": data_dict['confusion_matrix_plot_html']
    #         }
    #     })

    return {"markdown_output": result['markdown_table'],
            "train_data_time": result['train_data_time'],
            "valid_data_time": result['valid_data_time'],
            "samples_count_score": result['samples_count_score'],
            "unique_label_count": result['unique_label_count'],
            "unique_val_label_count": result['unique_val_label_count'],
            "accuracy": result['accuracy'],
            "recall": result['recall'],
            "precision": result['precision'],

            }


