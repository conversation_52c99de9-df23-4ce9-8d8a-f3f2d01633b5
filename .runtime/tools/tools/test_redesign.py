import io
import base64
import asyncio
from openpyxl import Workbook
from __tester__ import load_module, run_async
from __runner__ import Context

# === 修改为你的工具模块路径 ===
tools_module = load_module("redesign/v1/pinch.py")

# === 构造 data-url Excel ===
def _dummy_excel_data_url():
    wb = Workbook()
    ws = wb.active
    ws.append(["A", "B"])
    ws.append([1, 2])
    buf = io.BytesIO()
    wb.save(buf)
    buf.seek(0)
    binary = buf.read()
    b64 = base64.b64encode(binary).decode()
    return f"data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;name=test.xlsx;base64,{b64}"

# ✅ 自定义测试 Context，支持 interactions 模拟
class TestContext(Context):
    def __init__(self):
        super().__init__()
        self._interactions = {}

    async def get_interaction(self, key):
        return self._interactions.pop(key, None)

    def set_interaction(self, key, value):
        self._interactions[key] = value

# === 测试 upload_excel 工具 ===
def test_upload_excel_tool():
    context = TestContext()
    context.set_interaction("excel_form", {
        "file": _dummy_excel_data_url()
    })

    result = asyncio.run(run_async(tools_module.upload_excel, context, params={}))
    assert "excel_uid" in result
    print("✅ Excel 上传测试通过，UID:", result["excel_uid"])
    return result["excel_uid"]

# === 测试 pinch 工具 ===
def test_pinch_tool():
    context = TestContext()
    params = {
        "streams": [
            {"type": "hot",  "supply_temp": 300, "target_temp": 120, "heat_capacity_flow": 2.0},
            {"type": "cold", "supply_temp":  50, "target_temp": 200, "heat_capacity_flow": 1.8}
        ]
    }
    result = asyncio.run(run_async(tools_module.pinch, context, params))
    assert "夹点温度" in result
    assert "最小热能利用量" in result
    assert "需热量（Hot Utility）" in result
    assert "需冷量（Cold Utility）" in result
    print("✅ Pinch 测试通过:", result)

# === 主程序 ===
if __name__ == "__main__":
    uid = test_upload_excel_tool()
    test_pinch_tool()
    print("🎉 所有功能测试通过！")
