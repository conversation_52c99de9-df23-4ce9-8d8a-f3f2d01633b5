from __runner__ import tool, Context
import requests
import json
import datetime

@tool(private=True)
def get_project_id(context: Context, params: any):
    if not context.config["installed"]:
        return 0
    response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/project/getactive"
                            , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(response.text)
    return json_obj['data']['id']


@tool(private=True)
def is_agent_installed(context: Context, params: any):
    return context.config["installed"]


@tool(private=True)
async def add_card(context, params: any = None):
    if params['title'] is None:
        params['title'] = ""
    if params['card_type'] != "summary_file" and params['card_type'] != "card" and params['card_type'] != "page":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + params['title'] + "\r\n\r\n" + params['content']
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": params['card_type'],
                "title": params['title'],
                "description": params['description'],
                "details": params['content']
            }
        })


@tool(private=True)
def get_tuning_advice(context: Context, params: any):
    project_id = get_project_id(context, params)
    loopId = params['loopId']

    if not is_agent_installed(context, params):
        return {
            'algAdvice': 2,
            'algIntermediateVariable': [0,0,0,0,0,0,0,0]
        }

    endTime = datetime.datetime.now().timestamp() * 1000
    startTime = endTime - 3600000 * 6
    agentInstalled = is_agent_installed(context, params)

    response = requests.post(
        url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/loop/guidetunning/status" if agentInstalled else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/tuning/statusDetection"
        , json={
            'timeList': [startTime, endTime],
            'loopId': loopId
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'Cookie': f'tenant-id={context.tenant_id}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    detectResult = json.loads(response.text)['data'] if agentInstalled else json.loads(response.text)['content']

    return detectResult
