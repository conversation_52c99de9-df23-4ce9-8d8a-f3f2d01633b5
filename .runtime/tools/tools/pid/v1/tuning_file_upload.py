from collections import OrderedDict

from __runner__ import tool, Context
import requests
import json

@tool(version="*")
async def tuning_file_upload(context: Context, params: any):
    if context.call_tool("is_agent_installed", context=context):
        return {
            'fileId': -1
        }
    formResult = await context.get_interaction("tuning_file_form")
    if 'title' not in params:
        params['title'] = "请上传位号历史数据文件"
    if 'fieldDescription' not in params:
        params['fieldDescription'] = "位号历史数据文件(.csv)"
    if 'templateList' not in params:
        params['templateList'] = []
    if formResult is None:
        formFields = OrderedDict()
        formFields['file'] = {
            "title": params['fieldDescription'],
            "type": "string",
            "format": "data-url",
            "template": params['templateList']
        }
        requiredFields = ["file"]
        defaultValues = {}
        if 'extraFields' in params:
            for k, v in params['extraFields'].items():
                formFields[k] = v
        if 'requiredField' in params:
            requiredFields = requiredFields + params['requiredField']
        if 'defaultValue' in params:
            defaultValues = params['defaultValue']
        context.require_interaction({
            "id": "tuning_file_form",
            "title": params['title'],
            "description": params['title'],
            "type": "form",
            "form": {
                "form_type": "file" if 'extraFields' not in params else "normal",
                "schema": {
                    "type": "object",
                    "properties": formFields,
                    "required": requiredFields
                },
                "default": defaultValues
            }
        })
        return {}
    else:
        if 'file' not in formResult:
            return {
                'fileId': -1,
                'formResult': formResult
            }
        params['file'] = formResult['file']
        params['formResult'] = formResult
        return do_upload(context, params)


@tool(private=True)
def do_upload(context: Context, params: any):
    fileId = -1
    encodedFile = params['file'] + ''
    sessionId = None
    page = 0
    binary = False
    if 'binary' in params:
        binary = params['binary']
    while len(encodedFile) > 0:
        part = encodedFile[:102400]
        encodedFile = encodedFile[102400:]
        response = requests.post(url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/tuning/file/upload/append"
                                 , json={
                'partialFile': part,
                'done': len(encodedFile) == 0,
                'page': page,
                'binary': binary
            } if sessionId is None else {
                'partialFile': part,
                'done': len(encodedFile) == 0,
                'sessionId': sessionId,
                'page': page,
                'binary': binary
            }
                                 , headers={
                'Content-Type': 'application/json; charset=GB2312',
                'Authorization': context.config["inner_token"],
                'Cookie': f'tenant-id={context.tenant_id}',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })
        if len(encodedFile) > 0:
            sessionId = json.loads(response.text)['content']
        else:
            fileId = json.loads(response.text)['content']
        page = page + 1
    return {
        'fileId': fileId,
        'formResult': None if 'formResult' not in params else params['formResult']
    }
