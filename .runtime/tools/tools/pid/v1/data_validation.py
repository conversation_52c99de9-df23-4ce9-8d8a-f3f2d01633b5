from __runner__ import tool, Context
import requests
import json

@tool(version="*")
async def data_validation(context: Context, params: any):
    loopDetail = await context.call_tool("get_loop_detail", context=context, params=params)
    if context.call_tool("is_agent_installed", context=context):
        if params['loopId'] == -1:
            raise ValueError("回路不存在！")
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": f"获取{loopDetail['loopName']}基本信息后，检查其历史数据的完整性。",
                "description": "",
                "details": ""
            }
        })
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': f"""
获取{loopDetail['loopName']}基本信息后，检查其PV/SV/MV历史数据的完整性。数据完整则采用历史数据整定；无效则采用知识驱动（经验）提供初始PID参数。
""",
            'title': None,
            'description': None
        })
        msgDict = context.call_tool("get_tuning_advice", context=context, params=params)
        algAdvice = 0
        if msgDict is not str:
            algAdvice = msgDict['algAdvice']
        if algAdvice <= 0 or algAdvice >= 17:
            await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": f"未能获取{loopDetail['loopName']}回路的有效历史数据。",
                    "description": "",
                    "details": ""
                }
            })
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"未能获取{loopDetail['loopName']}回路的有效历史数据。因此，将采用知识驱动型整定方法，根据经验设置一组合理的初始PID参数。这组参数将为系统提供一个稳定的运行起点，并为后续的参数优化奠定基础。",
                'title': None,
                'description': None
            })
            return {'valid': False}
        else:
            await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": f"已确认{loopDetail['loopName']}回路存在完整的历史数据。",
                    "description": "",
                    "details": ""
                }
            })
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"已确认{loopDetail['loopName']}回路存在完整的历史数据，包括测量值(PV)、设定值(SV)、阀位输出(MV)等。接下来，将采用基于历史数据驱动的参数整定策略，利用这些数据进行更精准的PID参数整定，以优化控制性能。",
                'title': None,
                'description': None
            })
            return {'valid': True}
    else:
        openPageResult = await context.get_interaction("data_validation_data_preview")
        offlinePreviewResult = await context.get_interaction("data_validation_offline_data_preview")
        if offlinePreviewResult is not None:
            await context.set_cache("data_trend_image", offlinePreviewResult['file'])
            return {
                'valid': True
            }
        selectResult = await context.get_interaction("select_upload_his_data_file")
        dataUploadResult = await context.get_interaction("tuning_file_form")
        if openPageResult is None:
            if selectResult is None and openPageResult is None:
                await context.add_view({
                    "format": "tip",
                    "content": {
                        "type": 'default',
                        "title": '',
                        "content": f"获取{loopDetail['loopName']}基本信息后，检查其历史数据的完整性。",
                        "description": "",
                        "details": ""
                    }
                })
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"""
获取{loopDetail['loopName']}基本信息后，检查其PV/SV/MV历史数据的完整性。数据完整则采用历史数据整定；无效则采用知识驱动（经验）提供初始PID参数。
""",
                    'title': None,
                    'description': None
                })
            response = requests.get(
                url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/verify/history?loopId={params['loopId']}"
                , headers={
                    'Content-Type': 'application/json',
                    'Authorization': context.config["inner_token"],
                    'Cookie': f'tenant-id={context.tenant_id}',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            detectResult = json.loads(response.text)['content']
            if detectResult:
                await context.add_view({
                    "format": "tip",
                    "content": {
                        "type": 'default',
                        "title": '',
                        "content": f"请确认{loopDetail['loopName']}回路的历史数据趋势",
                        "description": "",
                        "details": ""
                    }
                })
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"已确认{loopDetail['loopName']}回路存在完整的历史数据，包括测量值(PV)、设定值(SV)、阀位输出(MV)等。接下来，将采用基于历史数据驱动的参数整定策略，利用这些数据进行更精准的PID参数整定，以优化控制性能。\r\n\r\n请确认是否基于以下历史数据进行参数整定。您也可挑选自定义时间段的历史数据进行整定。",
                    'title': None,
                    'description': None
                })
                context.require_interaction({
                    "id": "data_validation_data_preview",
                    "title": f"查看趋势  {loopDetail['loopName']}回路历史数据趋势",
                    "type": "open_page",
                    "open_page": f"/xpt-tpt-pid/echartshistory?loopId={params['loopId']}&type=execute",
                    "page_type": "execute"
                })
                return {}
            else:
                if selectResult is None:
                    await context.add_view({
                        "format": "tip",
                        "content": {
                            "type": 'default',
                            "title": '',
                            "content": "该回路的相关位号不存在有效历史数据，您是否需要基于历史数据文件进行后续步骤",
                            "description": "",
                            "details": ""
                        }
                    })
                    context.require_interaction({
                        "id": "select_upload_his_data_file",
                        "title": "该回路的相关位号不存在有效历史数据，您是否需要基于历史数据文件进行后续步骤",
                        "type": "select",
                        "select": [
                            {
                                'title': "是",
                                "data": True
                            },
                            {
                                'title': "否",
                                "data": False
                            }
                        ]
                    })
                    return {}
                else:
                    if selectResult[0]['data']: # 用户选择上传历史数据文件
                        if dataUploadResult is not None: # 用户已经上传了位号历史数据，准备上传数据到服务端
                            await context.add_view({
                                "format": "tip",
                                "content": {
                                    "type": 'default',
                                    "title": '',
                                    "content": "当前回路相关位号不存在有效历史数据。您已上传历史数据。",
                                    "description": "",
                                    "details": ""
                                }
                            })
                            await context.call_tool("add_card", context=context, params={
                                'card_type': "summary",
                                'content': "当前回路相关位号不存在有效历史数据。检测到您已上传历史数据。我将基于多策略自适应整定算法进行参数整定。",
                                'title': None,
                                'description': None
                            })
                            fileUploadResult = await context.call_tool("tuning_file_upload", context=context, params=params)
                            if 'fileId' in fileUploadResult and fileUploadResult['fileId'] != -1: # 上传数据到服务端成功
                                await context.log_info(f"fileId: {fileUploadResult['fileId']}")
                                await context.set_cache("data_validation_file_id", fileUploadResult['fileId'])
                                await context.set_cache("data_validation_sampling_time", fileUploadResult['formResult']['samplingTime'])
                                await context.set_cache("data_validation_sv_limit", fileUploadResult['formResult']['svLimit'])
                                await context.set_cache("data_validation_mv_limit", fileUploadResult['formResult']['mvLimit'])
                                context.require_interaction({
                                    "id": "data_validation_offline_data_preview",
                                    "title": f"查看趋势  {loopDetail['loopName']}回路历史数据趋势",
                                    "type": "open_page",
                                    "open_page": f"/xpt-tpt-pid/echartshistory?loopId={fileUploadResult['fileId']}&type=view",
                                    "page_type": "execute"
                                })
                                return {
                                    'valid': True
                                }
                            else:
                                raise ValueError("上传数据文件到服务端失败！")
                        else:
                            await context.add_view({
                                "format": "tip",
                                "content": {
                                    "type": 'default',
                                    "title": '已分析找到影响问题的关键因素，请上传历史数据文件',
                                    "content": "请按文件模板要求上传位号历史数据文件。上传成功后，点击相应文件名即可查看数据。",
                                    "description": "",
                                    "details": ""
                                }
                            })
                            params['extraFields'] = {
                                "svLimit": {
                                    'title': "设定值量程上下限",
                                    'type': "string",
                                    'description': "格式：下限值-上限值 例：0-100"
                                },
                                "mvLimit": {
                                    'title': "阀位值量程上下限",
                                    'type': "string",
                                    'description': "格式：下限值-上限值 例：0-100"
                                },
                                "samplingTime": {
                                    'title': "采样时间间隔(秒)",
                                    'type': "integer"
                                }
                            }
                            params['title'] = '请按文件模板要求上传位号历史数据文件。上传成功后，点击相应文件名即可查看数据。'
                            params['requiredField'] = ["samplingTime", "svLimit", "mvLimit"]
                            params['defaultValue'] = {
                                "samplingTime": 5,
                                "svLimit": "0-100",
                                "mvLimit": "0-100"
                            }
                            params['templateList'] = [
                                {
                                    'label': "位号历史数据文件模板说明",
                                    'url': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/tuning/fileTemplateInstructions?filename=位号历史数据文件格式说明.docx"
                                },
                                {
                                    'label': "位号历史数据文件模板",
                                    'url': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/tuning/hisFileTemplate?controlMode=2&filename=位号历史数据文件模板.csv"
                                }
                            ]
                            await context.call_tool("tuning_file_upload", context=context, params=params)
                            return {}
                    else:
                        await context.add_view({
                            "format": "tip",
                            "content": {
                                "type": 'default',
                                "title": '',
                                "content": f"当前{loopDetail['loopName']}回路相关位号不存在有效历史数据，您已拒绝上传历史数据文件。",
                                "description": "",
                                "details": ""
                            }
                        })
                        await context.call_tool("add_card", context=context, params={
                            'card_type': "summary",
                            'content': f"当前{loopDetail['loopName']}回路相关位号不存在有效历史数据，您已拒绝上传历史数据文件。因此，我将采用知识驱动型整定方法，根据经验设置一组合理的初始PID参数。这组参数将为系统提供一个稳定的运行起点，并为后续的参数优化奠定基础。",
                            'title': None,
                            'description': None
                        })
                        return {
                            'valid': False
                        }
        else:
            await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": "请稍作等待，后续可能需要您进行交互操作",
                    "description": "",
                    "details": ""
                }
            })
            await context.set_cache("data_validation_data_range_start_time", openPageResult['startTime'])
            await context.set_cache("data_validation_data_range_end_time", openPageResult['endTime'])
            await context.set_cache("data_trend_image", openPageResult['file'])
            return {
                'valid': True
            }
