from __runner__ import tool, Context
import requests
import json
import datetime

def get_project_id(context):
    response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/project/getactive"
    , headers={
        'Content-Type': 'application/json',
        'Authorization': context.config["inner_token"],
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    json_obj = json.loads(response.text)
    return json_obj['data']['id']
    
    
@tool(version="*")
async def loop_correlation_analysis(context: Context, params: any):
    project_id = get_project_id(context)
    if not('couplingDuration' in params):
        params['couplingDuration'] = 0.3
    # print(params['correlationTagNames'])
    tagNameList = [params['targetTagName']] + params['correlationTagNames']
    response = requests.post(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loop/tag/getTagIds"
    , json={
        'datasourceid': 0,
        'tags': tagNameList
    }
    , headers={
        'Content-Type': 'application/json',
        'Authorization': context.config["inner_token"],
        'Project': str(project_id),
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    tagIdList = json.loads(response.text)['list']
    
    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/correlation/table/add"
    , json={
        'analysisTagId': tagIdList[0],
        'correlationalTagIds': tagIdList[-(len(tagIdList) - 1):],
        'couplingDuration': params['couplingDuration'],
        'reportName': tagNameList[0] + f"报告详情 - {datetime.datetime.now().timestamp()}",
        'startTime': params['startTime'],
        'endTime': params['endTime']
    }
    , headers={
        'Content-Type': 'application/json',
        'Authorization': context.config["inner_token"],
        'Project': str(project_id),
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    resJson = json.loads(response.text)
    if not(resJson['code'] == 100000000):
        raise ValueError(f"Correlation analysis failed! Reason: {resJson['message']}")
    reportId = resJson['data']
    response = requests.get(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/correlation/report/detail?correlationId={reportId}"
    , headers={
        'Content-Type': 'application/json',
        'Authorization': context.config["inner_token"],
        'Project': str(project_id),
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    correlationDetail = json.loads(response.text)['data']
    return {
        'correlationDurationList': dict(zip(correlationDetail['tagNames'], correlationDetail['correlationDurationList'])), # 耦合作用时长对比图
        'correlationIndexTrend': correlationDetail['correlationIndexTrend'],
        'mainTagName': correlationDetail['mainTagName']
    }