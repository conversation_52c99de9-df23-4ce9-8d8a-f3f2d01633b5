from __runner__ import tool, Context
import requests
import json
import datetime

"""
装置实时自控率、平稳率计算
"""
@tool(version="*")
async def get_self_control_rate(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {
            'closeRate': 0,
            'stableRate': 0
        }
    groupId = params['groupId']

    project_id = context.call_tool("get_project_id", context=context)

    response = requests.post(
        url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/realtime/group/config/info/query"
        , json={
            'groupId': groupId
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(response.text)
    self_control_rate = json_obj['data']['item1'][0]['closeRate']
    stable_rate = json_obj['data']['item1'][0]['stableRate']
    return {'closeRate': self_control_rate, 'stableRate': stable_rate}


@tool(version="*")
async def loop_info_manage(context: Context, params: any):
    pageReult = await context.get_interaction("loop_info_manage_open_page")
    if pageReult is None:
        agentInstalled = context.call_tool("is_agent_installed", context=context)
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': "为您打开统一管理界面，集中查看所有控制回路。快速获取类型、参数、状态、工艺特性等基本信息，检查配置与参数完整性，并支持分类整理和调整。",
            'title': None,
            'description': None
        })
        context.require_interaction({
            "id": "loop_info_manage_open_page",
            "title": "回路信息管理页面",
            "type": "open_page",
            "open_page": f"/xpt-tpt-pid/loopConfig" if not agentInstalled else f"http://{context.config['agent_frontend_host']}/PID_WEBWATCH/config/loop?ticket={context.config['inner_token'][7:]}"
        })

    return {}


@tool(version="*")
async def get_group_or_loop_id_list(context: Context, params: any):
    selectResult = await context.get_interaction("select")
    formResult = await context.get_interaction("auto_config_form")
    # dataUploadResult = await context.get_interaction("tuning_file_form")
    agentInstalled = context.call_tool("is_agent_installed", context=context)
    queryingList = False
    needTagValue = False
    queryingCascade = False
    retDict = {}
    if 'queryingList' in params:
        queryingList = params['queryingList']
    if 'needTagValue' in params:
        needTagValue = params['needTagValue']
    if 'queryingCascade' in params:
        queryingCascade = params['queryingCascade']
    queryingLoop = 'queryingLoop' in params and params['queryingLoop']
    if not agentInstalled:
        if formResult is not None: # 用户已经填写了待创建回路的信息
            loopCreateResult = await loop_auto_config(context, params) # 尝试创建回路
            if 'failed' not in loopCreateResult: # 回路所属位号在datahub中存在，回路创建成功
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"系统将依据您补充的当前回路类型及相关属性继续进行后续步骤。" if loopCreateResult['fromCache'] else f"回路{formResult['loopName']}创建成功。",
                    'title': None,
                    'description': None
                })
                if loopCreateResult['fromCache']:
                    retDict['resultField'] = 'queryLoopWithoutTagValueSingleResult'
                    retDict['queryLoopWithoutTagValueSingleResult'] = loopCreateResult
                    return retDict

                loopName = formResult['loopName']
                params['loopName'] = [loopName]
                params['loopDesc'] = []
            else: # 回路所属位号在datahub中不存在，需要上传历史数据
                raise ValueError("回路创建失败！")
    if formResult is None and selectResult is None:
        await context.call_tool("add_card", context=context, params={
            'card_type': "markdown",
            'content': f"""
我需要先查询系统是否存在名为{params['loopName'][0] if params['queryingLoop'] and len(params['loopName']) > 0 else params['groupPath']}的{'回路' if params['queryingLoop'] and len(params['loopName']) > 0 else '装置'}。若存在则检查设定值（SV）、测量值（PV）、阀位值（MV）、量程以及PID等详细参数。不存在则核对名称后新增。
""",
            'title': None,
            'description': None
        })
    if selectResult is None:
        # params['loopName'] = formResult['回路名称']
        # params['loopDesc'] = formResult['回路描述']
        # params['groupPath'][0] = formResult['装置']
        project_id = context.call_tool("get_project_id", context=context)
        rootNode = None
        if agentInstalled:
            response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/config/node/all"
                                    , headers={
                    'Content-Type': 'application/json',
                    'Authorization': context.config["inner_token"],
                    'Project': str(project_id),
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            rootNode = json.loads(response.text)['data']
        else:
            response = requests.get(url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/config/node/all"
                                    , headers={
                    'Content-Type': 'application/json',
                    'Authorization': context.config["inner_token"],
                    'Cookie': f'tenant-id={context.tenant_id}',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            await context.log_info(response.text)
            rootNode = json.loads(response.text)['content']
        queue = []
        leafNodes = []
        if rootNode is not None:
            queue.append(rootNode)
        currentNode = None
        possibleNodes = []
        if 'groupPath' in params and len(params['groupPath']) > 0:
            while len(queue) > 0:
                currentNode = queue.pop(0)
                if params['groupPath'][0] in currentNode['groupPathName']:
                    possibleNodes.append(currentNode)
                if 'children' in currentNode:
                    queue = queue + currentNode['children']
        else:
            while len(queue) > 0:
                currentNode = queue.pop(0)
                possibleNodes.append(currentNode)
                if 'children' in currentNode:
                    queue = queue + currentNode['children']

        queue = queue + possibleNodes
        while len(queue) > 0:
            currentNode = queue.pop(0)
            if not ('children' in currentNode) or len(currentNode['children']) == 0:
                leafNodes.append(currentNode)

        groupList = []
        loopList = []

        for group in leafNodes:
            groupList.append({
                'groupName': group['groupName'],
                'groupPath': group['groupPathName'],
                'id': group['id']
            })

        suggestTuningMethod = 2
        selectType = 0
        condition = None
        retries = 0
        while retries < 2 and len(loopList) == 0:
            if 'loopDesc' in params and len(params['loopDesc']) > 0:
                selectType = 1
                condition = params['loopDesc']
            elif 'loopName' in params and len(params['loopName']) > 0:
                condition = params['loopName']
            elif 'advanceLoopType' in params and params['advanceLoopType'] is not None:
                selectType = 5
                condition = str(params['advanceLoopType'])
            if not (condition is None):
                subLoop = None
                if len(params['loopName']) == 2 and queryingCascade:
                    response = requests.get(
                        url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType=0&condition={condition[1]}&withvalue=true&groupId={rootNode['id']}&orderField=loopName&order=ascend" if agentInstalled else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType=0&condition={condition[1]}&withvalue=true&groupId={rootNode['id']}&orderField=loopName&order=ascend",
                        headers={
                            'Content-Type': 'application/json',
                            'Authorization': context.config["inner_token"],
                            'Project': str(project_id),
                            'Cookie': f'tenant-id={context.tenant_id}',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                        })
                    resList = json.loads(response.text)['list'] if agentInstalled else json.loads(response.text)['content'][
                        'list']

                    for loop in resList:
                        if loop['loopName'] == params['loopName'][1]:
                            subLoop = loop
                            break
                for group in leafNodes:
                    resList = []
                    if not queryingCascade:
                        for loopName in condition:
                            response = requests.get(
                                url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType={selectType}&condition={loopName}&withvalue=true&groupId={group['id']}&orderField=loopName&order=ascend" if agentInstalled else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType={selectType}&condition={loopName}&withvalue=true&groupId={group['id']}&orderField=loopName&order=ascend"
                                , headers={
                                    'Content-Type': 'application/json',
                                    'Authorization': context.config["inner_token"],
                                    'Project': str(project_id),
                                    'Cookie': f'tenant-id={context.tenant_id}',
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                                })
                            resList.extend(json.loads(response.text)['list'] if agentInstalled else
                                        json.loads(response.text)['content']['list'])
                    else:
                        response = requests.get(
                            url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType={selectType}&condition={condition[0]}&withvalue=true&groupId={group['id']}&orderField=loopName&order=ascend" if agentInstalled else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType={selectType}&condition={condition[0]}&withvalue=true&groupId={group['id']}&orderField=loopName&order=ascend"
                            , headers={
                                'Content-Type': 'application/json',
                                'Authorization': context.config["inner_token"],
                                'Project': str(project_id),
                                'Cookie': f'tenant-id={context.tenant_id}',
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                            })
                        resList.extend(
                            json.loads(response.text)['list'] if agentInstalled else json.loads(response.text)['content'][
                                'list'])
                    for loop in resList:
                        if not (subLoop is None):
                            if subLoop['advanceLoopType'] == 1 and loop['advanceLoopType'] == 1:
                                if subLoop['cascadeLoopName'] == loop['loopName']:
                                    suggestTuningMethod = 0
                                else:
                                    suggestTuningMethod = 1
                            elif subLoop['advanceLoopType'] == 0 and loop['advanceLoopType'] == 1 or subLoop[
                                'advanceLoopType'] == 1 and loop['advanceLoopType'] == 0:
                                suggestTuningMethod = 1
                            else:
                                suggestTuningMethod = 2
                        if needTagValue:
                            loopDetail = None
                            if agentInstalled:
                                params['loopId'] = loop['loopId']
                                loopDetail = await get_loop_detail(context, params)
                            loopList.append({
                                'id': loop['loopId' if agentInstalled else 'id'],
                                'loopName': loop['loopName'],
                                'loopDescription': loop['loopDescription' if agentInstalled else 'descript'],
                                'loopType': loop['loopType'],
                                'groupPath': loop['loopGroupNamePath'],
                                'groupName': loop['loopGroup'],
                                'groupId': loop['groupId'],
                                'advanceLoopType': loop['advanceLoopType'],
                                'cascadeLoopName': loop['cascadeLoopName'],
                                'masterOrSlave': loop['masterOrSlave'] == 0,
                                'suggestTuningMethod': suggestTuningMethod,
                                'currentPIDParam': loop['pidParaRealTimeValue'],
                                'tags': loopDetail['tags'] if agentInstalled else [
                                    loop['pvTag'],
                                    loop['svTag'],
                                    loop['mvTag'],
                                    loop['pTag'],
                                    loop['iTag'],
                                    loop['dTag'],
                                    loop['mvUpperLimitTag'],
                                    loop['mvLowerLimitTag'],
                                    loop['svUpperLimitTag'],
                                    loop['svLowerLimitTag'],
                                    loop['cascadeFlagTag'],
                                    loop['controlModeTag'],
                                    loop['controlDirectionTag'],
                                    loop['mvHighTag'],
                                    loop['mvLowTag'],
                                    loop['svHighTag'],
                                    loop['svLowTag']
                                ],
                                'tagValues': loopDetail['tagValues'] if agentInstalled else [
                                    loop['pvValue'],
                                    loop['svValue'],
                                    loop['mvValue'],
                                    loop['pValue'],
                                    loop['iValue'],
                                    loop['dValue'],
                                    loop['mvUpperLimitValue'],
                                    loop['mvLowerLimitValue'],
                                    loop['svUpperLimitValue'],
                                    loop['svLowerLimitValue'],
                                    loop['cascadeFlagValue'],
                                    loop['controlModeValue'],
                                    loop['controlDirectionValue'],
                                    loop['mvHighValue'],
                                    loop['mvLowValue'],
                                    loop['svHighValue'],
                                    loop['svLowValue']
                                ]
                            })
                        else:
                            loopList.append({
                                'id': loop['loopId' if agentInstalled else 'id'],
                                'loopName': loop['loopName'],
                                'loopDescription': loop['loopDescription' if agentInstalled else 'descript'],
                                'loopType': loop['loopType'],
                                'groupPath': loop['loopGroupNamePath'],
                                'groupName': loop['loopGroup'],
                                'groupId': loop['groupId'],
                                'advanceLoopType': loop['advanceLoopType'],
                                'cascadeLoopName': loop['cascadeLoopName'],
                                'masterOrSlave': loop['masterOrSlave'] == 0,
                                'suggestTuningMethod': suggestTuningMethod,
                                'currentPIDParam': loop['pidParaRealTimeValue']
                            })
            else:
                for group in leafNodes:
                    response = requests.get(
                        url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType={selectType}&condition=&withvalue=true&groupId={group['id']}&orderField=loopName&order=ascend" if agentInstalled else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/detail/selectgroup?current=1&pageSize=3000&selectType={selectType}&condition=&withvalue=true&groupId={group['id']}&orderField=loopName&order=ascend"
                        , headers={
                            'Content-Type': 'application/json',
                            'Authorization': context.config["inner_token"],
                            'Project': str(project_id),
                            'Cookie': f'tenant-id={context.tenant_id}',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                        })
                    resList = json.loads(response.text)['list'] if agentInstalled else json.loads(response.text)['content'][
                        'list']
                    for loop in resList:
                        if needTagValue:
                            loopDetail = None
                            if agentInstalled:
                                params['loopId'] = loop['loopId']
                                loopDetail = await get_loop_detail(context, params)
                            loopList.append({
                                'id': loop['loopId' if agentInstalled else 'id'],
                                'loopName': loop['loopName'],
                                'loopDescription': loop['loopDescription' if agentInstalled else 'descript'],
                                'loopType': loop['loopType'],
                                'groupPath': loop['loopGroupNamePath'],
                                'groupName': loop['loopGroup'],
                                'groupId': loop['groupId'],
                                'advanceLoopType': loop['advanceLoopType'],
                                'cascadeLoopName': loop['cascadeLoopName'],
                                'masterOrSlave': loop['masterOrSlave'] == 0,
                                'suggestTuningMethod': suggestTuningMethod,
                                'currentPIDParam': loop['pidParaRealTimeValue'],
                                'tags': loopDetail['tags'] if agentInstalled else [
                                    loop['pvTag'],
                                    loop['svTag'],
                                    loop['mvTag'],
                                    loop['pTag'],
                                    loop['iTag'],
                                    loop['dTag'],
                                    loop['mvUpperLimitTag'],
                                    loop['mvLowerLimitTag'],
                                    loop['svUpperLimitTag'],
                                    loop['svLowerLimitTag'],
                                    loop['cascadeFlagTag'],
                                    loop['controlModeTag'],
                                    loop['controlDirectionTag'],
                                    loop['mvHighTag'],
                                    loop['mvLowTag'],
                                    loop['svHighTag'],
                                    loop['svLowTag']
                                ],
                                'tagValues': loopDetail['tagValues'] if agentInstalled else [
                                    loop['pvValue'],
                                    loop['svValue'],
                                    loop['mvValue'],
                                    loop['pValue'],
                                    loop['iValue'],
                                    loop['dValue'],
                                    loop['mvUpperLimitValue'],
                                    loop['mvLowerLimitValue'],
                                    loop['svUpperLimitValue'],
                                    loop['svLowerLimitValue'],
                                    loop['cascadeFlagValue'],
                                    loop['controlModeValue'],
                                    loop['controlDirectionValue'],
                                    loop['mvHighValue'],
                                    loop['mvLowValue'],
                                    loop['svHighValue'],
                                    loop['svLowValue']
                                ]
                            })
                        else:
                            loopList.append({
                                'id': loop['loopId' if agentInstalled else 'id'],
                                'loopName': loop['loopName'],
                                'loopDescription': loop['loopDescription' if agentInstalled else 'descript'],
                                'loopType': loop['loopType'],
                                'groupPath': loop['loopGroupNamePath'],
                                'groupName': loop['loopGroup'],
                                'groupId': loop['groupId'],
                                'advanceLoopType': loop['advanceLoopType'],
                                'cascadeLoopName': loop['cascadeLoopName'],
                                'masterOrSlave': loop['masterOrSlave'] == 0,
                                'suggestTuningMethod': suggestTuningMethod,
                                'currentPIDParam': loop['pidParaRealTimeValue']
                            })
            selectList = []
            unique_dict = {}
            for loop in loopList:
                unique_dict[loop['id']] = loop
            loopList = list(unique_dict.values())
            if len(loopList) == 0:
                if selectType == 1:
                    selectType = 0
                    params['loopDesc'] = []
                    if 'loopName' not in params or len(params['loopName']) == 0:
                        retries = 3
            retries = retries + 1
        if not queryingLoop:
            for group in groupList:
                selectList.append({"title": group['groupPath'], "data": group})
        else:
            for loop in loopList:
                selectList.append({"title": loop['loopName'], "data": loop})

        if queryingList:
            if not queryingLoop:
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"已找到{len(groupList)}个符合条件的装置。",
                    'title': None,
                    'description': None
                })
                retDict['queryGroupListResult'] = groupList
                retDict['resultField'] = 'queryGroupListResult'
                return retDict
            else:
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"已找到{len(loopList)}条符合条件的回路。",
                    'title': None,
                    'description': None
                })
                retDict['queryLoopWithTagValueListResult' if needTagValue else 'queryLoopWithoutTagValueListResult'] = loopList
                retDict['resultField'] = 'queryLoopWithTagValueListResult' if needTagValue else 'queryLoopWithoutTagValueListResult'
                return retDict

        if len(selectList) > 1:
            if queryingLoop and not agentInstalled:
                selectList.append({"title": "没有找到符合的回路", "data": -1})
            context.require_interaction({
                "id": "select",
                "title": "请选择",
                "type": "select",
                "select": selectList
            })
            return {}
        elif len(selectList) == 1:
            selectList[0]['data']['found'] = None
            if queryingLoop:
                if needTagValue:
                    loop = selectList[0]['data']
                    idx = 0
                    for v in loop['tagValues']:
                        if v is None:
                            loop['tagValues'][idx] = 0
                        idx = idx + 1
                    await context.call_tool("add_card", context=context, params={
                        'card_type': "card",
                        'content': f"""
回路{loop['loopName']} {loop['loopDescription'] if loop['loopDescription'] is not None else ""}，所属装置：{loop['groupPath']}


## 回路位号实时值

|参数名称|位号名|实时值|
| :----: | :----: | :----: |
|测量值|{loop['tags'][0]}|{loop['tagValues'][0]:.2f}|
|设定值|{loop['tags'][1]}|{loop['tagValues'][1]:.2f}|
|阀位值|{loop['tags'][2]}|{loop['tagValues'][2]:.2f}|
|比例|{loop['tags'][3]}|{loop['tagValues'][3]:.2f}|
|积分|{loop['tags'][4]}|{loop['tagValues'][4]:.2f}|
|微分|{loop['tags'][5]}|{loop['tagValues'][5]:.2f}|
|阀位值量程上限|{loop['tags'][6]}|{loop['tagValues'][6]:.2f}|
|阀位值量程下限|{loop['tags'][7]}|{loop['tagValues'][7]:.2f}|
|设定值量程上限|{loop['tags'][8]}|{loop['tagValues'][8]:.2f}|
|设定值量程下限|{loop['tags'][9]}|{loop['tagValues'][9]:.2f}|
|是否串级|{loop['tags'][10]}|{loop['tagValues'][10]}|
|控制模式|{loop['tags'][11]}|{loop['tagValues'][11]}|
|正反作用|{loop['tags'][12]}|{loop['tagValues'][12]}|
|设定值限幅高限|{loop['tags'][13]}|{loop['tagValues'][13]:.2f}|
|设定值限幅低限|{loop['tags'][14]}|{loop['tagValues'][14]:.2f}|
|阀位值限幅高限|{loop['tags'][15]}|{loop['tagValues'][15]:.2f}|
|阀位值限幅低限|{loop['tags'][16]}|{loop['tagValues'][16]:.2f}|
""",
                        'title': "回路位号信息详细数据",
                        'description': ""
                    })
                loopTypeMap = ['流量', '压力', '液位', '温度', '质量', '其他', '浓度']
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"""
回路{selectList[0]['data']['loopName']}为{loopTypeMap[selectList[0]['data']['loopType']]}控制回路，所属装置为{selectList[0]['data']['groupPath']}。您可查看其详细信息，包括设定值（SV）、测量值（PV）、阀位值（MV）、量程以及PID等参数。
""",
                    'title': None,
                    'description': None
                })
            if queryingLoop:
                retDict['queryLoopWithTagValueSingleResult' if needTagValue else 'queryLoopWithoutTagValueSingleResult'] = selectList[0]["data"]
                retDict['resultField'] = 'queryLoopWithTagValueSingleResult' if needTagValue else 'queryLoopWithoutTagValueSingleResult'
            else:
                retDict['queryGroupSingleResult'] = selectList[0]["data"]
                retDict['resultField'] = 'queryGroupSingleResult'
            return retDict
        else:
            if not agentInstalled and queryingLoop:
                params['loopName'] = params['loopName'][0] if len(params['loopName']) > 0 else ''
                params['loopDesc'] = params['loopDesc'][0] if len(params['loopDesc']) > 0 else ''
                await loop_auto_config(context, params)
                return {}
            else:
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"找不到名为{params['loopName'] if queryingLoop else params['groupPath'][0]}的回路/装置。",
                    'title': None,
                    'description': None
                })
                retDict['queryLoopNotFoundResult'] = {
                    "missingLoopOrGroup": f"找不到名为{params['loopName'] if queryingLoop else params['groupPath'][0]}的回路/装置。",
                    "found": False,
                    "id": -1,
                    "loopName": params['loopName'][0] if 'loopName' in params and len(params['loopName']) > 0 else '',
                    "groupId": -1
                }
                retDict['resultField'] = 'queryLoopNotFoundResult'
                return retDict
    else:
        if queryingLoop:
            loop = selectResult[0]['data']
            if loop == -1:
                params['loopName'] = params['loopName'][0]
                params['loopDesc'] = ''
                await loop_auto_config(context, params)
                return {}
            if 'tagValues' in loop:
                idx = 0
                for v in loop['tagValues']:
                    if v is None:
                        loop['tagValues'][idx] = 0
                    idx = idx + 1
            if needTagValue:
                await context.call_tool("add_card", context=context, params={
                    'card_type': "card",
                    'content': f"""
回路{loop['loopName']}锅炉水流量，所属装置：{loop['groupPath']}


## 回路位号实时值

|参数名称|位号名|实时值|
| :----: | :----: | :----: |
|测量值|{loop['tags'][0]}|{loop['tagValues'][0]:.2f}|
|设定值|{loop['tags'][1]}|{loop['tagValues'][1]:.2f}|
|阀位值|{loop['tags'][2]}|{loop['tagValues'][2]:.2f}|
|比例|{loop['tags'][3]}|{loop['tagValues'][3]:.2f}|
|积分|{loop['tags'][4]}|{loop['tagValues'][4]:.2f}|
|微分|{loop['tags'][5]}|{loop['tagValues'][5]:.2f}|
|阀位值量程上限|{loop['tags'][6]}|{loop['tagValues'][6]:.2f}|
|阀位值量程下限|{loop['tags'][7]}|{loop['tagValues'][7]:.2f}|
|设定值量程上限|{loop['tags'][8]}|{loop['tagValues'][8]:.2f}|
|设定值量程下限|{loop['tags'][9]}|{loop['tagValues'][9]:.2f}|
|是否串级|{loop['tags'][10]}|{loop['tagValues'][10]}|
|控制模式|{loop['tags'][11]}|{loop['tagValues'][11]}|
|正反作用|{loop['tags'][12]}|{loop['tagValues'][12]}|
|设定值限幅高限|{loop['tags'][13]}|{loop['tagValues'][13]:.2f}|
|设定值限幅低限|{loop['tags'][14]}|{loop['tagValues'][14]:.2f}|
|阀位值限幅高限|{loop['tags'][15]}|{loop['tagValues'][15]:.2f}|
|阀位值限幅低限|{loop['tags'][16]}|{loop['tagValues'][16]:.2f}|
""",
                    'title': "回路位号信息详细数据",
                    'description': ""
                })
            loopTypeMap = ['流量', '压力', '液位', '温度', '质量', '其他', '浓度']
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
回路{selectResult[0]['data']['loopName']}为{loopTypeMap[selectResult[0]['data']['loopType']]}控制回路，所属装置为{selectResult[0]['data']['groupPath']}。您可查看其详细信息，包括设定值（SV）、测量值（PV）、阀位值（MV）、量程以及PID等参数。
""",
                'title': None,
                'description': None
            })
            retDict['queryLoopWithTagValueSingleResult' if needTagValue else 'queryLoopWithoutTagValueSingleResult'] = selectResult[0]["data"]
            retDict['resultField'] = 'queryLoopWithTagValueSingleResult' if needTagValue else 'queryLoopWithoutTagValueSingleResult'
        else:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
已查询到装置{selectResult[0]['data']['groupPath']}。
""",
                'title': None,
                'description': None
            })
            retDict['queryGroupSingleResult'] = selectResult[0]["data"]
            retDict['resultField'] = 'queryGroupSingleResult'
        return retDict


@tool(version="*")
async def loop_auto_config(context: Context, params: any):
    agentInstalled = context.call_tool("is_agent_installed", context=context)
    if agentInstalled:
        return {}
    if 'forDelete' in params and params['forDelete']:
        response = requests.get(
            url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/delByName?loopName={params['loopName']}"
            , headers={
                'Content-Type': 'application/json',
                'Authorization': context.config["inner_token"],
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': f'tenant-id={context.tenant_id}'
            })
        return {
            'msg': "",
            'failed': not json.loads(response.text)['content']
        }
    formResult = await context.get_interaction("auto_config_form")
    if formResult is None:
        response = requests.get(url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/template/list"
                                , headers={
                'Content-Type': 'application/json',
                'Authorization': context.config["inner_token"],
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': f'tenant-id={context.tenant_id}'
            })
        templateList = json.loads(response.text)['content']
        templateDropdownList = []
        for template in templateList:
            templateDropdownList.append(str(template['id']) + "-" + template['dcsTemplateName'])
        await context.call_tool("add_card", context=context, params={
            'card_type': "markdown",
            'content': "当前系统中未检测到此回路，请再次确认回路相关信息，我将再一次进行匹配查找",
            'title': None,
            'description': None
        })
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '需要确认您指定的回路，请补充信息',
                "content": "当前系统中未检测到此回路，请再次确认回路相关信息，我将再一次进行匹配查找",
                "description": "",
                "details": ""
            }
        })
        context.require_interaction({
            "id": "auto_config_form",
            "title": "请完善以下内容",
            "type": "form",
            "form": {
                "schema": {
                    "definitions": {
                        "templates": {
                            "enum": templateDropdownList
                        },
                        "loopTypes": {
                            "enum": [
                                "0-流量",
                                "1-压力",
                                "2-液位",
                                "3-温度",
                                "4-质量",
                                "5-其他"
                                # {
                                #     "name": "流量",
                                #     "value": 0
                                # },
                                # {
                                #     "name": "压力",
                                #     "value": 1
                                # },
                                # {
                                #     "name": "液位",
                                #     "value": 2
                                # },
                                # {
                                #     "name": "温度",
                                #     "value": 3
                                # },
                                # {
                                #     "name": "质量",
                                #     "value": 4
                                # },
                                # {
                                #     "name": "其他",
                                #     "value": 5
                                # }
                            ]
                        }
                    },
                    "type": "object",
                    "description": "",
                    "properties": {
                        "template": {
                            "title": "回路所属DCS设备",
                            "$ref": "#/definitions/templates"
                        },
                        "groupPath": {
                            "title": "回路所属装置",
                            "type": "string"
                        },
                        "loopName": {
                            "title": "回路名称",
                            "type": "string",
                            "pattern": "\\S"
                        },
                        "loopType": {
                            "title": "回路类型",
                            "$ref": "#/definitions/loopTypes"
                        },
                        "loopDesc": {
                            "title": "回路描述",
                            "type": "string"
                        }
                    },
                    "required": [
                        "loopName",
                        "loopType",
                        "groupPath",
                        "template"
                    ]
                },
                "default": {
                    "loopName": params['loopName'],
                    "loopDesc": params['loopDesc'],
                    "loopType": "0-流量",
                    "template": templateDropdownList[0],
                    "groupPath": "氯碱装置"
                }
            }
        })
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': f"""
<iframe src="/xpt-tpt-pid/loopConfig" width="100%" height="960px"></iframe>
""",
            'title': "点击查看回路列表",
            'description': None
        })
        return {}
    else:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "请稍作等待，后续需要您进行相关信息确认",
                "description": "",
                "details": ""
            }
        })
        loopName = formResult['loopName']
        loopDesc = formResult['loopDesc']
        groupPath = formResult['groupPath']
        templateId = formResult['template'].split("-")[0]
        loopType = formResult['loopType'].split("-")[0]
        await context.log_info(f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/add")
        response = requests.post(url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/add"
                                 , json={
                'loopName': loopName,
                'descript': loopDesc,
                'loopType': int(loopType),
                'loopProperty': 1,
                'dcsEquipmentId': int(templateId),
                'groupNamePath': groupPath,
                'datasourceId': 1,
                'samplingTime': 5,
                'steadyStateTime': 10
            }
                                 , headers={
                'Content-Type': 'application/json',
                'Authorization': context.config["inner_token"],
                'Cookie': f'tenant-id={context.tenant_id}',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })
        addResult = json.loads(response.text)
        await context.log_info(f"{addResult}")
        if addResult['success']:
            response = requests.get(
                url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/info/getfatdto?loopId={addResult['content']['id']}"
                , headers={
                    'Content-Type': 'application/json',
                    'Authorization': context.config["inner_token"],
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Cookie': f'tenant-id={context.tenant_id}'
                })
            fatDto = json.loads(response.text)['content']
            return {
                'groupName': fatDto['loopGroup'],
                'groupPath': fatDto['loopGroupNamePath'],
                'id': fatDto['id'],
                'groupId': fatDto['groupId'],
                'loopName': fatDto['loopName'],
                'advanceLoopType': fatDto['advanceLoopType'],
                'cascadeLoopName': fatDto['cascadeLoopName'],
                'masterOrSlave': fatDto['masterOrSlave'] is not None and fatDto['masterOrSlave'] == 0,
                'suggestTuningMethod': 0,
                'currentPIDParam': fatDto['pidParaRealTimeValue'],
                'fromCache': addResult['content']['fromCache']
            }
        else:
            return {
                "failed": True,
                "msg": f"回路{loopName}创建失败！"
            }


@tool(version="*")
async def get_loop_detail(context: Context, params: any):
    project_id = context.call_tool("get_project_id", context=context)
    loopId = params['loopId']

    if loopId == -1:
        return {
            'tagValues': [
                0, 0, 0, 0, 0, 0
            ],
            'algAdvice': 2,
            'loopName': '[离线回路]',
            'dcsEquipmentName': '[未知]'
        }

    response = requests.get(
        url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loop/info/getfatdto?loopId={loopId}" if context.call_tool("is_agent_installed", context=context)
        else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/loop/info/getfatdto?loopId={loopId}&withValue=true"
        , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    loopDetail = json.loads(response.text)['data'] if context.call_tool("is_agent_installed", context=context) else json.loads(response.text)[
        'content']
    if 'loopName' not in loopDetail:
        raise ValueError(f'获取回路详细信息失败！错误消息：{response.text}')
    return {
        'loopName': loopDetail['loopName'],
        'description': loopDetail['loopDescription' if context.call_tool("is_agent_installed", context=context) else 'descript'],
        'loopType': loopDetail['loopType'],
        'pidParams': loopDetail['pidParaNowTimeValue' if context.call_tool("is_agent_installed", context=context) else 'pidParaRealTimeValue'],
        'dcsEquipmentName': loopDetail['loopDcsEquipmentName' if context.call_tool("is_agent_installed", context=context) else 'dcsEquipmentName'],
        'tags': [
            loopDetail['pvTag'],
            loopDetail['svTag'],
            loopDetail['mvTag'],
            loopDetail['pTag'],
            loopDetail['iTag'],
            loopDetail['dTag'],
            loopDetail['mvUpperLimitTag'],
            loopDetail['mvLowerLimitTag'],
            loopDetail['svUpperLimitTag'],
            loopDetail['svlowerLimitTag' if context.call_tool("is_agent_installed", context=context) else 'svLowerLimitTag'],
            loopDetail['cascadeFlagTag'],
            loopDetail['controlModeTag'],
            loopDetail['controlDirectionTag'],
            loopDetail['mvHighTag'],
            loopDetail['mvLowTag'],
            loopDetail['svHighTag'],
            loopDetail['svLowTag']
        ],
        'tagValues': [
            loopDetail['pvValue'],
            loopDetail['svValue'],
            loopDetail['mvValue'],
            loopDetail['pValue'],
            loopDetail['iValue'],
            loopDetail['dValue'],
            loopDetail['mvUpperLimitValue'],
            loopDetail['mvLowerLimitValue'],
            loopDetail['svUpperLimitValue'],
            loopDetail['svLowerLimitValue'],
            loopDetail['cascadeFlagValue'],
            loopDetail['controlModeValue'],
            loopDetail['controlDirectionValue'],
            loopDetail['mvHighValue'],
            loopDetail['mvLowValue'],
            loopDetail['svHighValue'],
            loopDetail['svLowValue']
        ]
    }


"""
回路指标历史数据库查询
"""
@tool(version="*")
async def get_loop_2h_report(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    formResult = await context.get_interaction("loop2h_form")
    if (not ('startTime' in params) or not ('endTime' in params)) and formResult is None:
        context.require_interaction({
            "id": "loop2h_form",
            "title": "请输入以下内容",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "请输入查询的起止时间 yyyy-MM-dd HH:mm:ss格式",
                    "properties": {
                        "startTime": {
                            "title": "开始时间",
                            "type": "string"
                        },
                        "endTime": {
                            "title": "截止时间",
                            "type": "string"
                        }
                    },
                    "required": [
                        "startTime",
                        "endTime"
                    ]
                },
                "default": {
                }
            }
        })
        return {}
    if (not ('startTime' in params) or not ('endTime' in params)) and formResult is not None:
        params['startTime'] = formResult['startTime']
        params['endTime'] = formResult['endTime']
    project_id = context.call_tool("get_project_id", context=context)
    response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/config/node/all"
                            , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    groupTree = json.loads(response.text)['data']
    groupId = groupTree['id']
    loopNameConditions = []
    for loopName in params['loopName']:
        loopNameConditions.append({
            'field': 'loopName',
            'value': loopName,
            'operator': '='
        })
    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/loop/report/advance"
                             , json={
            'clazz': 0,
            'current': 1,
            'groupId': groupId,
            'orList': loopNameConditions,
            'reportType': params['reportType'],
            'size': 200,
            'timeList': [params['startTime'], params['endTime']]
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    reportList = json.loads(response.text)['list']
    return {"reportList": reportList}


"""
装置指标历史数据库查询
"""
@tool(version="*")
async def get_group_2h_report(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    formResult = await context.get_interaction("group2h_form")
    if (not ('startTime' in params) or not ('endTime' in params)) and formResult is None:
        context.require_interaction({
            "id": "group2h_form",
            "title": "请输入以下内容",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "请输入查询的起止时间 yyyy-MM-dd HH:mm:ss格式",
                    "properties": {
                        "startTime": {
                            "title": "开始时间",
                            "type": "string"
                        },
                        "endTime": {
                            "title": "截止时间",
                            "type": "string"
                        }
                    },
                    "required": [
                        "startTime",
                        "endTime"
                    ]
                },
                "default": {
                }
            }
        })
        return {}
    if (not ('startTime' in params) or not ('endTime' in params)) and formResult is not None:
        params['startTime'] = formResult['startTime']
        params['endTime'] = formResult['endTime']
    project_id = context.call_tool("get_project_id", context=context)
    groupId = params['groupId']
    if 'reportType' in params and params['reportType'] > 1:
        params['reportType'] = 1
    response = requests.post(
        url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/evaluation/report/getcyclegroupreport"
        , json={
            'comprehensiveClass': 0,
            'current': 1,
            'groupId': groupId,
            'size': 20,
            'statiMethod': params['reportType'],
            'timeList': [params['startTime'], params['endTime']]
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    reportList = json.loads(response.text)['list']
    return {
        'reportList': reportList
    }


"""
装置历史自控率、平稳率计算
"""
@tool(version="*")
async def get_group_2h_close_stable_rate(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    if not ('startTime' in params) or not ('endTime' in params):
        params['endTime'] = datetime.datetime.fromtimestamp(datetime.datetime.now().timestamp()).strftime(
            '%Y-%m-%d %H:%M:%S')
        params['startTime'] = datetime.datetime.fromtimestamp(datetime.datetime.now().timestamp() - 7200).strftime(
            '%Y-%m-%d %H:%M:%S')

    reportList = get_group_2h_report(context, params)
    if len(reportList) == 0:
        raise ValueError("Cannot find any group report!")
    return {
        'closeRate': reportList[0]['closeRate'],
        'stableRate': reportList[0]['stableRate']
    }


"""
人工整定结果查询
"""
@tool(version="*")
async def get_manual_tuning_record(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    project_id = context.call_tool("get_project_id", context=context)
    response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/config/node/all"
                            , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    groupTree = json.loads(response.text)['data']
    groupId = groupTree['id']
    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/artificial/tuning/get"
                             , json={
            'loopName': params['loopName'],
            'current': 1,
            'groupId': groupId,
            'size': 20,
            'loopType': -1
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    recordList = json.loads(response.text)['list']
    if len(recordList) == 0:
        raise ValueError(f"Cannot find manual tuning record of loop {params['loopName']}!")
    record = None
    for rec in recordList:
        if record['loopName'] == params['loopName']:
            record = rec
            break
    if record is None:
        raise ValueError(f"Loop {params['loopName']} not exist!")
    return {
        'loopName': record['loopName'],
        'pidNow': record['pidNow'],
        'pidSuggest': record['pidSuggest'],
        'tuningTime': record['tuningTime'],
        'grade': record['grade'] * 5,
        'dataInterval': record['dataInterval']
    }


"""
回路运行实时状态计算
"""
@tool(version="*")
async def get_loop_realtime_state(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    project_id = context.call_tool("get_project_id", context=context)
    groupId = None
    if 'groupId' in params:
        groupId = params['groupId']
    else:
        response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/config/node/all"
                                , headers={
                'Content-Type': 'application/json',
                'Authorization': context.config["inner_token"],
                'Project': str(project_id),
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })
        groupTree = json.loads(response.text)['data']
        groupId = groupTree['id']
    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/evaluation/realtime/loopdetail"
                             , json={
            'loopName': params['loopName'],
            'current': 1,
            'groupId': groupId,
            'pageSize': 200
        } if 'loopName' in params else {
            'current': 1,
            'groupId': groupId,
            'pageSize': 3000
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    recordList = json.loads(response.text)['list']
    if len(recordList) == 0:
        # raise ValueError(f"Cannot find the realtime state of loop {params['loopName']}!")
        return {
            'resultList': [{
                'loopName': params['loopName']if 'loopName' in params else '',
                'description': '',
                'selfControlStatus': '手动',
                'realtimeStableStatus': '平稳',
                'rejectCondition': '',
                'runningState': '正常运行'
            }]
        }
    if 'loopName' in params:
        record = None
        for rec in recordList: # 服务端返回的是模糊匹配的查询结果，但能力层这边的需求是全字匹配，所以用for循环来筛选符合名称的回路
            if rec['loopName'] == params['loopName']:
                record = rec
                break
        if record is None:
            raise ValueError(f"Loop {params['loopName']} not exist!")
        realtimeClosedStatus = "手动"
        if record['realtimeClosedStatus'] == 1:
            realtimeClosedStatus = "自动"
        elif record['realtimeClosedStatus'] == -1:
            realtimeClosedStatus = "异常"
        elif record['realtimeClosedStatus'] == -2:
            realtimeClosedStatus = "条件剔除"
        realtimeStableStatus = "波动"
        if record['realtimeStableStatus'] == 1:
            realtimeStableStatus = "平稳"
        elif record['realtimeStableStatus'] == -1:
            realtimeStableStatus = "异常"
        elif record['realtimeStableStatus'] == -2:
            realtimeStableStatus = "条件剔除"
        runningState = "正常运行" if (realtimeClosedStatus == "自动" or realtimeClosedStatus == "手动") and (
                    realtimeStableStatus == "平稳" or realtimeStableStatus == "波动") else "异常"
        if realtimeClosedStatus == "条件剔除" or realtimeStableStatus == "条件剔除":
            runningState = "条件剔除"
        return {
            "resultList": [{
                'loopName': record['loopName'],
                'description': record['desc'],
                'selfControlStatus': realtimeClosedStatus,
                'realtimeStableStatus': realtimeStableStatus,
                'rejectCondition': record['rejectCondition'],
                'runningState': runningState
            }]
        }
    else:
        resultList = []
        for record in recordList:
            realtimeClosedStatus = "手动"
            if record['realtimeClosedStatus'] == 1:
                realtimeClosedStatus = "自动"
            elif record['realtimeClosedStatus'] == -1:
                realtimeClosedStatus = "异常"
            elif record['realtimeClosedStatus'] == -2:
                realtimeClosedStatus = "条件剔除"
            realtimeStableStatus = "波动"
            if record['realtimeStableStatus'] == 1:
                realtimeStableStatus = "平稳"
            elif record['realtimeStableStatus'] == -1:
                realtimeStableStatus = "异常"
            elif record['realtimeStableStatus'] == -2:
                realtimeStableStatus = "条件剔除"
            runningState = "正常运行" if (realtimeClosedStatus == "自动" or realtimeClosedStatus == "手动") and (
                    realtimeStableStatus == "平稳" or realtimeStableStatus == "波动") else "异常"
            if realtimeClosedStatus == "条件剔除" or realtimeStableStatus == "条件剔除":
                runningState = "条件剔除"
            resultList.append({
                'loopName': record['loopName'],
                'description': record['desc'],
                'selfControlStatus': realtimeClosedStatus,
                'realtimeStableStatus': realtimeStableStatus,
                'rejectCondition': record['rejectCondition'],
                'runningState': runningState
            })
        return {
            'resultList': resultList
        }


@tool(version="*")
async def imc_tuning(context: Context, params: any):
    project_id = context.call_tool("get_project_id", context=context)
    loopId = params['loopId']
    loopDetail = await get_loop_detail(context, params)
    agentInstalled = context.call_tool("is_agent_installed", context=context)
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "感谢您对本任务信息的完善，我将全力执行任务。",
            "description": "",
            "details": ""
        }
    })
    await context.call_tool("add_card", context=context, params={
        'card_type': "summary",
        'content': f"""
若{params['loopName']}回路的历史数据完整且正常，那么我将基于历史数据识别系统行为模式并建立数学模型。基于模型精确评估现有参数效果并确定优化方向，确保整定后的参数能够使控制系统在稳定性、响应速度和准确性方面达到最佳平衡，提高控制品质。
""",
        'title': None,
        'description': None
    })
    startTime = datetime.datetime.strptime(params['startTime'], '%Y-%m-%d %H:%M:%S').timestamp()
    endTime = datetime.datetime.strptime(params['endTime'], '%Y-%m-%d %H:%M:%S').timestamp()

    if await context.get_cache("data_validation_file_id") is not None:
        params['fileId'] = int(await context.get_cache("data_validation_file_id"))
        await context.log_info(f"fileId: {params['fileId']}")

    await context.log_info("开始调用imc整定算法接口")
    response = requests.post(
        url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/pro/tuning/imc" if agentInstalled else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/tuning/imc"
        , json={
            'algType': 3,
            'controlDirection': 9999,
            'controlMode': 1,
            'controllerMode': 1,
            'delayIntervalLower': 0,
            'delayIntervalUpper': 3,
            'hisDataSegment': [startTime * 1000, endTime * 1000],
            'integralFlag': 0,
            'lambdaSpeedIndex': 0.5,
            'loopMainId': loopId,
            'modelOrder': 1,
            'pidType': 2,
            'timeSegments': [],
            'userModelEnable': 0,
            'userModelPara': [0, 0, 0, 0],
            'userPidEnable': 0,
            'userPidPara': [0, 0, 0]
        } if agentInstalled else {
            'algType': 3,
            'controlDirection': 9999,
            'controlMode': 1,
            'controllerMode': 1,
            'delayIntervalLower': 0,
            'delayIntervalUpper': 3,
            'hisDataSegment': [startTime * 1000, endTime * 1000],
            'integralFlag': 0,
            'lambdaSpeedIndex': 0.5,
            'loopMainId': loopId,
            'modelOrder': 1,
            'pidType': 2,
            'timeSegments': [],
            'userModelEnable': 0,
            'userModelPara': [0, 0, 0, 0],
            'userPidEnable': 0,
            'userPidPara': [0, 0, 0],
            'fileId': params['fileId'] if 'fileId' in params else 0
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'Cookie': f'tenant-id={context.tenant_id}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    await context.log_info("完成调用imc整定算法接口")
    resObj = json.loads(response.text)
    tuningResult = resObj['data' if agentInstalled else 'content']
    if tuningResult is None:
        raise ValueError(resObj['message' if agentInstalled else 'msg'])

    if not agentInstalled:
        reliabilityIndex = tuningResult['reliabilityIndex']
        if reliabilityIndex is not None and reliabilityIndex <= 0.2:
            tuningResult['pidParams'] = ['-', '-', '-']
        await context.set_cache("imc_tuning_result", tuningResult)
        loopType = tuningResult['loopType']
        if tuningResult['sessionId'] is not None:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
基于整定后的PID参数进行仿真，获取闭环阶跃响应仿真曲线：
""",
                'title': None,
                'description': None
            })
            await context.call_tool("add_card", context=context, params={
                'card_type': "page",
                'content': f"/xpt-tpt-pid/echartssimulation?loopId={tuningResult['sessionId']}&type=view",
                'title': "仿真曲线预览页面",
                'description': None
            })
            # context.require_interaction({
            #     "id": "imc_tuning_sim_curve_preview",
            #     "title": "仿真曲线预览页面",
            #     "type": "open_page",
            #     "open_page": f"http://{context.config['core_frontend_host']}/xpt-tpt-pid/echartssimulation?loopId={tuningResult['sessionId']}",
            #     "page_type": "execute"
            # })
            reliabilityIndex = tuningResult['reliabilityIndex']
            if tuningResult['pidParams'] is None or tuningResult['pidParams'] == ['-', '-', '-']:
                displayedMsg = tuningResult['message'] if 'message' in tuningResult else ''
                errorCode = ''
                if 'message' in tuningResult and tuningResult['message'] is not None:
                    displayedMsg = tuningResult['message'][9:]
                    errorCode = tuningResult['message'][:8]
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary",
                    'content': f"未能计算出PID参数或置信度很低，原因：{displayedMsg}",
                    'title': None,
                    'description': None
                })
                if errorCode == 'e1080801':
                    await context.call_tool("add_card", context=context, params={
                        'card_type': "summary_file",
                        'content': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/file/static?filename=PID%20阶跃测试步骤指导.pdf",
                        'title': "PID 阶跃测试步骤指导",
                        'description': ""
                    })
            if tuningResult['pidParams'] is not None:
                if tuningResult['sessionId'] is not None:
                    await context.call_tool("add_card", context=context, params={
                        'card_type': "summary_file",
                        'content': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/tuning/getTuningReportWordDocument?loopId={loopId}&pidSessionId={tuningResult['sessionId']}&tuningType=0",
                        'title': f"{loopDetail['loopName']} 多策略自适应参数整定报告",
                        'description': f""
                    })

            await print_imc_tuning_suggest(loopType, reliabilityIndex, context, tuningResult)
        else:
            displayedMsg = tuningResult['message'] if 'message' in tuningResult else ''
            errorCode = ''
            if 'message' in tuningResult and tuningResult['message'] is not None:
                displayedMsg = tuningResult['message'][9:]
                errorCode = tuningResult['message'][:8]
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"未能计算出PID参数或置信度很低，原因：{displayedMsg}",
                'title': None,
                'description': None
            })
            if errorCode == 'e1080801':
                await context.call_tool("add_card", context=context, params={
                    'card_type': "summary_file",
                    'content': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/file/static?filename=PID%20阶跃测试步骤指导.pdf",
                    'title': "PID 阶跃测试步骤指导",
                    'description': ""
                })
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "任务完成",
                "description": "",
                "details": ""
            }
        })
        return {
            'excitCoef': tuningResult['excitCoef'],
            'disturbCoef': tuningResult['disturbCoef'],
            'nonlinearCoef': tuningResult['nonlinearCoef'],
            'pidParams': tuningResult['pidParams'],
            'rDelay': tuningResult['rDelay'],
            'rGain': tuningResult['rGain'],
            'rT1': tuningResult['rT1'],
            'rT2': tuningResult['rT2'],
            'reliabilityIndex': reliabilityIndex,
            'loopType': loopType
        }
    else:
        loopType = 0
        reliabilityIndex = tuningResult['reliabilityIndex']
        if reliabilityIndex is not None and reliabilityIndex <= 0.2:
            tuningResult['pidParams'] = ['-', '-', '-']
        await print_imc_tuning_suggest(loopType, reliabilityIndex, context, tuningResult)
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "任务完成",
                "description": "",
                "details": ""
            }
        })
        return {
            'excitCoef': tuningResult['excitCoef'],
            'disturbCoef': tuningResult['disturbCoef'],
            'nonlinearCoef': tuningResult['nonlinearCoef'],
            'pidParams': tuningResult['pidParams'],
            'rDelay': tuningResult['rDelay'],
            'rGain': tuningResult['rGain'],
            'rT1': tuningResult['rT1'],
            'rT2': tuningResult['rT2'],
            'reliabilityIndex': reliabilityIndex,
            'loopType': loopType
        }


async def print_imc_tuning_suggest(loopType, reliabilityIndex, context, tuningResult):
    if loopType == 0:
        if reliabilityIndex > 0.6:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用流量对象的数据，进行智能化数据建模，成功构建了回路模型，模型评价为优秀。从中提取出关键参数：模型增益{tuningResult['rGain']}，时间常数{tuningResult['rT1']}分钟。根据模型的增益数据，比例作用应较弱，以免引起系统振荡或不稳定。同时为保证系统的响应速率，应使用较强的积分作用，以加速阀门的动作变化。此外，回路的滞后时间较短，因此无需引入微分作用。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        elif reliabilityIndex > 0.2:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用流量对象的数据进行智能化建模时，受数据质量限制，构建的回路模型精度不足，模型评价为中良。从中提取的关键参数（模型增益{tuningResult['rGain']}、时间常数{tuningResult['rT1']}分钟）可能存在偏差。由于模型增益数据的置信度不高，需适当弱比例作用强度，以维持基础控制稳定性，可能会使回路响应速度变慢。积分作用应加强，以防因模型辨识数据不佳造成回路参数无法满足基本控制需求。此外，模型对滞后时间的表征不准确，但根据流量回路特性暂不进行调整。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        else:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
基于流量对象历史数据开展的智能化建模工作，经模型评估显示模型评价为差。经诊断分析，根本原因在于控制回路的操纵变量（MV）其变化幅度过小或频次过低，导致数据驱动建模方法无法有效捕捉系统动态特性。当前建立的回路模型因缺乏足够的激励信号，暂不具备参数整定所需的数据支撑条件。建议您使用其他的整定方法进行整定或提供具有阶跃测试的数据。
""",
                'title': None,
                'description': None
            })
    elif loopType == 2:
        if reliabilityIndex > 0.6:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用液位对象的数据，进行智能化数据建模，成功构建了回路模型，模型评价为优秀。从中提取出关键参数：模型增益{tuningResult['rGain']}，时间常数{tuningResult['rT1']}分钟。鉴于液位回路的模型增益数据，比例作用应较强，以克服外界扰动。同时，根据回路的时间常数，为避免阀门的过度变化，应减弱积分作用。此外，回路的滞后时间较短，因此无需引入微分作用，微分时间应为0。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        elif reliabilityIndex > 0.2:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用液位对象的数据进行智能化建模时，受数据质量限制，构建的回路模型精度不足，模型评价为中良。从中提取的关键参数（模型增益{tuningResult['rGain']}、时间常数{tuningResult['rT1']}分钟）可能存在偏差。由于模型增益数据的置信度不高，需谨慎加强比例作用强度，以防止对外界扰动的抑制能力不足，但需结合实时反馈避免过度敏感。根据模型时间常数的潜在偏差，积分作用需适当减弱，以防阀门动作因模型误差累积而频繁振荡或超调。此外，模型对滞后时间的表征不准确，但根据液位回路特性暂不进行调整。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        else:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
基于液位对象历史数据开展的智能化建模工作，经模型评估显示模型评价为差。经诊断分析，根本原因在于控制回路的操纵变量（MV）其变化幅度过小或频次过低，导致数据驱动建模方法无法有效捕捉系统动态特性。当前建立的回路模型因缺乏足够的激励信号，暂不具备参数整定所需的数据支撑条件。建议您使用其他的整定方法进行整定或提供具有阶跃测试的数据。
""",
                'title': None,
                'description': None
            })
    elif loopType == 3:
        if reliabilityIndex > 0.6:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用温度对象的数据，进行智能化数据建模，成功构建了回路模型，模型评价为优秀。从中提取出关键参数：模型增益{tuningResult['rGain']}，时间常数{tuningResult['rT1']}分钟。鉴于温度回路的模型增益数据，比例作用应较强，应快速调整阀门开度，维持温度平稳，消除外界扰动。根据回路的时间常数，为避免阀门的过度变化，应减弱积分作用。此外，回路的滞后时间较短，因此无需引入微分作用，如若滞后依旧较大，可酌情添加5-20s的微分。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        elif reliabilityIndex > 0.2:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用温度对象的数据进行智能化建模时，受数据质量限制，构建的回路模型精度不足，模型评价为中良。从中提取的关键参数（模型增益{tuningResult['rGain']}、时间常数{tuningResult['rT1']}分钟）可能存在偏差。由于模型增益数据的置信度不高，需谨慎加强比例作用强度，以避免过度敏感，造成回路发散。根据模型时间常数的潜在偏差，积分作用需适当减弱，以防阀门动作因模型误差累积而频繁振荡或超调。此外，模型对滞后时间的表征不准确，但避免微分造成阀门高频抖动，暂不引入微分。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        else:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
基于温度对象历史数据开展的智能化建模工作，经模型评估显示模型评价为差。经诊断分析，根本原因在于控制回路的操纵变量（MV）其变化幅度过小或频次过低，导致数据驱动建模方法无法有效捕捉系统动态特性。当前建立的回路模型因缺乏足够的激励信号，暂不具备参数整定所需的数据支撑条件。建议您使用其他的整定方法进行整定或提供具有阶跃测试的数据。
""",
                'title': None,
                'description': None
            })
    elif loopType == 1:
        if reliabilityIndex > 0.6:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用压力对象的数据，进行智能化数据建模，成功构建了回路模型，模型评价为优秀。从中提取出关键参数：模型增益{tuningResult['rGain']}，时间常数{tuningResult['rT1']}分钟。若压力为非积分模型，需要比例增益减小弱化噪声对系统的影响，缩短积分时间（即增大积分作用），使得积分项更快地累积误差，从而加快系统的响应。若压力为积分模型，需要比例增益增强以克服外界干扰对系统的影响，加长积分时间（即减小积分作用），使得积分不超调，从而加快系统的响应。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        elif reliabilityIndex > 0.2:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用压力对象的数据进行智能化建模时，受数据质量限制，构建的回路模型精度不足，模型评价为中良关键参数（模型增益{tuningResult['rGain']}、时间常数{tuningResult['rT1']}分钟）可能存在偏差；针对非积分模型，比例增益需弱化，以维持基础控制稳定性，积分时间适度缩短以此来加速响应；针对积分模型，比例增益以保守克服干扰，根据模型时间常数的潜在偏差，积分作用需适当减弱，以防阀门动作因模型误差累积而频繁振荡或超调。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        else:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
基于压力对象历史数据开展的智能化建模工作，经模型评估显示模型评价为差。经诊断分析，根本原因在于控制回路的操纵变量（MV）其变化幅度过小或频次过低，导致数据驱动建模方法无法有效捕捉系统动态特性。当前建立的回路模型因缺乏足够的激励信号，暂不具备参数整定所需的数据支撑条件。建议您使用其他的整定方法进行整定或提供具有阶跃测试的数据。
""",
                'title': None,
                'description': None
            })
    else:
        if reliabilityIndex > 0.6:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用控制对象的数据，进行智能化数据建模，成功构建了回路模型，模型评价为优秀。从中提取出关键参数：模型增益{tuningResult['rGain']}，时间常数{tuningResult['rT1']}分钟。鉴于回路的模型增益数据，比例作用应较强，以克服外界扰动。同时，根据回路的时间常数，为避免阀门的过度变化，应减弱积分作用。此外，回路的滞后时间较短，因此无需引入微分作用，微分时间应为0。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        elif reliabilityIndex > 0.2:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
利用控制回路的数据进行智能化建模时，受数据质量限制，构建的回路模型精度不足，模型评价为中良。从中提取的关键参数（模型增益{tuningResult['rGain']}、时间常数{tuningResult['rT1']}分钟）可能存在偏差。由于模型增益数据的置信度不高，需谨慎加强比例作用强度，以防止对外界扰动的抑制能力不足，但需结合实时反馈避免过度敏感。根据模型时间常数的潜在偏差，积分作用需适当减弱，以防阀门动作因模型误差累积而频繁振荡或超调。此外，模型对滞后时间的表征不准确，但根据液位回路特性暂不进行调整。
综上所述，PID参数应为：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
""",
                'title': None,
                'description': None
            })
        else:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': f"""
基于控制回路的历史数据开展的智能化建模工作，经模型评估显示模型评价为差。经诊断分析，根本原因在于控制回路的操纵变量（MV）其变化幅度过小或频次过低，导致数据驱动建模方法无法有效捕捉系统动态特性。当前建立的回路模型因缺乏足够的激励信号，暂不具备参数整定所需的数据支撑条件。建议您使用其他的整定方法进行整定或提供具有阶跃测试的数据。
""",
                'title': None,
                'description': None
            })


@tool(version="*")
async def pre_tuning(context: Context, params: any):
    project_id = context.call_tool("get_project_id", context=context)
    loopId = params['loopId']
    loopDetail = await get_loop_detail(context, params)
    loopType = loopDetail['loopType']
    agentInstalled = context.call_tool("is_agent_installed", context=context)

    loopTypeMap = ['流量', '压力', '液位', '温度', '质量', '其他', '浓度']

    await context.call_tool("add_card", context=context, params={
        'card_type': "summary",
        'content': f"""
用户需整定回路。查询到目标回路（{loopDetail['loopName']}，{loopTypeMap[loopType]}回路），所属{loopDetail['dcsEquipmentName']}（P单位：比例度，I/D单位：秒）。因无运行数据，决定采用无数据的知识驱动型整定。将从装置经验库中寻找类似回路参数作为起点。参数适配性可能受限，新回路可投用此参数；已投用回路建议上传历史数据以进行更精准的整定。
""",
        'title': None,
        'description': None
    })

    response = requests.post(
        url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/pro/pretuning/params" if agentInstalled else f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/tuning/pre"
        , json={
            'loopMainId': loopId,
            'loopType': loopType,
            'smoothnessIndex': 0.5,
            'speedIndex': 0.5,
            'tuningLoopProperty': "1"
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'Cookie': f'tenant-id={context.tenant_id}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    tuningResult = json.loads(response.text)['data' if agentInstalled else 'content']
    if tuningResult['pidParams'] is None:
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': f"""
{loopDetail['loopName']}预整定失败，请检查量程上限是否大于量程下限，然后重试
""",
            'title': None,
            'description': None
        })
    else:
        if not agentInstalled:
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary_file",
                'content': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/tuning/getTuningReportWordDocument?loopId={loopId}&pidSessionId={tuningResult['sessionId']}&tuningType=1",
                'title': f"{loopDetail['loopName']} 知识驱动型参数整定报告",
                'description': f""
            })
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': f"""
鉴于{loopDetail['loopName']}{loopTypeMap[loopType]}回路缺乏运行数据，采用了知识驱动型PID参数整定方法，并依据经验库进行了初始参数设定。考虑到回路的具体应用情况，提供的初始PID参数旨在为系统提供一个稳定的运行起点。然而，若此回路已投入运行，强烈建议后续通过上传控制回路的实际运行数据（SV、PV和MV），进行更精确的历史数据驱动的参数整定，以优化控制效果。
初步整定得到的PID参数如下：
比例(P): {tuningResult['pidParams'][0]}(比例度)
积分时间(I): {tuningResult['pidParams'][1]}(秒)
微分时间(D): {tuningResult['pidParams'][2]}(秒)
请根据实际运行情况进行验证和调整。
""",
            'title': None,
            'description': None
        })
    return {
        'pidParams': tuningResult['pidParams']
    }


@tool(version="*")
async def batch_pre_tuning(context: Context, params: any):
    tuningResult = []
    openPageResult = await context.get_interaction("loop_batch_write_page")
    if openPageResult is None:
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': """
获取开车阶段所有回路的类型、装置、工艺段信息。分类后匹配经验库中相似回路，提取其历史相似参数作为起点，实现快速稳定开车控制。
""",
            'title': None,
            'description': None
        })
    sessionId = None
    if context.call_tool("is_agent_installed", context=context):
        tuningList = await loop_batch_write(context, params)
        for record in tuningList['successList']:
            pidParams = record['pidAfterWrite'].split("/")
            tuningResult.append({
                'loopName': record['loopName'],
                'pidParams': pidParams,
                'message': "整定成功"
            })
        for record in tuningList['failList']:
            tuningResult.append({
                'loopName': record['loopName'],
                'pidParams': ['-', '-', '-'],
                'message': "整定失败"
            })
    else:
        formResult = await context.get_interaction("tuning_file_form")
        if formResult is not None:
            uploadResult = await context.call_tool("tuning_file_upload", context=context, params=params)
            params['fileId'] = uploadResult['fileId']
        if 'fileId' in params:
            response = requests.post(url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/tuning/pre/batch"
                                     , json={
                    'fileId': str(params['fileId'])
                }
                                     , headers={
                    'Content-Type': 'application/json; charset=UTF-8',
                    'Authorization': context.config["inner_token"],
                    'Cookie': f'tenant-id={context.tenant_id}',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            tuningResult = json.loads(response.text)['content']['list']
            sessionId = json.loads(response.text)['content']['sessionId']
        elif 'loopIdList' in params and len(params['loopIdList']) > 0:
            response = requests.post(url=f"{context.config['minimal_host']}/inter-api/pid-configuration/v1/tuning/pre/batch"
                                     , json={
                    'loopIdList': params['loopIdList']
                }
                                     , headers={
                    'Content-Type': 'application/json; charset=UTF-8',
                    'Authorization': context.config["inner_token"],
                    'Cookie': f'tenant-id={context.tenant_id}',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            tuningResult = json.loads(response.text)['content']['list']
            sessionId = json.loads(response.text)['content']['sessionId']
        else:
            await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": "请按文件模板要求上传装置的回路列表数据。上传成功后，点击相应文件名即可查看数据。",
                    "description": "",
                    "details": ""
                }
            })
            await context.call_tool("add_card", context=context, params={
                'card_type': "summary",
                'content': "请按照文件模板上传装置的回路列表数据。我将根据回路类型、工艺特性等信息与装置经验库中的历史数据进行匹配，为当前所有回路提供一个合理的参数起点",
                'title': None,
                'description': None
            })
            params['fieldDescription'] = '回路列表文件(.csv)'
            params['templateList'] = [
                {
                    'label': "参数批量预设文件模板格式说明",
                    'url': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/tuning/fileTemplateInstructions?filename=参数批量预设文件模板格式说明.docx"
                },
                {
                    'label': "参数批量预设文件模板",
                    'url': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/tuning/hisFileTemplate?controlMode=3&filename=参数批量预设文件模板.csv"
                }
            ]
            await context.call_tool("tuning_file_upload", context=context, params=params)
            return {}
    if len(tuningResult) > 0:
        await context.call_tool("add_card", context=context, params={
            'card_type': "think",
            'content': """
|回路名称|整定后PID参数|整定消息|
| :----: | :----: | :----: |
""" + generate_batch_pretuning_form(tuningResult),
            'title': "输出列表",
            'description': "重整装置的参数批量整定结果"
        })
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary_file",
            'content': f"/xpt-tpt-pid/api/inter-api/pid-configuration/v1/tuning/getTuningReportWordDocument?loopId=0&pidSessionId={sessionId}&tuningType=2",
            'title': "重整装置的参数批量整定报告",
            'description': ""
        })
    await context.call_tool("add_card", context=context, params={
        'card_type': "summary",
        'content': """
根据开车阶段的需求，对所有相关回路进行了批量整定。首先依据回路类型和所属装置、工艺段对回路进行了分类，并与经验库中结构和工况相似的已投用回路进行了匹配。通过这种方式，为每个回路提取了最佳的历史整定参数，以确保在开车阶段能够快速实现稳定控制，减少现场调试时间并提升整体效率和系统稳定性。整定后的参数集已根据各类回路特性进行了优化，提供了合理的参数起点，具体包括比例(P)、积分时间(I)、微分时间(D)等关键参数的推荐值。这些参数旨在支持装置顺利开车，并建议在实际运行过程中根据具体情况进一步微调，以达到最优控制效果。
""",
        'title': None,
        'description': None
    })
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "任务完成",
            "description": "",
            "details": ""
        }
    })
    return {
        'resultList': tuningResult
    }


def generate_batch_pretuning_form(tuningResultList):
    ret = ""
    for tuningResult in tuningResultList:
        try:
            ret = ret + f"|{tuningResult['loopName']}|{tuningResult['pidParams'][0]}/{tuningResult['pidParams'][1]}/{tuningResult['pidParams'][2]}|{tuningResult['message']}|\r\n"
        except Exception as e:
            ret = ret + f"|{tuningResult['loopName']}|-/-/-||\r\n"
    return ret


@tool(version="*")
async def human_tuning(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    project_id = context.call_tool("get_project_id", context=context)
    loopId = params['loopId']
    if not ('startTime' in params) or not ('endTime' in params):
        endTime = datetime.datetime.now().timestamp() * 1000
        startTime = endTime - 3600000 * 6
    else:
        startTime = datetime.datetime.strptime(params['startTime'], '%Y-%m-%d %H:%M:%S').timestamp()
        endTime = datetime.datetime.strptime(params['endTime'], '%Y-%m-%d %H:%M:%S').timestamp()

    errorTol = 0.01
    loopDetail = await get_loop_detail(context, params)
    svsch = loopDetail['tagValues'][8]
    svscl = loopDetail['tagValues'][9]
    if svsch is not None and svscl is not None:
        errorTol = 0.01 * (svsch * svscl)
    elif loopDetail['tagValues'][0] is not None:
        errorTol = 0.01 * loopDetail['tagValues'][0]

    if 'errorTol' in params:
        errorTol = params['errorTol']

    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/pro/tuning/interactive"
                             , json={
            'algType': 3,
            'controlDirection': 9999,
            'controlMode': 1,
            'errorTol': errorTol,
            'expertFlag': 1,
            'hisDataSegment': [startTime, endTime],
            'loopMainId': loopId,
            'pidType': 2,
            'timeSegments': [],
            'tuningMethod': 3
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

    tuningResult = json.loads(response.text)['data']
    tuningMessage = {
        'fffffffe': "数据段手动占比过高，无法整定",
        'ffffffff': "数据段长度过短，无法整定",
        'fffffffd': "允许偏差绝对值输入为0，无法整定"
    }
    message = "整定成功"
    if 'message' in tuningResult:
        if tuningResult['message'] in tuningMessage:
            message = tuningMessage[tuningResult['message']]
    return {
        'pidParams': tuningResult['pidParams'],
        'message': message
    }


def interactive_guideline_tuning(loopId, context):
    project_id = context.call_tool("get_project_id", context=context)
    endTime = datetime.datetime.now().timestamp() * 1000
    startTime = endTime - 3600000 * 6

    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/pro/tuning/interactive"
                             , json={
            'algType': 3,
            'controlDirection': 9999,
            'controlMode': 1,
            'convergenceIndex': 0.47,
            'expertFlag': 1,
            'hisDataSegment': [startTime, endTime],
            'loopMainId': loopId,
            'oscillationIndex': 0.5,
            'overshootIndex': 0.58,
            'pidType': 2,
            'timeSegments': [],
            'tuningMethod': 1,
            'tuningSpeedIndex': 0.5
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

    tuningResult = json.loads(response.text)['data']
    return {
        'pidParams': tuningResult['pidParams']
    }


def interactive_vrft_tuning(loopId, context):
    project_id = context.call_tool("get_project_id", context=context)
    endTime = datetime.datetime.now().timestamp() * 1000
    startTime = endTime - 3600000 * 6

    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/pro/tuning/interactive"
                             , json={
            'algType': 3,
            'controlDirection': 9999,
            'controlMode': 1,
            'expertFlag': 1,
            'hisDataSegment': [startTime, endTime],
            'loopMainId': loopId,
            'pidType': 2,
            'timeSegments': [],
            'tuningMethod': 2,
            'vrftAdjustmentTime': 350,
            'vrftAdjustmentTimeHigh': 600,
            'vrftAdjustmentTimeLow': 100,
            'vrftOs': 25,
            'vrftOsHigh': 50,
            'vrftOsLow': 0
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

    tuningResult = json.loads(response.text)['data']
    return {
        'pidParams': tuningResult['pidParams']
    }


@tool(version="*")
async def guide_tuning(context: Context, params: any):
    detectResult = context.call_tool("get_tuning_advice", context=context, params=params)
    loopDetailInfo = await get_loop_detail(context, params)
    algAdvice = detectResult['algAdvice']
    if not context.call_tool("is_agent_installed", context=context):
        algAdvice = 2
    algIntermediateVariable = detectResult['algIntermediateVariable']

    excitCoef = 0.0
    isInitialState = False
    closeRate = 0.0
    stableRate = 0.0
    valveSaturation = 0.0
    oscillateIndex = 0.0
    controlMode = 20

    if algIntermediateVariable is not None and len(algIntermediateVariable) == 8:
        excitCoef = algIntermediateVariable[1]
        isInitialState = algIntermediateVariable[2] == 1  # 0不是初始状态；1是初始状态
        closeRate = algIntermediateVariable[3]
        stableRate = algIntermediateVariable[4]
        valveSaturation = algIntermediateVariable[5]
        oscillateIndex = algIntermediateVariable[6]
        controlMode = algIntermediateVariable[7]

    endTime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    startTime = (datetime.datetime.now() - datetime.timedelta(hours=6)).strftime('%Y-%m-%d %H:%M:%S')

    if await context.get_cache("data_validation_data_range_start_time") and await context.get_cache("data_validation_data_range_end_time"):
        endTime = datetime.datetime.fromtimestamp(int(await context.get_cache("data_validation_data_range_end_time")) / 1000).strftime('%Y-%m-%d %H:%M:%S')
        startTime = datetime.datetime.fromtimestamp(int(await context.get_cache("data_validation_data_range_start_time")) / 1000).strftime('%Y-%m-%d %H:%M:%S')

    if algAdvice == 0:  # 无历史数据
        return {
            'adviceMsg': f'没有有效历史数据',
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 1:  # 检测到该回路处于初始状态，且当前数据段激励程度弱，小助手提示您可以选择其他具有激励的数据段，或进行阶跃测试，或直接进入预整定模式。
        return {
            'adviceMsg': '检测到该回路处于初始状态，且当前数据段激励程度弱，小助手提示您可以选择其他具有激励的数据段，或进行阶跃测试，或直接进入预整定模式。',
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 2:  # 检测到该回路处于初始状态，且当前数据段具有一定程度的激励，小助手已为您优先选择IMC整定模式。
        params['startTime'] = startTime
        params['endTime'] = endTime
        params['loopName'] = loopDetailInfo['loopName']
        tuningResult = await imc_tuning(context, params)
        if tuningResult['pidParams'] is None:
            tuningResult['pidParams'] = [0.0, 0.0, 0.0]
        return {
            # 'adviceMsg': f"我先查看了该回路的PID参数，发现PID参数中比例度PB是{loopDetailInfo['tagValues'][3]}，积分时间常数TI为{loopDetailInfo['tagValues'][4]}，微分时间常数TD为{loopDetailInfo['tagValues'][5]}，这是中控ECS-700的DCS系统里PID功能块的初始默认参数，回路应该没整定过。我检查了下回路的运行情况，发现它一直没投用过自动，自控率是{closeRate}%。针对这种回路，我首先会查看它的长周期历史趋势，若控制回路的MV与PV变化程度能满足建模要求，我将采用模型辨识的IMC算法进行参数优化。若发现MV变化程度有限，导致回路数据缺乏显著的动态特性，这样我将借鉴以往成功案例的经验，类似的回路控制参数，为当前回路提供一个相对合理的参数起点。不过再看回路最近的运行数据，发现MV有明显变化和调整，激励指数达到{excitCoef}，这说明回路的数据特性比较明显。基于该回路数据，我决定使用基于历史数据进行模型辨识的IMC算法来进行参数整定。我会先从历史数据中提取回路的输入输出数据，建立过程模型，然后根据模型来优化PID参数，以提高控制效果。通过模型辨识，得出该回路的模型增益为{tuningResult['rGain']}、时间常数为{tuningResult['rT1']}，从而计算出控制回路的PID参数，新的比例度PB为{tuningResult['pidParams'][0]}，积分时间常数Ti为{tuningResult['pidParams'][1]}，微分时间常数TD为{tuningResult['pidParams'][2]}。之后，我会持续观察回路在新参数下的运行情况，看看实际效果如何，以便进一步调整和优化。",
            'adviceMsg': "检测到该回路处于初始状态，且当前数据段具有一定程度的激励，小助手已为您优先选择IMC整定模式。",
            'tuningResult': tuningResult,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 3:  # 阀门存在全开/全关/卡限等异常情况，不推荐您使用软件进行整定，建议您检查阀门状态。
        return {
            'adviceMsg': f'阀门存在全开/全关/卡限等异常情况，不推荐您使用软件进行整定，建议您检查阀门状态。',
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 4:  # 当前工艺条件不建议进行整定，请确定实际工艺情况。
        return {
            'adviceMsg': f'当前工艺条件不建议进行整定，请确定实际工艺情况。',
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 5:  # 检测到该回路数据段激励程度弱，小助手提示您可以选择其他具有激励的数据段，或进行阶跃测试，您也可以选择进行预整定。
        params['startTime'] = startTime
        params['endTime'] = endTime
        params['loopName'] = loopDetailInfo['loopName']
        return {
            'adviceMsg': '检测到该回路数据段激励程度弱，小助手提示您可以选择其他具有激励的数据段，或进行阶跃测试，您也可以选择进行预整定。',
            'tuningResult': await imc_tuning(context, params),
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 6:  # 检测到该回路数据段激励程度弱，小助手提示您可以选择其他具有激励的数据段，或进行阶跃测试，再执行整定操作。
        params['startTime'] = startTime
        params['endTime'] = endTime
        params['loopName'] = loopDetailInfo['loopName']
        return {
            'adviceMsg': '检测到该回路数据段激励程度弱，小助手提示您可以选择其他具有激励的数据段，或进行阶跃测试，再执行整定操作。',
            'tuningResult': await imc_tuning(context, params),
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 7:  # 检测到该回路参数暂未投用，建议您投用后根据响应效果再进行整定操作。
        params['startTime'] = startTime
        params['endTime'] = endTime
        params['loopName'] = loopDetailInfo['loopName']
        return {
            'adviceMsg': '检测到该回路参数暂未投用，建议您投用后根据响应效果再进行整定操作。',
            'tuningResult': await imc_tuning(context, params),
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 8:  # 检测到当前回路具有一定程度的激励，推荐您使用IMC整定。
        params['startTime'] = startTime
        params['endTime'] = endTime
        params['loopName'] = loopDetailInfo['loopName']
        return {
            'adviceMsg': '检测到当前回路具有一定程度的激励，推荐您使用IMC整定。',
            'tuningResult': await imc_tuning(context, params),
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 9:  # 检测到当前回路刚投上自动，请等待片刻后，更新状态再尝试整定，您也可以选择立即整定。
        params['startTime'] = startTime
        params['endTime'] = endTime
        params['loopName'] = loopDetailInfo['loopName']
        tuningResult = await imc_tuning(context, params)
        if tuningResult['pidParams'] is None:
            tuningResult['pidParams'] = [0.0, 0.0, 0.0]
        return {
            'adviceMsg': '检测到当前回路刚投上自动，请等待片刻后，更新状态再尝试整定，您也可以选择立即整定。',
            'tuningResult': tuningResult,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 10:  # 检测到该回路平稳率较高，您可根据工艺需要对参数进行调整.
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': f"""
检测到该回路平稳率较高，您可根据工艺需要对参数进行调整。
""",
            'title': None,
            'description': None
        })
        return {
            'adviceMsg': '检测到该回路平稳率较高，您可根据工艺需要对参数进行调整。',
            'tuningResult': interactive_guideline_tuning(params['loopId'], context),
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 11:  # 检测到该回路平稳率不高，且振荡较大，小助手已为您选择趋势线法整定
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': f"""
检测到该回路平稳率不高，且振荡较大，已为您选择趋势线法整定。
""",
            'title': None,
            'description': None
        })
        return {
            'adviceMsg': '检测到该回路平稳率不高，且振荡较大，小助手已为您选择趋势线法整定。',
            'tuningResult': interactive_guideline_tuning(params['loopId'], context),
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    elif algAdvice == 12:  # 检测到该回路平稳率不高，但振荡较小，小助手已为您选择虚拟参考曲线法整定
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': f"""
检测到该回路平稳率不高，但振荡较小，已为您选择虚拟参考曲线法整定。
""",
            'title': None,
            'description': None
        })
        return {
            'adviceMsg': '检测到该回路平稳率不高，但振荡较小，小助手已为您选择虚拟参考曲线法整定。',
            'tuningResult': interactive_vrft_tuning(params['loopId'], context),
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }
    else:  # 算法错误
        return {
            'adviceMsg': f'算法错误！ 消息：{detectResult["message"]}',
            'excitCoef': excitCoef,
            'isInitialState': isInitialState,
            'closeRate': closeRate,
            'stableRate': stableRate,
            'valveSaturation': valveSaturation,
            'oscillateIndex': oscillateIndex,
            'controlMode': controlMode
        }


@tool(version="*")
async def get_loop_2h_report_diagnosis(context: Context, params: any):
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "感谢您对本任务信息的完善，我将全力执行任务。",
            "description": "",
            "details": ""
        }
    })
    if not context.call_tool("is_agent_installed", context=context):
        await context.call_tool("add_card", context=context, params={
            'card_type': "summary",
            'content': "PID Agent未安装，跳过回路性能评估及诊断阶段。",
            'title': None,
            'description': None
        })
        return {}
    project_id = context.call_tool("get_project_id", context=context)
    response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/config/node/all"
                            , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    groupTree = json.loads(response.text)['data']
    groupId = groupTree['id']
    endTime = datetime.datetime.now().timestamp()
    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/loop/report/advance"
                             , json={
            'andList': [
                {
                    'field': 'loopName',
                    'value': params['loopName'],
                    'operator': '='
                }
            ],
            'clazz': 0,
            'current': 1,
            'groupId': groupId,
            'orList': [],
            'reportType': 0,
            'size': 20,
            'timeList': [datetime.datetime.fromtimestamp(endTime - 3600 * 24).strftime('%Y-%m-%d %H:%M'),
                         datetime.datetime.fromtimestamp(endTime).strftime('%Y-%m-%d %H:%M')],
            'orderField': "createTime",
            'order': "descend"
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    reportList = json.loads(response.text)['list']
    if len(reportList) == 0:
        raise ValueError(f"Loop {params['loopName']} not exist!")
    report = reportList[0]

    response = requests.get(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/loop/report/detail?loopId={report['loopId']}&time={report['createTime']}&reportType=0&reportLoopType=0"
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    reportDetail = json.loads(response.text)['data']
    if reportDetail is None:
        raise ValueError(f"Loop {params['loopName']} not exist!")

    suggestion = {
        1: '该回路状态异常，请检查数据源及质量码是否正常',
        2: '该回路在当前评估周期内有效运行时间较短，无法进行评估',
        3: '该回路开环，建议结合工艺要求优化控制参数或策略，提升自控水平',
        4: '阀门可能卡限，请检查阀门情况，有可能阀门大小不符合当前工艺要求，建议关注',
        5: '该回路阀门可能存在粘滞情况，建议对阀门进行检查',
        6: '当前回路运行状态良好，请保持',
        7: '该回路可能非单变量控制，建议结合实际工艺要求及场景，对回路进行维护',
        8: '该回路性能存在改善空间，建议对参数进行整定',
        9: '当前回路性能存在改善空间，请关注当前回路状态',
        10: '该回路状态异常，没有检测到测量值、设定值或阀位值数据，请检查数据源',
        11: '该回路状态异常，请检查数据源及质量码是否正常',
        12: '该回路有效运行时间较短，无法进行评估',
        13: '请首先将控制回路置为手动，查看振荡是否停止，若振荡持续，说明振荡应该是外部原因导致的，可以寻找具备相似振荡周期的回路进行相关性分析，以追踪回路问题',
        14: '当前回路性能存在改善空间，请关注当前回路状态',
        27: '阀门可能卡限，请检查阀门情况，有可能阀门大小不符合当前工艺要求，建议关注',
        28: '阀门可能存在粘滞情况，建议对阀门进行检查',
        32: '当前回路可能存在振荡,可以将PID参数的比例作用或积分作用削弱',
        37: '当前回路测量值可能处于超限状态,可以适当将PID参数的积分作用削弱'
    }

    isValveOscillate = (reportDetail['oscillationIndex'] >= 0.6)
    isValveStic = (reportDetail['valveStictionIndex'] >= 0.6)

    await context.call_tool("add_card", context=context, params={
        'card_type': "summary",
        'content': suggestion[report['suggestionFlag'] if report['suggestionFlag'] in suggestion else 1],
        'title': None,
        'description': None
    })
    return {
        'comprehensiveClass': report['comprehensiveClass'],
        'comprehensiveScore': report['comprehensiveScore'],
        'suggestion': suggestion[report['suggestionFlag'] if report['suggestionFlag'] in suggestion else 1],
        'isValveOscillate': isValveOscillate,
        'isValveStic': isValveStic,
        'valveTralve': reportDetail['valveTralve'],
        'oscillationIndex': reportDetail['oscillationIndex'],
        'valveStickingIndex': reportDetail['valveStictionIndex']
    }


@tool(version="*")
async def get_auto_tuning_records(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    project_id = context.call_tool("get_project_id", context=context)
    groupId = None
    if 'groupId' in params:
        groupId = params['groupId']
    else:
        response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/config/node/all"
                                , headers={
                'Content-Type': 'application/json',
                'Authorization': context.config["inner_token"],
                'Project': str(project_id),
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })
        groupTree = json.loads(response.text)['data']
        groupId = groupTree['id']
    endTime = datetime.datetime.now().timestamp()

    startTimeStr = ''
    endTimeStr = ''

    if 'endTime' in params:
        endTimeStr = params['endTime']
    else:
        endTimeStr = datetime.datetime.fromtimestamp(endTime).strftime('%Y-%m-%d %H:%M:%S')

    if 'startTime' in params:
        startTimeStr = params['startTime']
    else:
        startTimeStr = datetime.datetime.fromtimestamp(endTime - 3600 * 24).strftime('%Y-%m-%d %H:%M:%S')

    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-tuning/v1/auto/records"
                             , json={
            'current': 1,
            'groupId': groupId,
            'loopType': -1,
            'loopName': params['loopName'],
            'size': 20,
            'timeList': [startTimeStr, endTimeStr]
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    recordList = json.loads(response.text)['list']
    if len(recordList) == 0:
        raise ValueError(f"No auto tuning record found for loop {params['loopName']}!")

    record = None
    for rec in recordList:
        if record['loopName'] == params['loopName']:
            record = rec
            break
    if record is None:
        raise ValueError(f"Loop {params['loopName']} not exist!")
    return {
        'loopName': record['loopName'],
        'pidParaBeforeTuning': record['pidParaBeforeTuning'],
        'pidParaAfterTuning': record['pidParaAfterTuning'],
        'advice': record['msg'],
        'starsOfGrade': record['identificationModelReliability'] * 5
    }


# @tool(version="*")
async def loop_batch_write(context: Context, params: any):
    # if not context.call_tool("is_agent_installed", context=context):
    #     return {}
    # project_id = context.call_tool("get_project_id", context=context)
    # response = requests.get(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loopwrite/getloops"
    #                         , headers={
    #         'Content-Type': 'application/json',
    #         'Authorization': context.config["inner_token"],
    #         'Project': str(project_id),
    #         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #     })
    # loopList = json.loads(response.text)['list']
    # writableLoops = []
    # for loop in loopList:
    #     if loop['checked']:
    #         writableLoops.append(loop)
    #
    # response = requests.post(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loopwrite/loopparampreset"
    #                          , json=writableLoops
    #                          , headers={
    #         'Content-Type': 'application/json',
    #         'Authorization': context.config["inner_token"],
    #         'Project': str(project_id),
    #         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #     })
    # recordList = json.loads(response.text)['list']
    #
    # successList = []
    # failList = []
    #
    # for record in recordList:
    #     response = requests.post(url=f"http://{context.config['config_host']}/pidapi/inter-api/pid-configuration/v1/loopwrite/writeloop"
    #                              , json={
    #             'loopId': record['loopId'],
    #             'batchNum': record['batchNum'],
    #             'loopName': record['loopName'],
    #             'pValueBefore': record['pValueBefore'],
    #             'iValueBefore': record['iValueBefore'],
    #             'dValueBefore': record['dValueBefore'],
    #             'pValueAfter': record['pValueAfter'],
    #             'iValueAfter': record['iValueAfter'],
    #             'dValueAfter': record['dValueAfter'],
    #         }
    #                              , headers={
    #             'Content-Type': 'application/json',
    #             'Authorization': context.config["inner_token"],
    #             'Project': str(project_id),
    #             'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #         })
    #     respJson = json.loads(response.text)
    #     if respJson['code'] == 100000000 and respJson['data']['writtenResult']:
    #         successList.append({
    #             'loopName': respJson['data']['loopName'],
    #             'pidBeforeWrite': respJson['data']['prePreset'],
    #             'pidAfterWrite': respJson['data']['afterPreset']
    #         })
    #     else:
    #         failList.append({
    #             'loopName': respJson['data']['loopName'],
    #             'pidBeforeWrite': respJson['data']['prePreset']
    #         })
    # return {
    #     'successList': successList,
    #     'failList': failList
    # }
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    openPageResult = await context.get_interaction("loop_batch_write_page")
    if openPageResult is None:
        context.require_interaction({
            "id": "loop_batch_write_page",
            "title": "回路参数批量预设页面",
            "type": "open_page",
            "open_page": f"http://{context.config['agent_frontend_host']}/PID_WEBWATCH/preset/scan?ticket={context.config['inner_token'][7:]}",
            "page_type": "view"
        })
    return {
        'successList': [],
        'failList': []
    }


@tool(version="*")
async def get_loop_history_close_stable_rate(context: Context, params: any):
    if not context.call_tool("is_agent_installed", context=context):
        return {}
    project_id = context.call_tool("get_project_id", context=context)
    groupId = params['groupId']
    endTime = datetime.datetime.now().timestamp()

    startTimeStr = ''
    endTimeStr = ''

    if 'endTime' in params:
        endTimeStr = params['endTime']
    else:
        endTimeStr = datetime.datetime.fromtimestamp(endTime).strftime('%Y-%m-%d %H:%M')

    if 'startTime' in params:
        startTimeStr = params['startTime']
    else:
        startTimeStr = datetime.datetime.fromtimestamp(endTime - 7200).strftime('%Y-%m-%d %H:%M')

    response = requests.post(url=f"http://{context.config['eval_host']}/pidapi/inter-api/pid-evaluation/v1/loop/report/advance"
                             , json={
            'andList': [
                {
                    'field': 'loopName',
                    'value': params['loopName'],
                    'operator': '='
                }
            ],
            'clazz': 0,
            'current': 1,
            'groupId': groupId,
            'orList': [],
            'reportType': params['reportType'],
            'size': 20,
            'timeList': [startTimeStr, endTimeStr]
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': context.config["inner_token"],
            'Project': str(project_id),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    reportList = json.loads(response.text)['list']
    if len(reportList) == 0:
        raise ValueError(f"Loop {params['loopName']} not exist!")

    avgCloseRate = 0.0
    avgStableRate = 0.0

    for report in reportList:
        # startTime = datetime.datetime.strptime(report['dataInterval'][0], '%Y-%m-%d %H:%M:%S').timestamp()
        # endTime = datetime.datetime.strptime(report['dataInterval'][1], '%Y-%m-%d %H:%M:%S').timestamp()
        avgCloseRate = avgCloseRate + report['closeRate']
        avgStableRate = avgStableRate + report['stableRate']
    avgCloseRate = avgCloseRate / len(reportList)
    avgStableRate = avgStableRate / len(reportList)

    return {
        'closeRate': avgCloseRate,
        'stableRate': avgStableRate
    }
