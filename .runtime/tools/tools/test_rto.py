from __tester__ import load_module, run
from __runner__ import tool, Context
import json,requests

import csv
import base64
import io
import pandas as pd
import asyncio

# === 修改为你的工具模块路径 ===
rto = load_module("rto/v1/autonomous_opt.py")
#automl = load_module("automl/v1/auto_ml.py")
#context = Context()
#context.config = {"key": "value"}
params = {
   "nodeName":"氯碱装置"
}

context = Context()
context.config = {"installed": False}
context.cache = {
  }

def get_llm_summary(context: Context, params: any):
    success = 1
    llm_resp = ""
    try:
        llm_params = {
            "model": "qwen3",
            "messages": [{
                "role": "user",
                "content": params
            }]
        }
        response = requests.post(
            url= "http://nlb-2weu6cb4a97uoz9cqu39kvmj.nlb.cn-beijing.volces.com:32004/v1/chat/completions", 
            data=json.dumps(llm_params, indent=2, ensure_ascii=False), 
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
        if response.status_code == 200:
            llm_resp = response.json().get("choices")[0].get("message").get("content")
    except Exception as e:
         success = -1
         llm_resp = "调用LLM总结出现异常:" + response.text
    output_result  = {
        "success":success,
        "llm_resp":llm_resp
    }
    return output_result


# === 主程序 ===
if __name__ == "__main__":
   
    llm_resp_obj = get_llm_summary(context = context,params="你是谁")
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
      llm_resp = "sdffsdf"
    print(llm_resp)
   #result = run(rto.query_steady_params, context=context, params=params)
   #print(result)
  #  result = run(rto.opt_file_upload, context=context, params=params) 
  #  params1 = {
  #   "file" : result["file"]
  #   }
  #  result = run(rto.parse_opt_file_upload, context=context, params=params1)
  #  print(result)
  #  params2 = {
  #   "data" : result["data"]
  #   }
   
    # def convert_runtime_json(json_str):
    #   try:
    #       # 先解析外层JSON
    #       outer_json = json.loads(json_str)
    #       if 'data' in outer_json:
    #           # 解析内层JSON字符串
    #           inner_data = json.loads(outer_json['data'])
    #           # 重新构建JSON对象
    #           outer_json['data'] = inner_data
    #           return json.dumps(outer_json, indent=2, ensure_ascii=False)
    #       return json_str
    #   except json.JSONDecodeError as e:
    #       print(f"JSON解析错误: {e}")
    #   return json_str
   
    # response = requests.post(
    #   url="http://10.30.73.42:31668/call/app?name=TPT_optimize_2_py&built_in=1&time_out=6000", 
    #   data=json.dumps(result["data"], ensure_ascii=False), 
    #   headers={'Content-Type': 'application/json'})

  #  result = json.loads(convert_runtime_json(txt))

  #  print(result)

    file = {
    "bucket": "recommend",
    "name": "119e4506d00847ab95284350609608e7_output.csv",
    "object": "0/119e4506d00847ab95284350609608e7_output.csv"
    }

    alg_params = {
        "input_file" : file,
        "scenario_id"  : 9
    }
    RUNTIME_EXEC_URL = "http://10.30.73.42:31668/call/app?built_in=0&time_out=600"
    response = requests.post(
        url = RUNTIME_EXEC_URL +"&name=ts_data_eval_py", 
        data = json.dumps(alg_params,indent=2, ensure_ascii=False),
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    read_data = json.loads(json_obj["data"])
    # print(read_data)


 
