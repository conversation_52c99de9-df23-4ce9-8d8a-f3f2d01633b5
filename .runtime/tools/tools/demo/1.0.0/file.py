from __runner__ import tool, Context, validator
import json

@tool(version="*")
async def file(context: Context, params: any):
    # ID 最好全局唯一
    result = await context.get_interaction("demo/any_form_id")
    if result is None:
        context.require_interaction({
            "id": "demo/any_form_id",
            "title": "请输入以下内容",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "选择结果",
                    "properties": {
                        # 上传文件
                        # 单文件实际值格式为："{\"bucket\":\"recommend\",\"object\":\"0/admin/1bbf4bab9a6e4729ad883ead5b82c4f2_原始需求1_验收表 (1).xlsx\",\"name\":\"原始需求1_验收表 (1).xlsx\"}"
                        # 该格式和后续 add_file / get_file 方法的返回格式一致
                        "file": {
                            "type": "string",
                            "title": "Single file",
                            # 文件格式标识，必须是
                            # file-object 表示单个文件对象
                            # file-array 表示文件列表
                            "format": "file-object",
                            # 前端渲染标识，必须是这个值
                            "widget": "tptfile",
                            # 文件校验器，标识一个文件校验函数名，独立于能力
                            # 没有文件校验器时，默认通过
                            # 校验器不存在时，默认失败
                            "x-validator": "validate_file",
                            "x-validator-payload": {

                            },
                            # 文件模板列表路径
                            "x-template": [
                                {
                                    # OSS 文件路径，强制读取 agent-runner.global 存储桶
                                    # 需要提前上传
                                    "object": "demo/file.docx",
                                    # 可选，文件名
                                    "name": "示例文件"
                                },
                                {
                                    # 第三方服务器文件
                                    "url": "https://example.com/demo/file.docx",
                                    # 可选，文件名
                                    "name": "示例文件"
                                }
                            ]
                        },
                    },
                    "required": [
                        # 必传
                        "file"
                    ]
                }
            }
        })
        return {}
    
    # 通过对象信息，获取文件内容，返回值为字符串格式
    # file_content_string = context.get_file({
        # 可选，指定存储区域，可以根据运行时自动计算路径和桶，默认 session
        # 优先级高于 bucket
        # 支持 session, user, global, template
        # "scope": "session",
        # 可选，指定存储桶，通常用于全局存储，或者非本服务管理的存储桶
        # "bucket": "xx",
        # "object": "some/path/to/file.txt",
    # })

    # file_content_string = context.get_file("some/path/to/file.txt")  # 通过路径获取文件内容

    # 处理文件内容, 必须先解析
    file_info = json.loads(result.get("file"))

    file_content_string = await context.get_file(file_info)  # 获取文件内容，字符串格式，默认，必须为 utf-8 编码

    # 只打印最多128长度
    max_length = 128
    display_content = file_content_string[:max_length]
    if len(file_content_string) > max_length:
        display_content += "\n...(内容已截断)"
    await context.add_view({
        "format": "markdown",
        "content": f"文件内容: \n\n{display_content}"
    })

    file_content_string = await context.get_file(file_info, "string")  # 获取文件内容，字符串格式，必须为 utf-8 编码

    file_content_bytes = await context.get_file(file_info, "bytes")  # 获取文件内容，字节格式

    # 上传文件，返回文件信息，文件内容可以是二进制或字符串格式
    # 同名覆盖
    file_info = await context.add_file("some/path/file1.txt", b"Hello, World!")

    # 等效，默认 scope 为 session 级别
    file_info = await context.add_file(
        {
            "object": "some/path/file2.txt",
            # 可选，指定存储区域，可以根据运行时自动计算路径和桶，默认 session
            # 优先级高于 bucket
            # 支持 session, user, global, template
            "scope": "session",
            # 可选，指定存储桶，通常用于全局存储，或者非本服务管理的存储桶
            # scope / bucket 二选一
            # "bucket": "xxxxx"
        }, 
        b"Hello, World!"
    )

    return {}

@validator(version="*")
async def validate_file(context: Context, target: any, payload: any = None):
    # target 格式即为上述实际值格式
    file_info = json.loads(target)
    file = await context.get_file(file_info)  # 获取文件内容

    # 校验文件内容
    # ...

    # 错误结果，抛出异常
    # raise ValueError("文件校验失败, 详细说说哪里有问题")

    # 正确结果
    # 直接返回即为成功
    return
