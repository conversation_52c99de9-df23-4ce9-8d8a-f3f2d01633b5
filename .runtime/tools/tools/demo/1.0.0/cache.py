from __runner__ import tool, Context

@tool(version="*")
async def cache(context: Context, params: any):
    if await context.get_interaction("select") is None:
        await context.set_cache("key", "xxxxxxxxxxxxxxxx")
        context.require_interaction({
            "id": "select",
            "title": "请选择以下内容",
            "type": "select",
            "mode": "single",
            "select": [
                {
                    "title": "继续",
                    "data": None,
                },
            ]
        })

    await context.add_view({
        "format": "markdown",
        "content": f"cache: {await context.get_cache('key')}"
    })

    return {}
