from __runner__ import tool, Context

@tool(version="*")
async def select(context: Context, params: any):
    result = await context.get_interaction("select")
    if result is None:
        # id，交互标识，区别同函数中的多个交互
        # mode，选择模式 multiple/single，默认 single
        # data, 与选项同时返回，方便代码处理选项
        context.require_interaction({
            "id": "select",
            "title": "请选择以下内容",
            "type": "select",
            "mode": "single",
            "select": [
                {
                    "title": "选项1",
                    "data": None,
                },
                {
                    "title": "选项2",
                    "data": None,
                },
                {
                    "title": "选项3",
                    "data": None,
                } 
            ]
        })
        return {}
    else:
        return {
            "output": result
        }
