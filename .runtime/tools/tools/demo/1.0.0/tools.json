{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "test1", "description": "测试，没有输入输出", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object"}}, {"name": "test2", "description": "测试，没有输入输出", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object"}}, {"name": "test3", "description": "测试，没有输入输出", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object"}}, {"name": "cache", "description": "测试，没有输入输出", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object"}}, {"name": "file", "description": "测试，没有输入输出", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object"}}, {"name": "view", "description": "测试，没有输入输出", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object"}}, {"name": "greet", "description": "问候能力", "alias": [], "catalog": "statistics", "params": {"type": "object", "description": "问候能力的输入参数", "properties": {"name": {"type": "string", "description": "接收问候的人员姓名，将在问候消息中使用"}}, "required": ["name"]}, "result": {"type": "object", "description": "问候能力的输出结果", "properties": {"output": {"type": "string", "description": "包含针对指定人员的问候消息"}}, "required": ["output"]}}, {"name": "echo", "description": "回声能力", "alias": [], "catalog": "statistics", "params": {"type": "object", "description": "回声能力的输入参数", "properties": {"input": {"type": "string", "description": "需要被回显的输入文本"}}, "required": ["input"]}, "result": {"type": "object", "description": "回声能力的输出结果", "properties": {"output": {"type": "string", "description": "原样返回的输入文本内容"}}, "required": ["output"]}}, {"name": "select", "description": "选择能力", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object", "description": "选择结果", "properties": {"output": {"type": "array", "description": "所有选择结果", "items": {"type": "string", "description": "选择的内容"}}}, "required": ["output"]}}, {"name": "form", "description": "表单能力", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object", "description": "选择结果", "properties": {"field1": {"type": "string", "title": "字段1，这里会渲染为label", "description": "字段1的描述"}, "field2": {"type": "number", "title": "字段2", "description": "字段2，设置了最小值为0", "minimum": 0}}, "required": ["field1"]}}, {"name": "open_page", "description": "打开页面能力", "alias": [], "catalog": "statistics", "params": {"type": "object"}, "result": {"type": "object", "description": "打开页面的结果", "properties": {"output": {"type": "boolean", "description": "打开页面的成功与否"}}, "required": ["output"]}}, {"name": "weather", "description": "获取天气的能力", "alias": [], "catalog": "statistics", "params": {"type": "object", "description": "回声能力的输入参数", "properties": {"position": {"type": "string", "description": "需要被回显的输入文本"}}, "required": ["position"]}, "result": {"type": "object", "description": "获取天气的输出结果", "properties": {"status": {"type": "string", "description": "天气状态"}, "temperature": {"type": "number", "description": "当前温度"}, "humidity": {"type": "number", "description": "湿度百分比"}, "wind_speed": {"type": "number", "description": "风速"}, "wind_direction": {"type": "string", "description": "风向"}, "precipitation": {"type": "number", "description": "降水量"}}}}]}