from __runner__ import tool, Context

@tool(version="*")
async def form(context: Context, params: any):
    result = await context.get_interaction("any_form_id")
    if result is None:
        context.require_interaction({
            "id": "form",
            "title": "请输入以下内容",
            "type": "form",
            "form": {
                # form_type 字段可选，默认为 normal
                # file/normal 区分文件上传（file）和补充信息（normal）
                # 只有文件上传表单，认为是文件上传；混合表单，认为是补充信息
                "form_type": "normal",
                "schema": {
                    "type": "object",
                    "description": "选择结果",
                    "properties": {
                        "field1": {
                            "type": "string",
                            "title": "字段1，这里会渲染为label",
                            "description": "字段1的描述"
                        },
                        "field2": {
                            "type": "number",
                            "title": "字段2",
                            "description": "字段2，设置了最小值为0",
                            "minimum": 0
                        },
                        # 上传文件
                        "file": {
                            "type": "string",
                            # data-url 格式 base64 编码，可以参考解析函数解析为二进制
                            "format": "data-url",
                            "title": "Single file",
                            "template": [
                                # 文件模板列表，出现在文件上传窗口
                                "template/url"
                            ]
                        },
                    },
                    "required": [
                        "field1"
                    ]
                },
                "default": {
                    # 可以不给
                    # "field1": "xxx"
                }
            }
        })
        return {}
    else:
        return result
