from __runner__ import tool, Context

@tool(version="*")
async def view(context: Context, params: any): 
    # markdown 格式，显示在左侧对话
    await context.add_view({
        "format": "markdown",
        # markdown 内容，跨行内容注意顶格开始
        "content": """
# h1

text

## h2

text

table:

| Column A | Column B | Column C |
| -------- | -------- | -------- |
| A1       | B1       | C1       |
| A2       | B2       | C2       |
| A3       | B3       | C3       |

code block:
```rust
println!("hello world!");
```
"""
    })

    # html 格式，显示在左侧对话
    # await context.add_view({
    #     "format": "html",
    #     "content": """
    #     <html>
    #         <head>
    #             <title>HTML View</title>
    #         </head>
    #         <body>
    #             <h1>Hello, HTML!</h1>
    #             <p>This is a simple HTML view.</p>
    #         </body>
    #     </html>
    #     """
    # })

    # card 格式
    # 左侧展示卡片，可点击的 title、description，以及卡片内可展开收起的 content
    # 右侧展示详情 details
    await context.add_view({
        "format": "card",
        "content": {
            # 必填，卡片的 icon，影响卡片 content 和 details 渲染
            # 已支持类型：markdown、summary、think、open_page、file、html、echarts、search、error、warning、code
            # 要添加新渲染类型，可以联系前端
            "type": "summary",
            # 必填，卡片的标题（黑色主标题）
            "title": "工具执行结果",
            # 可选，卡片的副标题（灰色副标题）
            "description": "描述信息", 
            # 可选，卡片内的 body 内容
            "content": "工具“test”执行完成，状态正常，未产生输入输出数据，符合预定义的无输入输出特性。工作流仅包含单一步骤，成功执行无依赖关系。",
            # 可选，显示在右侧详情的内容
            "details": "",
            # 可选，隐藏右侧详情，与 hide_card 独立作用，默认 False
            "hide_details": False
            # 暂时没有用的字段
            # "link": "暂时没用的字段",
        },
        # （未实现）可选，隐藏左侧卡片，与 hide_details 独立作用，默认 False
        "hide_card": False
    })
