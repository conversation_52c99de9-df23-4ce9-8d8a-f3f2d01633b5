from __runner__ import tool, Context
import time

async def test_file(context: Context, params: any):
    import json

    test_file_path = "some/path/test.txt"
    test_file_data = "这是一个测试文件"

    info = await context.add_file(test_file_path, test_file_data.encode("utf-8"))

    data = await context.get_file(test_file_path)

    assert data == test_file_data

    await context.add_view({
        "format": "markdown",
        "content": f"file: {json.dumps(info)}"
    })

    await context.call_tool("file", params={})

async def test_screenshot(context: Context, params: any):
    result = await context.call_tool("screenshot", params={
        "url": "/tpt-app/",
        "delay": 5
    }, module_name="basic")

    # 按照容器大小缩放图片，设置最大宽度和高度为100%，自适应容器
    await context.add_view({
        "format": "html",
        "content": f"<img src='data:image/png;base64,{result['image']}' style='max-width:100%;max-height:100%;display:block;margin:auto;' />"
    })

async def test_cache(context: Context, params: any):
    await context.set_cache("key", "xxxxxxxxxxxxxxxx")

    data = await context.get_cache("key")

    assert data == "xxxxxxxxxxxxxxxx"

async def test_view(context: Context, params: any):
    max_count = 3
    count = 0
    period = 0.1

    while count < max_count:
        await context.add_view({
            "format": "markdown",
            "content": f"hello world! {count}"
        })

        await context.add_view({
            "format": "markdown",
            "content": f"cache: {await context.get_cache("key")}"
        })


        await context.add_view({
            "format": "card",
            "content": {
                "type": "think",
                "title": f"count {count}",
            }
        })

        count += 1
        time.sleep(period)

async def test_view_delta(context: Context, params: any):
    await context.add_view({
        "format": "card",
        "content": {
            "type": "think",
            "title": "hello world!"
        }
    })

    count = 0
    max_count = 5
    period = 0.1

    while count < max_count:
        await context.append_view({
            "content": f"delta {count} {time.time()} \n"
        })  

        count += 1
        time.sleep(period)

async def test_cancel(context: Context):
    await context.log_info("test_cancel start")
    # loop 1s add_view
    while True:
        await context.add_view({
            "format": "markdown",
            "content": f"test_cancel {time.time()}"
        })

        print(f"is_cancelled: {context.is_cancelled}")

        if context.is_cancelled:
            print("test_cancel cancelled")
            break

        time.sleep(1)

@tool(version="*")
async def test1(context: Context, params: any):
    # await test_cancel(context)

    base_url = context.config["base_url"]
    await context.log_info(f"base_url = {base_url}")

    await context.log_debug("logging hello")
    await context.log_info("logging hello")
    await context.log_warn("logging hello")
    await context.log_error(f"session_id = {context.session_id}")
    await context.log_error(f"tenant_id = {context.tenant_id}")
    await context.log_error(f"user_id = {context.user_id}")

    await test_screenshot(context, params)

    await test_view_delta(context, params)

    results = await context.call_tool(tool_name="llm_chat_post", params=params, module_name="llm")
    await context.add_view({
        "format": "markdown",
        "content":f"{results}"
    })

    await test_file(context, params)

    await test_cache(context, params)

    await test_view(context, params)

    return {}

@tool(version="*")
async def test2(context: Context, params: any):
    return {}

@tool(version="*")
async def test3(context: Context, params: any):
    return {}
