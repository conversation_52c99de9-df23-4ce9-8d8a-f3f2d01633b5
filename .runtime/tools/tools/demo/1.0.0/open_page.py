from __runner__ import tool, Context

@tool(version="*")
async def open_page(context: Context, params: any):
    result = await context.get_interaction("open_page")
    if result is None:
        context.require_interaction({
            "id": "open_page",
            "title": "打开页面",
            "type": "open_page",
            "open_page": "http://xxxx/yyyy#zzzzz"
        })
        return {}
    else:
        return {
            "output": result
        }
