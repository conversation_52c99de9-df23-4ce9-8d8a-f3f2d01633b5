{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "greet", "description": "问候能力", "params": {"type": "object", "description": "问候能力的输入参数", "properties": {"name": {"type": "string", "description": "接收问候的人员姓名，将在问候消息中使用"}}, "required": ["name"]}, "result": {"type": "object", "description": "问候能力的输出结果", "properties": {"output": {"type": "string", "description": "包含针对指定人员的问候消息"}}, "required": ["output"]}}, {"name": "echo", "description": "回声能力", "params": {"type": "object", "description": "回声能力的输入参数", "properties": {"input": {"type": "string", "description": "需要被回显的输入文本"}}, "required": ["input"]}, "result": {"type": "object", "description": "回声能力的输出结果", "properties": {"output": {"type": "string", "description": "原样返回的输入文本内容"}}, "required": ["output"]}}]}