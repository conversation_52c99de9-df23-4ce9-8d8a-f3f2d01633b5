from __tester__ import load_module, run
from __runner__ import Context


def test_transducer_template():
    transducer_template = load_module("pride/1.0.0/transducer_template.py")

    context = Context()

    result = run(transducer_template.transducer_template, context=context)
    print(result)


def test_transducer():
    transducer = load_module("pride/1.0.0/transducer.py")

    context = Context()
    params = {"device_id": "PT-3001"}

    result = run(transducer.transducer, context=context, params=params)
    print(result)


if __name__ == "__main__":
    test_transducer_template()
    # test_transducer()