import json.tool
from __runner__ import tool, Context
import requests
import base64
import pandas as pd
import io
import json
import os
import time
import ast
from datetime import datetime
import re

# 预警问题定义与标准化
@tool(version="*")
async def ap_question_definition_standard(context: Context, params: any):
    formResult = await context.get_interaction("ap_question_define_form")
    if formResult is None:
        await context.add_view({
            "format": "markdown",
            "content":"经过模型的分析与识别获取了一些关键的信息，现在请确认这些信息，以便更好的生成解决方案。"
        })

        target_vars = params["target_variables"]
        target_describs = [item["description"] for item in target_vars if isinstance(item, dict) and "description" in item]
        target_vars_describ_str = "、".join(target_describs) if target_describs else ""

        user_specified_input_vars= params["user_specified_input_variables"]
        user_specified_input_vars_describs = [item["description"] for item in user_specified_input_vars if isinstance(item, dict) and "description" in item]
        user_specified_input_vars_describ_str = "、".join(user_specified_input_vars_describs) if user_specified_input_vars_describs else ""
        
        context.require_interaction({
            "id": "ap_question_define_form",
            "title": "关键信息补充与确认",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        "industry": {
                            "title": "所属行业",
                            "type": "string"
                        },
                        "device_type": {
                            "title": "装置类型",
                            "type": "string"
                        },
                         "target_variables": {
                            "title": "您需要重点预警的目标变量为：",
                            "type": "string"
                        },
                        "lower_upper": {
                            "title": "预警目标变量的安全范围为（用低限~高限表示）：",
                            "type": "string"
                        },
                        "user_specified_input_variables": {
                            "title": "您认为对预警目标变量有影响的因素有哪些？(非必填)：",
                            "type": "string"
                        },
                        "prediction_output_time_period":{
                            "title": "您需要提前多久检测出异常（时长越短、预警越精准）：",
                            "type": "integer",
                            "enum": [5,10,30],
                            "enumNames": ["5分钟", "10分钟","30分钟"],
                            "widget": "radio",
                        },
                        "specified_algorithms":{
                            "title": "默认使用TPT时序大模型进行微调训练，您是否需要使用传统的机器学习方法：",
                            "type": "integer",
                            "enum": [0, 1],
                            "enumNames": ["TPT时序大模型", "传统机器学习模型"],
                            "widget": "radio",
                        },
                        "train_device_type":{
                            "title": "默认使用GPU进行微调训练，您是否需要使用CPU：",
                            "type": "string",
                            "enum": ["gpu", "cpu"],
                            "enumNames": ["GPU", "CPU"],
                            "widget": "radio",
                        },
                        "file": {
                            "title": "若您有相关工艺资料、历史维护记录，欢迎上传以便更精准的生成预警方案(非必填)：",
                            "type": "string",
                            "format": "file-object",
                            "widget": "tptfile"
                        }
                    },
                    "order":["industry","device_type","target_variables","lower_upper","user_specified_input_variables","prediction_output_time_period","specified_algorithms","train_device_type","file"],
                    "required": ["industry","device_type","target_variables","lower_upper"]
                },
                "default": {
                    "industry":params["domain_knowledge"]["industry"],
                    "device_type": params["domain_knowledge"]["device_type"],
                    "target_variables":target_vars_describ_str,
                    "user_specified_input_variables":user_specified_input_vars_describ_str,
                    "prediction_output_time_period": params["prediction_output_time_period"],
                    "specified_algorithms" :0,
                    "train_device_type" : "gpu"
                }
            }
        })   
    else:
        output_result = params
        target_vars_items  = formResult["target_variables"].split("、")
        lower_upper_items = formResult["lower_upper"].split("、")
        user_specified_input_vars_items  = formResult["user_specified_input_variables"].split("、")
        output_result["domain_knowledge"] = {
            "industry":formResult["industry"],
            "device_type": formResult["device_type"]
        }
        try:
            target_vars = []
            for i, item in enumerate(target_vars_items):
               lower_upper_arr = lower_upper_items[i].split("~")
               target_vars += [{"tag": "", "description": item.strip(),"lower":float(lower_upper_arr[0]),"upper":float(lower_upper_arr[1])}] 
            output_result["target_variables"]   =   target_vars
        except Exception as e:
            output_result["target_variables"] = [{"tag": "", "description": item.strip(),"lower":None,"upper":None} for item in target_vars_items]
        output_result["user_specified_input_variables"]= [{"tag": "", "description": item.strip()} for item in user_specified_input_vars_items]
        output_result["prediction_output_time_period"]= formResult["prediction_output_time_period"]
        output_result["prediction_input_time_period"]= output_result["prediction_output_time_period"]*4
        output_result["specified_algorithms"] = formResult["specified_algorithms"]
        output_result["train_device_type"] = formResult["train_device_type"]
        if 'file' not in formResult:
            output_result["alarm_Related_file"] = {
                "bucket":"recommend",
                "object":"",
                "name":""
            }
        else:
            output_result["alarm_Related_file"] = json.loads(formResult["file"])

        prompt_str  =f"""
#角色
你是一个专业的数据分析师，能够对输入的json数据进行精准解读与分析。你要根据json结构中的不同数据组合情况，按照对应的填充规则进行替换和总结分析，并确保总结内容总字数少于200字。

## 技能
### 技能 1: 分析json数据
1. 接收用户输入的json数据，数据结构如下：
{output_result} 
2. 根据json数据中的不同情况进行分析总结：
    - 如果“prediction_output_time_period”与“user_specified_input_variables”都有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法针对[3]变量执行预测预警算法，包含未来趋势的预测预警和历史数据的异常检测，明确关键的影响因素为[5]。
    - 如果“prediction_output_time_period”与“user_specified_input_variables”都没有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法针对[3]变量执行预测预警算法，包含未来趋势的预测预警和历史数据的异常检测，不清楚关键影响因素，未填写内容后续我将根据您上传的数据自动判断。
    - 如果“prediction_output_time_period”有内容填写，“user_specified_input_variables”没有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法针对[3]变量执行预测预警算法，包含未来趋势的预测预警和历史数据的异常检测，不清楚关键影响因素，未填写内容后续我将根据您上传的数据自动判断。
    - 如果“prediction_output_time_period”没有内容填写，“user_specified_input_variables”有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法针对[3]变量执行预测预警算法，包含未来趋势的预测预警和历史数据的异常检测，明确关键的影响因素为[5]，未填写内容后续我将根据您上传的数据自动判断。
3. 填充规则:
    - `[0]`: 对应 `task_type`，其中，"0"表示RTO，"1"表示预警，"2"表示时间序列预测，"3"表示回归；(最后回答时候，用对应的中文表示)
    - `[1]`: 对应 `domain_knowledge.industry`。(最后回答时候，用对应的中文表示)
    - `[2]`: 对应 `domain_knowledge.device_type`。(最后回答时候，用对应的中文表示)
    - `[6]`: 对应 `specified_algorithms`，0是TPT时序大模型，1是传统机器学习模型。(最后回答时候，用对应的中文表示)
    - `[3]`: 对应 `target_variables`的内容，如果多个用顿号连接。
    - `[4]`: 对应 `prediction_output_time_period`。
    - `[5]`: 对应 `user_specified_input_variables` 的内容，如果多个用顿号连接。

## 限制:
- 只围绕输入的json数据进行分析解读，拒绝回答与数据结构分析无关的话题。
- 总结分析内容必须按照给定的示例格式进行组织，不能偏离框架要求。
- 总结分析部分不能超过200字。
- 回答内容不要产生多余字眼，[]对应的内容替换完整，不要出现中括号。
/no_think
"""
        #调用LLM润色
        prompt_str_obj = {
            "prompt_str" :prompt_str
        }
        llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
        if llm_resp_obj["success"] == 1:
            llm_resp = llm_resp_obj["llm_resp"]
        else:
            llm_resp = f"""
已明确您的意图，识别到这是一个预警任务，您所属行业为{output_result["domain_knowledge"]["industry"]}，装置类型是{output_result["domain_knowledge"]["device_type"]}，
希望基于{"TPT时序大模型" if output_result["specified_algorithms"] == 0 else "传统机器学习模型"}算法针对{output_result["task_objective_description"]}执行预测预警算法，包含未来趋势的预测和历史数据的异常检测，不清楚关键影响因素，未填写内容后续我将根据您上传的数据自动判断。
"""
        #用LLM润色返回的问题
        output_result["task_objective_description"] = llm_resp
        await context.add_view({
            "format": "markdown",
            "content":llm_resp
        })

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '数据源匹配',
                "content":"识别到数据未接入到实时数据系统中，且未找到已经训练好的模型，需要您上传离线数据文件以进行模型调整。",
                "description": "",
                "details": ""
           }
        })

        return output_result

# 离线预警数据选择
@tool(version="*")
async def ap_offline_file_upload(context: Context, params: any):
    formResult = await context.get_interaction("ap_offline_file_upload_form")
    if formResult is None:
        url = context.config["llm_agent_recommend_params"]       
        agent_params= {
            "question": params["task_objective_description"],
            "task_type": params["task_type"]
        }
        # 发送 POST 请求
        response = requests.post(url, json=agent_params)
        # 检查响应状态码
        if response.status_code == 200:
            # 输出响应内容（JSON 格式）
            output = response.json()
            await context.add_view({
                "format": "markdown",
                "content": output["text"]
            })
            #将推荐参数预存在cache中
            await context.set_cache("recommended_tags" , output["parameters"]) 
        else:
            return f"调用参数预设推荐请求失败，状态码: {response.status_code}"

        context.require_interaction({
            "id": "ap_offline_file_upload_form",
            "title": "请上传位号历史数据文件",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "请上传位号历史数据文件",
                    "properties": {
                        "file": {
                            "title": "位号历史数据文件(.csv)，仅支持utf-8编码的.csv格式文件",
                            "type": "string",
                            "format": "file-object",
                            "widget": "tptfile",
                            # "x-validator": "csv_judge_pred"
                        }
                    },
                    "required": ["file"]
                },
                "default": {}
            }
        })
    else:{}

    file = json.loads(formResult["file"])
    # todo 调用数据解析脚本进行解析，得到报告内容，调用算法管理发布的算法
    try:
        alg_params = {
            "input_file" : file,
            "scenario_id"  : params["task_type"]
        }
        response = requests.post(
            url = context.config["ts_data_eval_alg_exec_runtime_url"], 
            data = json.dumps(alg_params,indent=2, ensure_ascii=False),
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        if response.status_code == 200:
            json_obj = json.loads(response.text)
            read_data = json.loads(json_obj["data"])
            try:
                frequency = int(pd.to_timedelta(read_data['time_statistics']['dominant_frequency']).total_seconds())
            except Exception as e:
                frequency = 5
        else:
           return f"调用数据解析请求失败，状态码: {response.status_code}" 
    except Exception as e:
        return "数据质量分析出现异常：" + str(e)
    
    prompt_str  =f"""
# 角色
你是一位专业的数据质量评估师，擅长对各类数据进行深入分析和评估，能够根据数据的各项指标，精准判断数据质量状况，并给出针对性的总结和建议。
## 技能
### 技能 1: 数据质量评估分析
1. 接收输入的类似以下格式的json数据：
{read_data}
2. 针对每一个字段名称，依据标准差、散度（变异系数）、缺失占比、重复值比例、自相关性、偏自相关等重点指标，进行数据质量评估分析。
### 技能 2: 输出总结文字
根据数据质量评估分析结果，输出一段总结，清晰描述数据质量整体状况（好、中、差）。例如：整体来看，数据在缺失情况方面表现良好，但重复值问题较为突出，尤其是 “混合气流量指示控制” 字段。部分字段的自相关性和偏自相关较高，有利于时间序列分析，但也需注意潜在风险。综合考虑，数据质量处于中等水平，在使用这些数据进行分析前，建议对重复值进行处理，以提高数据质量和分析结果的准确性。
## 限制:
- 仅围绕数据质量评估相关内容进行分析和总结，拒绝回答无关话题。
- 总结需逻辑清晰、简洁明了，全面反映数据质量状况。 
/no_think
"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp = ""
    
    markdown_info_1 = ""
    markdown_info_2 = ""

    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_1 += f"| {read_data['en_columns'][i]} | {k} | {v['missing_percentage']} | {v['missing_count']} | {v['duplicate_ratio']} | {v['duplicate_count']} |\n"
        i += 1
    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_2 += f"| {read_data['en_columns'][i]} | {k} | {v['min']} | {v['max']} | {v['mean']} | {v['std']} | {v['divergence']} | {v['q1']} | {v['q2']} | {v['q3']} | {v['iqr']} | {v['autocorrelation_lag1']} | {v['autocorrelation_lag5']} | {v['partial_autocorrelation_lag1']} | {v['partial_autocorrelation_lag5']} | \n"
        i += 1
    markdownRet = f"""
# 数据质量评估报告

## 一、数据概况

### 1. 基本信息

在开始评估之前，我们首先梳理数据集的基本情况如下：

- **文件名称**：{file["name"]}  
- **数据量**：{read_data['row_count']} 行，{len(read_data["feature_statistics"])} 列  
- **时间范围**：{read_data['time_statistics']['min_time']}———{read_data['time_statistics']['max_time']}
- **数据采集频次**：每 {f"{frequency}s"} 采集一次  
- **主要字段说明**：{list(read_data["feature_statistics"].keys())}

### 2. 完整性和唯一性
从完整性和唯一性两个维度分析数据，完整性表示数据是否存在缺失，唯一性表示数据是否存在重复记录，具体情况如下表：
| 位号 | 位号描述 | 缺失值比例 | 缺失值数量 | 重复值比例 | 重复值数量 |
| ------- |-------- | --- | --- | --- | --- |
{markdown_info_1}
### 3. 统计分析
对数据进行统计分析，快速了解数据分布情况，为后续数据清洗和模型训练，重点展示数据的最小值、最大值、分位数等关键统计指标。
计算方法：
标准差：衡量数据离散程度的统计量，表示数据集合中每个数据点与平均值的偏离程度。值越大，数据分布越发散，值越小，数据越集中。
散度：衡量数据分布或者离散程度的相对统计量，消除不同数据集量纲影响，离散系数大，说明数据的离散程度大，携带的信息越多。
第一四分位数(Q1)：25%分位数，将数据分为前25%
第二四分位数(Q2)：50%分位数，即中位数，数据的中间值
第三四分位数(Q3)：75%分位数，将数据分为前75%
自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，反映了同一时间序列在不同时间点的相关程度。
偏自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，同时消除了其他滞后阶数的影响。
| 位号 | 位号描述 | 最小值 | 最大值 | 均值 | 标准差 | 散度 | 第一四分位数 | 中位数 | 第三四分位数 | 四分位距 | 自相关性（滞后1阶）| 自相关性（滞后5阶） | 偏自相关（滞后1阶）| 偏自相关（滞后5阶）|
| ------- |-------- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | 
{markdown_info_2}      
{llm_resp}  

"""

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '数据集解析',
            "description": "数据集解析信息展示",
            "details": markdownRet
        }
    })
    
    output_result = params
    output_result["file"] = file
    output_result["recommended_tags"] = await context.get_cache("recommended_tags")
    output_result["file_variables"] = [{"tag": tag, "description": desc} for tag, desc in zip(read_data["en_columns"], read_data["zn_columns"])]
    
    return output_result

# 预测预警方案制定
@tool(version="*")
async def ap_parse_file_upload(context: Context, params: any):
    result = await context.get_interaction("open_ap_param_page")
    if result is None:  
        #调用参数预设推荐
        agnent_params = {
            "task_type": params["task_type"],
            "user_requirement": params["task_objective_description"],
            "file_variables": params["file_variables"],
            "recommended_tags": params["recommended_tags"]
        }
        response = requests.post(
            url= context.config["llm_agent_auto_model_rec"], 
            data=json.dumps(agnent_params, indent=2, ensure_ascii=False), headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        if response.status_code == 200 :
            #不规则的模版解析出来的json结构存在问题 调用出错
            json_obj = json.loads(response.text)
            #先赋值上下限，再赋值
            for i , item in enumerate(json_obj["target_variables"]):
                item["upper"] = params["target_variables"][i]["upper"]
                item["lower"] = params["target_variables"][i]["lower"]
            params["target_variables"] = json_obj["target_variables"]
            params["input_variables"] = json_obj["input_variables"]
        else:
            return f"调用参数预设推荐请求失败，状态码: {response.status_code}"
        
        target_describs = [item["description"] for item in params["target_variables"] if isinstance(item, dict) and "description" in item]
        target_vars_describ_str = "、".join(target_describs) if target_describs else ""
        input_describs = [item["description"] for item in params["input_variables"] if isinstance(item, dict) and "description" in item]
        input_vars_describ_str = "、".join(input_describs) if input_describs else ""

        await context.add_view({
            "format": "markdown",
            "content":f"""
根据用户上传的数据，结合用户的提问，基于大模型分析得到{target_vars_describ_str}预警方案如下：   
**待检测变量**：{target_vars_describ_str} 
**关键影响因素**：{input_vars_describ_str}
**模型配置**：  1. 时间序列预测模型：用于未来趋势异常研判；2. 异常检测模型：实现历史异常波动捕捉  
具体识别出的变量如界面所示，请确认：
"""
        })

        address = "/tpt-auto-model/warnForm"
        context.require_interaction({
            "id": "open_ap_param_page",
            "title": "模型微调参数信息",
            "type": "open_page",
            "open_page": address,
            "page_type":"execute",
            #增加页面传参，保持回填数据一致
            "page_data":params
        })
    else: {}


    json_obj= {
        "file": params['file'],
        "domain_knowledge" : params["domain_knowledge"],
        "task_type":params["task_type"],
        "target_variables": result["target_variables"],
        "specified_algorithms":result["specified_algorithms"],
        "prediction_input_time_period":result["prediction_input_time_period"],
        "prediction_output_time_period": result["prediction_output_time_period"],
        "input_variables": result["input_variables"],
        "train_device_type":params["train_device_type"]
    }

    return json_obj

# 预警模型微调训练
@tool(version="*")
async def ap_molel_train_file_upload(context: Context, params: any):
    task_type =  params["task_type"]  
    file = params["file"]
    alg_name = "tpt"
    if params["specified_algorithms"] ==1:
        alg_name = "AutoML"
    input_vars = [item["tag"] for item in params["input_variables"]]
    target_vars = [item["tag"] for item in params["target_variables"]]
    origin_params_targetvars = params["target_variables"]
    params["alg_name"] = alg_name
    params["input_variables"]  = input_vars
    params["target_variables"] = target_vars
    params["prediction_input_time_period"] = params["prediction_input_time_period"]*60
    params["prediction_output_time_period"] = params["prediction_output_time_period"]*60

    await context.add_view({
    "format": "markdown",
    "content":f"""
开始对数据进行预处理，获取干净数据，为后续分析和应用奠定良好基础：  
"""
})
    alg_params = {
        "input_file" : file,
        "scenario_id" : params["task_type"],
        "features": input_vars,
        "targets": target_vars
    }
    response = requests.post(
        url = context.config["ts_data_process_alg_exec_runtime_url"], 
        data = json.dumps(alg_params,indent=2, ensure_ascii=False),
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    if response.status_code == 200:
        json_obj = json.loads(response.text)
        #数据处理后产生的新的数据文件
        file = json.loads(json_obj["data"])
    else:
        return f"调用数据预处理请求失败，状态码: {response.status_code}" 
    
    # todo 数据预处理完，对预处理的数据继续进行数据分析，此时 "scenario_id" 默认为9
    alg_params = {
        "input_file" : file,
        "scenario_id"  : 9
    }
    response = requests.post(
        url = context.config["ts_data_eval_alg_exec_runtime_url"], 
        data = json.dumps(alg_params,indent=2, ensure_ascii=False),
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    if response.status_code == 200:
        json_obj = json.loads(response.text)
        read_data = json.loads(json_obj["data"])
        try:
            frequency = int(pd.to_timedelta(read_data['time_statistics']['dominant_frequency']).total_seconds())
        except Exception as e:
            frequency = 5
    else:
        return f"调用预处理后的数据解析请求失败，状态码: {response.status_code}" 
    
    prompt_str  =f"""
# 角色
你是一位专业的数据质量评估师，擅长对各类数据进行深入分析和评估，能够根据数据的各项指标，精准判断数据质量状况，并给出针对性的总结和建议。
## 技能
### 技能 1: 数据质量评估分析
1. 接收输入的类似以下格式的json数据：
{read_data}
2. 针对每一个字段名称，依据标准差、散度（变异系数）、缺失占比、重复值比例、自相关性、偏自相关等重点指标，进行数据质量评估分析。
### 技能 2: 输出总结文字
根据数据质量评估分析结果，输出一段总结，清晰描述数据质量整体状况（好、中、差）。例如：整体来看，数据在缺失情况方面表现良好，但重复值问题较为突出，尤其是 “混合气流量指示控制” 字段。部分字段的自相关性和偏自相关较高，有利于时间序列分析，但也需注意潜在风险。综合考虑，数据质量处于中等水平，在使用这些数据进行分析前，建议对重复值进行处理，以提高数据质量和分析结果的准确性。
## 限制:
- 仅围绕数据质量评估相关内容进行分析和总结，拒绝回答无关话题。
- 总结需逻辑清晰、简洁明了，全面反映数据质量状况。 
/no_think
"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp = ""
    
    markdown_info_1 = ""
    markdown_info_2 = ""
    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_1 += f"| {read_data['en_columns'][i]} | {k} | {v['missing_percentage']} | {v['missing_count']} | {v['duplicate_ratio']} | {v['duplicate_count']} |\n"
        i += 1
    i = 0    
    for k, v in read_data["feature_statistics"].items():
        markdown_info_2 += f"| {read_data['en_columns'][i]} | {k} | {v['min']} | {v['max']} | {v['mean']} | {v['std']} | {v['divergence']} | {v['q1']} | {v['q2']} | {v['q3']} | {v['iqr']} | {v['autocorrelation_lag1']} | {v['autocorrelation_lag5']} | {v['partial_autocorrelation_lag1']} | {v['partial_autocorrelation_lag5']} | \n"
        i += 1
    markdownRet = f"""
# 数据质量评估报告

## 一、数据概况

### 1. 基本信息

在开始评估之前，我们首先梳理数据集的基本情况如下：

- **文件名称**：{file["name"]}  
- **数据量**：{read_data['row_count']} 行，{len(read_data["feature_statistics"])} 列  
- **时间范围**：{read_data['time_statistics']['min_time']}———{read_data['time_statistics']['max_time']}
- **数据采集频次**：每 {f"{frequency}s"} 采集一次  
- **主要字段说明**：{list(read_data["feature_statistics"].keys())}

### 2. 完整性和唯一性
从完整性和唯一性两个维度分析数据，完整性表示数据是否存在缺失，唯一性表示数据是否存在重复记录，具体情况如下表：
| 位号 | 位号描述 | 缺失值比例 | 缺失值数量 | 重复值比例 | 重复值数量 |
| ------- |-------- | --- | --- | --- | --- |
{markdown_info_1}
### 3. 统计分析
对数据进行统计分析，快速了解数据分布情况，为后续数据清洗和模型训练，重点展示数据的最小值、最大值、分位数等关键统计指标。
计算方法：
标准差：衡量数据离散程度的统计量，表示数据集合中每个数据点与平均值的偏离程度。值越大，数据分布越发散，值越小，数据越集中。
散度：衡量数据分布或者离散程度的相对统计量，消除不同数据集量纲影响，离散系数大，说明数据的离散程度大，携带的信息越多。
第一四分位数(Q1)：25%分位数，将数据分为前25%
第二四分位数(Q2)：50%分位数，即中位数，数据的中间值
第三四分位数(Q3)：75%分位数，将数据分为前75%
自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，反映了同一时间序列在不同时间点的相关程度。
偏自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，同时消除了其他滞后阶数的影响。
| 位号 | 位号描述 | 最小值 | 最大值 | 均值 | 标准差 | 散度 | 第一四分位数 | 中位数 | 第三四分位数 | 四分位距 | 自相关性（滞后1阶）| 自相关性（滞后5阶） | 偏自相关（滞后1阶）| 偏自相关（滞后5阶）|
| ------- |-------- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | 
{markdown_info_2}    
{llm_resp}  

"""
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '数据预处理',
            "description": "数据预处理质量评估",
            "details": markdownRet
            }
    })

    #模型训练
    await context.add_view({
        "format": "markdown",
        "content":f"""
接下来，我将选择并构建合适的模型，开始进行模型训练‌：
"""
    })
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": 'TPT模型微调',
            "description": "微调过程",
            "details": """
模型训练日志：   
"""
        }
    })
      
    #用数据处理后的file执行
    csv_path = "s3://" + file["bucket"] + "/" + file["object"]
    params["csv_path"] = csv_path  
    params["frequency"] = frequency
    try:
        alg_result = await context.call_tool("execute_predict_alg",context = context,params=params,module_name="automl")
        if(alg_result["executeStatus"] ==1):
            try:
                output = alg_result["result"][0]
            except Exception as e:
                return f"读取模型训练结果失败，返回信息如: {str(e)}"
        else :
            return f"模型微调过程失败，返回信息如: {alg_result["message"]}"
    except Exception as e: 
            return f"执行模型微调出现异常，返回信息如: {str(e)}"
    
    output_datas = [
    {
        "format": "text",
        "content": f"""
使用{output["method"]}算法，已经训练好模型文件{output["model_file"]}，模型评估结果如下所示：  
| 平均绝对误差MAE | 均方误差MSE | 均方根误差RMSE | 决定系数R2 | 平均绝对百分比误差MAPE |
|-----|-----|-----|-----|-----|
| {output['eval']['MAE']} | {output['eval']['MSE']}  | {output['eval']['RMSE']} | {output['eval']['R2']} | {output['eval']['MAPE']} |   
        
"""
    }]
    if output["loss"] is not None:
        loss_data = output["loss"]
        #生成x轴标签（）
        xAxisData = []
        for i in range(len(loss_data)):
            xAxisData.append(f'{i + 1}')
        echart_options={
            "title": {
                "text": "训练损失 (Loss) 曲线",
            },
            "legend": {
                "data": ["Loss"],
                "top": 10,
                "right": 10
            },
            "tooltip": {
                "trigger": "axis",
                "formatter": """function(params) {return 'Loss: ' + params[0].value.toFixed(3) + '<br/>' +'Epoch: ' + (params[0].dataIndex + 1);}"""
            },
            "grid": {
                "left": "3%",
                "right": "10%",
                "bottom": "3%",
                "containLabel": True
            },
            "xAxis": {
                "name":"",
                "type": "category",
                "boundaryGap": False,
                "data": xAxisData,
                "axisLabel": {
                    "interval": len(loss_data) // 10, 
                    "rotate": 0
                }
            },
            "yAxis": {
                "type": "value",
                "scale": True,
                "name": "Loss值",
                "splitLine": {
                    "show": True
                }
            },
            "series": [
                {
                    "name": "Loss",
                    "type": "line",
                    "smooth": True,
                    "showSymbol": False,
                    "lineStyle": {
                        "type": "solid",
                        "width": 1
                    },
                    "markPoint": {
                        "data": [
                            { "type": "min", "name": "最小值", "symbolSize": 60 },
                            { "type": "max", "name": "最大值" }
                        ],
                        "label": {
                            "formatter": """function(params) {return params.value.toFixed(3);}"""
                        }
                    },
                    "markLine": {
                        "data": [
                            { "type": "average", "name": "平均值" }
                        ],
                        "label": {
                            "formatter": """function(params) {return "平均: " + params.value.toFixed(3);}"""
                        }
                    },
                    "data": loss_data
                }
            ]
        }
        charts_content = {
            "format": "echarts",
            "content": [
                {
                    "chartTheme": 'light',
                    "chartConfig": echart_options,
                },
                {
                    "chartTheme": 'dark',
                    "chartConfig": echart_options,
                }] # 注意保持数组格式
        }
        output_datas.append(charts_content)

    await context.add_view({
        "format": "card",
        "content": {
            "type": "markdown",
            "title": 'TPT模型微调',
            "description": "微调结果展示",
            "details": output_datas
         }
    })
 
    train_obj = {
        "domain_knowledge" : params["domain_knowledge"],
        "csv_path" : csv_path,
        "model_file": output["model_file"],
        "specified_algorithms" : params["alg_name"],
        "target_variables" : origin_params_targetvars,
        "input_variables"  : params["input_variables"],
        "prediction_input_time_period": params["prediction_input_time_period"],
        "prediction_output_time_period": params["prediction_output_time_period"],
        "frequency": frequency
    }

    return train_obj

# 预测预警算法执行
@tool(version="*")
async def ap_exec_file_upload(context: Context, params: any):
   
    try:
        alg_params = {
            "csv_path": params["csv_path"],
            "methods": params["specified_algorithms"],
            "model_file": params["model_file"],
            "prediction_input_time_period": params["prediction_input_time_period"],
            "prediction_output_time_period": params["prediction_output_time_period"],
            "frequency": params["frequency"],
            "input_variables": params["input_variables"],
            "target_variables": params["target_variables"]
        }

        response = requests.post(
            url = context.config["ap_alg_exec_runtime_url"],
            data=json.dumps(alg_params, ensure_ascii=False),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        if response.status_code == 200:
            json_obj = json.loads(response.text)
            json_obj = json.loads(json_obj["data"]["result"]) 
        else:
            return f"调用推理API出现异常，异常代码：{response.status_code}"
    except Exception as e:
        return "执行模型微调验证推理出现异常：" +  str(json.dumps(alg_params,  ensure_ascii=False))

    prompt_str = f"""
角色定位：你是资深预测预警分析师，专注于工业数据的异常诊断与趋势研判，擅长从历史数据与未来预测中精准识别风险信号。
任务说明：
数据输入格式：接收预测预警算法结果如下：
{json_obj}
具体字段含义如下： 
    "tag_describe"：位号描述 
    "upper"：预警上限
    "lower"：预警下限 
    "tag_status"：位号预警状态（10 = 正常，20 = 预警，30 = 报警） 
    "predict_evaluation_info"：预测状态评估，包含： 
        "anomaly_phenomena"：预测趋势异常现象 
        "history_value"：历史值列表（含历史时间段与对应历史值） 
        "predict_value"：预测值列表（含预测时间段与对应预测值） 
    "history_evaluation_info"：历史异常状态评估，包含： 
        "anomaly_number"：历史异常时间段数量 
        "anomaly_time_interval"：历史异常具体时间段 
        "history_anomaly_phenomena"：历史异常时间段对应的异常现象
输出要求：
以 JSON 格式输出四维度分析报告
输出格式如下：
"prediction_analysis": "基于预测数据的趋势解读（趋势变化、异常判断及监测建议）"
"anomaly_reason": "若处于预警/报警状态，分析异常根源；否则留空"
"optimization_suggestions": "若处于预警/报警状态，提出针对性改进方案；否则留空"
"historical_summary": "总结历史异常事件（数量、时段、现象）并强调重点关注时段"
输出示例：
"Prediction_Analysis": "从历史值来看，H2S 浓度呈上升趋势，预测值也持续上升，且部分预测值将超过预警上限4.0，存在超阈值风险。建议持续结合实时数据验证预测趋势，以便及时采取措施。"
"Anomaly_Reason": "结合历史总结及常见工业异常因素，可能是混合气流量异常波动、酸性气流量（负荷）异常波动、主副氧流量波动或阀门异常、系统压力波动导致 H2S 浓度异常升高。"
"Optimization_Suggestions": "建议检查混合气流量、酸性气流量及主副氧流量是否稳定，排查阀门是否存在异常，监测系统压力变化情况。若发现问题及时进行调整或维修。"
"Historical_Summary": "历史上该位号出现过 2 次异常事件，异常时段分别为 2025-06-20 14:30:00  2025-06-20 15:15:00, 2025-06-21 08:45:00  2025-06-21 09:20:00，对应的异常现象为 震荡异常，H2S异常偏高。重点关注历史异常时段，当接近或处于类似工况时加强监测。"
分析要点:
预测趋势分析：结合预警阈值（upper/lower），详细说明未来数据的上升 / 下降趋势、波动幅度，明确是否存在超阈值风险，强调结合实时数据验证预测的必要性。
异常原因挖掘：从设备参数、工艺流程、原料波动等工业场景常见因素出发，结合anomaly_phenomena字段，系统性推导异常诱因。
优化策略制定：针对异常原因，提供可落地的解决方案（如设备检修、参数调整、原料检测等）。
历史异常复盘：梳理历史异常事件，关联异常时段与当前预警的潜在联系，为风险防控提供参考。
执行规范:
严格围绕数据异常检测与预警分析展开，拒绝无关内容。
表述需简洁凝练，确保关键信息一目了然，完整呈现位号运行风险态势。
输出必须是一个有效的JSON对象，不要包含任何其他文本或解释，不要出现tag_status字段。
/no_think
"""
    prediction_analysis =""
    anomaly_reason = ""
    optimization_suggestions =""
    historical_summary = ""

    status_mapping = {
            10: "正常",
            20: "预警",
            30: "报警"
        }

    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
    if llm_resp_obj["success"] == 1:
        # 使用正则表达式匹配有效的 JSON 字符串
        llm_resp = llm_resp_obj["llm_resp"]
        match = re.search(r'\{.*\}', llm_resp, re.DOTALL)
        if match:
            valid_json_str = match.group(0)
            llm_resp_json = json.loads(valid_json_str)
            prediction_analysis = llm_resp_json["prediction_analysis"]
            anomaly_reason = llm_resp_json["anomaly_reason"]
            optimization_suggestions = llm_resp_json["optimization_suggestions"]
            historical_summary = llm_resp_json["historical_summary"]
    else:
        llm_resp = str(json_obj)
        await context.log_info(llm_resp_obj["llm_resp"])
    try :  
        for tag_name, tag_ap_data in json_obj.items():

            alarm_content = f"""
**位号名称**：{tag_name}
**位号描述**：{tag_ap_data["tag_describe"]}
**当前状态**：{status_mapping.get(tag_ap_data["tag_status"], "未知状态")}<br>
        """

#             output_alg_datas = [
#             {
#                 "format": "markdown",
#                 "content": f"""
# **位号名称**：{tag_name}
# **位号描述**：{tag_ap_data["tag_describe"]}
# **当前状态**：{status_mapping.get(tag_ap_data["tag_status"], "未知状态")}<br>
#         """
#             }]

            markarea_intervals = []
             #构建异常区域
            if( tag_ap_data["history_evaluation_info"]["anomaly_number"] >0):
                anomaly_time_datas =  tag_ap_data["history_evaluation_info"]["anomaly_time_interval"]
                anomaly_reason_datas =  tag_ap_data["history_evaluation_info"]["history_anomaly_phenomena"]
                markdown_table = ""
                for i,  interval in enumerate(anomaly_time_datas):
                    start_str, end_str = interval.split(" ~ ")     
                    markarea_intervals.append([
                        {
                            "itemStyle": {
                                "color": "rgba(255, 173, 177, 0.5)"
                            },
                            "xAxis": start_str
                        },
                        {
                            "xAxis": end_str
                        }
                    ])
                    start_time = datetime.strptime(start_str, "%Y-%m-%d %H:%M:%S")
                    end_time = datetime.strptime(end_str, "%Y-%m-%d %H:%M:%S")
                    delta_total_seconds = (end_time - start_time).total_seconds()
                    # 计算到小时、分钟、秒
                    hours = int(delta_total_seconds // 3600)
                    delta_total_seconds %= 3600
                    minutes = int(delta_total_seconds // 60)
                    seconds = int(delta_total_seconds % 60)
                    markdown_table += f"|{i+1}|{start_str}|{end_str}|{hours}小时{minutes}分钟{seconds}秒|{anomaly_reason_datas[i]}\n"
                # 历史异常总结
                alarm_content += f"""
| 序号 | 开始时间 | 结束时间 | 持续时长 | 异常原因 | 
| ---  | ------- | ------- | ------- | ------- |
{markdown_table}

**历史异常总结**：{historical_summary}
            """ 
            output_alg_datas = [
            {
                "format": "markdown",
                "content": alarm_content
            }]
            # 未来趋势分析
            history_data = tag_ap_data["predict_evaluation_info"]["history_value"]
            predict_data = tag_ap_data["predict_evaluation_info"]["predict_value"]

            xAxisData = sorted(set([item["tag_time"] for item in history_data] + [item["tag_time"] for item in predict_data]))
            #为list合并无序准备先默认排好序
            #predict_data_map = {item["tag_time"]: item["predict_value"] for item in predict_data}
            #history_data_map =  {item["tag_time"]: item["history_value"] for item in history_data}
            
            past_values = [item["history_value"] for item in history_data]
            predict_values = [item["predict_value"] for item in predict_data]
    
            past_data = past_values + [""] * len(predict_values)
            pre_data = [""] * (len(past_values)-1) +[past_values[-1]]+ predict_values
            max_data = [tag_ap_data["upper"]]*len(xAxisData)
            min_data = [tag_ap_data["lower"]]*len(xAxisData)   
            markarea_intervals.append([
                {
                    "itemStyle": {
                        "color": "rgba(229, 236, 246)"
                    },
                    "yAxis": tag_ap_data["lower"]
                },
                {
                    "yAxis": tag_ap_data["upper"]
                }
            ])                            
            
            echart_options={
                "title": {
                    "text": tag_name,
                },
                "legend": {
                    "data": ["实际值","预测值","上限","下限"], 
                    "top": 10,
                    "right":10
                },
                "tooltip": {
                    "trigger": "axis", 
                    "axisPointer": {
                        "type": "cross"
                    },
                    "formatter": """function (params) {
                        let result= `${params[0].axisValue} <br/>`;
                        var isAdded = false;
                        params.slice(0, 2).forEach((item) =>{
                        if(item.value && isAdded == false)
                        {
                            result +=`${item.marker} ${item.seriesName}: <b>${parseFloat(`${item.value}`).toFixed(2)}</b><br/>`;        
                            isAdded = true;
                        }
                        else
                        {
                            isAdded = false;
                        }
                    });
                        return result;
                    }
                    """
                },
                "grid": {
                    "left": "3%",
                    "right": "10%",
                    "bottom": "5%",
                    "containLabel": True
                },
                "xAxis": {
                    "name":"时间",
                    "type": "category",
                    "boundaryGap": False,
                    "data": xAxisData
                },
                "yAxis": {
                    "type": "value",
                    "scale": True,
                    "name": "位号值",
                    "splitLine": {
                        "show": True
                    },
                    "min": """function (value) {return (parseFloat(value.min)-Math.abs(parseFloat(value.min)) * 0.05).toFixed(2);}""",
                    "max": """function (value) {return (parseFloat(value.max)+Math.abs(parseFloat(value.max)) * 0.05).toFixed(2);}""",
                    "axisLabel": {
                        "formatter": """function(value) {return parseFloat(`${value}`).toFixed(2);}
                        """
                    }
                },
                "series": [
                    {
                        "name": "实际值",
                        "type": "line",
                        "smooth": True,
                        "showSymbol": False,
                        "lineStyle": {
                            "type": "solid",
                            "width": 2
                        },
                        "markArea": {
                            "data": markarea_intervals
                        },
                        "data": past_data
                    },
                    {
                        "name": "预测值",
                        "type": "line",
                        "smooth": True,
                        "showSymbol": False,
                        "lineStyle": {
                            "type": "dashed",
                            "width": 2
                        },
                        "data": pre_data 
                    },
                    {
                        "name": "上限",
                        "type": "line",
                        "smooth": True,
                        "showSymbol": False,
                        "lineStyle": {
                            "type": "dashed",
                            "width": 2
                        },
                        "itemStyle": {
                            "color":"red"
                        },
                        "data": max_data 
                    },
                    {
                        "name": "下限",
                        "type": "line",
                        "smooth": True,
                        "showSymbol": False,
                        "lineStyle": {
                            "type": "dashed",
                            "width": 2,
                            "color":"rgba(250,200,88)"
                        },
                        "data": min_data 
                    }
                ]
            }
            charts_content = {
                "format": "echarts",
                "content": [
                    {
                        "chartTheme": 'light',
                        "chartConfig": echart_options,
                    },
                    {
                        "chartTheme": 'dark',
                        "chartConfig": echart_options,
                    }] # 注意保持数组格式
            }
            output_alg_datas.append(charts_content) 
            predict_content = f"""
**未来趋势分析**：{prediction_analysis}
        """
            if(tag_ap_data["tag_status"]!=10):
                predict_content+= f"""
**异常原因**：{anomaly_reason}   
**优化建议**：{optimization_suggestions}   
        """
            output_alg_datas.append(
            {
                "format": "markdown",
                "content": predict_content
            })
    except Exception as e:
        return "预警图形化展示出现异常!"+ str(e)
    
    await context.add_view({
        "format": "card",
        "content": {
            "type": "markdown",
            "title": "预测预警算法执行结果",
            "description": "结果分析展示",
            "details": output_alg_datas
         }
    })

    llm_agl_param = {
        "device": params["domain_knowledge"]["device_type"],
        "method": alg_params["methods"],
        "model_file" : alg_params["model_file"],
        "input_variables": alg_params["input_variables"],
        "target_variables": alg_params["target_variables"],
        "alarmResult": llm_resp
    }

    prompt_str  =f"""
你是一个工业预测预警解决方案专家，需要根据提供的预测预警数据（device,method,input_variables,target_variables,alarmResult等字段），生成一份结构化技术报告。报告需包含动态生成的预测预警结果分析，并严格遵循以下格式要求。
{llm_agl_param}

根据上述内容，以target_variables中所有“description”为预测预警目标，生成如下报告：

{"device"}{"target_variables"}预警解决方案(根据用户输入的target_variables中“description”作为关键词生成报告名，字体为整个报告中最大的)

1. {"device"}工艺背景
根据自身对相应工艺的了解，简单叙述，尽可能贴近报告名称的内容。
2.预测预警影响因素分析
根据自身了解，科普可能存在的影响因素，最终引导至input_variables中的位号
3.预测预警模型搭建
为解决“target_variables”中所有“description”预警的问题，通过查找历史数据库案例及阅读文献，建立了以“input_variables”中所有“description”为输入，“target_variables”中所有“description”为输出的“specified_algorithms”模型，并进行微调训练，最终模型为 “model_file”。要求所有值都用输入的中文对应值取代。
组件            配置
输入变量        “input_variables”中所有“description”,多个用、号连接
输出目标        “target_variables”中所有“description”,多个用、号连接
算法架构        {"TPT时序大模型" if  {"method"} == "tpt" else "传统机器学习模型"}
模型版本        {"model_file"}
4.预测预警结果
基于历史运行数据，我构建了“method”预警模型，预警结果表明：
基于“alarmResult”的内容，以预测趋势分析、异常原因、优化建议、历史异常总结四个方面分段描述

后续建议：
1.验证装置中“target_variables”中所有“description”预测预警方案的可行性。
2.验证装置中“target_variables”中所有“description”预测值与实际值的偏差。
3.基于“alarmResult”的内容，给出一句生产建议。
"""   
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" : prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        await context.log_info(llm_resp_obj["llm_resp"])
    # await context.add_view({
    # "format": "card",
    # "content": {
    #     "type": 'markdown',
    #     "title": '预测预警总结',
    #     "description": "解决方案报告",
    #     "details":  llm_resp
    #     }
    # })

    report_file_path = "reports/异常检测解决方案.md"
    file_info = await context.add_file(report_file_path, llm_resp)
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'summary_file',
            "title": '预测预警解决方案',
            "content": "预测预警解决方案已成功生成，请点击 预测预警解决方案 查看",
            "details": file_info,
            "description": "预测预警解决方案.md"
        }
    })
    
    json_obj = {
        "output":json_obj
    }

    return json_obj