from __runner__ import tool, Context
from pathlib import Path

import requests
from pydantic import BaseModel
from typing import List
import json

async def upload_fbd(context,parent_path):
    # 上传数据,返回地址
    fbd_upload_interaction = await context.get_interaction("excel_fbd")
    # 上传标准文件
    if fbd_upload_interaction is None:
        tip1 = "请您根据模板选择禁止匹配的冷热流股。"
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content":tip1,
                "description": "",
                "details": ""
            }
        })

        #提供下载模版
        context.require_interaction(
            {
                "id": "excel_fbd",
                "title": "请上传限制条件（*.xlsx）",
                "type": "form",
                "form": {
                    "form_type":"file",
                    "schema": {
                        "type": "object",
                        "properties": {

                            "file": {
                                "type": "string",
                                "format": "file-object",
                                "widget": "tptfile",
                                "title": "上传限制条件",
                                "template": [{
                                    # OSS 文件路径，强制读取 agent-runner.global 存储桶
                                    "bucket":"redesign",
                                    # 需要提前上传
                                    "object": f"{parent_path}/限制条件输入模板.xlsx",
                                    # 可选，文件名
                                    "name": "限制条件输入模板"
                                }]
                            },

                        },
                        "required": ["file"]
                    },
                    "default": {}
                }
            }
        )
        return {}  # 等待用户操作
    # 上传标准输入
    fbd_file_path = json.loads(fbd_upload_interaction.get("file"))
    return fbd_file_path


async def add_card(context, card_type, content,title):
    await context.add_view({
        "format": "card",
        "content": {
            "type": card_type,
            "title": title,
            "details": content
        }
    })


@tool(version="*")
async def select_forbidden_rule(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]

    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"
    result = await context.get_interaction("forbidden_select")
    if result is None:
        pre_info = "这个环节中，需要用户选择在换热网络改造中如何限制冷热流股之间进行换热。\n"\
                   "选择不考虑限制，则所有冷热流股之间可以自由换热；选择沿用上次限制条件，会检测用户之前是否上传过冷热流股换热限制条件，如果有则直接使用；选择重新指定限制条件，则需要用户上传新的冷热流股换热限制条件。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        tip="请您确认以何种方式在换热网络改造中限制冷热流股之间进行换热。"
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": tip,
                "description": "",
                "details": ""
            }
            })
        context.require_interaction({
            "id": "forbidden_select",
            "title": "需要确认您的现场限制条件，请补充信息",   # 这里直接用思维链中的ability业务名称
            "type": "select",
            "select": [
                {
                    "title": "不考虑现场限制",
                    "data": "no_forbidden",
                    "description":"所有冷热流股都可以互相换热"
                },
                {
                    "title": "沿用上次限制条件",
                    "data": "use_history",
                    "description":"采用上次运行时的冷热流股互相换热限制条件"
                },
                {
                    "title": "重新指定限制条件",
                    "data": "create_new",
                    "description":"根据模版输入禁止互相换热的冷热流股"
                }
            ]
        })
        return {}


    await context.add_view({
    "format": "tip",
    "content": {
        "type": 'default',
        "title": '',
        "content": "请稍作等待，后续需要您补充信息",
        "description": "",
        "details": ""
    }
    })
    if result[0]["data"]=="no_forbidden":
        await context.add_view({
        "format": "markdown",
        "content": "好的，用户未指定任何限制条件，这意味着在接下来的换热网络改造中，所有冷热流股之间均可重新匹配进行互相换热，可能会出现不符合现场情况的冷热流股匹配结果，请用户注意。"
    })
        return {
            "output_fbd": result[0]["data"],
            "fbd_filepath":[],
            "branch_result":0,
            "description":"好的，用户未指定任何限制条件，这意味着在接下来的换热网络改造中，所有冷热流股之间均可重新匹配进行互相换热，可能会出现不符合现场情况的冷热流股匹配结果，请用户注意。"
        }
    elif result[0]["data"]=="use_history":
        # ----------------------------------------判断是否存在处理后的文件--------------------------------
        try:
            input_params={"parent_path":parent_path,"file_name":"/fbd.xlsx"}
            exist_resp = requests.post(
                    url=f"{HEAT_API_HOST}?name=file_exists_py&built_in=0&time_out=600",
                    data=json.dumps(input_params, indent=2, ensure_ascii=False),
                    headers={
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                    }
            )
            exist = exist_resp.json()['data']['exists']
        except Exception as e:
            raise RuntimeError(f"判断上次限制条件文件是否存在失败: {str(e)}")
        if exist:
            await context.add_view({
        "format": "markdown",
        "content": "好的，用户选择了沿用上次限制条件，我们将根据上次上传的限制条件，在换热网络改造时禁止指定冷热流股之间的互相换热，避免改造结果与现场情况发生冲突。"
    })
            return {
                "output_fbd": result[0]["data"],
                "fbd_filepath":[{"bucket":"redesign","object":parent_path + '/fbd.xlsx'}],
                "branch_result":1,
                "description":"好的，用户选择了沿用上次限制条件，我们将根据上次上传的限制条件，在换热网络改造时禁止指定冷热流股之间的互相换热，避免改造结果与现场情况发生冲突。"
            }
        else:
            #生成模板文件
            try:
                fbd_input={"info_path":parent_path}
                fbd_resp = requests.post(
                        url=f"{HEAT_API_HOST}?name=restricted_new_build_py&built_in=0&time_out=600",
                    data=json.dumps(fbd_input, indent=2, ensure_ascii=False),
                    headers={
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                    }
                )
                fbd_resp = fbd_resp.json()['data']
                if fbd_resp["success"]:
                    fbd_file_path=await upload_fbd(context=context,parent_path=parent_path)
                    await context.add_view({
                        "format": "markdown",
                        "content": "好的，用户选择了沿用上次限制条件，但是我们并未检测到有历史限制条件的输入，我们将根据用户重新上传的限制条件，在换热网络改造时禁止指定冷热流股之间的互相换热，避免改造结果与现场情况发生冲突。"
                    })
                    return {
                        "output_fbd": result[0]["data"],
                        "fbd_filepath": [fbd_file_path],
                        "branch_result":1,
                        "description":"好的，用户选择了沿用上次限制条件，但是我们并未检测到有历史限制条件的输入，我们将根据用户重新上传的限制条件，在换热网络改造时禁止指定冷热流股之间的互相换热，避免改造结果与现场情况发生冲突。"
                    }
                else:
                    await context.add_view({
                        "format": "markdown",
                        "content": "好的，用户选择了沿用上次限制条件，但是历史限制条件的输入存在错误，无法解析，此次换热网络改造时不设置限制条件，如果用户想指定限制条件，可以修改后重新上传限制条件。"
                    })
                    return {
                        "output_fbd": result[0]["data"],
                        "fbd_filepath":[],
                        "branch_result":1,
                        "description":"好的，用户选择了沿用上次限制条件，但是历史限制条件的输入存在错误，无法解析，此次换热网络改造时不设置限制条件，如果用户想指定限制条件，可以修改后重新上传限制条件。"
                        }

            except Exception as e:
                raise RuntimeError(f"读取上次限制条件模板文件失败: {str(e)}")
    else:
        # 生成模板文件
        try:
            fbd_input={"info_path":parent_path}
            fbd_resp = requests.post(
                    url=f"{HEAT_API_HOST}?name=restricted_new_build_py&built_in=0&time_out=600",
                data=json.dumps(fbd_input, indent=2, ensure_ascii=False),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
            )
            fbd_resp = fbd_resp.json()['data']
            if fbd_resp["success"]:
                fbd_file_path=await upload_fbd(context=context,parent_path=parent_path)
                await context.add_view({
                        "format": "markdown",
                        "content": "好的，用户选择了上传新的限制条件，我们将根据用户重新上传的限制条件，在换热网络改造时禁止指定冷热流股之间的互相换热，避免改造结果与现场情况发生冲突。"
                    })
                return {
                    "output_fbd": result[0]["data"],
                    "fbd_filepath": [fbd_file_path],
                    "branch_result":1,
                    "description":"好的，用户选择了上传新的限制条件，我们将根据用户重新上传的限制条件，在换热网络改造时禁止指定冷热流股之间的互相换热，避免改造结果与现场情况发生冲突。"
                }
            else:
                await context.add_view({
                        "format": "markdown",
                        "content": "好的，用户选择了上传新的限制条件，但是限制条件的输入存在错误，无法解析，此次换热网络改造时不设置限制条件，如果用户想指定限制条件，可以重新上传限制条件。"
                    })
                return {
                    "output_fbd": result[0]["data"],
                    "fbd_filepath": [],
                    "branch_result":1,
                    "description":"好的，用户选择了上传新的限制条件，但是限制条件的输入存在错误，无法解析，此次换热网络改造时不设置限制条件，如果用户想指定限制条件，可以重新上传限制条件。"
                }
        except Exception as e:
            raise RuntimeError(f"重新指定限制条件失败: {str(e)}")