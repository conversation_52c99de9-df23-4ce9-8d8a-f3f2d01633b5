from __runner__ import tool, Context

@tool(version="*")
async def select_optimization_objective(context: Context, params: any):
    result = await context.get_interaction("opt_select")
    if result is None:
        pre_info = "这个环节中需要了解用户的改造目标。\n"\
                   "如果用户想综合考虑运行成本和设备投资成本，改造目标选择年度净收益最大。\n如果用户想优先考虑运行成本最优的方案，改造目标选择年度化运行成本最低，生成方案的同时会给出总设备投资成本进行参考。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        tip="年度净收益最大会综合考虑运行成本和年度设备投资成本。\n年度运行成本最低只考虑运行成本，但会给出总设备投资成本进行参考。"
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": tip,
                "description": "",
                "details": ""
            }
            })
        context.require_interaction({
            "id": "opt_select",
            "title":  "需要确认您的改造改造目标，请补充信息",  # 这里设置为你的流程节点中文名
            "type": "select",
            "select": [
                {
                    "title": "年度净收益最大",
                    "data": "max_annualized_benefit",
                    "description":"同时考虑公用工程消耗节省量和设备投资情况进行改造"
                },
                {
                    "title": "年度运行成本最低",
                    "data": "min_operating_cost",
                    "description":"只考虑公用工程消耗情况进行改造"
                }
            ]
        })
        return {}
    await context.add_view({
    "format": "tip",
    "content": {
        "type": 'default',
        "title": '',
        "content": "请稍作等待，后续需要您补充信息",
        "description": "",
        "details": ""
    }
    })

    if result[0]["data"]=="max_annualized_benefit":
        await context.add_view({
        "format": "markdown",
        "content": "用户选择了年度净收益最大作为接下来的改造目标。在换热网络中，年度运行成本一般指换热网络公用工程的消耗量，包括水、电、蒸汽、燃料等，按一年运行8400h计算；年度化投资成本主要是换热器的采购和安装费用分摊到每年的投资成本，按设备使用时间为5年进行计算；在改造时，我们会综合考虑年度运行成本的节省量和年度化投资成本这两项的费用，给出合适的改造方案。"
    })
        return {
            "opt_aim": result[0]["data"],
            "description":"用户选择了年度净收益最大作为接下来的改造目标。在换热网络中，年度运行成本一般指换热网络公用工程的消耗量，包括水、电、蒸汽、燃料等，按一年运行8400h计算;\
                            年度化投资成本主要是换热器的采购和安装费用分摊到每年的投资成本，按设备使用时间为5年进行计算；在改造时，我们会综合考虑年度运行成本的节省量和年度化投资成本这两项的费用，给出合适的改造方案。"
        }
    else:
        await context.add_view({
        "format": "markdown",
        "content":"用户选择了年度运行成本最低作为接下来的改造目标。在换热网络中，年度运行成本一般指换热网络公用工程的消耗量，包括水、电、蒸汽、燃料等，按一年运行8400h计算；在改造时，我们会只考虑年度运行成本，给出合适的改造方案，同时会给出一次性投资成本供用户进行参考。"
    })
        return {
            "opt_aim": result[0]["data"],
            "description":"用户选择了年度运行成本最低作为接下来的改造目标。在换热网络中，年度运行成本一般指换热网络公用工程的消耗量，包括水、电、蒸汽、燃料等，按一年运行8400h计算;\
                            在改造时，我们会只考虑年度运行成本，给出合适的改造方案，同时会给出一次性投资成本供用户进行参考。"
        }


