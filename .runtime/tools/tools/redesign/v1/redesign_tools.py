from pathlib import Path
from __runner__ import tool, Context
import requests
from pydantic import BaseModel
from typing import List
import json
from urllib.parse import quote
import ast


async def upload_all(context):
    # 上传数据,返回地址
    std_upload_interaction = await context.get_interaction("std_form")
    # 上传标准文件
    if std_upload_interaction is None:
        # ----------------------------------------提示词--------------------------------
        info1 = "请根据您的需求提供以下信息（可部分填写）：\n"
        info1 += "1. 换热器设计信息（必填）：\n主要包括换热器的热负荷、换热面积、进出口温度、流量、型号等信息；\n可以用于计算换热器的设计效率及改造潜力；\n"
        info1 += "2. 流股信息（建议填写）：\n主要包括流股的流量、流股分支情况和流股流经的换热设备等信息；\n是进行换热网络分析和后续改造的重要信息之一，可以用于绘制换热网络结构图；\n"
        info1 += "3. 公用工程信息（建议填写）：\n主要包括公用工程介质、公用工程价格以及哪些设备属于公用工程设备等信息；\n是进行换热网络分析和后续改造的重要信息之一，和流股信息一起可以对换热器在换热网络中的重要性进行评估；\n"
        info1 += "4. 运行信息（选填）：\n主要包括换热器的温度流量位号以及运行数据；\n提供运行信息可以提供更适合当前运行情况下换热器和换热网络的评估和改造结果；\n"
        info1 += "\n"
        tip1 = "请根据您的需求提供以下信息（可部分填写）：\n"
        tip1 += "1. 换热器设计信息（必填）：\n可以用于计算换热器的设计效率、改造潜力及能量损耗；\n"
        tip1 += "2. 流股信息（建议填写）：\n是进行换热网络分析和后续改造的重要信息之一，可以用于绘制换热网络结构图；\n"
        tip1 += "3. 公用工程信息（建议填写）：\n是进行换热网络分析和后续改造的重要信息之一，和流股信息一起可以对换热器的重要性进行评估；\n"
        tip1 += "4. 运行信息（选填）：\n提供运行信息可以提供更适合当前运行情况下换热器和换热网络的评估和改造结果；\n"
        tip1 += "\n"
        await context.add_view({
            "format": "markdown",
            "content": info1,
        })
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content":tip1,
                "description": "",
                "details": ""
            }
        })

        context.require_interaction(
            {
                "id": "std_form",
                "title": "请上传换热网络设计及运行信息（*.xlsx)",
                "type": "form",
                "form": {
                    "form_type":"file",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "design_file": {
                                "type": "string",
                                "format": "file-object",
                                "widget": "tptfile",
                                "title": "上传换热网络设计信息（.xlsx）",
                                "template": [
                                   {
                                    # OSS 文件路径，强制读取 agent-runner.global 存储桶
                                    "bucket":"redesign",
                                    # 需要提前上传
                                    "object": "hen_opt/换热网络设计信息输入模板.xlsx",
                                    # 可选，文件名
                                    "name": "换热网络设计信息输入模板"
                                }]
                            },
                            "run_file": {
                                "type": "string",
                                "format": "file-object",
                                "widget": "tptfile",
                                "title": "上传换热网络运行信息（.xlsx）",
                                "template": [
                                   {
                                    # OSS 文件路径，强制读取 agent-runner.global 存储桶
                                    "bucket":"redesign",
                                    # 需要提前上传
                                    "object": "hen_opt/换热网络运行信息输入模板.xlsx",
                                    # 可选，文件名
                                    "name": "换热网络运行信息输入模板"
                                }]
                            },
                        },
                        "required": ["design_file"]
                    },
                    "default": {}
                }
            }
        )
        return {}  # 等待用户操作
  
    await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "请稍作等待，后续需要您补充信息",
                "description": "",
                "details": ""
            }
        })
    # 上传标准输入
    std_file_info = json.loads(std_upload_interaction.get("design_file"))
    try:
        run_file_info = json.loads(std_upload_interaction.get("run_file"))
    except:
        run_file_info ={}
    result = {'std': std_file_info, 'run': run_file_info}
    return result


async def add_card(context, card_type, content,title):
    await context.add_view({
        "format": "card",
        "content": {
            "type": card_type,
            "title": title,
            "details": content
        }
    })


# --------------------------------------1)数据校验--------------------------------------------------------------------

@tool(version="*")
async def info_check(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]

    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"

    # ----------------------------------------判断是否存在处理后的文件--------------------------------
    try:
        input_params={"parent_path":parent_path,"file_name":"/std_input.xlsx"}
        exist_resp = requests.post(
                url=f"{HEAT_API_HOST}?name=file_exists_py&built_in=0&time_out=600",
                data=json.dumps(input_params, indent=2, ensure_ascii=False),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
        )
        exist = exist_resp.json()['data']['exists']
    except Exception as e:
        raise RuntimeError(f"请求失败: {repr(e)}，入参{input_params}")

    if exist:
        # ----------------------------------------是否更新输入文件的选择------------------------------
        try:
            result_update = await context.get_interaction("update_select")
            if result_update is None:
                pre_info = "这个环节会先检测用户是否已经上传过数据，如果用户没有上传数据，则需要根据模版进行上传，如果已经上传了，则可以选择是否需要更新数据。" \
                           "上传数据后，我会对数据进行处理，计算换热器、流股的性质，并校验上传的数据是否满足用户的需求。"
                await context.add_view({
                    "format": "markdown",
                    "content": pre_info
                })
                await context.add_view({
                    "format": "markdown",
                    "content": "检测到历史输入信息存在，请问您是否需要更新换热网络设计信息及运行数据？"
                })
                await context.add_view({
                    "format": "tip",
                    "content": {
                        "type": 'default',
                        "title": '',
                        "content": "检测到历史输入信息存在，请问您是否需要更新换热网络设计信息及运行数据？",
                        "description": "",
                        "details": ""
                    }
                })
                await context.require_interaction({
                    "id": "update_select",
                    "title": "请确认您是否需要更新换热网络信息",
                    "type": "select",
                    "select": [
                        {"title": "更新", "data": True},
                        {"title": "不更新", "data": False},
                    ],
                })
                return {}
            update = result_update[0]["data"] if isinstance(result_update, list) else result_update["data"][0]
        except Exception as e:
            raise RuntimeError(f"是否更新选择失败: {repr(e)}")
        await context.add_view({
                    "format": "tip",
                    "content": {
                        "type": 'default',
                        "title": '',
                        "content": "请稍作等待，后续需要您补充信息",
                        "description": "",
                        "details": ""
                    }
                })
        # 更新,先上传再校验
        if update:
            try:
                file_paths = await upload_all(context=context)
                std_file_path, run_file_path = file_paths['std'], file_paths['run']
            except Exception as e:
                raise RuntimeError(f"上传数据失败: {repr(e)}")

            # ----------------------------------------输入处理----------------------------------------
            try:
                deal_input = {"parent_path":parent_path, "design_content":std_file_path, "run_data_info":run_file_path}
                input_deal_resp = requests.post(
                                        url=f"{HEAT_API_HOST}?name=input_deal_api_py&built_in=0&time_out=600",
                                        data=json.dumps(deal_input, indent=2, ensure_ascii=False),
                                        headers={
                                            'Content-Type': 'application/json',
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                                    })
            except Exception as e:
                raise RuntimeError(f"数据处理请求失败: {repr(e)},入参{deal_input}")
            try:
                input_deal_result = input_deal_resp.json()["data"]
                input_deal_result["message"]=ast.literal_eval(input_deal_result["message"])
                if input_deal_result['success']:
                    deal_info=input_deal_result["message"]["deal_info"]
                else:
                    return {
                        "success": input_deal_result["success"],
                        "message": input_deal_result["message"]["err_info"],
                        "able": False
                    }
            except Exception as e:
                raise RuntimeError(f"错误信息：{str(e)}，数据处理返回失败: {input_deal_resp.text}，请求内容:{deal_input}")

            # ----------------------------------------数据校验----------------------------------------
            try:
                check_input={"parent_path":parent_path,"aim":"固定拓扑结构改造"}
                check_resp = requests.post(
                                        url=f"{HEAT_API_HOST}?name=info_check_py&built_in=0&time_out=600",
                                        data=json.dumps(check_input, indent=2, ensure_ascii=False),
                                        headers={
                                            'Content-Type': 'application/json',
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
                                        )
            except Exception as e:
                raise RuntimeError(f"信息校验访问失败: {repr(e)},入参{check_input}")
            result = check_resp.json()['data']
            result["message"]=ast.literal_eval(result["message"])
            await context.add_view({
                "format": "markdown",
                "content": result["message"]["info_after"]
            })
            await add_card(context,"summary",deal_info+""+"\n"+result["message"]["check_info"],title="数据处理与校验结果")

            return {
                "success": result["success"],
                "message": deal_info+""+"\n"+result["message"]["check_info"],
                "able": result["able"]
            }
        # 不更新,直接校验
        else:
            #文件预览
            await add_card(context, "file",{"bucket":"redesign","object":parent_path+"/std_input.xlsx"},
                           title="当前换热网络输入信息")
            try:
                check_input={"parent_path":parent_path,"aim":"固定拓扑结构改造"}
                check_resp = requests.post(
                                        url=f"{HEAT_API_HOST}?name=info_check_py&built_in=0&time_out=600",
                                        data=json.dumps(check_input, indent=2, ensure_ascii=False),
                                        headers={
                                            'Content-Type': 'application/json',
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
                                        )
            except Exception as e:
                raise RuntimeError(f"信息校验访问失败: {repr(e)},入参{check_input}")
            result = check_resp.json()['data']
            result["message"]=ast.literal_eval(result["message"])
            await context.add_view({
                "format": "markdown",
                "content": result["message"]["info_after"]
            })
            await add_card(context,"summary",result["message"]["check_info"],title="数据处理与校验结果")

            return {
                "success": result["success"],
                "message": result["message"]["check_info"],
                "able": result["able"]
            }
    else:
        try:
            file_paths = await upload_all(context=context)
            std_file_path, run_file_path = file_paths['std'], file_paths['run']
        except Exception as e:
            raise RuntimeError(f"上传数据失败: {repr(e)}")

        # ----------------------------------------输入处理----------------------------------------
        try:
            deal_input = {"parent_path":parent_path, "design_content":std_file_path, "run_data_info":run_file_path}
            input_deal_resp = requests.post(
                                    url=f"{HEAT_API_HOST}?name=input_deal_api_py&built_in=0&time_out=600",
                                    data=json.dumps(deal_input, indent=2, ensure_ascii=False),
                                    headers={
                                        'Content-Type': 'application/json',
                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                                })
        except Exception as e:
            raise RuntimeError(f"数据处理请求失败: {repr(e)},入参{deal_input}")
        try:
            input_deal_result = input_deal_resp.json()["data"]
            input_deal_result["message"]=ast.literal_eval(input_deal_result["message"])
            if input_deal_result['success']:
                deal_info=input_deal_result["message"]["deal_info"]
            else:
                return {
                    "success": input_deal_result["success"],
                    "message": input_deal_result["message"]["err_info"],
                    "able": False
                }
        except Exception as e:
            raise RuntimeError(f"错误信息：{str(e)},入参{deal_input},返回{input_deal_resp.text}")

        # ----------------------------------------数据校验----------------------------------------
        try:
            check_input={"parent_path":parent_path,"aim":"固定拓扑结构改造"}
            check_resp = requests.post(
                                    url=f"{HEAT_API_HOST}?name=info_check_py&built_in=0&time_out=600",
                                    data=json.dumps(check_input, indent=2, ensure_ascii=False),
                                    headers={
                                        'Content-Type': 'application/json',
                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
                                    )
        except Exception as e:
                raise RuntimeError(f"信息校验访问失败: {repr(e)},入参{check_input}")
        result = check_resp.json()['data']
        result["message"]=ast.literal_eval(result["message"])
        await context.add_view({
            "format": "markdown",
            "content": input_deal_result["message"]["info_after"] + "\n" +result["message"]["info_after"]
        })
        await add_card(context,"summary",deal_info+""+"\n"+result["message"]["check_info"],title="数据处理与校验结果")

        return {
            "success": result["success"],
            "message": deal_info+""+"\n"+result["message"]["check_info"],
            "able": result["able"]
        }


# --------------------------------------2)换热设备评估--------------------------------------------------------------------
@tool(version="*")
async def ex_eval(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]
    
    pre_info="这个环节会根据换热器的公用工程使用情况、设计效率、运行负荷、改造潜力等指标，对换热器进行评估，评估靠前的换热器建议优先改造。"
    await context.add_view({
        "format": "markdown",
        "content":pre_info
    })
    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"
    try:
        ex_eval_input={"parent_path":parent_path}
        resp = requests.post(
                url=f"{HEAT_API_HOST}?name=ex_eval_api_py&built_in=0&time_out=600",
                data=json.dumps(ex_eval_input, indent=2, ensure_ascii=False),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
        )
    except Exception as e:
        raise RuntimeError(f"换热器评估访问失败: {repr(e)},入参{ex_eval_input}")

    try:
        result = resp.json()['data']
        result["message"]=ast.literal_eval(result["message"])
        await context.add_view({
            "format": "markdown",
            "content": result["message"]["info_after"]
        })
        await add_card(context,"summary",result["message"]["eval_info"],title="换热器评估详情")
        if result["success"]:
            result["eval_result"]=ast.literal_eval(result["eval_result"])
            await add_card(context,"summary_file",result["eval_result"],title="换热器（设备级）评估结果")
        return {
            "success": result["success"],
            "message": result["message"]["eval_info"],
        }
    except Exception as e:
        raise RuntimeError(f"换热器评估返回失败: {repr(e)},入参{ex_eval_input},返回{resp.text}")


# --------------------------------------3)固定拓扑结构改造--------------------------------------------------------------------

@tool(version="*")
async def fix_opt(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]
    
    fix_opt_interaction = await context.get_interaction("fix_opt_form")
    if not fix_opt_interaction:
        pre_info = "这个环节中会根据换热器的评估结果，确定需要优先改造的换热器，再根据用户指定的优化目标、换热器数量、最大改造幅度等条件，寻找最优的换热器面积改造组合。\n"\
                "改造的过程中会考虑换热器的空间限制、压力限制、型号限制等条件，选择对应的标准换热器。\n"\
                "改造的收益计算主要包括运行成本和投资费用，运行成本按年运行时长8400h计算，设备投资费用按照更换换热器的安装费和购置费计算。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        tip="需要您指定改造换热器的数量和换热器面积改造幅度。\n"+\
            "指定后会根据换热器评估的结果选择指定数量的换热器进行改造，并且换热面积的增加幅度不会超过指定的换热器面积改造幅度。"
        # tip="需要确认您想改造的换热器的数量和换热器面积增加的最大幅度，请补充信息。"
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": tip,
                "description": "",
                "details": ""
            }
        })
        context.require_interaction({
            "id": "fix_opt_form",
            "title": "需要确认换热器改造的限制条件，请补充信息",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        "ex_opt_num": {
                            "type": "number",
                            "title": "换热器改造数量",
                            "description": "请指定需要改造的换热器数量",
                            "minimum": 1
                        },
                        "ex_opt_range": {
                            "type": "number",
                            "title": "换热器面积改造幅度",
                            "description": "请指定需要改造的换热器换热面积增加的最大比例（百分比）",
                            "minimum": 20
                        },
                    },
                    "required": [
                        "ex_opt_num",
                        "ex_opt_range",
                    ]
                },
                "default": {
                    "ex_opt_num": 5,
                    "ex_opt_range": 100,
                }
            }
        })
        return {}

    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "换热器改造所需要的信息和数据已补齐，生成改造方案预计需要3~5分钟，您可以关闭此对话离开，如有结果将及时通知您！",
            "description": "",
            "details": ""
        }
    })
    
    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"

    try:
        fix_opt_input = {"parent_path":parent_path,"fix_opt_aim":params['fix_opt_aim'],"ex_opt_name":[],
                    "ex_opt_num":fix_opt_interaction['ex_opt_num'],"ex_opt_range":fix_opt_interaction['ex_opt_range']}
        resp = requests.post(
            url=f"{HEAT_API_HOST}?name=ex_fix_opt_py&built_in=0&time_out=600",
                data=json.dumps(fix_opt_input, indent=2, ensure_ascii=False),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
        )
    except Exception as e:
        raise RuntimeError(f"固定拓扑结构改造访问失败: {repr(e)},入参{fix_opt_input},返回{resp.text}")

    try:
        result = resp.json()['data']
        result["message"]=ast.literal_eval(result["message"])
        await context.add_view({
            "format": "markdown",
            "content": result["message"]["info_after"]
        })
        if result["success"]:
            result["fix_opt_result"]=ast.literal_eval(result["fix_opt_result"])
            result['fix_opt_report']=ast.literal_eval(result['fix_opt_report'])
            result['html_content']=ast.literal_eval(result['html_content'])
            await context.add_view({
                        "format": "card",                      
                        "content": {
                            "title": "换热网络结构图",
                            "details": result['html_content'],
                            "type":"plotly",
                        }
                    })
            await add_card(context, "summary_file", result["fix_opt_result"], title="换热器改造结果")
            await add_card(context, "summary_file",result['fix_opt_report'],title="换热器改造方案报告")
        await add_card(context, "summary", result["message"]["fix_opt_info"],title="换热器改造结果")
        return {
            "success": result["success"],
            "message": result["message"]["fix_opt_info"],}
    except Exception as e:
        raise RuntimeError(f"换热器改造返回失败: {repr(e)}，请求内容为{fix_opt_input}，返回内容为{resp.text}")



# --------------------------------------4)夹点分析--------------------------------------------------------------------
##

@tool(version="*")
async def pinch_point(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]

    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"
    
    pinch_point_interaction = await context.get_interaction("deltaT_form")
    if not pinch_point_interaction:
        pre_info = "该能力将采用夹点分析方法定位换热网络中的能量利用瓶颈，包括识别违反夹点规则的换热器及公用工程。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        tip="最小传热温差会决定夹点分析中的关键夹点位置，一般设置为10~20℃。"
        await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": tip,
            "description": "",
            "details": ""
        }
       })
        context.require_interaction({
            "id": "deltaT_form",
            "title": "需要确认换热网络的最小传热温差，请补充信息",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        "deltaT": {
                            "type": "number",
                            "title": "最小传热温差",
                            "description": "请指定换热器的最小传热温差，最小为10℃，默认为15℃",
                            "minimum": 10
                        },
                    },
                    "required": [
                        "deltaT"
                    ]
                },
                "default": {
                    "deltaT": 15
                }
            }
        })

        return {}

    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "请稍作等待，后续需要您补充信息",
            "description": "",
            "details": ""
        }
    })
    try:
        # 入参采用字典格式
        input_params={"parent_path":parent_path,
                       "dt_min":pinch_point_interaction["deltaT"]}
        # 发送请求
        pinch_deal_resp = requests.post(url=f"{HEAT_API_HOST}?name=pinch_point_py&built_in=0&time_out=600",
                                        data=json.dumps(input_params, indent=2, ensure_ascii=False),
                                        headers={
                                            'Content-Type': 'application/json',
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                                        }
                                        )
    except Exception as e:
        raise RuntimeError(f"请求失败: {repr(e)},入参{input_params}")
    
    try:
        # 获取算法return的内容（['data']不能去掉）
        pinch_deal_result = pinch_deal_resp.json()['data']

        pinch_deal_result["message"]=ast.literal_eval(pinch_deal_result["message"])
        await context.add_view({
            "format": "markdown",
            "content": pinch_deal_result["message"]["result_show"]
        })
        if pinch_deal_result["success"]:
            pinch_deal_result['html_content']=ast.literal_eval(pinch_deal_result['html_content'])
            await context.add_view({
                        "format": "card",                      
                        "content": {
                            "title": "换热网络结构图",
                            "details": pinch_deal_result['html_content'],
                            "type":"plotly",
                        }
                    })
        await add_card(context, "summary",pinch_deal_result["message"]["content"],title="换热网络评估结果")
        return {
            "success": pinch_deal_result["success"],
            "message": pinch_deal_result["message"]["content"],
        }
    except Exception as e:
        raise RuntimeError(f"夹点分析返回失败: {repr(e)}，请求内容为{input_params}，返回内容为{pinch_deal_resp.text}")


# --------------------------------------5)禁配校验--------------------------------------------------------------------

@tool(version="*")
async def restricted_judge(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]

    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"
    
    pre_info = "我需要对用户设定的限制条件进行验证，校验冷热流股配置的合理性"
    await context.add_view({
        "format": "markdown",
        "content": pre_info
    })

    try:
        if params['restricted_excel'] is []:
            # 入参采用字典格式
            input_params={"info_path": parent_path,
                            "restricted_excel": {}}
        else:
            input_params={"info_path": parent_path,
                            "restricted_excel": params["restricted_excel"][0]}
        
        # 发送请求
        restrict_deal_resp = requests.post(url=f"{HEAT_API_HOST}?name=restricted_judge_py&built_in=0&time_out=600",
                                        data=json.dumps(input_params, indent=2, ensure_ascii=False),
                                        headers={
                                            'Content-Type': 'application/json',
                                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                                        }
                                        )
    except Exception as e:
        raise RuntimeError(f"请求失败: {repr(e)},入参:{input_params}")
    # 获取算法return的内容（['data']不能去掉）
    try:
        restrict_deal_result = restrict_deal_resp.json()['data']
        restrict_deal_result["message"]=ast.literal_eval(restrict_deal_result["message"])
        if restrict_deal_result["success"]:
            await context.add_view({
                "format": "markdown",
                "content": restrict_deal_result["message"]["err_info"]
            })
            # await add_card(context, "summary", result["message"]["err_info"], title="限制条件校验结果")
            return {
                "success": restrict_deal_result["success"],
                "return_items": restrict_deal_result["message"]["return_items"],
                "message": restrict_deal_result["message"]["err_info"]
            }
        else:
            return {
                "success": restrict_deal_result["success"],
                "return_items":[],
                "message": restrict_deal_result["message"]["err_info"]
            }
    except Exception as e:
        raise RuntimeError(f"禁配校验返回失败: {repr(e)}，请求内容为{input_params}，返回内容为{restrict_deal_resp.text}")


# ------------------------------------------6)重构改造-------------------------------------------------------------------


@tool(version="*")
async def reconstruct_redesign(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]
    
    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"

    pinch_point_interaction = await context.get_interaction("deltaT_form")
    if not pinch_point_interaction:
        pre_info = "我需要严格按照用户所设定的目标和指定的限制条件，对换热网络实施优化改造："\
                "首先基于当前换热网络拓扑结构，大范围搜寻可能结果，生成一系列的初始方案；之后应用精英策略，在保留优势方案的前提下，不断迭代优化，筛选出所有可行解中的最优解，作为最终改造方案输出给用户。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        tip="最小传热温差是换热过程中冷热流体间允许的最小温度差值，该参数会影响换热网络改造方案的可行性，一般设置为10~20℃。"
        await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": tip,
            "description": "",
            "details": ""
        }
        })
        context.require_interaction({
            "id": "deltaT_form",
            "title": "需要确认换热网络的最小传热温差，请补充信息",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        "deltaT": {
                            "type": "number",
                            "title": "最小传热温差",
                            "description": "请指定换热器的最小传热温差，最小为10℃，默认为15℃",
                            "minimum": 10
                        },
                    },
                    "required": [
                        "deltaT"
                    ]
                },
                "default": {
                    "deltaT": 15
                }
            }
        })

        return {}

    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "换热器网络所需要的信息和数据已补齐，生成改造方案预计需要3~10分钟，您可以关闭此对话离开，如有结果将及时通知您！",
            "description": "",
            "details": ""
        }
    })
    # #一次性输出
    try:
        await context.add_view({
            "format": "markdown",
            "content": "方案生成中，预计需要5~10分钟......"
        })
        # 入参采用字典格式
        input_params={"info_path": parent_path,
                    "target_name": params["target_name"],
                    "restricted_input": [],
                    "deltaT_min": pinch_point_interaction["deltaT"]}
        # 发送请求
        recons_deal_resp = requests.post(url=f"{HEAT_API_HOST}?name=reconstruct_redesign_py&built_in=0&time_out=600",
                                    data=json.dumps(input_params, indent=2, ensure_ascii=False),
                                    headers={
                                        'Content-Type': 'application/json',
                                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                                    },
                                    )
    except Exception as e:
        raise RuntimeError(f"访问重构改造失败: {repr(e)}，入参{input_params}")
    try:
        result = recons_deal_resp.json()['data']
        result["message"]=ast.literal_eval(result["message"])
    
        if result["success"]:
            result["info_save_path"]=ast.literal_eval(result["info_save_path"])
            result['html_content']=ast.literal_eval(result['html_content'])
            await context.add_view({
                        "format": "card",                      
                        "content": {
                            "title": "换热网络结构图",
                            "details": result['html_content'],
                            "type":"plotly",
                        }
                    })
            await add_card(context, "summary_file", result["info_save_path"], title="换热网络改造方案报告")
            await context.append_view("方案已生成")
            await context.append_view(result["message"]["think_output"])
        await add_card(context, "summary", result["message"]["info_output"],title="换热网络改造结果")
        return {
            "success": result["success"],
            "message": result["message"]["info_output"]
        }
    except Exception as e:
        raise RuntimeError(f"重构改造返回失败: {repr(e)}，入参{input_params},返回{recons_deal_resp.text}")


    # 直接打印开始信息（替代卡片日志）
    # 入参采用字典格式
    # input_params={"info_path": parent_path,
    #               "target_name": params["target_name"],
    #               "restricted_input": [],
    #                "deltaT_min": pinch_point_interaction["deltaT"]}
    # # 发送请求
    # recons_deal_resp = requests.post(url=f"{HEAT_API_HOST}?name=reconstruct_redesign_py&built_in=0&time_out=600",
    #                             data=json.dumps(input_params, indent=2, ensure_ascii=False),
    #                             headers={
    #                                 'Content-Type': 'application/json',
    #                                 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #                             },
    #                             stream=True
    #                             )

    # try:
    #     for line in recons_deal_resp.iter_lines():
    #         if line.startswith(b"data:"):
    #             data = json.loads(line.decode("utf-8")[5:].strip())
    #             if "before_cal" in data:
    #                 await add_card(context, "summary", data["before_cal"], title="换热网络改造前置条件分析")
    #             elif "progress" in data:
    #                 await context.add_view({
    #                     "format": "markdown",
    #                     "content": data["progress"]
    #                 })
    #             elif "success" in data:
    #                 await context.add_view({
    #                     "format": "markdown",
    #                     "content": data["message"]["think_output"]
    #                 })
    #                 if data["success"]:
    #                     await add_card(context,"html",data['html_content'],title="查看换热网络结构图")
    #                 await add_card(context, "summary", data["message"]["info_output"],title="换热网络（系统级）改造结果")
    #                 await add_card(context, "summary_file", f"{HEAT_API_HOST}/api/file/download?filename=opt_report.docx&file_path={parent_path}", title="换热网络（系统级）改造方案报告")
    #                 return {"success": data["success"],
    #                         "message": data["message"]["info_output"]} # 返回最终结果
    # except Exception as e:
    #     raise RuntimeError(f"请求失败{repr(e)}")

    # return {
    #         "success": False,
    #         "message": '未成功展示'
    #     }

# ------------------------------------------7)㶲计算-------------------------------------------------------------------

@tool(version="*")
async def exergy_cal(context: Context, params: any):
    HEAT_API_HOST = context.config["base_url"]
    #minio_path=context.config["minio_path"]
    
    tenant_id=context.tenant_id
    user_id=context.user_id
    if tenant_id=='':
        tenant_id=str(0)
    if user_id=='':
        user_id=str(0)
    id_all=tenant_id+"_"+user_id
    parent_path = f"hen_opt/{id_all}_upload"
    # 上传数据,返回地址
    exergy_upload_interaction = await context.get_interaction("exergy_form")

    # 上传标准文件
    if exergy_upload_interaction is None:
        pre_info = '常减压装置一般由电脱盐、闪蒸塔、常压炉、常压塔、减压炉、减压塔等单元组成，主要产品流股包括常顶油、常一线、常二线、常三线、减一线、减二线、减三线、'+\
                '减渣等，主要外部供能包括电、蒸汽、水、燃料等，通过分析每个单元的耗能情况可以清楚的定位到节能降耗的瓶颈。'
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "这各环节需要您上传包含原料信息和流股信息的输入文件，方便我们对每个单元的能量损耗进行分析。",
            "description": "",
            "details": ""
        }
        })
        context.require_interaction(
            {
                "id": "exergy_form",''
                "title": "请上传原料和流股信息（.xlsx）",
                "type": "form",
                "form": {
                    "form_type":"file",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "file": {
                                "type": "string",
                                "format": "file-object",
                                "widget": "tptfile",
                                "title": "上传装置流股信息（.xlsx）",
                                "template": [
                                {
                                    # OSS 文件路径，强制读取 agent-runner.global 存储桶
                                    "bucket":"redesign",
                                    # 需要提前上传
                                    "object": "hen_opt/常减压装置瓶颈分析输入模板.xlsx",
                                    # 可选，文件名
                                    "name": "节能降耗瓶颈分析输入模板"
                                }],
                            },
                        },
                        "required": ["file"]
                    },
                    "default": {}
                }
            }
        )
        return {}  # 等待用户操作
    
    await context.add_view({
    "format": "tip",
    "content": {
        "type": 'default',
        "title": '',
        "content": "请稍作等待，后续需要您补充信息。",
        "description": "",
        "details": ""
    }
    })

    file_info = json.loads(exergy_upload_interaction.get("file"))
    #文件预览
    await add_card(context, "file",{"bucket":file_info["bucket"],"object":file_info["object"]},title="常减压装置整体信息")

    try:
        input_params={"input_filepath":parent_path,"input_content":{"bucket":file_info["bucket"],"object":file_info["object"]}}
        input_deal_resp = requests.post(
                url=f"{HEAT_API_HOST}?name=AT_exergy_cal_py&built_in=0&time_out=600",
                data=json.dumps(input_params, indent=2, ensure_ascii=False),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
        )
    except Exception as e:
        raise RuntimeError(f"请求失败: {repr(e)},入参{input_params}")
    
    try:
        input_deal_result = input_deal_resp.json()['data']
        if input_deal_result['success']:
            await context.add_view({
                "format": "markdown",
                "content": input_deal_result["info"]
            })
        return {
            "success": input_deal_result["success"],
            "message": input_deal_result["info"],
        }
    except Exception as e:
        raise RuntimeError(f"入参{input_params},返回{input_deal_resp.text}")


