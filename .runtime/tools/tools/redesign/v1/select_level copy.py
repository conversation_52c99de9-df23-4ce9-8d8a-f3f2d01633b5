from __runner__ import tool, Context

@tool(version="*")
async def select_retrofit_level(context: Context, params: any):
    result = await context.get_interaction("level_select")
    if result is None:
        pre_info = "这个环节中需要用户选择后续换热网络改造的幅度。\n"\
                   "换热器改造会在不修改当前换热网络结构的情况下通过更换换热器进行改造。\n换热网络改造会在用户指定的限制条件下让冷热流股重新匹配进行改造。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        tip="请您确认要进行换热器改造还是换热网络改造。"
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": tip,
                "description": "",
                "details": ""
            }
            })
        context.require_interaction({
            "id": "level_select",
            "title": "需要确认您的改造方式，请补充信息",  # 这里设置为你的流程节点中文名
            "type": "select",
            "select": [
                {
                    "title": "换热器改造",
                    "data": "device_level_retrofit",
                    "description":"仅更换换热器，投资较少，工程量小，收益较低"

                },
                {
                    "title": "换热网络改造",
                    "data": "system_level_retrofit",
                    "description":"优化换热网络结构，投资较大，工程量大，收益较高"
                    
                }
            ]
        })
        return {}
    await context.add_view({
    "format": "tip",
    "content": {
        "type": 'default',
        "title": '',
        "content": "请稍作等待，后续需要您补充信息",
        "description": "",
        "details": ""
    }
    })

    if result[0]["data"]=="device_level_retrofit":
        await context.add_view({
        "format": "markdown",
        "content": "用户选择了换热器改造，我会先对换热器的设计效率、运行负荷、公用工程信息进行综合评估，再通过更换关键换热器的换热面积进行改造。"
    })
        return {
            "retrofit_level": result[0]["data"],
            "branch_result":0,
            "description":"先对换热器的设计效率、运行负荷、公用工程信息进行综合评估，再针对关键换热器的换热面积进行改造。"
        }
    elif result[0]["data"]=="system_level_retrofit":
        await context.add_view({
        "format": "markdown",
        "content": "用户选择了换热网络改造，我会在用户指定的限制条件下，重新匹配冷热流股进行换热网络的优化改造。"
    })
        return {
            "retrofit_level": result[0]["data"],
            "branch_result":1,
            "description":"先对整个换热网络进行夹点分析，找出跨夹点换热的低效换热器，并针对跨夹点情况重新匹配冷热流股进行改造"
        }
