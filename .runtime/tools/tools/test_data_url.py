import urllib.parse
import base64

def parse_data_url(data_url):
    """
    解析包含文件名和base64数据的data-url
    返回包含媒体类型、解码后的文件名和二进制内容的字典
    """
    # 分离头部元数据和实际内容
    header, encoded_data = data_url.split(",", 1)
    parts = header.split(";")
    
    # 初始化解析结果
    result = {
        "media_type": parts[0],
        "filename": "file",
        "is_base64": False,
        "data": None
    }
    
    # 解析参数部分
    for param in parts[1:]:
        if param.lower() == "base64":
            result["is_base64"] = True
        elif "=" in param:
            key, value = param.split("=", 1)
            if key.lower() == "name":
                # URL解码文件名
                result["filename"] = urllib.parse.unquote(value)

    # 解码数据内容
    if result["is_base64"]:
        # Base64解码（自动处理填充问题）
        decoded_data = base64.b64decode(encoded_data)
    else:
        # 处理普通URL编码数据
        decoded_data = urllib.parse.unquote_to_bytes(encoded_data)
    
    result["data"] = decoded_data
    
    # 尝试转换为UTF-16文本（适用于Windows注册表文件）
    try:
        result["text_content"] = decoded_data.decode("utf-16")
    except UnicodeDecodeError:
        result["text_content"] = "二进制内容无法转换为文本"
    
    return result

data = {
  "file": "data:application/octet-stream;name=VF%E6%B3%A8%E5%86%8C%E8%A1%A8.reg;base64,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",
}

# 示例用法
if __name__ == "__main__":
    data_url = data["file"]
    
    parsed = parse_data_url(data_url)
    
    print(f"解析结果:")
    print(f"文件类型: {parsed['media_type']}")
    print(f"文件名: {parsed['filename']}")
    print(f"数据长度: {len(parsed['data'])} 字节")
    print("\n文本内容预览:")
    print(parsed["text_content"][:500] + "...")  # 预览前500字符