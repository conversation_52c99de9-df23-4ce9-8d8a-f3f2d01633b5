from __tester__ import load_module, run
from __runner__ import Context

pid = load_module("pid/v1/get_device_self_control_rate.py")

context = Context()
context.config = {"installed": False}
context.cache = {
  "tuning_file_form": {
    "file": "data:text/csv;name=%E6%A8%A1%E7%89%88-%E9%87%8D%E6%95%B4%E8%A3%85%E7%BD%AE%E5%9B%9E%E8%B7%AF%E8%A1%A8%E6%A0%BC.csv;base64,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"
  }
}

params = {
  "advanceLoopType": None,
  "groupPath": [],
  "loopDesc": [],
  "loopName": [
    "22200FIC00503"
  ],
  "needTagValue": True,
  "queryingCascade": False,
  "queryingList": False,
  "queryingLoop": True
}

result = run(pid.get_group_or_loop_id_list, context=context, params=params)
print(result)
