from dotenv import load_dotenv
from __runner__ import tool, Context, Log
import os
import aiohttp
import asyncio
import ulid

load_dotenv()

PRIDE_ALGORITHM_ABILITY_HOST = os.getenv(
    "PRIDE_ALGORITHM_ABILITY_HOST", "http://127.0.0.1:6789"
)

@tool(version="^1.0.0")
async def transducer(context: Context, params: any):
    device_id = params.get("device_id")
    url = f"{PRIDE_ALGORITHM_ABILITY_HOST}/api/rpc"
    _id = ulid.new().__str__()
    headers = {
        "Content-Type": "application/json",
        "tenant_id": "default",
        "language": "zh_CN",
    }
    data = {
        "jsonrpc": "2.0",
        "method": "transducer",
        "params": {"device_id": device_id},
        "id": _id,
    }
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.post(url, headers=headers, json=data, timeout=10)
            response.raise_for_status()
            result = await response.json()
            # print(f"Response:{result}")
            """
            {
                "jsonrpc": "2.0",
                "result": {"result": "设备 PT-5001 的最新诊断结果为: 正常"},
                "id": 1,
            }
            """
            return {"output": result["result"]["result"]}
        except aiohttp.ClientError as e:
            # print(f"Request failed: {e}")
            return {"error": str(e)}
        except asyncio.TimeoutError as e:
            # print(f"Request timeout: {e}")
            return {"error": "timeout"}
