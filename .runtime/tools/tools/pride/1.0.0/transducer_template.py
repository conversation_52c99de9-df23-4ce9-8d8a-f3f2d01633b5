from dotenv import load_dotenv
from __runner__ import tool, Context, Log
import os
import aiohttp
import asyncio

load_dotenv()

PRIDE_ALGORITHM_ABILITY_HOST = os.getenv(
    "PRIDE_ALGORITHM_ABILITY_HOST", "http://127.0.0.1:6789"
)

@tool(version="^1.0.0")
async def transducer_template(context: Context, params: any):
    url = f"{PRIDE_ALGORITHM_ABILITY_HOST}/api/v1/template/transducer"
    headers = {
        "Content-Type": "application/json",
        "tenant_id": "default",
        "language": "zh_CN",
    }
    await context.add_view(
        {
            "format": "card",
            "content": {
                "type": "file",
                "title": "变送器历史数据模版下载",
                "details": url,
            },
        }
    )
    return {}
    # async with aiohttp.ClientSession() as session:
    #     try:
    #         response = await session.get(url, headers=headers, timeout=10)
    #         response.raise_for_status()
    #         content = await response.read()
    #         # with open('transducer_template.json', 'wb') as f:
    #         #     f.write(content)
    #         # print(f"{content}")
    #         return content
    #     except aiohttp.ClientError as e:
    #         log.error(f"Request failed: {e}")
    #         return {"error": str(e)}
    #     except asyncio.TimeoutError as e:
    #         log.error(f"Request timeout: {e}")
    #         return {"error": "timeout"}
