import time

from __runner__ import tool, Context
import requests
import json
import os


# from tools.idx.v1.utils import format_range1

# TABLE_NAME = "milvus_m3_indicator"

# IDX_URL = "http://10.50.0.25:8080"

# CHART_URL = "http://10.16.11.25:9412"

# WEB_URL = "http://10.30.72.62:31501/"
# 数据向量查询接口
# DATA_VEC_URL = "http://10.30.73.42:32543"

NO_MATCH_INDEX_NAME="没有找到符合的指标"

@tool(version="*")
async def get_index_properties_by_names(context: Context, params: any):
    # /indexService/dataApi/getIndexPropertiesByNames

    tenant_id = "0" if context.tenant_id == "" else context.tenant_id
    userinput = params.get("userinput")
    indexNames = params.get("indexNames")
    userIndexNames = params.get("userIndexNames")
    if indexNames is None or len(indexNames) == 0:
        valid = False

        return {
            "valid": valid,
            "indexNames": [],
            "userIndexNames": userIndexNames,
            "userinput": userinput
        }

    # get_result = None
    if len(indexNames) > 0:
        get_result = await context.get_interaction("select")
        if get_result is None:
            context.require_interaction({
                "id": "select",
                "title": "根据您的问题，系统匹配到以下相关指标",
                "type": "select",
                "mode": "multiple",
                "select": generate_select_name(indexNames)

            })

        # 提取所有 title，生成新数组
        indexNames = [item["title"] for item in get_result]

    #如果用户选择了"没有找到符合的指标",则走到上传报表的步骤
    if len(indexNames) == 1 and indexNames[0] == NO_MATCH_INDEX_NAME:
        valid = False
        return {
            "valid": valid,
            "indexNames": [],
            "userIndexNames": userIndexNames,
            "userinput": userinput
        }


    response = requests.post(url=context.config["IDX_URL"] + '/indexService/dataApi/getIndexPropertiesByNames'
                             , json={
            'indexNames': indexNames
        },
                             headers={
                                 'Content-Type': 'application/json',
                                 'tenant-id': tenant_id,
                                 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                             })
    if response.status_code == 200:

        json_obj = json.loads(response.text)
        if json_obj.get("code") == 200:
            data = json_obj.get('data')
            if len(data) == 0:
                valid = False
                return {
                    "valid": valid,
                    "indexNames": [],
                    "userIndexNames": userIndexNames,
                    "userinput": userinput
                }
            else:
                valid = True
                indexNames = [
                    item["name"]
                    for item in data
                ]

                return {
                    "valid": valid,
                    "indexNames": indexNames,
                    "userIndexNames": userIndexNames,
                    "userinput": userinput
                }

        else:
            raise RuntimeError(f"指标查询失败-异常信息 {json_obj.get('msg')}")

    else:
        raise RuntimeError(f"指标查询失败-code {response.status_code} 异常信息:{response.text}")


@tool(version="*")
async def data_report(context: Context, params: any):
    userIndexNames = params.get("userIndexNames")
    userinput = params.get("userinput")
    if len(userIndexNames) == 0:
        raise RuntimeError(f"用户问题中提炼的指标名称为空，请检查")

    result = await context.get_interaction("form")
    if result is None:
        context.require_interaction({
            "id": "form",
            "title": "请上传包含问题中所提及指标的Excel文件",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "选择结果",
                    "properties": {
                        "file": {
                            "type": "string",
                            "format": "data-url",
                            "title": "请上传包含问题中所提及指标的Excel文件"
                        },
                    },
                    "required": [
                        "file"
                    ]
                },
                "default": {
                    # 可以不给
                    # "field1": "xxx"
                }
            }
        })
    else:
        # print("=========================")
        pass

    data_url = result['file']

    # return data_url
    # return file_result
    result = parse_data_url(data_url)
    filename = result["filename"]

    # await context.add_view({
    #     "format": "markdown",
    #     "content": f"filename {filename}"
    # })
    if os.path.splitext(filename)[1] != ".xlsx" and os.path.splitext(filename)[1] != ".xls":
        raise RuntimeError("仅支持的EXCEL的格式为.xlsx和.xls,请检查")

    # 4. 构造 multipart/form-data 格式文件对象（类似上传文件）
    files = {
        'files': (filename, result["data"], result["media_type"])  # ('字段名', (文件名, 二进制内容, MIME类型))
    }

    upload_url = f'{context.config["IDX_URL"]}/indicator/report/excel/upload'
    tenant_id = "0" if context.tenant_id == "" else context.tenant_id
    headers = {
        #'Content-Type': 'application/json',
        #'user_id':context.user_id,
        'tenant-id':tenant_id,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }

    data = {
        "user_id": context.user_id  # 表单字段，键 "name" 是接收方期望的字段名
    }

    #upload_response = requests.post(upload_url,json=data,files=files, headers=headers)
    upload_response = requests.post(upload_url, data=data, files=files, headers=headers)

    #await context.log_info(f"|||upload_url {upload_url} |||headers {headers}")
    # tenant_id = request.headers.get("tenant-id")
    if upload_response.status_code == 200:
        json_obj = json.loads(upload_response.text)
        if json_obj.get("code") == 200:
            #await context.log_info(f"|||json_obj {json_obj}")
            items = json_obj.get('data', {}).get('items')
            if len(items) == 0:
                raise RuntimeError(f"上传EXCEL文件失败,返回文件路径为空 {upload_response.text}")

            fileId = items[0].get('fileId')
            taskId = items[0].get('taskId')

            return {"taskId": taskId,
                    "userIndexNames": userIndexNames, "fileId": fileId, "userinput": userinput}
        else:
            raise RuntimeError(f"上传EXCEL文件失败,{upload_response.text}")


    else:
        raise RuntimeError(f"上传EXCEL文件失败，错误信息为：{upload_response.text}")


from urllib.parse import urlencode, quote


@tool(version="*")
async def index_data_confirm(context: Context, params: any):
    if params is None:
        raise RuntimeError("入参未正常传入，请检查")

    userIndexNames = params.get("userIndexNames")

    userinput = params.get("userinput")
    fileId = params.get("fileId")
    # full_filename = os.path.basename(filePath)

    index_flag = await context.get_cache("index_flag")

    # file_path = params['file_path']
    taskId = params['taskId']
    url = f"{context.config['IDX_URL']}/indicator/report/status/{taskId}?user_id={context.user_id}"

    get_data_params = {
        "collection_name": context.config["VEC_TABLE_NAME"],
        "param": userIndexNames[0],
        # "tenant_id": "0"
    }
    all_status_desc = []
    pre = 0
    tenant_id = "0" if context.tenant_id == "" else context.tenant_id
    for _ in range(1800):
        response = requests.get(url=url, headers={
            'Content-Type': 'application/json',
            'tenant-id': tenant_id,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

        if response.status_code == 200:
            json_obj = json.loads(response.text)
            data = json_obj.get('data')
            status = data.get('status')
            status_desc = data.get('statusDesc')
            real_status_desc = status_desc[pre:]

            all_status_desc.extend(real_status_desc)

            pre = len(status_desc)
            result = '\n'.join(line.strip() for line in real_status_desc)
            # result = real_status_desc
            # await context.log_info(f"result11111 results={result}")
            if status == 'COMPLETED' or status == 'PARTIAL_COMPLETION':

                index_resolve_url = f"{context.config['IDX_URL']}/indicator/report/excel/indexdata?user_id={context.user_id}"

                if index_flag == '' or index_flag == None:
                    await context.add_view({
                        "format": "markdown",
                        "content": f"{result}"
                    })
                    filtered_list = [item for item in all_status_desc if "查看结果" in item]

                    if len(filtered_list) > 0:
                        for desc in filtered_list:
                            match = re.search(r'<([^>]+)>', desc)
                            if match:
                                sheetName = match.group(1)
                                index_resolve_param = {
                                    'fileId': fileId,
                                    'sheetName': sheetName
                                }
                                # await context.log_info(f"|||index_resolve_param ={index_resolve_param} index_resolve_url {index_resolve_url}")

                                index_resolve_response = requests.post(url=index_resolve_url
                                                                       , json=index_resolve_param,
                                                                       headers={
                                                                           'Content-Type': 'application/json',
                                                                           'tenant-id': tenant_id,
                                                                           'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                                                                       })

                                if index_resolve_response.status_code == 200:

                                    index_resolve_obj = json.loads(index_resolve_response.text)
                                    if index_resolve_obj.get("code") == 200:
                                        index_resolve_data = index_resolve_obj.get("data")
                                        reportTitle = index_resolve_data.get("reportTitle")
                                        reportSummary = index_resolve_data.get("reportSummary")
                                        real_data = index_resolve_data.get("parsedIndex")
                                        uploadTime = index_resolve_data.get("uploadTime")
                                        fileSize = index_resolve_data.get("fileSize")

                                        # await context.log_info(f"desc {desc} reportTitle={reportTitle}")

                                        await context.add_view({
                                            "format": "card",
                                            "content": {
                                                "type": 'markdown',
                                                "title": f"{reportTitle}",
                                                "description": f"报表数据解析结果",
                                                "details": f"""   
# **{reportTitle}**                                                  
上传时间：{uploadTime}                                                                文件大小：{fileSize} \n             
摘要：{reportSummary} \n                                      

|指标名|维度|值|
| :----: | :----: | :----: |
"""
                                                           + generate_index_resolve_data(real_data),
                                            }
                                        })
                                    else:
                                        raise RuntimeError(f"报表解析接口异常,{index_resolve_obj.get('msg')}")
                                else:
                                    raise RuntimeError(f"报表解析接口异常,{index_resolve_response.text}")

                            else:

                                continue

                time.sleep(1)

                LLM_URL = f"{context.config['LLM_URL']}/llm/parameter"
                llm_params = {
                    "messages": [{
                        "role": "user",
                        "content": f"{userinput}"
                    }],
                    "schema": {
                        "description": "指标名称列表",
                        "properties": {
                            "indexNames": {
                                "description": "指标名称",
                                "items": {
                                    "description": "单个指标名称",
                                    "type": "string"
                                },
                                "type": "array",
                                "x-source": [
                                    "milvus_m3_indicator"
                                ]
                            },
                            "userIndexNames": {
                                "description": "用户问题中的指标名称",
                                "items": {
                                    "description": "用户问题中的单个指标名称",
                                    "type": "string"
                                },
                                "type": "array"
                            }
                        },
                        "type": "object"
                    },
                    "tenant_id": tenant_id
                }
                # await context.log_info( f"|||llm_params ={llm_params}")

                llm_data_response = requests.post(url=LLM_URL, headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                },
                                                  json=llm_params)

                if llm_data_response.status_code == 200:
                    llm_data = json.loads(llm_data_response.text)
                    if llm_data.get("isSuccess"):
                        res = llm_data.get("param")
                        indexNames = res.get("indexNames")
                        if len(indexNames) > 0:
                            get_result = await context.get_interaction("select_data")
                            if get_result is None:
                                await context.set_cache("index_flag", "True")

                                context.require_interaction({
                                    "id": "select_data",
                                    "title": "根据您的问题，系统匹配到以下相关指标",
                                    "type": "select",
                                    "mode": "multiple",
                                    "select": generate_select(indexNames)

                                })

                                # 提取所有 title，生成新数组
                            indexNames = [item["title"] for item in get_result]

                            return {"indexNames": indexNames, "userinput": userinput, "valid": True}
                        else:
                            await context.set_cache("index_flag", "True")
                            return {"indexNames": indexNames, "userinput": userinput, "valid": False}
                            # raise RuntimeError(f"LLM数据接口未查询到数据")
                    else:
                        await context.set_cache("index_flag", "True")
                        raise RuntimeError(f"LLM数据接口查询异常,{llm_data_response.text}")
                else:
                    await context.set_cache("index_flag", "True")
                    raise RuntimeError(f"LLM数据接口查询异常,{llm_data_response.text}")

                # get_data_url = f"{context.config['DATA_VEC_URL']}/v1/search"
                # get_data_response = requests.get(url=get_data_url, headers={
                #     'Content-Type': 'application/json',
                #     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                # },
                #                                  params=get_data_params)
                #
                # if get_data_response.status_code == 200:
                #     get_data = json.loads(get_data_response.text)
                #     if get_data.get("success") == 1:
                #         data = get_data.get("data")
                #         if len(data) > 0:
                #             index_names = data[0].get("metadata").get("name")
                #
                #             get_result = await context.get_interaction("select_data")
                #             if get_result is None:
                #                 await context.set_cache("index_flag", "True")
                #
                #                 context.require_interaction({
                #                     "id": "select_data",
                #                     "title": "根据您的问题，系统匹配到以下相关指标",
                #                     "type": "select",
                #                     "mode": "multiple",
                #                     "select": generate_select([index_names])
                #
                #                 })
                #
                #                 # 提取所有 title，生成新数组
                #             indexNames = [item["title"] for item in get_result]
                #
                #             return {"indexNames": indexNames}
                #
                #         else:
                #             raise RuntimeError(f"向量数据接口未查询到数据")
                #     else:
                #         raise RuntimeError(f"向量数据接口查询异常,{get_data.get('msg')}")
                # else:
                #     raise RuntimeError(f"向量数据接口查询异常,{get_data_response.text}")


            # return {"task_status": status}

            elif status == 'ERROR':
                raise RuntimeError(status_desc)

            else:
                if len(real_status_desc) == 0:
                    # print("skip")
                    pass
                else:
                    await context.add_view({
                        "format": "markdown",
                        "content": f"{result}"
                    })

                time.sleep(5)

        else:
            raise RuntimeError(f"请求文件解析任务接口失败，错误信息为：{response.text}")
    raise RuntimeError(f"文件解析任务超时十分钟,任务id为{taskId}")

    # from urllib.parse import urlencode, urlunparse, quote

    # address = f'/indicator-web/report?fileurl={quote(file_path)}'
    # result = await context.get_interaction("open_page")
    # if result is None:
    #     context.require_interaction({
    #         "id": "open_page",
    #         "title": "打开指标数据确认页面",
    #         "type": "open_page",
    #         "open_page": address
    #     })
    #     return {}
    # else:
    #     return {
    #         "output": result
    #     }


@tool(version="*")
async def get_multiple_index_values(context: Context, params: any):
    if params is None:
        return "入参未正常传入，请检查"

    indexQueries = params['indexQueries']
    # 输出格式：RAW-直接输出原始数据，CHART-图表输出
    outputFormat = params['outputFormat']
    # question = params.get('question', '默认趋势图')

    questionType = params.get('questionType')
    chartType = params.get('chartType')
    chartName = params.get('chartName')
    groupDimension = params.get('groupDimension')
    userinput = params.get("userinput")
    question = params.get("question")
    userId = context.user_id
    tenant_id = "0" if context.tenant_id == "" else context.tenant_id
    # if len(indexQueries) > 0:
    #     check_date_window = indexQueries[0].get("dateWindow")
    #     if time_range_pattern(check_date_window):
    #         # 遍历 indexQueries 并修改 queryType 的值为 "aa"
    #         for item in indexQueries:
    #             item["queryType"] = "DETAIL"
    # else:
    #     raise RuntimeError("param indexQueries is null,please check")
    # question = "11"

    # if dateWindow.count('-') != 0 and dateWindow.count('-') > 1:
    #     return f"dateWindow {dateWindow} 格式生成错误，请检查"
    #
    # dateWindow = format_range(dateWindow)

    request_json = {
        'indexQueries': indexQueries,
        'outputFormat': outputFormat,
        'question': question,
        'userinput': userinput,
        'questionType': questionType,
        'chartType': chartType,
        'chartName': chartName,
        'groupDimension': groupDimension,
        'userId' : userId
    }

    # '/indexService/dataApi/getMultipleIndexValues'
    response = requests.post(url=context.config["IDX_URL"] + '/indexService/dataApi/getIndexValuesByAi'
                             , json=request_json,
                             headers={
                                 'Content-Type': 'application/json',
                                 'tenant-id': tenant_id,
                                 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                             })

    if response.status_code == 200:

        json_obj = json.loads(response.text)
        # 提取 indexValuesMap 下的所有列表项

        if json_obj.get("code") == 200:

            index_values_map = json_obj.get("data", {}).get("indexValues", {})
            if not index_values_map:
                return {"result_data": []}
                # raise RuntimeError("指标数据查询结果为空 ，请检查")
            if outputFormat == 'RAW':
                # if False:
                result_data = []
                expre = ''
                dateType = ''
                for key in index_values_map:

                    for item in index_values_map[key]:
                        dateWindow = item.get("dateWindow")
                        name = item.get("name")
                        dateType = item.get("dateType")
                        teamTime = item.get("teamTime")
                        value = item.get("value")
                        quality = item.get("quality")
                        unit = item.get("unit")
                        dateWindowStr = item.get("dateWindowStr")
                        expre = item.get("expression")
                        desc = item.get("desc")

                        result_data.append({
                            "dateWindow": dateWindow,
                            "dateWindowStr": dateWindowStr,
                            "name": name,
                            "dateType": dateType,
                            "teamTime": teamTime,
                            "value": value,
                            "quality": quality,
                            "unit": unit,
                            "desc": desc
                        })

                # graph_date_window = indexQueries[0].get("dateWindow")

                if (expre is not None and expre != '') and (
                        dateType == 'year' or dateType == 'month' or dateType == 'day' or dateType == 'shift'):
                    graph_index_values_type = indexQueries[0].get("indexValueType")
                    graph_query_type = indexQueries[0].get("queryType")
                    graph_team_time = indexQueries[0].get("teamTime", "")

                    from urllib.parse import quote
                    if len(indexQueries) == 1:
                        graph_index_codes = indexQueries[0].get("indexNames")[0]
                    else:
                        # 提取所有 indexNames 的值
                        tmp_index_names = []
                        for item in indexQueries:
                            tmp_index_names.extend(item["indexNames"])

                        graph_index_codes = ",".join(list(set(tmp_index_names)))

                    encoded_graph_index_codes = quote(graph_index_codes, safe='')

                    # address = f"http://***********:8800/topoGraph?dateWindow={graph_date_window}&indexCodes={graph_index_codes}&indexValueType={graph_index_values_type}&queryType={graph_query_type}&teamTime={graph_team_time}"
                    # graph_address = f'/indicator-web/topoGraph?dateWindow={graph_date_window}&indexCodes={encoded_graph_index_codes}&indexValueType={graph_index_values_type}&queryType={graph_query_type}&teamTime={graph_team_time}'
                    if len(result_data) > 0:
                        graph_date_window = result_data[0].get("dateWindow")
                    else:
                        graph_date_window = "2025-10-01"

                    # graph_address = f'{context.config["WEB_URL"]}/indicator-web/topoGraph?dateWindow={graph_date_window}&indexCodes={encoded_graph_index_codes}&indexValueType={graph_index_values_type}&queryType={graph_query_type}&teamTime={graph_team_time}&dateType={dateType}'
                    graph_address = f'/indicator-web/topoGraph?dateWindow={graph_date_window}&indexCodes={encoded_graph_index_codes}&indexValueType={graph_index_values_type}&queryType={graph_query_type}&teamTime={graph_team_time}&dateType={dateType}'
                    # await context.log_info(f"||||graph_address={graph_address}")
                    graph_result = await context.get_interaction("open_page")
                    if graph_result is None:
                        if len(result_data) > 0:
                            await context.add_view({
                                "format": "card",
                                "content": {
                                    # 必填，卡片的 icon，影响卡片 content 和 details 渲染
                                    # 已支持类型：markdown、summary、think、open_page、file、html、echarts、search、error、warning、code
                                    # 要添加新渲染类型，可以联系前端
                                    "type": "search",
                                    # 必填，卡片的标题（黑色主标题）
                                    "title": "查看计算拓扑图",
                                    # 可选，卡片的副标题（灰色副标题）
                                    "description": "拓扑图信息",
                                    # 可选，卡片内的 body 内容
                                    "content": """
|时间窗口|指标名|时间类型|班次名称|指标值|质量码|单位|
| :----: | :----: | :----: |:----: | :----: | :----: |:----: |
"""
                                               + generate_form(result_data),
                                    # 可选，显示在右侧详情的内容
                                    "details": graph_address,
                                    # 可选，隐藏右侧详情，与 hide_all 独立作用，默认 False
                                    "hide_details": False
                                    # 暂时没有用的字段
                                    # "link": "暂时没用的字段",
                                },
                                # 可选，隐藏左侧卡片，与 hide_details 独立作用，默认 False
                                "hide_all": False
                            })

                    #                         await context.add_view({
                    #
                    #                             "format": "markdown",
                    #                             "url": graph_address,
                    #                             "content": """
                    # |时间窗口|指标名|班组名称|班次名称|指标值|质量码|单位|
                    # | :----: | :----: | :----: |:----: | :----: | :----: |:----: |
                    # """
                    #                                        + generate_form(result_data)
                    #                         })
                    # context.require_interaction({
                    #     "id": "open_page",
                    #     "title": "打开计算过程",
                    #     "type": "open_page",
                    #     "open_page": graph_address
                    # })

                else:
                    graph_result = await context.get_interaction("open_page")
                    if graph_result is None:
                        if len(result_data) > 0:
                            await context.add_view({
                                "format": "card",
                                "content": {
                                    # 必填，卡片的 icon，影响卡片 content 和 details 渲染
                                    # 已支持类型：markdown、summary、think、open_page、file、html、echarts、search、error、warning、code
                                    # 要添加新渲染类型，可以联系前端
                                    "type": "markdown",
                                    # 必填，卡片的标题（黑色主标题）
                                    "title": "结果",
                                    # 可选，卡片的副标题（灰色副标题）
                                    "description": "结果",
                                    # 可选，卡片内的 body 内容
                                    "content": """
|时间窗口|指标名|时间类型|班次名称|指标值|质量码|单位|
| :----: | :----: | :----: |:----: | :----: | :----: |:----: |
"""
                                               + generate_form(result_data)
                                }
                            })

                return {"result_data": result_data}

            elif outputFormat == 'CHART':

                image_data = json_obj.get("data", {}).get("echartOptions", {})
                # 遍历每个 key 对应的列表
                result_data = []
                tmp_result_data = {}
                tmp_result_data.update({"schema": []})
                tmp_result_data.update({"data": {}})

                schema = {
                    "dateWindow": "时间窗口",
                    "dateWindowStr": "时间窗口字符串",
                    "name": "指标名称",
                    "dateType": "时间类型",
                    "teamTime": "班次名称",
                    "value": "指标值",
                    "quality": "质量码",
                    "unit": "单位",
                    "desc": "指标数据描述"

                }

                for key in index_values_map:

                    tmp_result_data.update({"schema": {key: schema}})

                    index_data = []
                    for item in index_values_map[key]:
                        dateWindow = item.get("dateWindow")
                        dateWindowStr = item.get("dateWindowStr")
                        name = item.get("name")
                        dateType = item.get("dateType")
                        teamTime = item.get("teamTime")
                        value = item.get("value")
                        quality = item.get("quality")
                        unit = item.get("unit")
                        desc = item.get("desc")

                        index_data.append({
                            "dateWindow": dateWindow,
                            "dateWindowStr": dateWindowStr,
                            "name": name,
                            "dateType": dateType,
                            "teamTime": teamTime,
                            "value": value,
                            "quality": quality,
                            "unit": unit,
                            "desc": desc
                        })

                        result_data.append({
                            "dateWindow": dateWindow,
                            "dateWindowStr": dateWindowStr,
                            "name": name,
                            "dateType": dateType,
                            "teamTime": teamTime,
                            "value": value,
                            "quality": quality,
                            "unit": unit,
                            "desc": desc
                        })

                    tmp_result_data["data"][key] = index_data

                # res = [
                #     {
                #         "format":"text",
                #         "content":"> 图表生成规则:基于指标名称与时间范围（格式：yyyyMMdd-yyyyMMdd），从指标历史数据库中执行数据查询，并按预设可视化逻辑生成图表"
                #     },
                #     {
                #         "format": "echarts",
                #         "content":image_data
                #     },
                #     {
                #         "format": "text",
                #         "content": "> 注:以上数据来源指标历史数据库,由指标服务提供，数据更新延迟≤10分钟"
                #     }
                #
                # ]
                if len(image_data) > 0:
                    # 构建新结构
                    res = [
                        {
                            "format": "text",
                            "content": f"> {image_data[0]['dataGenerationRule']}"
                        },
                        {
                            "format": "echarts",
                            "content": image_data  # 或者只保留前两个字段也可以过滤
                        },
                        {
                            "format": "text",
                            "content": f"> {image_data[0]['dataSourceTypeDesc']}"
                        }
                    ]
                else:
                    res = [
                        {
                            "format": "text",
                            "content": f"> 默认数据"
                        },
                        {
                            "format": "echarts",
                            "content": image_data  # 或者只保留前两个字段也可以过滤
                        },
                        {
                            "format": "text",
                            "content": f"> 默认数据"
                        }
                    ]

                await context.add_view({
                    "hide_all": False,
                    "type": "view",
                    "format": "card",
                    "content": {
                        "content": "",
                        "description": "",
                        "details": res,
                        "hide_details": False,
                        "link": "",
                        "title": question,
                        "type": "markdown"
                    }
                })

                # await context.add_view({
                #     "hide_all": False,
                #     "type": "view",
                #     "format": "card",
                #     "content":  {
                #           "content": "",
                #           "description": "",
                #           "details": image_data,
                #           "hide_details": False,
                #           "link": "",
                #           "title": question,
                #           "type": "echarts"
                #         }
                # })

                # await context.add_view({
                #     "format": "plotly",
                #     "content": image_data
                # })

                return {
                    "result_data": result_data,
                    "outputFormat": outputFormat,
                    "image_data": image_data,
                }
                # return {
                #     "result_data": result_data,
                #     "image_data": image_data
                # }
                #
                # chart_response = requests.post(url=CHART_URL + '/api/industry_bi/plotting',
                #     json={
                #         'question': question,
                #         'schema': tmp_result_data.get("schema"),
                #         'data': tmp_result_data.get("data")
                #     },
                #     headers={'Content-Type': 'application/json',
                #              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                #              })
                #
                # if chart_response.status_code == 200:
                #
                #     chat_json_obj = json.loads(chart_response.text)
                #
                #     if chat_json_obj.get("code") == 200:
                #         image_data = chat_json_obj.get("image_data")
                #
                #         await context.add_view({
                #             "format": "plotly",
                #             "content": image_data
                #         })
                #
                #         #return result_data
                #         return {"result_data": result_data}
                #         #return image_data
                #     else:
                #         return f"图表生成失败{chart_response.text}"
                # else:
                #     #return "gen chart32111"
                #     return f"图表生成失败 {chart_response.text}"
                # return {"result_data":result_data }
            else:
                raise RuntimeError(f"不支持的输出格式 {outputFormat}")

        else:
            raise RuntimeError(f"指标数据查询-异常信息 {json_obj.get('msg')}")

    else:
        raise RuntimeError(f"指标数据查询-code {response.status_code} 异常信息:{response.text}")


def generate_index_resolve_data(tuningResultList):
    ret = ""
    if len(tuningResultList) > 0:
        for tuningResult in tuningResultList:
            ret = ret + f"|{tuningResult['indexName']}|{tuningResult['timeDimension']}| {tuningResult['value']}|\r\n"
    return ret


def generate_form(tuningResultList):
    # 定义日期类型代码到中文名称的映射
    date_type_mapping = {
        'shift': '班',
        'day': '日',
        'month': '月',
        'year': '年',
        'hour': '小时',
        'minute': '分钟'
    }
    ret = ""
    if len(tuningResultList) > 0:
        for tuningResult in tuningResultList:
            # 获取日期类型代码对应的中文名称，如果不存在则使用原始代码
            date_type = date_type_mapping.get(tuningResult['dateType'], tuningResult['dateType'])

            ret = ret + f"|{tuningResult['dateWindowStr']}|{tuningResult['name']}|{date_type}|{tuningResult['teamTime']}|{tuningResult['value']}|{tuningResult['quality']}|{tuningResult['unit']}|\r\n"

    return ret

def generate_select_name(index_names):
    result = [{"title": name, "data": name} for name in index_names]
    result.append({"title": NO_MATCH_INDEX_NAME, "data": NO_MATCH_INDEX_NAME})
    return result
def generate_select(index_names):
    result = [{"title": name, "data": name} for name in index_names]
    return result


def generate_index(index_names: list):
    return [i + 1 for i in range(len(index_names))]


import urllib.parse
import base64
import re


def parse_data_url(data_url):
    """
    解析包含文件名和base64数据的data-url
    返回包含媒体类型、解码后的文件名和二进制内容的字典
    """
    # 分离头部元数据和实际内容
    header, encoded_data = data_url.split(",", 1)
    parts = header.split(";")

    # 初始化解析结果
    result = {
        "media_type": parts[0],
        "filename": "file",
        "is_base64": False,
        "data": None
    }

    # 解析参数部分
    for param in parts[1:]:
        if param.lower() == "base64":
            result["is_base64"] = True
        elif "=" in param:
            key, value = param.split("=", 1)
            if key.lower() == "name":
                # URL解码文件名
                result["filename"] = urllib.parse.unquote(value)

    # 解码数据内容
    if result["is_base64"]:
        # Base64解码（自动处理填充问题）
        decoded_data = base64.b64decode(encoded_data)
    else:
        # 处理普通URL编码数据
        decoded_data = urllib.parse.unquote_to_bytes(encoded_data)

    result["data"] = decoded_data

    # 尝试转换为UTF-16文本（适用于Windows注册表文件）
    try:
        result["text_content"] = decoded_data.decode("utf-16")
    except UnicodeDecodeError:
        result["text_content"] = "二进制内容无法转换为文本"

    return result


def format_range(s: str) -> str:
    # 如果不是仅包含一个 '-'，则直接返回原字符串
    if s.count('-') != 1:
        return s

    prefix, suffix = s.split('-')
    if len(prefix) == len(suffix):
        return s
    # 根据 prefix 长度判断格式类型
    if len(prefix) == 6:  # YYYYMM
        # 示例: 202505-06 → 202505-202506
        full_suffix = f"{prefix[:4]}{suffix}"
    elif len(prefix) == 8:  # YYYYMMDD
        # 示例: 20250514-18 → 20250514-20250518
        full_suffix = f"{prefix[:6]}{suffix}"
    elif len(prefix) == 10:  # YYYYMMDDHH
        # 示例: 2025051408-16 → 2025051408-2025051416
        full_suffix = f"{prefix[:8]}{suffix}"
    elif len(prefix) == 12:  # YYYYMMDDHHMM
        # 示例: 202505140810-20 → 202505140810-202505140820
        full_suffix = f"{prefix[:10]}{suffix}"
    else:
        # 不符合支持的格式，返回原字符串
        return s

    return f"{prefix}-{full_suffix}"


def time_pattern(time_text: str):
    pattern = r"^(?:\d{4}$|\d{4}(0[1-9]|1[0-2])$|\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$|\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])$|\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])[0-5]\d)$|^((\d{4}-\d{4})|(\d{4}(0[1-9]|1[0-2])-\d{4}(0[1-9]|1[0-2]))|(\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])-\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01]))|(\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])-\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3]))|(\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])[0-5]\d-\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])[0-5]\d))$"
    match = re.match(pattern, time_text)
    if match:
        return True
    else:
        return False


def time_range_pattern(time_range: str):
    pattern = r"""
        ^(?:\d{4}-\d{4})$                          # 年范围
        |^(?:\d{4}(0[1-9]|1[0-2])-\d{4}(0[1-9]|1[0-2]))$  # 月范围
        |^(?:\d{4}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])-\d{4}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01]))$  # 日范围
        |^(?:\d{4}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])(?:[01]\d|2[0-3])-\d{4}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])(?:[01]\d|2[0-3]))$  # 小时范围
        |^(?:\d{4}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])(?:[01]\d|2[0-3])[0-5]\d-\d{4}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])(?:[01]\d|2[0-3])[0-5]\d)$  # 分钟范围
    """
    if re.match(pattern, time_range, re.VERBOSE):
        return True
    else:
        return False
