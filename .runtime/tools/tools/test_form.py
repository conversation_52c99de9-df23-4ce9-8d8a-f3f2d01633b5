from __tester__ import load_module, run
from __runner__ import Context, InteractionError

module = load_module("demo/1.0.0/form.py")

context = Context()

has_interaction = False

try:
    result = run(module.form, context, {})
except InteractionError as e:
    print(f"Interaction: {e}")
    has_interaction = True

assert has_interaction, "Interaction error was expected but not raised."

context.cache["any_form_id"] = {
    "field1": "aa",
    "field2": 1
}

result = run(module.form, context, {})

assert result == context.cache["any_form_id"], f"Expected {context.cache['any_form_id']}, but got {result}"

