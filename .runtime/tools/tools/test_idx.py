import json

import requests

from __tester__ import load_module, run
from __runner__ import Context

idx = load_module("idx/v1/get_index_data.py")

context = Context()
context.config = {"key": "value"}

# 测试不传context参数
# params = {
#     "indexQueries":[
#         {
#              "dateWindow": "202401-202404",
#             "indexCodes":  ["6#汽机发电量"],
#             "indexValueType": "MEA",
#             "queryType": "DETAIL",
#             "teamTime": ""
#         }
#     ],
#     "outputFormat": "RAW"
# }

LLM_URL = f"http://***********:8800/llm/parameter"
llm_params = {
    "messages": [{
        "role": "user",
        "content": f"炼油装置出料是多少"
    }],
    "schema": {
        "description": "指标名称列表",
        "properties": {
            "indexNames": {
                "description": "指标名称",
                "items": {
                    "description": "单个指标名称",
                    "type": "string"
                },
                "type": "array",
                "x-source": [
                    "milvus_m3_indicator"
                ]
            },
            "userIndexNames": {
                "description": "用户问题中的指标名称",
                "items": {
                    "description": "用户问题中的单个指标名称",
                    "type": "string"
                },
                "type": "array"
            }
        },
        "type": "object"
    }
}

llm_data_response = requests.post(url=LLM_URL, headers={
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
},
                                 json=llm_params)

if llm_data_response.status_code == 200:
    llm_data = json.loads(llm_data_response.text)
    if llm_data.get("isSuccess") :
        res = llm_data.get("param")
        indexNames = res.get("indexNames")


params =  {
        "chartName": "multi-bar",
        "chartType": "bar",
        "groupDimension": "TIME_AXIS",

        "indexQueries": [
            {
                "dateWindow": "20250625",
                "groupName": "今天",
                "indexNames": [
                    "一期一次盐水工序_外供_一次盐水"
                ],
                "indexValueType": "MEA",
                "queryType": "SINGLE"
            },
            {
                "dateWindow": "20250624",
                "groupName": "昨天",
                "indexNames": [
                    "一期一次盐水工序_外供_一次盐水"
                ],
                "indexValueType": "MEA",
                "queryType": "SINGLE"
            },
            {
                "dateWindow": "20250623",
                "groupName": "前天",
                "indexNames": [
                    "6#汽机发电量"
                ],
                "indexValueType": "MEA",
                "queryType": "SINGLE"
            }
        ],

        "outputFormat": "RAW",
        "question": "6#汽机发电量季度对比柱状图（2024Q1 vs 2023Q1）",
        "questionType": "compare"
    }


params1 = {
    "indexNames": [
        "6#汽机发电量"
    ]
}


params2 = {
    "file_path":"111",
    "task_id": "36e0f73e-1aab-461e-8ff2-80b6999664f91"
}
#result = run(idx.get_index_properties_by_names, context=context, params=params1)

#result = run(idx.data_report, context=context, params=params)
# result = run(idx.get_multiple_index_values, context=context, params=params)

result = run(idx.index_data_confirm, context=context, params=params2)


print(result)
