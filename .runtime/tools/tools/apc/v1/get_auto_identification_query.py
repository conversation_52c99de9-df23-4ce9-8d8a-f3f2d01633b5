from __runner__ import tool, Context
import requests
import json
import os
from datetime import datetime
from furl import furl
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为APC_Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息


# 根据用户输入的模型名称对数据进行查询
# add by liupeng 2025-05-14
@tool(version="*")
async def get_auto_identification_query(context: Context, params: any):
    modelName = ''
    if "modelName" in params:
        modelName = params['modelName']  # 模型名称
    # 打开page
    # 创建 URL 对象并修改各部分
    f = furl()
    f.path = '/xpt-tpt-apc/modelMatrixList'
    # if len(modelName) > 0:
    #     f.args['modelName'] = modelName
    result = await context.get_interaction("get_auto_identification_query")
    if result is None:
        context.require_interaction({
            "id": "get_auto_identification_query",
            "title": "打开辨识模型数据信息",
            "type": "open_page",
            "open_page": f.url
        })
        return {}
    else:
        return {
            "output": result
        }
