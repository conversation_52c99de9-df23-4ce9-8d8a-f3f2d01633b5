from __runner__ import tool, Context
from furl import furl


# 系统辨识-变量编辑确认界面
# add by liupeng 2025-07-08
@tool(private=True)
async def get_variable_modification(context: Context, params: any):
    # 创建 URL 对象并修改各部分
    mv_list = []
    pv_list = []
    variable_modification_result = await context.get_interaction("variable_modification")
    if variable_modification_result is None:
        await context.log_info(f"上传数据信息 result={params}")
        identProcessInputVariableList = params['result']['identProcessInputVariableList']
        identProcessOutputVariableList = params['result']['identProcessOutputVariableList']
        # 输入变量
        for s in identProcessInputVariableList:
            if "paramName" in s:
                tmp_data = {
                    'variableName': s['variableName'],  # 变量名称
                    'paramName': s['paramName']  # 描述信息
                }
            else:
                tmp_data = {
                    'variableName': s['variableName'],  # 变量名称
                    'paramName': ''  # 描述信息
                }

            mv_list.append(tmp_data)
        # 输出变量
        for s in identProcessOutputVariableList:
            if "paramName" in s:
                tmp_data = {
                    'variableName': s['variableName'],
                    'paramName': s['paramName']
                }
            else:
                tmp_data = {
                    'variableName': s['variableName'],
                    'paramName': ''
                }
            pv_list.append(tmp_data)
        f = furl()
        f.path = '/xpt-tpt-apc/configTable'
        if len(mv_list) > 0:
            f.args['mvList'] = str(mv_list).replace(" ", "")  # 输入变量
        if len(pv_list) > 0:
            f.args['dvList'] = str(pv_list).replace(" ", "")  # 输出变量
        context.require_interaction({
            "id": "variable_modification",
            "title": "请打开变量编辑信息，确认数据信息后点击【下一步】按钮进入模型矩阵",
            "type": "open_page",
            "page_type": "execute",
            "open_page": f.url
        })
        return {}
    return variable_modification_result
