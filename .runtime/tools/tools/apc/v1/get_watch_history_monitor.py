import logging

from __runner__ import tool, Context
import requests
import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

# 控制器/装置指标历史数据查询
# 报表类型为班次时 开始时间和结束时间为空时 默认取前一天时间
# 报表类型为日时 开始时间和结束时间为空时 默认取前一天时间
# 报表类型为周时 开始时间和结束时间为空时 默认取前7天时间
# 报表类型为月时 开始时间和结束时间为空时 默认取前31天时间
# add by liupeng 2025-04-10
@tool(version="*")
async def get_watch_history_monitor(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    myType = params["type"]
    deviceName = '全厂'
    if 'deviceName' in params:
        tempDeviceName = params['deviceName']  # 分组或装置名称
        if tempDeviceName:
            deviceName = tempDeviceName
    controllerName = None
    if 'controllerName' in params:
        controllerName = params.get('controllerName')  # 控制器或回路的名称 get方式不存在返回None

    deviceParam = {'deviceName': deviceName}
    loopId = 0
    if myType == 5:
        # 自定义指标查询 如果返回失败
        metricResponse = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/history/metric/list'
                                      , params=params, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        metricJson = json.loads(metricResponse.text)
        metricTempList = metricJson['data']
        if not metricTempList:
            return format_response(
                success=False,
                message="未找到自定义指标"
            )
        device_id = None
        for item in metricTempList:  # 替换your_json_array为你的实际变量名
            if item.get('metricName') == deviceName:
                device_id = item.get('id')
                break  # 找到第一个匹配项就停止

        # 后续可以直接使用target_id
        if device_id is None:
            return format_response(
                success=False,
                message="未找到对应的自定义指标"
            )

    elif myType == 6:
        # 此情况为回路查询 但是回路条件查询可以不输入回路，只根据分组或装置查询
        device_id = await context.call_tool("get_device_id_by_name", params=deviceParam)  # 根据用户输入的装置名称对装置ID进行获取
        # 如果回路名称没有的情况下就按照全部查询
        if controllerName:
            tempUrl = f"{APC_AGENT_URL}/inter-api/apc-dashboard/v1/history/loop/list?id={device_id}"
            loopMetricResponse = requests.get(tempUrl, headers={
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
            loopMetricJson = json.loads(loopMetricResponse.text)
            loopMetricTempList = loopMetricJson['data']
            for item in loopMetricTempList:  # 替换your_json_array为你的实际变量名
                if item.get('loopName') == controllerName:
                    loopId = item.get('id')
                    break  # 找到第一个匹配项就停止
    else:
        device_id = await context.call_tool("get_device_id_by_name", params=deviceParam)  # 根据用户输入的装置名称对装置ID进行获取

    if device_id == -1:
        return format_response(
            success=False,
            message="未找到对应的装置"
        )

    controllerId = 0  # 控制器id
    searchType = 2  # 报表类型 1班次 2日 3周 4月
    if 'searchType' in params:
        searchType = params["searchType"]  # 报表类型 1班次 2日 3周 4月

    myType = params["type"]  # 1全厂 2装置级 3控制器级 4变量级 5自定义指标 6回路
    groupType = device_id  # 参数id（装置级查询为装置id、控制器查询为控制器id以此类推）
    defaultTimeParam = get_last_7days_exact()
    startTime = defaultTimeParam['start_str']  # 开始时间
    endTime = defaultTimeParam['end_str']  # 开始时间
    if 'startTime' in params:
        tempStartTime = params['startTime']
        if tempStartTime:
            startTime = tempStartTime

    if 'endTime' in params:
        tempEndTime = params['endTime']
        if tempEndTime:
            endTime = tempEndTime
    current = 1  # 页码 默认显示一页
    pageSize = 99999999  # 每页显示多少条数据 默认显示99999999
    if myType == 3 or myType == 4:
        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/controller/list', headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
        controllerJson = json.loads(response.text)
        controllerDataList = [emp for emp in controllerJson['list'] if
                              emp["controllerName"] == controllerName]  # 根据控制器名称去匹配
        controllerId = 0
        if len(controllerDataList) > 0:
            controllerId = controllerDataList[0]['id']  # 控制器id
            groupType = controllerDataList[0]['sysGroupId']  # 控制器id
        else:
            ## 控制器级别可以不用输入控制器，但是变量级别的是需要控制器的
            if myType == 4:
                return format_response(
                    success=False,
                    message="未查询到该控制器"
                )

        # TmpDataList = await context.call_tool("get_controller_table_select", params=params)  # 获取控制器数据列表信息
        # if len(TmpDataList) > 0:
        #     if ControllerName:
        #         DataList = [emp for emp in TmpDataList if emp["controllerName"] == ControllerName]  # 根据控制器名称去匹配得到控制器ID
        #         if len(DataList) > 0:
        #             controllerId = DataList[0]['id']  # 控制器id
        #         else:
        #             raise Exception(f"数据查询失败，参数信息: {params}，APC_AGENT_URL:{APC_AGENT_URL},APC_TOKEN:{APC_AGENT_TOKEN}")
        #             ##return '数据查询失败！'
        # else:
        #     raise Exception(f"数据查询失败，参数信息: {params}，APC_AGENT_URL:{APC_AGENT_URL},APC_TOKEN:{APC_AGENT_TOKEN}")
        ##return '数据查询失败！'
    # current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 当前时间
    # Is_Date = await context.call_tool("is_valid_date_string", params=params)  # 判断字符串数据是否为日期格式
    # if Is_Date:
    #     Is_Time = await context.call_tool("is_valid_datetime_string", params=params)  # 判断字符串数据是否为日期+时间格式
    #     if not Is_Time:  # 时间格式错误 取默认时间段
    #         if searchType == 3:  # 周
    #             formatted_time = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")
    #         elif searchType == 4:  # 月
    #             formatted_time = (datetime.now() - timedelta(days=31)).strftime("%Y-%m-%d %H:%M:%S")
    #         else:
    #             formatted_time = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
    #         startTime = formatted_time  # 开始时间
    #         endTime = current_time_str  # 结束时间
    # else:
    #     if searchType == 3:  # 周
    #         formatted_time = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")
    #     elif searchType == 4:  # 月
    #         formatted_time = (datetime.now() - timedelta(days=31)).strftime("%Y-%m-%d %H:%M:%S")
    #     else:
    #         formatted_time = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
    #     startTime = formatted_time  # 开始时间
    #     endTime = current_time_str  # 结束时间
    if myType == 2 or myType == 5:  # 装置级和自定义指标用的是id
        params = {'current': current, "pageSize": pageSize, "id": device_id, "searchType": searchType,
                  "startTime": startTime,
                  "endTime": endTime, "type": myType}
    elif myType == 3:  # 控制器级别 用户可以不用输入任务控制器  可以查询全部控制器
        if controllerId == 0:  # 当等于0时候相当于插叙全部控制器
            params = {'current': current, "pageSize": pageSize, "groupType": groupType, "searchType": searchType,
                      "startTime": startTime, "endTime": endTime, "type": myType}
        else:  # 查询某个固定的控制器
            params = {'current': current, "pageSize": pageSize, "groupType": groupType, "id": controllerId,
                      "searchType": searchType, "startTime": startTime, "endTime": endTime, "type": myType}
    elif myType == 4:  # 控制器变量历史指标查询
        params = {'current': current, "pageSize": pageSize, "groupType": groupType, "controller": controllerId,
                  "searchType": searchType, "variableType": 3, "startTime": startTime, "endTime": endTime,
                  "type": myType}
    else:
        # 如果looid为空的情况下 说明没有二级查询条件 只有groupType查询字段
        if loopId == 0:
            params = {'current': current, "pageSize": pageSize, "groupType": groupType, "searchType": searchType,
                      "startTime": startTime, "endTime": endTime, "type": myType}
        else:
            params = {'current': current, "pageSize": pageSize, "groupType": groupType, "searchType": searchType,
                      "startTime": startTime, "endTime": endTime, "type": myType, "id": loopId}

    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/history/list'
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if not json_obj:
        return format_response(
            success=False,
            message="无法查询到历史指标信息"
        )
    tempList = json_obj['list']
    if not tempList:
        return format_response(
            success=False,
            message="无法查询到历史指标信息"
        )
    tempInfo = generate_batch_form(tempList, myType, searchType)
    # return {"markDownDetail": detail, "allDeviceHistoryList": allDeviceHistoryList,
    #         "deviceHistoryList": deviceHistoryList, "controllerHistoryList": controllerHistoryList,
    #         "varHistoryList": varHistoryList, "customHistoryList": customHistoryList, "loopHistoryList": loopHistoryList}

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '装置指标历史数据查询',
            "description": "装置指标历史数据查询",
            "details": f"""
{tempInfo["markDownDetail"]}
"""
        }
    })
    tempInfo.pop("markDownDetail")
    return format_response(
        success=True,
        data=tempInfo
    )



def get_last_7days_exact():
    now = datetime.now()

    # 计算开始时间：7天前的当前时刻
    start = now - timedelta(days=7)

    # 格式化为字符串（保留毫秒）
    start_str = start.strftime('%Y-%m-%d %H:%M:%S')
    end_str = now.strftime('%Y-%m-%d %H:%M:%S')

    return {"start_str": start_str, "end_str": end_str}


def generate_batch_form(tempList, myType, searchType):
    ret = ""
    allDeviceHistoryList = []
    deviceHistoryList = []
    controllerHistoryList = []
    varHistoryList = []
    customHistoryList = []
    loopHistoryList = []
    # 报表类型 1班次 2日 3周 4月
    tempTpye = "未知"
    if searchType == 1:
        tempTpye = "班次"
    if searchType == 2:
        tempTpye = "日"
    if searchType == 3:
        tempTpye = "周"
    if searchType == 4:
        tempTpye = "月"
    detail = ""
    # 厂级
    if myType == 1:
        for s in tempList:
            ret = ret + (
                f"|{s.get('name', '')}"
                f"|{s.get('startTime', '')}-{s.get('endTime', '')}"
                f"|{s.get('countOperateFrequency', '')}"
                f"|{s.get('operateFrequency', '')}"
                f"|{s.get('operateRate', '')}"
                f"|{s.get('usableOperateRate', '')}"
                f"|{s.get('closeRate', '')}"
                f"|{s.get('createTime', '')}"
                f"|{tempTpye}\r\n"
            )
            allDeviceTempTime = f"{s.get('startTime', '')}-{s.get('endTime', '')}"
            allDeviceHistoryItem = {"name": s.get('name', ''),
                                    "startTimeAndEndTime": allDeviceTempTime,
                                    "countOperateFrequency": s.get('countOperateFrequency', ''),
                                    "operateFrequency": s.get('operateFrequency', ''),
                                    "operateRate": s.get('operateRate', ''),
                                    "usableOperateRate": s.get('usableOperateRate', ''),
                                    "closeRate": s.get('closeRate', ''),
                                    "createTime": s.get('createTime', '')
                                    }
            allDeviceHistoryList.append(allDeviceHistoryItem)
        detail = f"""
|      名称   |  时间段      |  先控操作频次  |  操作频次  |  投运率(%)  |  有效投运率(%)  |  自控率(%)  |  报表生成时间  |  报表类型  |
|:----------:|:-----------:|:------------:|:--------:|:----------:|:--------------:|:----------:|:-----------:|:---------:|
{ret}
"""
    # 装置级
    if myType == 2:
        for s in tempList:
            ret = ret + (
                f"|{s.get('name', '')}"
                f"|{s.get('groupName', '')}"
                f"|{s.get('startTime', '')}-{s.get('endTime', '')}"
                f"|{s.get('operateRate', '')}"
                f"|{s.get('mvOperateRate', '')}"
                f"|{s.get('cvOperateRate', '')}"
                f"|{s.get('usableOperateRate', '')}"
                f"|{s.get('closeRate', '')}"
                f"|{s.get('countOperateFrequency', '')}"
                f"|{s.get('operateFrequency', '')}"
                f"|{s.get('controllerAllSwitchOperatePercent', '')}"
                f"|{s.get('createTime', '')}"
                f"|{tempTpye}\r\n"
            )
            deviceTempTime = f"{s.get('startTime', '')}-{s.get('endTime', '')}"
            deviceHistoryItem = {
                "name": s.get('name', ''),
                "groupName": s.get('groupName', ''),
                "startTimeAndEndTime": deviceTempTime,
                "operateRate": s.get('operateRate', ''),
                "mvOperateRate": s.get('mvOperateRate', ''),
                "cvOperateRate": s.get('cvOperateRate', ''),
                "usableOperateRate": s.get('usableOperateRate', ''),
                "closeRate": s.get('closeRate', ''),
                "countOperateFrequency": s.get('countOperateFrequency', ''),
                "operateFrequency": s.get('operateFrequency', ''),
                "controllerAllSwitchOperatePercent": s.get('controllerAllSwitchOperatePercent', ''),
                "createTime": s.get('createTime', '')
            }
            deviceHistoryList.append(deviceHistoryItem)
        detail = f"""
|      名称   |  时间段      |     分组    |  装置投运率(%)  |  装置MV投运率(%)  |  装置CV投运率(%)  |  装置有效投运率(%)  |   装置自控率(%)  |  装置总先控操作频次  |  装置操作频次  | 装置控制器开关投运个数百分比(%)  | 报表生成时间 | 报表类型  |
|:----------:|:-----------:|:----------:|:--------------:|:---------------:|:---------------:|:----------------:|:--------------:|:-----------------:|:------------:|:---------------------------:|:----------:|:--------:|
{ret}
"""

    # 控制器级
    if myType == 3:
        for s in tempList:
            ret = ret + (
                f"|{s.get('name', '')}"
                f"|{s.get('startTime', '')}-{s.get('endTime', '')}"
                f"|{s.get('groupName', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('mvCountOperateFrequency', '')}"
                f"|{s.get('cvCountOperateFrequency', '')}"
                f"|{s.get('mvOperatePercent', '')}"
                f"|{s.get('mvOperateRate', '')}"
                f"|{s.get('mvUsableOperateRate', '')}"
                f"|{s.get('cvOperatePercent', '')}"
                f"|{s.get('cvOperateRate', '')}"
                f"|{s.get('cvUsableOperateRate', '')}"
                f"|{s.get('countOperateFrequency', '')}"
                f"|{s.get('controllerAllSwitchOperateRate', '')}"
                f"|{s.get('operateRate', '')}"
                f"|{s.get('usableOperateRate', '')}"
                f"|{s.get('createTime', '')}"
                f"|{tempTpye}\r\n"
            )
            controllerTempTime = f"{s.get('startTime', '')}-{s.get('endTime', '')}"
            controllerHistoryItem = {
                "name": s.get('name', ''),
                "startTimeAndEndTime": controllerTempTime,
                "groupName": s.get('groupName', ''),
                "mvCountOperateFrequency": s.get('mvCountOperateFrequency', ''),
                "cvCountOperateFrequency": s.get('cvCountOperateFrequency', ''),
                "mvOperatePercent": s.get('mvOperatePercent', ''),
                "mvOperateRate": s.get('mvOperateRate', ''),
                "mvUsableOperateRate": s.get('mvUsableOperateRate', ''),
                "cvOperatePercent": s.get('cvOperatePercent', ''),
                "cvOperateRate": s.get('cvOperateRate', ''),
                "cvUsableOperateRate": s.get('cvUsableOperateRate', ''),
                "countOperateFrequency": s.get('countOperateFrequency', ''),
                "controllerAllSwitchOperateRate": s.get('controllerAllSwitchOperateRate', ''),
                "operateRate": s.get('operateRate', ''),
                "usableOperateRate": s.get('usableOperateRate', ''),
                "createTime": s.get('createTime', '')
            }
            controllerHistoryList.append(controllerHistoryItem)

        detail = f"""
|  控制器名称  |  时间段  |  分组 |  控制器描述  |  MV总先控操作频次  | CV总先控操作频次 | MV投运个数百分比(%) | MV平均投运速率(%) | MV平均有效投运速率(%) | CV投运个数百分比(%) | CV平均投运速率(%) | CV平均有效投运速率(%) | 总先控操作频次 | 总开关投运率(%) | 投运率(%) | 有效投运率(%) | 报表生成时间 | 报表类型  |
|:----------:|:-------:|:----:|:-----------:|:---------------:|:-------------:|:----------------:|:---------------:|:------------------:|:-----------------:|:--------------:|:------------------:|:------------:|:------------:|:--------:|:------------:|:---------:|:--------:|
{ret}
"""
    # 变量级
    if myType == 4:
        for s in tempList:
            ret = ret + (
                f"|{s.get('name', '')}"
                f"|{s.get('startTime', '')}-{s.get('endTime', '')}"
                f"|{s.get('groupName', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('controllerName', '')}"
                f"|{s.get('mvOperateRate', '')}"
                f"|{s.get('mvUsableOperateRate', '')}"
                f"|{s.get('mvUpperRate', '')}"
                f"|{s.get('mvLowerRate', '')}"
                f"|{s.get('mvLimitRate', '')}"
                f"|{s.get('countOperateFrequency', '')}"
                f"|{s.get('createTime', '')}"
                f"|{tempTpye}\r\n"
            )

            varTempTime = f"{s.get('startTime', '')}-{s.get('endTime', '')}"
            varHistoryItem = {
                "name": s.get('name', ''),
                "startTimeAndEndTime": varTempTime,
                "groupName": s.get('groupName', ''),
                "controllerName": s.get('controllerName', ''),
                "mvOperateRate": s.get('mvOperateRate', ''),
                "mvUsableOperateRate": s.get('mvUsableOperateRate', ''),
                "mvUpperRate": s.get('mvUpperRate', ''),
                "mvLowerRate": s.get('mvLowerRate', ''),
                "mvLimitRate": s.get('mvLimitRate', ''),
                "countOperateFrequency": s.get('countOperateFrequency', ''),
                "createTime": s.get('createTime', '')
            }

            varHistoryList.append(varHistoryItem)

        detail = f"""
| 变量名称 | 时间段 | 分组 | 变量描述 | 所属控制器 | MV投运率(%) | MV有效投运率(%) | MV卡上限率(%) | MV卡下限率(%) | MV卡限率(%) | MV先控操作频次 | 报表生成时间 | 报表类型 |
|:------:|:-----:|:----:|:------:|:--------:|:----------:|:-------------:|:------------:|:-----------:|:----------:|:------------:|:----------:|:------:|
{ret}
"""
    # 自定义指标
    if myType == 5:
        for s in tempList:
            ret = ret + (
                f"|{s.get('name', '')}"
                f"|{s.get('startTime', '')}-{s.get('endTime', '')}"
                f"|{s.get('groupName', '')}"
                f"|{s.get('metricValue', '')}"
                f"|{s.get('createTime', '')}"
                f"|{tempTpye}\r\n"
            )
            customTempTime = f"{s.get('startTime', '')}-{s.get('endTime', '')}"
            customHistoryItem = {
                "name": s.get('name', ''),
                "startTimeAndEndTime": customTempTime,
                "groupName": s.get('groupName', ''),
                "metricValue": s.get('metricValue', ''),
                "createTime": s.get('createTime', '')
            }
            customHistoryList.append(customHistoryItem)
        detail = f"""
| 自定义指标名称 | 时间段 | 分组 | 计算结果 | 报表生成时间 | 报表类型 |
|:-----------:|:-----:|:---:|:-------:|:----------:|:------:|
{ret}
"""
    # 回路级
    if myType == 6:
        for s in tempList:
            ret = ret + (
                f"|{s.get('name', '')}"
                f"|{s.get('startTime', '')}-{s.get('endTime', '')}"
                f"|{s.get('groupName', '')}"
                f"|{s.get('closeRate', '')}"
                f"|{s.get('operateFrequency', '')}"
                f"|{s.get('createTime', '')}"
                f"|{tempTpye}\r\n"
            )
            loopTempTime = f"{s.get('startTime', '')}-{s.get('endTime', '')}"
            loopHistoryItem = {
                "name": s.get('name', ''),
                "startTimeAndEndTime": loopTempTime,
                "groupName": s.get('groupName', ''),
                "closeRate": s.get('closeRate', ''),
                "operateFrequency": s.get('operateFrequency', ''),
                "createTime": s.get('createTime', '')
            }
            loopHistoryList.append(loopHistoryItem)

        detail = f"""
| 回路名称 | 时间段 | 分组 | 自控率(%) | 操作频次 |报表生成时间 | 报表类型 |
|:------:|:-----:|:----:|:-------:|:-------:|:---------:|:-------:|
{ret}    
"""
    #变量的历史指标数据已经单独抽取出来可以单独使用 所以此接口不用支持变量级别
    return {"markDownDetail": detail, "allDeviceHistoryList": allDeviceHistoryList,
            "deviceHistoryList": deviceHistoryList, "controllerHistoryList": controllerHistoryList,
            "customHistoryList": customHistoryList, "loopHistoryList": loopHistoryList}


def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }
