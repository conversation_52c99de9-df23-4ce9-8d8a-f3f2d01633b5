from datetime import datetime
from __runner__ import tool, Context

@tool(private=True)
async def is_valid_datetime_string(context:Context,params: any):
    startTime = params["startTime"]
    return valid_datetime_string(context,startTime)
# 判断是否为日期+时间格式
def valid_datetime_string(context:Context,s, tempFormat="%Y-%m-%d %H:%M:%S"):
    try:
        datetime.strptime(s, tempFormat)
        return True
    except ValueError:
        return False

@tool(private=True)
async def is_valid_date_string(context:Context,params: any):
    startTime = params["startTime"]
    return valid_datetime_string(context,startTime)
# 判断是否为日期格式
def valid_date_string(s, tempFormat="%Y-%m-%d"):
    try:
        datetime.strptime(s, tempFormat)
        return True
    except ValueError:
        return False