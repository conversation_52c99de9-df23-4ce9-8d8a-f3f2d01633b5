from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为APC_Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息


# 主要实现的功能是 根据用户输入的时间段和输入输入变量进行自动辨识
# add by liupeng 2025-04-15
@tool(version="*")
async def get_auto_ident(context: Context, params: any):
    mvList = params['InputNames']  # 输入变量/位号
    pvList = params['OutputNames']  # 输出变量/位号
    startTime = params['startTime']  # 开始时间
    endTime = params['endTime']  # 结束时间
    runCycle = 30  # 采样周期默认30
    modelStepRespPVsLength = 120  # 模型长度默认120
    if "runCycle" in params:
        runCycle = params['runCycle']  # 采样周期
    if "modelStepRespPVsLength" in params:
        modelStepRespPVsLength = params['modelStepRespPVsLength']  # 模型长度
    response = requests.post(url=APC_URL + '/inter-api/apc-dashboard/v1/autoSubspace/customizeSubspace'
                             , json={
            'mvList': mvList,
            'pvList': pvList,
            'startTime': startTime,
            'endTime': endTime,
            'runCycle': runCycle,
            'modelStepRespPVsLength': modelStepRespPVsLength
        }
                             , headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if 'status' in json_obj:
        return json_obj['message']
    elif json_obj['code'] == 100000000:
        return json_obj['data']
    else:
        return json_obj['message']

