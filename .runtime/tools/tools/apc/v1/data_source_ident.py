from __runner__ import tool, Context
import requests
import json
import os
from furl import furl
from datetime import datetime, timedelta
import time


# add by liupeng 2025-05-14
@tool(private=True)
# 通过位号历史数据进行系统辨识 Edit by liupeng 2025-06-10
async def data_source_ident(context: Context, params):
    APC_URL = context.config["APC_URL"]
    mv_list = []
    pv_list = []
    id = 0
    dataSourceFlag = 0  # 数据源标识 默认0查询数据源 1导入文件数据
    algorithmCallFlag = 0  # 0调用2个算法进行辨识 1子空间算法 2FIR算法
    mvQuantity = 0
    pvQuantity = 0
    model_name = datetime.now().strftime("%Y%m%d%H%M%S")
    runCycle = 30
    modelStepRespPVsLength = 200
    startTime = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")  # 当前时间减去7天
    endTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 当前时间

    if "InputNames" in params:
        mv_list = params['InputNames']
        mvQuantity = len(params['InputNames'])
    if "OutputNames" in params:
        pv_list = params['OutputNames']
        pvQuantity = len(params['OutputNames'])
    if "modelName" in params:
        model_name = params['modelName']
    if "mvQuantity" in params:
        mvQuantity = params['mvQuantity']
    if "pvQuantity" in params:
        pvQuantity = params['pvQuantity']
    result = await context.get_interaction("data_source_ident")
    if result is None:
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '模型信息',
                "description": "包含模型基本信息、输入变量信息、输出变量信息",
                "details": f"""
## 模型信息
|   模型名称   |  输入变量  | 输出变量 |  模型周期  |  模型长度  |              数据时间范围       |
|:--------:|:------:|:------:|:------:|:------:|:---------------------------------------:|
|{model_name} |{mvQuantity}|{pvQuantity}|{runCycle}s|{modelStepRespPVsLength}|{startTime}~{endTime}| 
"""
            }
        })
        # 打开page
        # 创建 URL 对象并修改各部分
        f = furl()
        f.path = '/xpt-tpt-apc/modelMatrix'
        f.args['rowCount'] = mvQuantity  # 输入变量个数 行数
        f.args['colCount'] = pvQuantity  # 输出变量个数 列数
        f.args['modelName'] = model_name  # 模型名称
        f.args['sampleCylc'] = runCycle  # 模型周期
        f.args['modelLen'] = modelStepRespPVsLength  # 模型长度
        f.args['identStartTime'] = startTime  # 开始时间
        f.args['identEndTime'] = endTime  # 结束时间
        f.args['algorithmCallFlag'] = algorithmCallFlag  # 采用那种算法进行辨识 0两种算法 1子空间 2FIR算法
        f.args['dataSourceFlag'] = dataSourceFlag  # 数据源标识 默认0查询数据源 1导入文件数据
        f.args['ID'] = id
        if len(mv_list) > 0:
            f.args['mvList'] = str(mv_list).replace(" ", "")  # 输入变量
        if len(pv_list) > 0:
            f.args['pvList'] = str(pv_list).replace(" ", "")  # 输出变量
        messages = {'key': '模型矩阵确认'}
        result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
        await context.add_view({
            "format": "markdown",
            "content": f"""
{result_message}
        """
        })
        context.require_interaction({
            "id": "data_source_ident",
            "title": "请打开辨识模型矩阵，确认数据信息后点击【下一步】按钮开始辨识",
            "type": "open_page",
            "page_type": "execute",
            "open_page": f.url
        })
        return {

        }
    else:
        # 循环和sleep轮询 获取辨识过程中产生的过程数据信息
        while True:
            Id = result['result']
            response = requests.post(url=APC_URL + '/tpt/ident/getIdentLog'
                                     , json={
                    'id': Id
                }
                                     , headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
                })
            json_obj = json.loads(response.text)
            identEndFlag = json_obj['result']['identEndFlag']
            time.sleep(5)  # 5秒钟（休眠）
            if identEndFlag == "1" or identEndFlag == "2":  # 0是辨识中 1是辨识成功 2辨识错误 1或者2时代表辨识结束，需要进行下一步操作
                dataSegmentLogs = ''
                if len(json_obj['result']['dataSegmentLogs']) > 0:
                    tmp_dataSegmentLogs = json_obj['result']['dataSegmentLogs']
                    for val in tmp_dataSegmentLogs:
                        dataSegmentLogs += "- " + val + '\r\n'

                dataQualityLogs = ''
                if len(json_obj['result']['dataQualityLogs']) > 0:
                    tmp_dataQualityLogs = json_obj['result']['dataQualityLogs']
                    dataQualityLogs = generate_dataQualityLogs(tmp_dataQualityLogs)
                errorLog = '辨识成功！'
                if "errorLog" in json_obj['result']:
                    errorLog = json_obj['result']['errorLog']

                messages = {'key': '辨识过程已完成'}
                result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
                await context.add_view({
                    "format": "markdown",
                    "content": f"""
{result_message}
        """
                })

                await context.add_view({
                    "format": "card",
                    "content": {
                        "type": 'markdown',
                        "title": '辨识过程信息展示',
                        "description": "过程信息小结(数据清洗、数据质量评价、模型辨识、模型质量评价)",
                        "details": f"""
## 辨识过程信息展示
- 首先，对数据集进行数据清洗，剔除数据中的坏值和异常值；
- 然后，调用自动切片算法，选取数据中激励充分的数据段，得到各个输出变量对应的优质数据段以及各数据段的质量评价结果如下：

|          输出变量       |  数据时间范围      |  质量评价  |
|:----------------------:|:---------------:|:--------:|
{dataQualityLogs}
- 数据质量评价：数据质量分为4个等级，质量高低顺序为：A>B>C>D，默认选择AB数据段进行建模；
- 数据质量评价完毕后，将选取质量等级为优、良的数据段进行建模；
- 辨识数据选取完毕后，将调用FIR辨识算法和子空间辨识算法，得到辨识模型，并输出模型的评价结果；
- 模型质量评价：模型质量分为4个等级，质量高低顺序为：A>B>C>D，根据不确定度指标和拟合度指标进行综合评价，推荐选择AB模型用于控制；
- 最后，根据由辨识得到的模型对全段数据进行仿真预测，输出仿真结果。
## 辨识结果：
{errorLog}
"""
                    }
                })
                break
    return {
        "preProjectId": Id,
        "identEndFlag": identEndFlag
    }


def generate_batch_pretuning_form(identProcessVariableList):
    ret = ""
    for s in identProcessVariableList:
        ret = ret + f"|{s['variableName']}|{s['paramName']}|\r\n"
    return ret


def generate_dataQualityLogs(dataQualityLogsList):
    ret = ""
    for s in dataQualityLogsList:
        fruits = s.split(';')
        ret = ret + f"|{fruits[0]}|{fruits[1]}|{fruits[2]}|\r\n"
    return ret
