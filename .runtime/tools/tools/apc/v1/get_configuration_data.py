from __runner__ import tool, Context
import requests
import json
import os
from furl import furl
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息


# 根据控制器名称进行组态新增操作(仅适合全流程操作)
# add by liupeng 2025-04-22
@tool(version="*")
async def get_configuration_data(context: Context, params: any):
    adconName = params["adconName"]  # 用户输入的控制器名称
    baseProjectId = 0  # 控制器组态的ID
    devicenumber = params["devicenumber"]  # 用户输入的设备编号、装置编号
    response = requests.post(
        url=APC_URL + '/inter-api/apc-project/v1/process/addGoal'
        , headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if 'data' in json_obj:
        baseProjectId = json_obj['data']
    else:
        return '控制器创建失败，无法进行下一步操作！'
    # 打开page
    # 创建 URL 对象并修改各部分
    f = furl()
    f.path = '/xpt-tpt-apc/guidance/distillation/' + str(baseProjectId)
    f.args['apc-token'] = APC_inner_token
    f.args['devicenumber'] = devicenumber  # 设备编号、装置编号
    result = await context.get_interaction("get_configuration_data")
    if result is None:
        context.require_interaction({
            "id": "get_configuration_data",
            "title": "打开页面",
            "type": "open_page",
            "open_page": f.url
        })
        return {}
    else:
        return {
            "output": result
        }
