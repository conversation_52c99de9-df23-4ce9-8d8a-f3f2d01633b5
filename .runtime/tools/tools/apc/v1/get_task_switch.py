from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# 主要功能是获取自学习任务列表数据
# add by liupeng 2025-04-10
@tool(version="*")
async def get_task_switch(context: Context, params: any):
    status = params['status']
    taskid = 0
    dataList = await context.call_tool("get_task_query", params=params)
    if len(dataList) > 0:
        taskid = dataList[0]['id']
    params = {'status': status, 'taskid': taskid}
    response = requests.get(url=APC_URL + '/inter-api/apcl-config/v1/task/switch'
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    return json_obj['message']
