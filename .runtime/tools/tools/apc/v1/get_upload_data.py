from __runner__ import tool, Context, validator
import json
import os
import requests


# 控制方案-继续上传数据功能
@tool(private=True)
async def get_upload_data(context: Context, params: any):
    APC_URL = context.config["APC_URL"]
    form_result = await context.get_interaction("get_upload_data")
    if form_result is None:
        header_information = '时间'
        cv_list = params['cv_list']
        for s in cv_list:
            header_information += ',' + s.replace(" ", "")
        mv_list = params['mv_list']
        for m in mv_list:
            header_information += ',' + m.replace(" ", "") + '测量值'
            header_information += ',' + m.replace(" ", "") + '设定值'
            header_information += ',' + m.replace(" ", "") + '阀位值'
        dv_list = params['dv_list']
        for d in dv_list:
            header_information += ',' + d.replace(" ", "")
        await context.add_view({
            "format": "markdown",
            "content": "为了帮助您进一步完善控制器结构与控制方案，现在我需要获取相关变量的历史趋势数据，请下载数据模板并上传至少5秒采样间隔的历史数据（其中建议包含设定值或阀位值变化）"
        })
        template_list = []
        file_name = '变量历史趋势数据模板'
        tmp_template = {'label': '变量历史趋势数据模板',
                        'url': f"{APC_URL}/tpt/control/tagSampleTemplateExport?headerInformation={header_information}&fileName={file_name}"}
        template_list.append(tmp_template)
        tmp_template = {'label': '变量历史趋势数据模板说明',
                        'url': f"{APC_URL}/tpt/control/varHistoryTrendExplainExport"}
        template_list.append(tmp_template)

        context.require_interaction({
            "id": "get_upload_data",
            "title": "变量数据获取",
            "type": "form",
            "form": {
                "form_type": "file",
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        "file": {
                            "title": "变量历史趋势数据：",
                            "type": "string",
                            "format": "file-object",
                            "widget": "tptfile",
                            "template": template_list,
                            "x-validator": "tag_data_check_file",
                            "x-validator-payload": {
                                'header_information': header_information

                            },
                        }
                    },
                    "required": [
                        "file"
                    ]
                },
                "default": {

                }
            }
        })
        return {}
    else:
        # 处理文件内容, 必须先解析
        file_info = json.loads(form_result.get("file"))
        # file_content_bytes = await context.get_file(file_info, "bytes")  # 获取文件内容，字节格式
        # file_contents = str(file_content_bytes, encoding='utf-8', errors='strict')  # 转换成字符串格式
        # await context.log_info(f"file_info22222: {file_info}")
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "正在基于变量历史趋势数据进行数据特征分析，从变量间因果关系、耦合强度等多个维度检查控制结构，给出优化建议，预计需要1-3分钟，请在生成建议后及时确认。",
                "description": "",
                "details": ""
            }
        })
        return {
            'file': file_info
        }


@validator(version="*")
async def tag_data_check_file(context: Context, target: any, payload: any):
    APC_URL = context.config["APC_URL"]
    # 使用 split() 方法分割字符串
    data_list = payload['header_information'].split(',')
    filtered_columns = [col for col in data_list if col != '时间']
    # target 格式即为上述实际值格式
    if target is None or target == "":
        raise ValueError("请选择有效数据文件！")
    file_info = json.loads(target)
    object_path = file_info["object"]
    _, extension = os.path.splitext(object_path)
    # 清洗扩展名：移除点号 + 转小写
    clean_extension = extension.lstrip('.').lower() if extension else None
    if clean_extension != "csv":
        raise ValueError("文件格式仅支持.csv，请重新上传文件！")
    response = requests.post(url=APC_URL + '/tpt/control/fileVerify'
                             , json={
            'bucket': file_info["bucket"],
            'object': file_info["object"],
            'name': file_info["name"],
            'list': filtered_columns,
            'datatype': 1  # 0是系统辨识 1是控制方案
        }
                             , headers={
            'Content-Type': 'application/json',
            'Cookie': 'tenant-id=0',
            'Accept-Language': 'zh-CN',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    return_data = json.loads(response.text)
    if return_data['code'] != 200:
        # 错误结果，抛出异常
        raise ValueError(f"您上传的文件不符合要求，错误信息如下: {return_data['message']}")
    # 直接返回即为成功
    return
