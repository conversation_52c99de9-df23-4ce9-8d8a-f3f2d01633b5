from __runner__ import tool, Context
import requests
import json
from datetime import datetime, timedelta


# 控制方案-根据控制方案进行系统辨识
# add by liupeng 2025-07-01
@tool(private=True)
async def get_control_ident(context: Context, params: any):
    APC_URL = context.config["APC_URL"]
    dataSourceFlag = 1  # 数据源标识 默认0查询数据源 1导入文件数据
    modelName = datetime.now().strftime("%Y%m%d%H%M%S")
    sampleCylc = 30  # 采样周期
    modelLen = 200  # 模型长度
    algorithmCallFlag = 0  # 采用那种算法进行辨识 0两种算法都需要 fir和子空间
    identProcessInputVariableList = []
    identProcessOutputModelList = []
    identProcessOutputVariableList = []
    cvNum = params['cv_num']  # cv个数
    mvNum = params['mv_num']  # mv个数
    dvNum = params['dv_num']  # dv个数
    cvList = params['cv_list']  #
    mvList = params['mv_list']  #
    dvList = params['dv_list']  #
    id = params['id']
    identStartTime = params['identStartTime']
    identEndTime = params['identEndTime']
    for s in mvList:
        tmp_mv = {
            "variableName": s,
            "paramName": s,
            "variableType": 1
        }
        identProcessInputVariableList.append(tmp_mv)
    for s in dvList:
        tmp_mv = {
            "variableName": s,
            "paramName": s,
            "variableType": 1
        }
        identProcessInputVariableList.append(tmp_mv)
    for s in cvList:
        tmp_cv = {
            "variableValue": 0,
            "variableName": s,
            "paramName": s,
            "variableType": 1
        }
        identProcessOutputVariableList.append(tmp_cv)

    # 将matrix的值写入identProcessOutputModelList
    for i in range(len(params['matrix'])):
        tmp_isModelFlag = []
        for j in range(len(params['matrix'][i])):
            if params['matrix'][i][j] == 1:
                tmp_ss = {
                    "isModelFlag": 0
                }
            else:
                tmp_ss = {
                    "isModelFlag": 1
                }
            tmp_isModelFlag.append(tmp_ss)
        identProcessOutputModelList.append(tmp_isModelFlag)

    await context.log_info(f"identProcessOutputModelList数据信息 result={identProcessOutputModelList}")

    ident_params = {
        "dataSourceFlag": dataSourceFlag,
        "id": id,
        "modelName": modelName,
        "sampleCylc": sampleCylc,
        "modelLen": modelLen,
        "algorithmCallFlag": algorithmCallFlag,
        "identStartTime": identStartTime,
        "identEndTime": identEndTime,
        "identProcessInputVariableList": identProcessInputVariableList,
        "identProcessOutputModelList": identProcessOutputModelList,
        "identProcessOutputVariableList": identProcessOutputVariableList,
    }
    await context.log_info(f"调用java辨识的入参数据信息 result={ident_params}")
    await context.log_info(f"222222 result={params}")
    # 调用java后端辨识接口进行系统辨识
    response = requests.post(
        url=f'{APC_URL}/tpt/ident/startIdent'
        , json=ident_params
        , headers={
            'Content-Type': 'application/json',
            'Cookie': 'tenant-id=0',
            'Accept-Language': 'zh-CN',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    return_data = json.loads(response.text)
    return return_data
