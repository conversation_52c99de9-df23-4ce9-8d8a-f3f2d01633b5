from __runner__ import tool, Context
import requests
import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息


# 主要实现的功能是 根据控制器名称创建自学习任务
# 获取控制器信息 数据源 模型数据 输入输出变量
# add by liupeng 2025-04-08
@tool(version="*")
async def get_self_learning_tasks(context: Context, params: any):
    controllerName = params["controllerName"]  # 用户输入控制器名称
    baseProjectId = 0  # 控制器ID
    dataSourceId = 0  # 数据源ID
    params = {'groupType': 8}

    # 获取全厂ID
    response_config = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/config/node/all'
                                   , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj_config = json.loads(response_config.text)
    # 解析数据 得到全厂id
    groupId = json_obj_config['data']['id']
    params_config = {'groupId': groupId}
    # 根据全厂ID对控制器信息进行查询
    response_projecttree = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/project/data/projecttree'
                                        , params=params_config, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj_projecttree = json.loads(response_projecttree.text)
    dataList = [emp for emp in json_obj_projecttree['data']['loadedProjectList'] if
                emp["projectName"] == controllerName]  # 根据工程名称进行筛选（已加载数据）
    if len(dataList) > 0:
        baseProjectId = dataList[0]['id']
        dataSourceName = dataList[0]['dataSourceName']  # 数据源名称
        # 根据数据源名称查询数据源ID
        response_configlist = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/datasource/configlist'
                                           , params=params_config, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_inner_token,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })
        json_obj_configlist = json.loads(response_configlist.text)
        configlist = [emp for emp in json_obj_configlist['list'] if
                      emp["datasourceName"] == dataSourceName]  # 根据数据源名称筛选数据
        if len(configlist) > 0:
            dataSourceId = configlist[0]['id']  # 数据源ID
    else:
        return controllerName + '未能加载，无法创建自学习任务！'
    # 根据控制器ID对该控制器的MV和CV进行查询
    params_params = {'baseProjectId': baseProjectId, 'filterRole': 1}
    response_params = requests.get(url=APC_URL + '/inter-api/apc-project/v1/controller/params/list'
                                   , params=params_params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj_params = json.loads(response_params.text)
    # print('根据控制器ID对该控制器的MV和CV进行查询如下')
    # print(json_obj_params['data']['mvs'])
    # print(json_obj_params['data']['cvs'])
    # print('根据控制器ID对该控制器的MV和CV进行查询结束')
    # 根据数据源ID获取该数据源下所有位号信息
    condition = dataSourceId  # 数据源ID
    mvslist = []
    cvslist = []
    # 自学习任务名称采用adcon控制器名称+当前时间
    startTime = datetime.now().strftime("%Y%m%d%H%M%S")  # 当前时间作为开始时间
    taskName = controllerName + startTime  # 自学习任务名称
    result = await context.call_tool("get_controller_id", params=params)  # 获取全厂ID
    if len(result) > 0:
        groupId = result['id']
    params_querytaginfolist = {'current': 1, 'pageSize': 99999999, 'selectType': 2, 'condition': condition,
                               'groupId': groupId}
    response_querytaginfolist = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/tag/querytaginfolist'
                                             , params=params_querytaginfolist, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj_querytaginfolist = json.loads(response_querytaginfolist.text)
    # mv数据
    if len(json_obj_querytaginfolist['list']) > 0 and len(json_obj_params['data']['mvs']) > 0:
        for item in json_obj_params['data']['mvs']:
            querytaginfolist = [emp for emp in json_obj_querytaginfolist['list'] if emp["tagName"] == item['MV_name']]
            if len(querytaginfolist) > 0:
                tag_id = querytaginfolist[0]['id']
                mvslist.append({'descript': querytaginfolist[0]['tagDescription'], 'id': tag_id, 'tagId': tag_id,
                                'tagName': querytaginfolist[0]['tagName'], 'unit': querytaginfolist[0]['tagUnit'],
                                'variableName': querytaginfolist[0]['tagName']})
            else:
                return item['MV_name'] + '不存在该数据源中！'
    else:
        return '自学习任务创建失败！'
    # cv数据
    if len(json_obj_querytaginfolist['list']) > 0 and len(json_obj_params['data']['cvs']) > 0:
        for item in json_obj_params['data']['cvs']:
            querytaginfolist = [emp for emp in json_obj_querytaginfolist['list'] if emp["tagName"] == item['CV_name']]
            if len(querytaginfolist) > 0:
                tag_id = querytaginfolist[0]['id']
                cvslist.append({'descript': querytaginfolist[0]['tagDescription'], 'id': tag_id, 'tagId': tag_id,
                                'tagName': querytaginfolist[0]['tagName'], 'unit': querytaginfolist[0]['tagUnit'],
                                'variableName': querytaginfolist[0]['tagName']})
            else:
                return item['CV_name'] + '不存在该数据源中！'
    else:
        return '自学习任务创建失败！'
    # 创建自学习任务能力
    response = requests.post(
        url=APC_URL + '/inter-api/apcl-config/v1/task/create'
        , json={
            'serverid': dataSourceId,  # 数据源ID
            'taskName': taskName,  # 任务名称
            'variableInput': mvslist,
            'variableOutput': cvslist
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(response.text)
    return json_obj
