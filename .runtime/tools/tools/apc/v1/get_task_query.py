import json
import os
import requests
from __runner__ import tool, Context
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# 主要功能是获取自学习任务列表数据
# add by liupeng 2025-04-10
@tool(version="*")
async def get_task_query(context: Context, params: any):
    page = 1  # 页码
    pageSize = 100  # 一页显示多少条数据
    taskName=''
    if params:
        taskName = params['taskName']  # 自学习任务名称
    params = {'page': page, 'pageSize': pageSize}
    response = requests.get(url=APC_URL + '/inter-api/apcl-config/v1/task/query'
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if len(taskName) > 0:
        dataList = [emp for emp in json_obj['list'] if emp["taskName"] == taskName]  # 根据自学习任务名称进行筛选
        return dataList
    else:
        return json_obj['list']


