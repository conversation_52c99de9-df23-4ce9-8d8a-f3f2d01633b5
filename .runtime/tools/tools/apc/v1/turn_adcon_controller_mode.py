from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# h主要是修改ADCON控制器在线运行和在线调试状态  在线运行是0 在线调试是1
@tool(version="*")
async def turn_adcon_controller_mode(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    # 查询在线控制器
    statusType = 0  # [0:在线运行（运行） 1 在线调试（切除）]
    if 'type' in params:
        statusType = params['type']

    controllerName = ''
    if 'controllerName' in params:
        controllerName = params['controllerName']
        if not controllerName:
            return format_response(
                success=False,
                message="请输入控制器名称"
            )
    if not controllerName:
        return format_response(
            success=False,
            message="请输入控制器名称"
        )

    # 根据控制器名称查询控制器id
    projectNo = get_adcon_online_controller_project_no(context,controllerName)
    if projectNo == -1:
        return format_response(
            success=False,
            message="该(" + controllerName + ")控制器不存在"
        )
    # 查询控制器状态 如果不满足逻辑要求 则直接返回错误
    queryParam = {"projectNo": projectNo}
    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-project/v1/controller/mode/query',  # 该接口是获取所有装置数据
                            params=queryParam, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    controllerModeData = json_obj['data']
    if controllerModeData is None or controllerModeData == '':
        return format_response(
            success=False,
            message="该(" + controllerName + ")控制器状态不合法"
        )
    tempMode = controllerModeData["mode"]
    if tempMode == 1 and statusType == 1:
        return format_response(
            success=False,
            message="该(" + controllerName + ")控制器已经进入切除状态"
        )
    if tempMode == 0 and statusType == 0:
        return format_response(
            success=False,
            message="该(" + controllerName + ")控制器已经进入投运状态"
        )
    # 修改状态
    updateParam = {"paramValue": statusType, "projectNo": projectNo}
    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-project/v1/controller/mode/update',  # 该接口是获取所有装置数据
                            json=updateParam, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    updateModeData = json_obj['data']
    if updateModeData:
        return format_response(
            success=True,
            message="执行成功"
        )
    else:
        return format_response(
            success=False,
            message="执行失败"
        )

# 递归寻找第一个名称
def find_project_no_by_name(data, target_name):
    """
    递归查找JSON结构中匹配target_name的第一个节点，返回其id
    :param data: 要搜索的字典/列表
    :param target_name: 要查找的name值
    :return: 匹配的id，未找到返回None
    """
    if isinstance(data, dict):
        if data.get('name') == target_name and data.get('isLeaf'):
            return data.get('projectNo')
        if 'children' in data:
            return find_project_no_by_name(data['children'], target_name)
    elif isinstance(data, list):
        for item in data:
            result = find_project_no_by_name(item, target_name)
            if result is not None:
                return result
    return None


# add by liupeng 2025-04-07
# 根据控制器名称查询控制器id
def get_adcon_online_controller_project_no(context: Context,controllerName):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all?groupType=6'  # 该接口是获取所有装置数据
                            , headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if len(controllerName) > 0:
        temp_id = find_project_no_by_name(json_obj['data'], controllerName)
        if temp_id is not None:
            return temp_id
        else:
            return -1
    else:
        return -1

def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }