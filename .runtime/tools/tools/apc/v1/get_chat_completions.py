import json
from __runner__ import tool, Context
import requests
import re


# url = "http://10.16.11.36:8005/v1/chat/completions"

@tool(private=True)
async def get_chat_completions(context: Context, params):
    LLM_API_URL = context.config["LLM_API_URL"]
    payload = {
        "messages": [
            {
                "role": "system",
                "content": params['content_data']
                # "content": '基于用户指令与输出要求,输出用户装置/设备范围先进控制方案中控制器可能涉及的核心工艺设备名称及可变数量关键属性列表,颗粒度到加热炉、相关的精馏塔、重要的脱盐罐等大型核心设备即可,不需要包括阀门、储罐、储槽等细节控制对象,可变属性描述对于加热炉裂解炉等设备输出支路个数、精馏塔类设备输出侧线个数,其余设备不输出.输出要求:输出设备列表格式为["设备名称","设备名称"]的列表,特殊条件判断:如果用户指令包含气化炉,输出["粉煤气化炉,喷嘴个数:","水煤浆气化炉,喷嘴个数:"];如果用户指令包含氯碱与电解槽关键字,输出["离子膜电解槽,个数:"]'
            },
            {
                "role": "user",
                "content": "用户指令：" + str(params['content'])
            }
        ],
        "model": "qwen3",
        "stream": False
    }
    headers = {"content-type": "application/json"}
    response = requests.post(LLM_API_URL + '/v1/chat/completions', json=payload, headers=headers)
    json_data = response.json()
    await context.log_info(f"LLM_URL result={LLM_API_URL}")
    await context.log_info(f"json_data result={json_data}")
    tmp_content = ''
    if "content" in json_data['choices'][0]['message']:
        tmp_content = json_data['choices'][0]['message']['content']
        # tmp_content = parse_code(tmp_content)

    # await context.log_info(f"tmp_content result={tmp_content}")
    result_data = json.loads(tmp_content)
    return result_data


def parse_code(text: str, lang: str = "json") -> str:
    pattern = rf"```{lang}.*?\s+(.*?)```"
    match = re.search(pattern, text, re.DOTALL)
    if match:
        code = match.group(1)
    else:
        raise Exception
    return code
