from __runner__ import tool, Context
import requests
import json


# 主要实现的功能是 调用大语言实现搜索功能
# add by liupeng 2025-06-17
@tool(private=True)
async def get_update_matrix(context: Context, params: any):
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "将根据用户编辑确认的控制器结构，更新先进控制方案内容与最终的控制器结构，预计需要1-3分钟。",
            "description": "",
            "details": ""
        }
    })
    LLM_URL = context.config["LLM_URL"]
    types = 'markdown'
    await context.log_info(f"update_matrix result={params}")
    response = requests.post(url=LLM_URL + '/api/update_matrix'
                             , json=params
                             , headers={
            'Content-Type': 'application/json',
            'Cookie': 'tenant-id=0',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        },
                             stream=True)
    await context.add_view({
        "format": "markdown",
        "content": """
接下来，我将按照确认的控制关系更新控制方案内容与最终控制器结构文件。
                                    """
    })
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '正在更新控制方案文档',
            "details": '',
        }
    })
    for line in response.iter_lines():
        if line:
            if line:
                try:
                    line = line.decode("utf-8")
                except Exception as e:
                    continue
                line = line[6:]
                if line:
                    try:
                        line = json.loads(line)
                        datas = line['data']
                        if line['type'] == "append_text":
                            types = 'append'
                        elif line['type'] == "file":
                            types = 'summary_file'

                        if types == 'append':
                            await context.append_view({
                                "details": datas
                            })
                        elif types == 'summary_file':
                            await context.add_view({
                                "format": "card",
                                "content": {
                                    "type": types,
                                    "title": '更新控制方案文档',
                                    "details": datas,
                                    "description": "控制方案文档"
                                }
                            })
                    except Exception as e:
                        continue

    return {
        'code': 200,
        'message': '控制方案创建成功！'
    }
