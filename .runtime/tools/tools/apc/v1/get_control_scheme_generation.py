from __runner__ import tool, Context
import requests
import json
from datetime import datetime, timedelta
from furl import furl


# 构造数据信息给到后端进行辨识 确定模型矩阵关系
def identVariableAnalysis(data_result, get_search_all_result, apc_url):
    tmp_cvs = []
    dataSourceFlag = 1
    modelName = datetime.now().strftime("%Y%m%d%H%M%S")
    sampleCylc = 30  # 采样周期
    modelLen = 200  # 模型长度
    algorithmCallFlag = 1  # 0调用2个算法进行辨识 1子空间算法 2FIR算法
    identProcessInputVariableList = []
    identProcessOutputModelList = []
    identProcessOutputVariableList = []
    matrix = []  # 大语言给出的模型关系
    tmp_mvdv_list = []
    cvNum = get_search_all_result['cv_num']  # cv个数
    mvNum = get_search_all_result['mv_num']  # mv个数
    dvNum = get_search_all_result['dv_num']  # dv个数
    cvList = get_search_all_result['cv_list']  #
    mvList = get_search_all_result['mv_list']  #
    dvList = get_search_all_result['dv_list']  #
    matrix = get_search_all_result['matrix']  #
    name = data_result['file']['name']
    bucket = data_result['file']['bucket']
    object = data_result['file']['object']
    for s in mvList:
        tmp_mv = {
            "variableName": s.replace(" ", ""),
            "paramName": s.replace(" ", ""),
            "variableType": 1
        }
        identProcessInputVariableList.append(tmp_mv)
    for s in dvList:
        tmp_mv = {
            "variableName": s.replace(" ", ""),
            "paramName": s.replace(" ", ""),
            "variableType": 1
        }
        identProcessInputVariableList.append(tmp_mv)
    for s in cvList:
        tmp_cv = {
            "variableValue": 0,
            "variableName": s.replace(" ", ""),
            "paramName": s.replace(" ", ""),
            "variableType": 1
        }
        identProcessOutputVariableList.append(tmp_cv)

    for s in range(cvNum):
        tmp_is_model_flag = {
            "isModelFlag": 0
        }
        tmp_cvs.append(tmp_is_model_flag)

    counts = mvNum + dvNum
    for m in range(counts):
        identProcessOutputModelList.append(tmp_cvs)

    tmp_result = {
        "dataSourceFlag": dataSourceFlag,
        "id": "",
        "modelName": modelName,
        "sampleCylc": sampleCylc,
        "modelLen": modelLen,
        "algorithmCallFlag": algorithmCallFlag,
        "identStartTime": "",
        "identEndTime": "",
        "identProcessInputVariableList": identProcessInputVariableList,
        "identProcessOutputModelList": identProcessOutputModelList,
        "identProcessOutputVariableList": identProcessOutputVariableList
    }
    ident_resut = {
        "identProjectProcessDTO": tmp_result,
        "cvNum": cvNum,
        "mvNum": mvNum,
        "dvNum": dvNum,
        "cvList": [item.replace(" ", "") for item in cvList],
        "mvList": [item.replace(" ", "") for item in mvList],
        "dvList": [item.replace(" ", "") for item in dvList],
        "bucket": bucket,
        "object": object,
        "name": name,
        "matrix": matrix
    }
    response = requests.post(url=apc_url + '/tpt/control/identVariableAnalysis'
                             , json=ident_resut
                             , headers={
            'Content-Type': 'application/json',
            'Cookie': 'tenant-id=0',
            'Accept-Language': 'zh-CN',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    return_data = json.loads(response.text)
    return return_data


# 主要实现的功能是 控制方案优化
# add by liupeng 2025-07-03
@tool(version="*")
async def get_control_scheme_generation(context: Context, params: any):
    APC_URL = context.config["APC_URL"]
    # TPT_URL = context.config["TPT_URL"]
    # 获取数据信息
    # await context.log_info(f"3 result={params}")
    get_control_scheme_result = params['control_scheme_result']
    # get_search_all_result = params['search_all_result']
    get_variable_data_result = await context.get_cache("get_variable_data_result")
    if get_variable_data_result is None:
        variable_result = await context.call_tool("get_variable_data", params=params)  # 获取数据信息
        await context.set_cache("get_variable_data_result", variable_result)
        get_variable_data_result = variable_result
    if get_variable_data_result['data'] == 1:  # 直接生成现有控制方案
        # await context.log_info(f"1 result={get_control_scheme_result}")
        # await context.log_info(f"2 result={get_search_all_result}")
        # 更新控制方案文档
        get_update_matrix_result = await context.get_cache("get_update_matrix_result")
        if get_update_matrix_result is None:
            tmp_params = {
                'file_name': params['file_name'],
                'matrix_data': get_control_scheme_result
            }
            matrix_result = await context.call_tool("get_update_matrix", params=tmp_params)  # 更新控制方案文档
            f = furl()
            f.path = '/xpt-tpt-apc/controlScheme'
            f.args['theme'] = 'dark'
            f.args['name'] = get_control_scheme_result['name']  # 名称
            f.args['cv_num'] = get_control_scheme_result['cv_num']  # cv个数
            f.args['mv_num'] = get_control_scheme_result['mv_num']  # mv个数
            f.args['dv_num'] = get_control_scheme_result['dv_num']  # dv个数
            f.args['cv_list'] = str(get_control_scheme_result['cv_list']).replace(" ", "")  #
            f.args['mv_list'] = str(get_control_scheme_result['mv_list']).replace(" ", "")  #
            f.args['dv_list'] = str(get_control_scheme_result['dv_list']).replace(" ", "")  #
            f.args['matrix'] = str(get_control_scheme_result['matrix']).replace(" ", "")  #
            f.args['isview'] = False
            await context.add_view({
                "format": "card",
                "content": {
                    "type": 'page',
                    "title": '更新控制器结构',
                    "description": "",
                    "details": f.url
                }
            })
            await context.set_cache("get_update_matrix_result", matrix_result)
            get_update_matrix_result = matrix_result
            # await context.log_info(f"get_update_matrix_result result={get_update_matrix_result}")
        return {
            'code': get_update_matrix_result['code'],
            'message': get_update_matrix_result['message'],
            "preProjectId": '0',
            "identEndFlag": 1,
            'Isover': True,
        }
    # 继续上传数据
    get_upload_data_result = await context.get_cache("get_upload_data_result")
    if get_upload_data_result is None:
        data_result = await context.call_tool("get_upload_data", params=get_control_scheme_result)  # 继续上传数据功能
        # await context.log_info(f"get_control_scheme_result123 result={get_control_scheme_result}")
        # 根据用户上传的位号历史数据和模型矩阵结构调用后端接口，得到最终的模型关系数据
        # return_data = identVariableAnalysis(data_result, get_control_scheme_result, APC_URL)
        tmp_cvs = []
        dataSourceFlag = 1
        modelName = datetime.now().strftime("%Y%m%d%H%M%S")
        sampleCylc = 30  # 采样周期
        modelLen = 120  # 模型长度(根据产品要求将模型长度200改成120 2025-07-23)
        algorithmCallFlag = 1  # 0调用2个算法进行辨识 1子空间算法 2FIR算法
        identProcessInputVariableList = []
        identProcessOutputModelList = []
        identProcessOutputVariableList = []
        cvNum = get_control_scheme_result['cv_num']  # cv个数
        mvNum = get_control_scheme_result['mv_num']  # mv个数
        dvNum = get_control_scheme_result['dv_num']  # dv个数
        cvList = get_control_scheme_result['cv_list']  #
        mvList = get_control_scheme_result['mv_list']  #
        dvList = get_control_scheme_result['dv_list']  #
        matrix = get_control_scheme_result['matrix']  #
        name = data_result['file']['name']
        bucket = data_result['file']['bucket']
        object_data = data_result['file']['object']
        for s in mvList:
            tmp_mv = {
                "variableName": s.replace(" ", ""),
                "paramName": s.replace(" ", ""),
                "variableType": 1
            }
            identProcessInputVariableList.append(tmp_mv)
        for s in dvList:
            tmp_mv = {
                "variableName": s.replace(" ", ""),
                "paramName": s.replace(" ", ""),
                "variableType": 1
            }
            identProcessInputVariableList.append(tmp_mv)
        for s in cvList:
            tmp_cv = {
                "variableValue": 0,
                "variableName": s.replace(" ", ""),
                "paramName": s.replace(" ", ""),
                "variableType": 1
            }
            identProcessOutputVariableList.append(tmp_cv)

        for s in range(cvNum):
            tmp_is_model_flag = {
                "isModelFlag": 0
            }
            tmp_cvs.append(tmp_is_model_flag)

        counts = mvNum + dvNum
        for m in range(counts):
            identProcessOutputModelList.append(tmp_cvs)

        tmp_result = {
            "dataSourceFlag": dataSourceFlag,
            "id": "",
            "modelName": modelName,
            "sampleCylc": sampleCylc,
            "modelLen": modelLen,
            "algorithmCallFlag": algorithmCallFlag,
            "identStartTime": "",
            "identEndTime": "",
            "identProcessInputVariableList": identProcessInputVariableList,
            "identProcessOutputModelList": identProcessOutputModelList,
            "identProcessOutputVariableList": identProcessOutputVariableList
        }
        ident_resut = {
            "identProjectProcessDTO": tmp_result,
            "cvNum": cvNum,
            "mvNum": mvNum,
            "dvNum": dvNum,
            "cvList": [item.replace(" ", "") for item in cvList],
            "mvList": [item.replace(" ", "") for item in mvList],
            "dvList": [item.replace(" ", "") for item in dvList],
            "bucket": bucket,
            "object": object_data,
            "name": name,
            "matrix": matrix
        }
        response = requests.post(url=APC_URL + '/tpt/control/identVariableAnalysis'
                                 , json=ident_resut
                                 , headers={
                'Content-Type': 'application/json',
                'Cookie': 'tenant-id=0',
                'Accept-Language': 'zh-CN',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        await context.log_info(f"调用identVariableAnalysis接口返回的数据：{response.text}")
        return_data = json.loads(response.text)
        await context.set_cache("get_upload_data_result", return_data)
        get_upload_data_result = return_data
    if get_upload_data_result['code'] != 200:
        await context.add_view({
            "format": "markdown",
            "content": f"""
## 数据校验失败，主要原因如下：
{return_data['message']}
"""
        })
        return

    tmp_params = {
        'matrix_data': get_control_scheme_result,
        'data_information': get_upload_data_result['result']
    }

    get_feature_data_analysis_result = await context.get_cache("get_feature_data_analysis_result")
    if get_feature_data_analysis_result is None:
        analysis_result = await context.call_tool("get_feature_data_analysis", params=tmp_params)  # 特征数据分析
        await context.set_cache("get_feature_data_analysis_result", analysis_result)
        get_feature_data_analysis_result = analysis_result

    get_control_rationality_check_result = await context.get_cache("get_control_rationality_check_result")
    if get_control_rationality_check_result is None:
        rationality_result = await context.call_tool("get_control_rationality_check", params=tmp_params)  # 控制合理性检查
        # await context.log_info(f"111 result={rationality_result}")
        await context.set_cache("get_control_rationality_check_result", rationality_result)
        get_control_rationality_check_result = rationality_result
    if get_control_rationality_check_result['data'] == 1:  # 查看并修改详细修改建议
        suggestion_check_result = await context.get_cache("suggestion_check_result")
        if suggestion_check_result is None:
            check_results = await context.call_tool("get_revision_suggestion_check", params=tmp_params)  # 修改建议检查
            await context.set_cache("suggestion_check_result", check_results)
            suggestion_check_result = check_results

        # 构造数据对控制方案矩阵进行更新
        tmp_control = {
            'get_search_all_result': get_control_scheme_result,
            'check_results': suggestion_check_result,
            'file_name': params['file_name']
        }
        get_control_update_result = await  context.get_cache("get_control_update_result")
        if get_control_update_result is None:
            update_result = await context.call_tool("get_control_update", params=tmp_control)
            await context.set_cache("get_control_update_result", update_result)
            get_control_update_result = update_result
    else:  # 接受修改建议
        tmp_control = {
            'get_search_all_result': get_control_scheme_result,
            'check_results': tmp_params['data_information'],
            'file_name': params['file_name']
        }
        get_control_update_result = await  context.get_cache("get_control_update_result")
        if get_control_update_result is None:
            update_result = await context.call_tool("get_control_update", params=tmp_control)
            await context.set_cache("get_control_update_result", update_result)
            get_control_update_result = update_result

    # 是否需要进行模型辨识 add by liupeng 2025-07-01
    get_is_index_result = await  context.get_cache("get_is_index_result")  # 是否需要系统辨识选择操作
    if get_is_index_result is None:
        is_index_result = await context.call_tool("get_is_index", params=tmp_control)
        await context.set_cache("get_is_index_result", is_index_result)
        get_is_index_result = is_index_result

    if get_is_index_result['data'] == 1:  # 需要继续进行模型辨识，获得控制器模型矩阵
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "将根据上传的历史数据与生成的控制器结构进行模型辨识，输出辨识过程信息，预计需要1-3分钟，请在输出后及时确认。",
                "description": "",
                "details": ""
            }
        })
        get_control_ident_result = await context.get_cache("get_control_ident_result")
        if get_control_ident_result is None:
            # get_control_scheme_result['id'] = get_upload_data_result['result']['id']
            # get_control_scheme_result['identStartTime'] = get_upload_data_result['result']['identStartTime']
            # get_control_scheme_result['identEndTime'] = get_upload_data_result['result']['identEndTime']
            # await context.log_info(f"get_control_update_result result={get_control_update_result}")
            tmp_update_result = {
                'cv_list': get_control_update_result['matrix_data']['cv_list'],
                'cv_num': get_control_update_result['matrix_data']['cv_num'],
                'dv_list': get_control_update_result['matrix_data']['dv_list'],
                'dv_num': get_control_update_result['matrix_data']['dv_num'],
                'matrix': get_control_update_result['matrix_data']['matrix'],
                'mv_list': get_control_update_result['matrix_data']['mv_list'],
                'mv_num': get_control_update_result['matrix_data']['mv_num'],
                'name': get_control_update_result['matrix_data']['name'],
                'id': get_upload_data_result['result']['id'],
                'identStartTime': get_upload_data_result['result']['identStartTime'],
                'identEndTime': get_upload_data_result['result']['identEndTime']
            }
            # await context.log_info(f"get_control_scheme_result result={get_control_scheme_result}")
            # await context.log_info(f"tmp_update_result result={tmp_update_result}")
            ident_result = await context.call_tool("get_control_ident", params=tmp_update_result)  # 开始辨识
            await context.set_cache("get_control_ident_result", ident_result)
            get_control_ident_result = ident_result

            get_ident_log_result = await context.get_cache("get_ident_log_result")
            if get_ident_log_result is None:
                log_result = await context.call_tool("get_ident_getIdentLog", params=ident_result)  # 对辨识过程中日志信息查询
                await context.set_cache("get_ident_log_result", log_result)
                get_ident_log_result = log_result
                await context.log_info(f"get_ident_log_result result={get_ident_log_result}")

            if get_ident_log_result['identEndFlag'] == "2":
                await context.add_view({
                    "format": "markdown",
                    "content": f"""
## 辨识失败，主要原因如下：
    {get_ident_log_result['errorLog']}
    """
                })
                return {
                    'code': get_control_ident_result['code'],
                    'message': get_control_ident_result['message'],
                    "preProjectId": get_control_ident_result['result'],
                    "identEndFlag": get_ident_log_result['identEndFlag'],
                    'Isover': False,
                }
            return {
                'code': get_control_ident_result['code'],
                'message': get_control_ident_result['message'],
                "preProjectId": get_control_ident_result['result'],
                "identEndFlag": 1,
                'Isover': False,
            }
    else:  # 不进行辨识 结束流程
        return {
            'code': 200,
            'message': '控制方案创建成功！',
            "preProjectId": '0',
            "identEndFlag": 1,
            'Isover': True,
        }
