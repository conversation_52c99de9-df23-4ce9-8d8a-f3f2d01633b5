from __runner__ import tool, Context
from furl import furl


# 控制方案-特征数据分析
# add by liupeng 2025-06-17
@tool(private=True)
async def get_feature_data_analysis(context: Context, params: any):
    # await context.log_info(f"2222222222 result={params}")
    # TPT_URL = context.config["TPT_URL"]
    result = await context.get_interaction("get_feature_data_analysis")
    if result is None:
        await context.add_view({
            "format": "markdown",
            "title": "变量数据获取",
            "content": "用户提交的数据格式正确，通过检验，接下来我将进行特征数据分析。\r\n我将根据用户提供的数据，对变量间因果关系及关联程度(耦合强度)进行分析。\r\n首先，我将结合工艺生产数据进行量化分析，在明确APC先进过程控制目标、关键工艺参数（被控变量CV、操作变量MV和扰动变量DV）以及APC控制矩阵设计信息的基础上，深入研究控制模型矩阵及各变量间的数学关系，并输出数据分析结果。"
        })
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '正在进行数据特征计算',
                "description": ""
            }
        })
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '思考过程',
                "description": "数据分析过程"
            }
        })
        cv_list = params['matrix_data']['cv_list'],  # CV集合数据
        mv_list = params['matrix_data']['mv_list'],  # MV集合数据
        dv_list = params['matrix_data']['dv_list'],  # DV集合数据
        influence_granger_index = [[round(item, 1) for item in row] for row in
                                   params['data_information']['influenceGrangerIndex']]  # 相关性指数矩阵
        gain_level = params['data_information']['gainLevel']  # 增益强弱矩阵
        get_chat_completions_results = await context.get_cache("get_chat_completions_results")
        if get_chat_completions_results is None:
            tmp_content = '请根据工艺工程师上传的用户指令(包含多变量模型预测控制器相关变量列表及以二维数组格式的因果指标与耦合强度指标),按照输出格式要求给出因果指标角度和耦合强度角度的两段详细分析描述文字到CausalInformation和StrongInformation:1.首先介绍各指标的含义与作用,其次详细总结各个变量间的指标与对控制方案设计的可能影响,最后给出最重要的变量间关系总结,帮助工艺工程师理解数据角度的变量间关系.2.用户指令属性说明如下:cv_list,mv_list,dv_list分别代表被控变量,操作变量与干扰变量列表;cv_num,mv_num,dv_num代表被控变量,操作变量与干扰变量的数量,与列表中变量数量一致;influenceGrangerIndex因果指标是将mv和dv拼接作为矩阵的行,cv作为矩阵的列变量,逐个元素按照GrangerCausality=1-unrestrictError/restrictError计算的值,其中unrestrictError为使用输入指定变量和被控变量预测未来被控变量的误差,restrictError为使用被控变量预测未来被控变量的误差;relativeGain耦合强度指标是将mv和dv拼接作为矩阵的行,cv作为矩阵的列变量,逐个元素按照RGA相对增益获得对应数值,越接近1与其他控制关系的耦合越弱;忽略没有提到的用户数据中的其他属性字段,不要在生成的StrongInformation字段中体现RGA具体数值.3.输出内容要求:不能出现RGA相对增益和格兰杰的字样,不能出现相对增益具体数值.4.严格按照以下格式输出,保留示例中的英文逗号作为两个字段的分割,禁止以外的内容输出,示例如下:{"CausalInformation":"xxx","StrongInformation":"xxx"}/no_think'
            tmp_params = {
                'content_data': tmp_content,
                'content': params
            }
            result_data = await context.call_tool("get_chat_completions", params=tmp_params)  # 根据提示词和数据调用大语言获取对应的数据信息
            if result_data is not None:
                # await context.add_view({
                #     "format": "markdown",
                #     "content": f"""
                # ## TPT_URL：
                #         {TPT_URL}
                #                 """
                # })
                CausalInformation = result_data['CausalInformation']  # 模型关系因果分析结果
                StrongInformation = result_data['StrongInformation']  # 模型关系强弱分析结果
                f = furl()
                f.path = '/xpt-tpt-apc/analysisResult'
                f.args['theme'] = 'dark'
                f.args['cv_list'] = str(cv_list).replace(" ", "")  #
                f.args['mv_list'] = str(mv_list).replace(" ", "")  #
                f.args['dv_list'] = str(dv_list).replace(" ", "")  #
                f.args['influenceGrangerIndex'] = str(influence_granger_index).replace(" ", "")  #
                f.args['gainLevel'] = str(gain_level).replace(" ", "")  #
                f.args['CausalInformation'] = CausalInformation  # 模型关系因果分析结果
                f.args['StrongInformation'] = StrongInformation  # 模型关系强弱分析结果
                await context.add_view({
                    "format": "card",
                    "content": {
                        "type": 'page',
                        "title": '输出',
                        "description": "数据分析结果",
                        "details": f.url
                    }
                })
                return {
                    'code': 200
                }
    else:
        return {
            'result': result
        }
