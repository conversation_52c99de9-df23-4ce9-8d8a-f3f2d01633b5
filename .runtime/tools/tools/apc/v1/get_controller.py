from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
# APC_URL = os.getenv("APC_URL")  # 读取网址信息

APC_inner_token = 'Bearer f4590963-4376-4550-83e1-44406ae107d2'  # 读取env文件中的key值为Inner_token的值
APC_URL = 'http://10.30.72.24:10677'
X_TPT_TOKEN  = "tpt-token"

# add by liupeng 2025-04-07
# 获取控制器分组数据信息 包含全厂 装置信息
@tool(version="*")
async def get_controller_id(context: Context, params: any):
    if 'DeviceName' in params:
        DeviceName = params["DeviceName"]  # 装置名称(用户输入)
    else:
        DeviceName = ""
    response = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/config/node/all?groupType=0'  # 该接口是获取所有装置数据
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if len(DeviceName) > 0:
        dataList = [emp for emp in json_obj['data']['children'] if emp["name"] == DeviceName]  # 根据装置名称去匹配
        if len(dataList) > 0:
            # 根据装置ID对控制器实时的投运率进行获取
            sysGroupId = dataList[0]['id']  # 装置ID
            return sysGroupId
        else:
            return json_obj['data']
    else:
        return json_obj['data']
