from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv
from dateutil.parser import parse

load_dotenv()


# 主要实现的功能是 APC控制器信息查询
# 首先需要获取全厂ID
# add by liupeng 2025-04-08
@tool(version="*")
async def get_adcon_controller_online_list(context: Context, params: any):

    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    try:
        groupType = 8  # 写死
        deviceName = '全厂'
        if 'deviceName' in params:
            tempDeviceName = params["deviceName"]
            if tempDeviceName != '':
                deviceName = tempDeviceName

        groupParams = {'customGroupType': groupType, "deviceName": deviceName}
        # 获取装置id
        device_id = await context.call_tool("get_device_id_by_name", params=groupParams)  # 根据用户输入的装置名称对装置ID进行获取
        if device_id == -1:
            return format_response(
                success=False,
                message="未找到该装置(" + deviceName + ")"
            )
        online_project_param = {'groupId': device_id}
        # 根据全厂ID对控制器信息进行查询
        response_projecttree = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/project/data/projecttree'
                                            , params=online_project_param, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_projecttree = json.loads(response_projecttree.text)
        dataInfo = json_obj_projecttree['data']
        loadedProjectList = []
        unLoadedProjectList = []
        if dataInfo:
            loadedProjectList = dataInfo['loadedProjectList']
            unLoadedProjectList = dataInfo['unLoadedProjectList']
        # 加载控制器
        controller_load_project_info = controller_load_project_detail(loadedProjectList)
        # 未加载控制器
        controller_unload_project_info = controller_unload_project_detail(unLoadedProjectList)
        adconOnlineControllerList = controller_load_project_info["online_controller_table"] + controller_unload_project_info["online_controller_table"]

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": 'APC控制器信息查询',
                "description": "APC控制器信息查询",
                "details": f"""
{adconOnlineControllerList}
    """
            }
        })

        return format_response(
            success=True,
            data={"loadProject": controller_load_project_info["online_controller_detail_return"],
                  "unLoadProject": controller_unload_project_info["online_controller_detail_return"]}
        )

    except Exception as e:
        # 处弹原始异常 e
        errorMsg=e.args[0] # 获欣原始错误信息
        if isinstance(errorMsg, bytes):
            errorMsg=errorMsg.decode('utf-8','ignore') # 字节转字符串
        fullMsg = f"select controller error! error message: {errorMsg}"
        raise Exception(fullMsg) # 重新封装并抛出


## 已加载控制器
def controller_load_project_detail(loadedProjectList):
    online_controller_detail = ''
    online_controller_detail_return = []
    if loadedProjectList:
        for index, s in enumerate(loadedProjectList, start=1):
            run_status_desc = get_run_status_desc(s.get('runState', ''))
            controller_switch_desc = get_status_desc(s.get('controllerSwitch', ''))

            last_running_time = custom_format_time(s.get('lastRunningTime', ''))
            last_loading_time = custom_format_time(s.get('lastLoadingTime', ''))
            online_controller_detail = online_controller_detail + (
                f"|{index}"
                f"|{s.get('projectName', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('type', '')}"
                f"|{run_status_desc}"
                f"|{controller_switch_desc}"
                f"|{s.get('processId', '')}"
                f"|{s.get('serverPort', '')}"
                f"|{last_running_time}"
                f"|{last_loading_time}"
                f"|{s.get('priority', '')}"
                f"|{s.get('projectName', '')}"
                f"|{s.get('dataSourceName', '')}\r\n"
            )
            online_controller_detail_item = {"projectName": s.get('projectName', ''), "type": s.get('type', ''),
                                             "runState": run_status_desc, "controllerSwitch": controller_switch_desc,
                                             "processId": s.get('processId', ''), "serverPort": s.get('serverPort', ''),
                                             "lastRunningTime": last_running_time, "lastLoadingTime": last_loading_time,
                                             "priority": str(s.get('priority', '')),
                                             "dataSourceName": s.get('dataSourceName', '')}
            online_controller_detail_return.append(online_controller_detail_item)

    online_controller_table = f"""
## 加载工程(控制器列表)
| 序号| 应用工程 | 描述 | 工程类型 | 工程状态 | 控制开关 | 进程ID | 端口 | 最后一次运行时间 | 最后一次加载时间 | 自动启动顺序 | 控制器 | 数据源 |
|:--:|:-------:|:---:|:------:|:-------:|:-------:|:-----:|:---:|:-------------:|:-------------:|:----------:|:-----:|:-----:|
{online_controller_detail}
"""
    return {"online_controller_table": online_controller_table,
            "online_controller_detail_return": online_controller_detail_return}


## 已加载控制器
def controller_unload_project_detail(unLoadedProjectList):
    online_controller_detail = ''
    online_controller_detail_return = []
    if unLoadedProjectList:
        for index, s in enumerate(unLoadedProjectList, start=1):
            run_status_desc = get_run_status_desc(s.get('runState', ''))
            last_running_time = custom_format_time(s.get('lastRunningTime', ''))
            last_loading_time = custom_format_time(s.get('lastLoadingTime', ''))
            online_controller_detail = online_controller_detail + (
                f"|{index}"
                f"|{s.get('projectName', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('type', '')}"
                f"|{run_status_desc}"
                f"|{last_running_time}"
                f"|{last_loading_time}"
                f"|{s.get('priority', '')}"
                f"|{s.get('projectName', '')}"
                f"|{s.get('dataSourceName', '')}\r\n"
            )
            online_controller_detail_item = {"projectName": s.get('projectName', ''), "type": s.get('type', ''),
                                             "runState": run_status_desc, "priority": str(s.get('priority', '')),
                                             "lastRunningTime": last_running_time, "lastLoadingTime": last_loading_time,
                                             "dataSourceName": s.get('dataSourceName', '')}
            online_controller_detail_return.append(online_controller_detail_item)

    online_controller_table = f"""
## 未加载工程(控制器列表)
| 序号| 应用工程 | 描述 | 工程类型 | 工程状态 | 最后一次运行时间 | 最后一次加载时间 | 自动启动顺序 | 控制器 | 数据源 |
|:--:|:-------:|:---:|:-------:|:------:|:-------------:|:-------------:|:----------:|:-----:|:-----:|
{online_controller_detail}
"""
    return {"online_controller_table": online_controller_table,
            "online_controller_detail_return": online_controller_detail_return}


RUN_STATUS_DICT = {
    "Ready": "就绪",
    "Loaded": "已加载",
    "Running": "运行中",
    "Reloading": "重新加载中",
    "UnLoaded": "未加载",
    "Stopped": "已停止",
    "Error": "错误",
    "unknown": "错误",
    "TimeOut": "超时",
    "Exception": "异常",
    "Loading Failed": "加载失败",
    "Loading": "加载中",
    "Accident Terminated": "异常中止"
}


## 根据编码返回中文名称
def get_run_status_desc(status_code):
    """根据英文状态名称获取中文描述"""
    return RUN_STATUS_DICT.get(status_code, "未知状态")


# 状态码映射字典
STATUS_MAPPING = {
    0: "OFF",
    1: "ON"
}


def get_status_desc(code):
    """根据状态码获取中文描述"""
    return STATUS_MAPPING.get(code, "-")


def custom_format_time(time_str):
    try:
        # 使用dateutil更灵活地解析各种格式
        dt = parse(time_str)
        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        # errorMsg=e.args[0] # 获欣原始错误信息
        # if isinstance(errorMsg, bytes):
        #     errorMsg=errorMsg.decode('utf-8','ignore') # 字节转字符串
        # fullMsg = f"格式化日期出错:{errorMsg}"
        # print(fullMsg)
        return time_str
    return formatted_time


def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }
