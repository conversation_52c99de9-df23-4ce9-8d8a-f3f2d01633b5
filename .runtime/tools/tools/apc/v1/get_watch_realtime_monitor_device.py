from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv
from dateutil.parser import parse

load_dotenv()

# 根据用户输入的装置名称对装置指标实时指标数据进行查询查询
# add by liupeng 2025-04-10
@tool(version="*")
async def get_watch_realtime_monitor_device(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    deviceRunDetailInfo = ''
    deviceRunDetailReturn = {}
    selectTarget = []
    if 'selectTarget' in params:
        selectTarget = params["selectTarget"]

    orderType = 0
    if "orderType" in params:
        orderType = params["orderType"]

    deviceName = params.get('deviceName', '')  # 装置名称(用户输入)
    if not deviceName:
        deviceName = "全厂"
    ## 0:实时,1:班次,2:日,3:周,4:月
    countType = 2
    if 'countType' in params:
        countType = params['countType']
    controllerCountType = 2
    ## 如果传入过来的是实时计算 那就把控制器查询的维度变成实时  其他都变成日
    if countType == 0:
        controllerCountType = 0
        countType = 2
    try:
        sysGroupId = 0
        deviceParam = {"deviceName": deviceName}
        deviceId = await context.call_tool("get_device_id_by_name", params=deviceParam)  # 根据用户输入的装置名称对装置ID进行获取
        if deviceId > 0:
            sysGroupId = deviceId
        else:
            return {"error": "该装置(" + deviceName + ")不存在"}
        params = {'sysGroupId': sysGroupId}
        # 该能力主要获取的信息有：自控率 投运率 MV投运率 CV投运率 操作频次 控制器投运个数占比 MV投运个数占比 CV投运个数占比 装置运行状态总览
        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/select'
                                , params=params, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj = json.loads(response.text)
        device_data = json_obj['data']
        # 获取操作变量卡限率Top10（日）
        order = 'desc'
        if orderType == 1:
            order = 'asc'
        indicatorUnitTitle = 11
        variableType = 3
        params_rank = {'countType': countType, 'sysGroupId': sysGroupId, 'order': order,
                       'indicatorUnitTitle': indicatorUnitTitle, 'variableType': variableType}
        response_rank = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/rank/variable/select'
                                     , params=params_rank, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_rank = json.loads(response_rank.text)
        rank_data = json_obj_rank['list']
        # 操作变量投运率Bottom10（日）
        params_rank_operation = {'countType': countType, 'sysGroupId': sysGroupId, 'order': order,
                                 'indicatorUnitTitle': 4, 'variableType': variableType}
        response_rank_Operation = requests.get(
            url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/rank/variable/select'
            , params=params_rank_operation, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_rank_operation = json.loads(response_rank_Operation.text)
        operation_data = json_obj_rank_operation['list']
        # 被控变量超限率Top10（日）
        params_rank_over = {'countType': countType, 'sysGroupId': sysGroupId, 'order': order,
                            'indicatorUnitTitle': 8, 'variableType': 5}
        response_rank_over = requests.get(
            url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/rank/variable/select'
            , params=params_rank_over, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_rank_over = json.loads(response_rank_over.text)
        over_data = json_obj_rank_over['list']
        # 被控变量标准差下降率Bottom10（日）
        params_rank_decline = {'countType': countType, 'sysGroupId': sysGroupId, 'order': order,
                               'indicatorUnitTitle': 10, 'variableType': 5}
        response_rank_decline = requests.get(
            url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/rank/variable/select'
            , params=params_rank_decline, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_rank_decline = json.loads(response_rank_decline.text)
        decline_data = json_obj_rank_decline['list']
        # APC切除次数Top10
        params_controller = {'countType': countType, 'sysGroupId': sysGroupId, 'order': order,
                             'indicatorUnitTitle': 15}
        response_controller = requests.get(
            url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/rank/controller/select'
            , params=params_controller, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_controller = json.loads(response_controller.text)
        controller_data = json_obj_controller['list']
        # 获取操作频次
        params_operation = {'countType': countType, 'sysGroupId': sysGroupId}
        response_operation = requests.get(
            url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/operation/frequency/select'
            , params=params_operation, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_operation = json.loads(response_operation.text)
        operation_datas = json_obj_operation['data']
        # 控制器指标信息
        params_device = {'countType': controllerCountType, 'sysGroupId': sysGroupId}
        # 该接口是获取该装置下的控制器信息
        controllerResponse = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/controller/select'
                                          , params=params_device, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_controller = json.loads(controllerResponse.text)
        device_datas = json_controller['list']
        # 总计
        indicatorTitleNo = 1  # 1自控率 2投运率 4MV投运率 5CV投运率
        # countType = 0
        params_charts = {'indicatorTitleNo': indicatorTitleNo, 'sysGroupId': sysGroupId,
                         'countType': controllerCountType}
        controller_charts = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/charts/select'
                                         , params=params_charts, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_charts = json.loads(controller_charts.text)
        charts_data = json_charts['data']
        # return {'Device_Data': Device_Data, 'Rank_data': rank_data, 'Operation_data': Operation_data,
        #         'Over_data': Over_data, 'Decline_data': Decline_data, 'Controller_data': controller_data,
        #         'Operation_datas': operation_datas, 'device_datas': device_datas, 'Charts_data': charts_data}
        tempParam = {'device_data': device_data, 'rank_data': rank_data, 'operation_data': operation_data,
                     'over_data': over_data, 'decline_data': decline_data, 'controller_data': controller_data,
                     'Operation_datas': operation_datas, 'device_datas': device_datas, 'Charts_data': charts_data}

        device_data_base_info = device_data_format_data(tempParam, countType)

        device_run_info_temp = device_run_info(tempParam)
        # 卡线率
        operation_param_card_line_info = operation_param_card_line(tempParam, countType)

        put_into_operation_info = put_into_operation(tempParam, countType)

        over_limit_rate_info = over_limit_rate(tempParam, countType)

        decline_data_rate_info = decline_data_rate(tempParam, countType)

        removal_frequency_info = removal_frequency(tempParam, countType)

        #controller_run_detail_info = controller_run_detail(tempParam, controllerCountType)
        deviceRunDetailInfo = (
                device_data_base_info["device_data_detail_table"] +
                device_run_info_temp["device_data_detail_table"] +
                operation_param_card_line_info["rank_data_detail_table"] +
                put_into_operation_info["operation_data_detail_table"] +
                over_limit_rate_info["over_data_detail_table"] +
                decline_data_rate_info["decline_data_table"] +
                removal_frequency_info["removal_frequency_table"])
        if 0 in selectTarget:
            deviceRunDetailReturn = {"deviceStatusAndBaseData": device_data_base_info["device_data_detail_return"],
                                     "deviceRunInfo": device_run_info_temp["device_data_detail_return"],
                                     "operationParamCardLineData": operation_param_card_line_info["rank_data_detail_return"],
                                     "putIntoOperationData": put_into_operation_info["operation_data_detail_return"],
                                     "overLimitRateData": over_limit_rate_info["over_data_detail_return"],
                                     "declineDataRateData": decline_data_rate_info["decline_data_detail_return"],
                                     "removalFrequencyData": removal_frequency_info["removal_frequency_detail_return"]}
        else:
            if 1 in selectTarget:
                deviceRunDetailReturn["deviceStatusAndBaseData"] = device_data_base_info["device_data_detail_return"]
            if 2 in selectTarget:
                deviceRunDetailReturn["deviceRunInfo"] = device_run_info_temp["device_data_detail_return"]
            if 3 in selectTarget:
                deviceRunDetailReturn["operationParamCardLineData"] = operation_param_card_line_info["rank_data_detail_return"]
            if 4 in selectTarget:
                deviceRunDetailReturn["putIntoOperationData"] = put_into_operation_info["operation_data_detail_return"]
            if 5 in selectTarget:
                deviceRunDetailReturn["overLimitRateData"] = over_limit_rate_info["over_data_detail_return"]
            if 6 in selectTarget:
                deviceRunDetailReturn["declineDataRateData"] = decline_data_rate_info["decline_data_detail_return"]
            if 7 in selectTarget:
                deviceRunDetailReturn["removalFrequencyData"] = removal_frequency_info["removal_frequency_detail_return"]
    except Exception as e:
        # print("装置指标实时指标数据查询失败:APC_AGENT_URL:" + APC_AGENT_URL + str(e))
        raise Exception(f"装置指标实时指标数据查询失败，参数信息:" + str(e))
        # return {"error": "装置指标实时指标数据查询失败,参数信息:"+DeviceName}

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '装置指标实时指标数据展示',
            "description": "装置指标实时指标数据展示",
            "details": f"""
{deviceRunDetailInfo}
"""
        }
    })
    return format_response(
        success=True,
        data=deviceRunDetailReturn
    )


def custom_format_time(time_str):
    try:
        # 使用dateutil更灵活地解析各种格式
        dt = parse(time_str)
        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        # print(f"格式化日期出错: {time_str} - 错误: {str(e)}")
        return time_str
    return formatted_time


def get_count_type(count_type):
    """
    根据统计类型返回对应的时间单位
    :param count_type: 1-班次 2-日 3-周 4-月
    :return: 对应的时间单位字符串
    """
    count_type_mapping = {
        0: "实时",
        1: "班次",
        2: "日",
        3: "周",
        4: "月"
    }
    return count_type_mapping.get(count_type, "未知")


## 保留两位小数
def format_decimal(value):
    if value == '' or value is None:
        return value  # 或者返回'0.00'/'NaN'等默认值

    try:
        # 先转换为float再格式化
        return "{:.2f}".format(float(value))
    except (ValueError, TypeError):
        return value  # 处理非法输入


## 装置运行状态总览
def device_data_format_data(tempData, countType):
    device_data = tempData.get('device_data')
    device_data_detail = ''
    device_data_detail_return = {}
    try:
        if device_data:
            status_text = ''
            begin_time_text = ''
            operation_time_text = ''
            shutdown_time_text = ''
            controller_status_desc = ''
            runingStatusAndTimeDto = device_data.get('runingStatusAndTimeDto')
            if runingStatusAndTimeDto:
                ##运行状态
                temp_status = runingStatusAndTimeDto.get('status')
                status_text = '开启'
                if temp_status != 1:
                    status_text = '停止'
                ##开始时间
                begin_time_text = runingStatusAndTimeDto.get('beginTime')
                begin_time_text = custom_format_time(begin_time_text)

                ##累计运行时长
                operation_time_text = runingStatusAndTimeDto.get('operationTime')
                ##累计停工时长
                shutdown_time_text = runingStatusAndTimeDto.get('shutdownTime')
                ##控制器运行状态
                controller_status_num_Dto = runingStatusAndTimeDto.get('controllerStatusNumDto')
                # openNum：正常运行 closedNum： 关闭 conditionNum： 条件剔除
                closed_num = '0'
                open_num = '0'
                condition_num = '0'
                if controller_status_num_Dto:
                    closed_num = controller_status_num_Dto.get('closedNum')
                    open_num = controller_status_num_Dto.get('openNum')
                    condition_num = controller_status_num_Dto.get('conditionNum')
                controller_status_desc = f"正常运行:{open_num} 关闭:{closed_num} 剔除个数:{condition_num}"
                device_data_detail = device_data_detail + (
                    f"|{status_text}"
                    f"|{begin_time_text}"
                    f"|{operation_time_text}"
                    f"|{shutdown_time_text}"
                    f"|{controller_status_desc}\r\n"
                )
                device_data_detail_return = {"status": status_text, "beginTime": begin_time_text,
                                             "operationTime": operation_time_text, "shutdownTime": shutdown_time_text,
                                             "controllerStatus": {"open_num": open_num, "closed_num": closed_num,
                                                                  "condition_num": condition_num}}

    except Exception as e:
        # print("解析装置运行状态总览出错:" + str(e))
        pass
    # tempCountType = get_count_type(countType)
    device_data_detail_table = f"""
## 装置运行状态总览
|  运行状态 |  投运开始时间 | 累计运行时长 | 累计停工时长 | 控制器运行状态 |
|:-------:|:-----------:|:----------:|:----------:|:-----------:|
{device_data_detail}
"""
    return {"device_data_detail_table": device_data_detail_table,
            "device_data_detail_return": device_data_detail_return}


## 装置运行指标
def device_run_info(tempData):
    device_data = tempData.get('device_data')
    device_data_detail = ''
    device_data_detail_return = {}
    try:
        if device_data:
            # 自控率
            operation_self_control_value = ''
            operation_self_control_change = ''
            # 投运率
            tyl_value = ''
            tyl_value_change = ''
            # MV投运率
            mv_tyl_value = ''
            mv_tyl_change = ''
            # CV投运率
            cv_Tyl_value = ''
            cv_Tyl_change = ''
            # 操作频次
            operation_count_text = ''
            # 控制器投运个数占比
            tyl_gs_value = ''
            tyl_gs_change = ''
            # MV投运个数占比
            mv_tyl_gs_value = ''
            mv_tyl_gs_change = ''
            # CV投运个数占比
            cv_tyl_gs_value = ''
            cv_tyl_gs_change = ''

            operation_indicator_list = device_data.get('operationIndicatorDtoList')
            if operation_indicator_list:
                # 自控率
                operation_self_control = operation_indicator_list[0]
                operation_self_control_value = operation_self_control.get("value")
                operation_self_control_change = operation_self_control.get("change")
                # 投运率
                tyl = operation_indicator_list[1]
                tyl_value = tyl.get("value")
                tyl_value_change = tyl.get("change")
                # MV投运率
                mvTyl = operation_indicator_list[2]
                mv_tyl_value = mvTyl.get("value")
                mv_tyl_change = mvTyl.get("change")

                # CV投运率
                cv_Tyl = operation_indicator_list[3]
                cv_Tyl_value = cv_Tyl.get("value")
                cv_Tyl_change = cv_Tyl.get("change")

                # 操作频次
                operation_count = operation_indicator_list[4]
                operation_count_text = operation_count.get("value")
                # 操作频次
                tyl_gs = operation_indicator_list[5]
                tyl_gs_value = tyl_gs.get("value")
                tyl_gs_change = tyl_gs.get("change")
                # MV投运个数占比
                mv_tyl_gs = operation_indicator_list[6]
                mv_tyl_gs_value = mv_tyl_gs.get("value")
                mv_tyl_gs_change = mv_tyl_gs.get("change")

                # CV投运个数占比
                cv_tyl_gs = operation_indicator_list[7]
                cv_tyl_gs_value = cv_tyl_gs.get("value")
                cv_tyl_gs_change = cv_tyl_gs.get("change")

            device_data_detail = device_data_detail + (
                f"|{operation_self_control_value}({operation_self_control_change})"
                f"|{tyl_value}({tyl_value_change})"
                f"|{mv_tyl_value}({mv_tyl_change})"
                f"|{cv_Tyl_value}({cv_Tyl_change})"
                f"|{operation_count_text}"
                f"|{tyl_gs_value}({tyl_gs_change})"
                f"|{mv_tyl_gs_value}({mv_tyl_gs_change})"
                f"|{cv_tyl_gs_value}({cv_tyl_gs_change})\r\n"
            )
            device_data_detail_return = {
                "operationSelfControlValue": operation_self_control_value,
                "operationSelfControlChange": operation_self_control_change,
                "tylValue": tyl_value,
                "tylValueChange": tyl_value_change,
                "mvTylValue": mv_tyl_value,
                "mvTylChange": mv_tyl_change,
                "cvTylValue": cv_Tyl_value,
                "cvTylChange": cv_Tyl_change,
                "operationCountText": operation_count_text,
                "tylGsValue": tyl_gs_value,
                "tylGsChange": tyl_gs_change,
                "mvTylGsValue": mv_tyl_gs_value,
                "mvTylGsChange": mv_tyl_gs_change,
                "cvTylGsValue": cv_tyl_gs_value,
                "cvTylGsChange": cv_tyl_gs_change,
            }
    except Exception as e:
        # print("解析装置运行状态总览出错:" + str(e))
        pass
    # tempCountType = get_count_type(countType)
    device_data_detail_table = f"""
## 装置运行指标
| 自控率(%) | 投运率(%) | MV投运率(%) | CV投运率(%) | 操作频次 | 控制器投运个数占比(%) |  MV投运个数占比(%) | CV投运个数占比(%) |
|:--------:|:--------:|:----------:|:----------:|:------:|:------------------:|:---------------:|:---------------:|
{device_data_detail}
"""
    return {"device_data_detail_table": device_data_detail_table,
            "device_data_detail_return": device_data_detail_return}


## 操作变量卡线率TOP10
def operation_param_card_line(tempData, countType):
    rank_data_list = tempData.get('rank_data')
    rank_data_detail = ''
    rank_data_detail_return = []
    if rank_data_list:
        for index, s in enumerate(rank_data_list, start=1):
            tempValue = format_decimal(s.get('value', ''))
            rank_data_detail = rank_data_detail + (
                f"|{index}"
                f"|{s.get('name', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('controllerName', '')}"
                f"|{tempValue}\r\n"
            )
            temp_detail_return = {"name": s.get('name', ''), "controllerName": s.get('controllerName', ''),
                                  "value": tempValue}
            rank_data_detail_return.append(temp_detail_return)

    tempCountType = get_count_type(countType)
    rank_data_detail_table = f"""
## 操作变量卡限率TOP10({tempCountType})     
| 序号| 变量名称 | 描述 | 所属控制器 | 卡线率(%) |
|:--:|:-------:|:----:|:--------:|:-------:|
{rank_data_detail}
"""
    return {"rank_data_detail_table": rank_data_detail_table, "rank_data_detail_return": rank_data_detail_return}


## 操作变量投运率TOP10
def put_into_operation(tempData, countType):
    operation_data_list = tempData.get('operation_data')
    operation_data_detail = ''
    operation_data_detail_return = []
    if operation_data_list:
        for index, s in enumerate(operation_data_list, start=1):
            tempValue = format_decimal(s.get('value', ''))
            operation_data_detail = operation_data_detail + (
                f"|{index}"
                f"|{s.get('name', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('controllerName', '')}"
                f"|{tempValue}\r\n"
            )
            operation_data_detail_return.append(
                {"name": s.get('name', ''), "controllerName": s.get('controllerName', ''), "value": tempValue})

    tempCountType = get_count_type(countType)
    operation_data_detail_table = f"""
## 操作变量投运率TOP10({tempCountType})     
| 序号| 变量名称 | 描述 | 所属控制器 | 投运率(%) |
|:--:|:-------:|:----:|:--------:|:-------:|
{operation_data_detail}
"""
    return {"operation_data_detail_table": operation_data_detail_table,
            "operation_data_detail_return": operation_data_detail_return}


## 被控变量超限率TOP10
def over_limit_rate(tempData, countType):
    over_data_list = tempData.get('over_data')
    over_data_detail = ''
    over_data_detail_return = []
    if over_data_list:
        for index, s in enumerate(over_data_list, start=1):
            tempValue = format_decimal(s.get('value', ''))
            over_data_detail = over_data_detail + (
                f"|{index}"
                f"|{s.get('name', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('controllerName', '')}"
                f"|{tempValue}\r\n"
            )
            over_data_detail_return.append(
                {"name": s.get('name', ''), "controllerName": s.get('controllerName', ''), "value": tempValue})

    tempCountType = get_count_type(countType)
    over_data_detail_table = f"""
## 被控变量超限率TOP10({tempCountType})     
| 序号| 变量名称 | 描述 | 所属控制器 | 超限率(%) |
|:--:|:-------:|:----:|:--------:|:-------:|
{over_data_detail}
"""
    return {"over_data_detail_table": over_data_detail_table, "over_data_detail_return": over_data_detail_return}


## 被控变量标准差下降率Bottom10
def decline_data_rate(tempData, countType):
    decline_data_list = tempData.get('decline_data')
    decline_data_detail = ''
    decline_data_detail_return = []
    if decline_data_list:
        for index, s in enumerate(decline_data_list, start=1):
            tempValue = format_decimal(s.get('value', ''))
            decline_data_detail = decline_data_detail + (
                f"|{index}"
                f"|{s.get('name', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('controllerName', '')}"
                f"|{tempValue}\r\n"
            )
            decline_data_detail_return.append(
                {"name": s.get('name', ''), "controllerName": s.get('controllerName', ''), "value": tempValue})
    tempCountType = get_count_type(countType)
    decline_data_table = f"""
## 被控变量标准差下降率Bottom10({tempCountType})     
| 序号| 变量名称 | 描述 | 所属控制器 | 标准差下降率(%) |
|:--:|:-------:|:----:|:--------:|:-------:|
{decline_data_detail}
"""
    return {"decline_data_table": decline_data_table, "decline_data_detail_return": decline_data_detail_return}


## 切除次数
def removal_frequency(tempData, countType):
    removal_frequency_list = tempData.get('controller_data')
    removal_frequency_detail = ''
    removal_frequency_detail_return = []
    if removal_frequency_list:
        for index, s in enumerate(removal_frequency_list, start=1):
            tempValue = format_decimal(s.get('value', ''))
            removal_frequency_detail = removal_frequency_detail + (
                f"|{index}"
                f"|{s.get('name', '')}"
                f"|{s.get('desc', '')}"
                f"|{tempValue}\r\n"
            )
            removal_frequency_detail_return.append({"name": s.get('name', ''), "value": tempValue})

    tempCountType = get_count_type(countType)
    removal_frequency_table = f"""
## APC切除次数TOP10({tempCountType})     
| 序号| 控制器名称 | 描述 | 切除次数 |
|:--:|:---------:|:---:|:------:|
{removal_frequency_detail}
"""
    return {"removal_frequency_table": removal_frequency_table,
            "removal_frequency_detail_return": removal_frequency_detail_return}


## 控制器指标信息
def controller_run_detail(tempData, countType):
    controller_run_detail_list = tempData.get('device_datas')
    controller_run_details = ''
    controller_run_details_return = []
    if controller_run_detail_list:
        for index, s in enumerate(controller_run_detail_list, start=1):
            controller_run_details = controller_run_details + (
                f"|{index}"
                f"|{s.get('controllerName', '')}"
                f"|{s.get('desc', '')}"
                f"|{s.get('apcOperationFrequency', '')}"
                f"|{s.get('controllerOperationalRate', '')}({s.get('controllerOperationalRateChange', '')})"
                f"|{s.get('controllerEffectiveOperationalRate', '')}({s.get('controllerEffectiveOperationalRateChange', '')})"
                f"|{s.get('mvOperationalRate', '')}({s.get('mvOperationalRateChange', '')})"
                f"|{s.get('mvOperationalPercent', '')}({s.get('mvOperationalPercentChange', '')})"
                f"|{s.get('cvOperationalRate', '')}({s.get('cvOperationalRateChange', '')})"
                f"|{s.get('cvOperationalPercent', '')}({s.get('cvOperationalPercentChange', '')})"
                f"|{s.get('mvStuckLimitRate', '')}({s.get('mvStuckLimitRateChange', '')})"
                f"|{s.get('cvOverLimitRate', '')}({s.get('cvOverLimitRateChange', '')})\r\n"
            )
            controller_run_details_return.append({"controllerName": s.get('controllerName', ''),
                                               "apcOperationFrequency": s.get('apcOperationFrequency', ''),
                                               "controllerOperationalRate": s.get('controllerOperationalRate', ''),
                                               "controllerOperationalRateChange": s.get('controllerOperationalRateChange', ''),
                                               "controllerEffectiveOperationalRate": s.get('controllerEffectiveOperationalRate', ''),
                                               "controllerEffectiveOperationalRateChange": s.get('controllerEffectiveOperationalRateChange', ''),
                                               "mvOperationalRate": s.get('mvOperationalRate', ''),
                                               "mvOperationalRateChange": s.get('mvOperationalRateChange', ''),
                                               "mvOperationalPercent": s.get('mvOperationalPercent', ''),
                                               "mvOperationalPercentChange": s.get('mvOperationalPercentChange', ''),
                                               "cvOperationalPercent": s.get('cvOperationalPercent', ''),
                                               "cvOperationalPercentChange": s.get('cvOperationalPercentChange', ''),
                                               "cvOperationalRate": s.get('cvOperationalRate', ''),
                                               "cvOperationalRateChange": s.get('cvOperationalRateChange', ''),
                                               "mvStuckLimitRate": s.get('mvStuckLimitRate', ''),
                                               "mvStuckLimitRateChange": s.get('mvStuckLimitRateChange', ''),
                                               "cvOverLimitRate": s.get('cvOverLimitRate', ''),
                                               "cvOverLimitRateChange": s.get('cvOverLimitRateChange', '')})

    tempCountType = get_count_type(countType)
    controller_run_detail_table = f"""
## 控制器指标信息({tempCountType})     
| 序号| 控制器名称 | 控制器描述 | 操作频次 | 投运率 |有效投运率 | mv投运率 | mv投运个数占比 | cv投运率 | cv投运个数占比 | mv卡限率 | cv超限率 |
|:--:|:--------:|:---------:|:------:|:-----:|:-------:|:-------:|:------------:|:-------:|:-----------:|:--------:|:-------:|
{controller_run_details}
"""
    return {"controller_run_detail_table": controller_run_detail_table,
            "controller_run_details_return": controller_run_details_return}


def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }
