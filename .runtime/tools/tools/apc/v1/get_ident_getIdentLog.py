from __runner__ import tool, Context
import requests
import json
import time


# 主要实现的功能是 系统自动辨识日志查询
# add by liupeng 2025-07-02
@tool(private=True)
async def get_ident_getIdentLog(context: Context, params: any):
    APC_URL = context.config["APC_URL"]
    await context.log_info(f"params33333333333 result={params}")
    Id = params['result']
    # 循环和sleep轮询 获取辨识过程中产生的过程数据信息
    while True:
        time.sleep(5)  # 5秒钟（休眠）
        response = requests.post(url=APC_URL + '/tpt/ident/getIdentLog'
                                 , json={
                'id': Id
            }
                                 , headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        if response.status_code == 200:
            json_obj = json.loads(response.text)
            await context.log_info(f"json_obj是是是 result={json_obj}")
            identEndFlag = json_obj['result']['identEndFlag']
            if identEndFlag == "1" or identEndFlag == "2":  # 0是辨识中 1是辨识成功 2辨识错误 1或者2时代表辨识结束，需要进行下一步操作
                dataSegmentLogs = ''
                if len(json_obj['result']['dataSegmentLogs']) > 0:
                    tmp_dataSegmentLogs = json_obj['result']['dataSegmentLogs']
                    for val in tmp_dataSegmentLogs:
                        dataSegmentLogs += "- " + val + '\r\n'

                dataQualityLogs = ''
                if len(json_obj['result']['dataQualityLogs']) > 0:
                    tmp_dataQualityLogs = json_obj['result']['dataQualityLogs']
                    # for val in tmp_dataQualityLogs:
                    #     dataQualityLogs += "- " + val + '\r\n'
                    dataQualityLogs = generate_dataQualityLogs(tmp_dataQualityLogs)
                errorLog = '辨识成功！'
                if identEndFlag == "2":
                    errorLog = '辨识失败！'
                if "errorLog" in json_obj['result']:
                    errorLog = json_obj['result']['errorLog']

                messages = {'key': '辨识过程已完成'}
                result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
                await context.add_view({
                    "format": "markdown",
                    "content": f"""
{result_message}
            """
                })

                await context.add_view({
                    "format": "card",
                    "content": {
                        "type": 'markdown',
                        "title": '辨识过程信息展示',
                        "description": "过程信息小结(数据清洗、数据质量评价、模型辨识、模型质量评价)",
                        "details": f"""
## 辨识过程信息展示
- 首先，对数据集进行数据清洗，剔除数据中的坏值和异常值；
- 然后，调用自动切片算法，选取数据中激励充分的数据段，得到各个输出变量对应的优质数据段以及各数据段的质量评价结果如下：

|          输出变量       |  数据时间范围      |  质量评价  |
|:----------------------:|:---------------:|:--------:|
{dataQualityLogs}
- 数据质量评价：数据质量分为4个等级，质量高低顺序为：A>B>C>D，默认选择AB数据段进行建模。
- 数据质量评价完毕后，将选取质量等级为优、良的数据段进行建模；
- 辨识数据选取完毕后，调用FIR辨识算法和子空间辨识算法，得到辨识模型，并输出模型的评价结果；
- 模型质量评价：模型质量分为4个等级，质量高低顺序为：A>B>C>D，根据不确定度指标和拟合度指标进行综合评价，推荐选择AB模型用于控制。
- 最后，根据由辨识得到的模型对全段数据进行仿真预测，对模型进行仿真，输出仿真结果。
## 辨识结果：
{errorLog}
"""
                    }
                })
                break
    return {
        "preProjectId": Id,
        "identEndFlag": identEndFlag,
        "errorLog":errorLog
    }


def generate_batch_pretuning_form(identProcessVariableList):
    ret = ""
    for s in identProcessVariableList:
        ret = ret + f"|{s['variableName']}|{s['paramName']}|\r\n"
    return ret


def generate_dataQualityLogs(dataQualityLogsList):
    ret = ""
    for s in dataQualityLogsList:
        fruits = s.split(';')
        ret = ret + f"|{fruits[0]}|{fruits[1]}|{fruits[2]}|\r\n"
    return ret
