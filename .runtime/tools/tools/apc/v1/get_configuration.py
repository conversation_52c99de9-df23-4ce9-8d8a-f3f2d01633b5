from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# 主要实现的功能是读取所有Adcon组态控制器数据信息
# add by liupeng 2025-04-08
@tool(private=True)
async def get_configuration():
    response = requests.post(
        url=APC_URL + '/inter-api/apc-project/v1/configuration/list'
        , json={
            'selectType': 0
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    return json_obj['data']
