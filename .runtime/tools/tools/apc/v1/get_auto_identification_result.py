from __runner__ import tool, Context
from furl import furl


# 主要实现的功能是 系统自动辨识
# add by liupeng 2025-05-14
@tool(version="*")
async def get_auto_identification_result(context: Context, params: any):
    await context.log_info(f"params22222 result={params}")
    APC_URL = context.config["APC_URL"]
    if "identEndFlag" in params:
        if params['identEndFlag'] == 2:
            return
    if "preProjectId" in params:
        preProjectId = params['preProjectId']  # preProjectId
    # 打开page
    # 创建 URL 对象并修改各部分
    f = furl()
    f.path = '/xpt-tpt-apc/modelMatrixView'
    f.args['preProjectId'] = preProjectId  # preProjectId
    result = await context.get_interaction("get_auto_identification_result")
    if result is None:

        messages = {'key': '辨识结果确认'}
        result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
        await context.add_view({
            "format": "markdown",
            "content": f"""
{result_message}
        """
        })
        context.require_interaction({
            "id": "get_auto_identification_result",
            "title": "请打开辨识结果，点击【下一步】按钮生成模型数据文件和辨识结果报告",
            "type": "open_page",
            "page_type": "execute",
            "open_page": f.url
        })
        return {}
    else:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "正在根据模型辨识结果生成模型工程文件与辨识报告，请在输出后及时确认。",
                "description": "",
                "details": ""
            }
        })
        if result['code'] == 200:
            id = result['result']
            messages = {'key': '结果输出'}
            result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
            await context.add_view({
                "format": "markdown",
                "content": f"""
{result_message}
        """
            })
        #             await context.add_view({
        #                 "format": "card",
        #                 "content": {
        #                     "type": 'markdown',
        #                     "title": '辨识结果和辨识报告说明',
        #                     "details": """
        # ## 辨识结果和辨识报告说明
        # > **辨识结果说明**：
        # > - 该文件包含所有模型的参数，以及各个模型的阶跃响应序列，可用于后续控制器的设计；
        # >
        # > **辨识结果报告说明**：
        # > - 该报告内包含模型信息，输入变量，输出变量，辨识数据段评价，辨识模型结果，模型仿真结果等详细信息；
        # """
        #                 }
        #             }),
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'summary_file',
                "title": '辨识模型工程文件',
                "details": f"{APC_URL}/tpt/model/modelExport?id={id}",
                "description": "辨识模型工程文件.mdl(暂不支持在线预览)"
            }
        }),
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'summary_file',
                "title": '辨识报告',
                "details": f"{APC_URL}/tpt/ident/exportIdentReport?id={id}",
                "description": "辨识结果报告.docx"
            }
        })
        return result
