from __runner__ import tool, Context
import requests
import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

# APC_AGENT_TOKEN = os.getenv('APC_AGENT_TOKEN')  # 读取env文件中的key值为Inner_token的值
# APC_AGENT_URL = os.getenv("APC_AGENT_URL")  # 读取网址信息


# 主要实现的功能是 控制器日志查询
# add by liupeng 2025-04-08
@tool(version="*")
async def get_project_log_list(context: Context, params: any):

    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    global startTime, endTime
    if "startTime" in params and "endTime" in params:
        Is_Time = await context.call_tool("is_valid_datetime_string", params=params)  # 判断字符串数据是否为日期+时间格式
        if not Is_Time:  # 时间格式错误
            Is_Date = await context.call_tool("is_valid_date_string", params=params)  # 判断字符串数据是否为日期格式
            if not Is_Date:  # 日期格式错误
                endTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 当前时间作为结束时间
                startTime = datetime.now().strftime("%Y-%m-%d") + " 00:00:00"  # 当前时间
            else:
                startTime = params["startTime"] + ' 00:00:00'  # 开始时间
                endTime = params["endTime"] + " 23:59:59"  # 结束时间
        else:
            ##如果是时间则直接使用时间
            startTime = params["startTime"]
            endTime = params["endTime"]
    else:  # 用户没有输入开始时间和结束时间 取近7天的日志数据
        startTime = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")  # 当前时间减去7天
        endTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 当前时间
    # 比较时间
    if startTime > endTime:
        return format_response(
            success=False,
            message="时间范围不合理，开始时间不能大于结束时间！"
        )
    #print('startTime：' + startTime)
    #print('endTime：' + endTime)
    current = 1  # 页码
    pageSize = 100
    if "pageSize" in params:
        pageSize = params['pageSize']  # 一页显示多少条数据
    projectName = None
    if 'projectName' in params:
        projectName = params['projectName']  # 用户输入的控制器名称
    userName = None
    if 'userName' in params:
        userName = params['userName']  # 用户名称
    logSource = None
    if 'logSource' in params:
        logSource = params['logSource']  # 日志来源
    tempContext = None
    if 'tempContext' in params:
        tempContext = params['tempContext']  # 事件
    tempType = None
    if 'logType' in params:
        tempType = params['logType']  # 日志类别

    severity = None
    if 'severity' in params:
        severity = params['severity']  # 日志类别

    logLevel = None
    if 'logLevel' in params:
        logLevel = params['logLevel']  # 日志等级

    params = {'startTime': startTime, 'endTime': endTime, 'current': current, 'pageSize': pageSize,
              'severity': severity, 'logLevel': logLevel,
              'projectName': projectName, 'userName': userName, 'logSource': logSource, 'context': tempContext,
              'type': tempType}
    response = requests.get(url=APC_AGENT_URL + '/inter-api/project/log/list'
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(response.text)
    project_log_list = json_obj['list']
    projectLogInfo = foreach_project_log_list(project_log_list)

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '日志查询',
            "description": "日志查询",
            "details": f"""
{projectLogInfo["project_log_list_table"]}
"""
        }
    })
    return format_response(
        success=True,
        data=projectLogInfo["project_log_list_detail_return"]
    )


## 控制器日志列表格式化
def foreach_project_log_list(project_log_list):
    project_log_list_detail = ''
    project_log_list_detail_return = []
    if project_log_list:
        for index, s in enumerate(project_log_list, start=1):
            severity_desc = get_severity_status_desc(s.get('severity', ''))
            log_level_desc = get_log_level_status_desc(s.get('logLevel', ''))
            type_desc = get_type_status_desc(s.get('type', ''))

            project_log_list_detail = project_log_list_detail + (
                f"|{index}"
                f"|{log_level_desc}"
                f"|{severity_desc}"
                f"|{s.get('addTime', '')}"
                f"|{s.get('context', '')}"
                f"|{s.get('projectName', '')}"
                f"|{s.get('logSource', '')}"
                f"|{s.get('userName', '')}"
                f"|{type_desc}\r\n"
            )
            project_log_list_detail_item = {"logLevel": log_level_desc, "severity": severity_desc,
                                            "addTime": s.get('addTime', ''), "context": s.get('context', ''),
                                            "projectName": s.get('projectName', ''),
                                            "logSource": s.get('logSource', ''),
                                            "userName": s.get('userName', ''), "type": type_desc}
            project_log_list_detail_return.append(project_log_list_detail_item)
    project_log_list_table = f"""
## 日志查询
| 序号| 级别 | 安全等级 | 创建时间 | 事件 | 应用工程 | 日志来源 | 用户名称 | 类别 |
|:--:|:---:|:-------:|:-------:|:--:|:-------:|:-------:|:------:|:----:|
{project_log_list_detail}
"""
    return {"project_log_list_table": project_log_list_table,
            "project_log_list_detail_return": project_log_list_detail_return}


SEVERITY_STATUS_MAPPING = {
    "INFO": "普通",
    "WARNING": "警告",
    "ERROR": "错误"
}


## 根据编码返回中文名称
def get_severity_status_desc(severity):
    """根据英文状态名称获取中文描述"""
    return SEVERITY_STATUS_MAPPING.get(severity, "-")


# "eumDesc": "{1:普通,2:重要,3:关键}"

LOG_LEVEL_STATUS_MAPPING = {
    1: "普通",
    2: "重要",
    3: "关键"
}


## 根据编码返回中文名称
def get_log_level_status_desc(logLevel):
    """根据英文状态名称获取中文描述"""
    return LOG_LEVEL_STATUS_MAPPING.get(logLevel, "-")


# "eumDesc": "{1:APC操作日志,2:后台自动记录,3:其他}"
# 日志类别
TYPE_STATUS_MAPPING = {
    1: "APC操作日志",
    2: "后台自动记录",
    3: "其他"
}


## 根据编码返回中文名称
def get_type_status_desc(tempType):
    """根据英文状态名称获取中文描述"""
    return TYPE_STATUS_MAPPING.get(tempType, "-")


def format_response(data=None, success=True, message=""):
    if data is None:
        data = []
    return {
        "success": success,
        "message": message,
        "data": data
    }
