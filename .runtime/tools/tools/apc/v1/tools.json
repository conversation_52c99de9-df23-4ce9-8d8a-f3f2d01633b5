{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "get_watch_history_monitor_var", "description": "获取某个控制器下的变量指标历史数据，支持不同变量类型，不同时间段，不同报表类型，特定表头，指标类型进行汇总", "params": {"type": "object", "description": "控制器下的变量指标历史数据入参信息", "properties": {"controllerName": {"type": "string", "description": "控制器名称而装置名称"}, "deviceName": {"type": "string", "description": "装置名称，分组名称"}, "variableName": {"type": "string", "description": "变量名称"}, "variableType": {"type": "integer", "description": "变量类型,枚举类 3.操作变量或者mv变量 5.被控变量或者cv变量，不输入此参数有默认参数，默认查询mv变量", "eumDesc": "{3:操作变量或者mv变量,5:被控变量或者cv变量}"}, "startTime": {"type": "string", "description": "开始时间 (格式是：yyyy-MM-dd HH:mm:ss)"}, "endTime": {"type": "string", "description": "结束时间 (格式是：yyyy-MM-dd HH:mm:ss)"}, "resultType": {"type": "array", "items": {"type": "integer", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 11, 12, 13]}, "description": "指标类型、表头、查询结果类型(可多选)，枚举类：1.投运率 2.有效投运率 3.(MV)卡上限率 4.(MV)卡下限率 5.(CV)超上限率 6.(CV)超下限率 7.(CV)标准差 8.(CV)标准差下降率 11.卡限率 12.超限率 13.先控操作频次，此参数不输入则默认查询全部指标即下面枚举的指标全部展示", "eumDesc": "{1:投运率,2:有效投运率,3:(MV)卡上限率,4:(MV)卡下限率,5:(CV)超上限率,6:(CV)超下限率,7:(CV)标准差,8:(CV)标准差下降率,11:卡限率,12:超限率,13:先控操作频次}"}, "reportType": {"type": "integer", "description": "维度类型，报告类型，枚举类： 1.实时/当前/班次 2.日报 3.周报 4.月报", "eumDesc": "{1:实时/当前/班次,2:日报,3:周报,4:月报}"}}, "required": ["startTime", "endTime"]}, "result": {"type": "object", "description": "返回全厂，装置，控制器，变量，自定义指标，回路等级别下的历史指标数据", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "object", "description": "控制器变量历史指标数据", "properties": {"cvVarDetail": {"type": "array", "description": "控制器被控变量历史指标数据列表，具体列表包括包括变量名称,时间段,分组,所属控制器,CV投运率,CV有效投运率,CV超上限率,CV超下限率，CV标准差，CV标准差下降率，CV超限率，CV先控操作频次，报表生成时间", "items": {"description": "返回控制器的历史指标数据列表", "type": "object", "properties": {"name": {"type": "string", "description": "变量名称"}, "startTimeAndEndTime": {"type": "string", "description": "时间段"}, "groupName": {"type": "string", "description": "所属装置，所属分组"}, "controllerName": {"type": "string", "description": "所属控制器"}, "createTime": {"type": "string", "description": "报表生成时间"}, "cvOperateRate": {"type": "string", "description": "cv投运率"}, "cvUsableOperateRate": {"type": "string", "description": "cv有效投运率"}, "cvUpperRate": {"type": "string", "description": "cv超上限率"}, "cvLowerRate": {"type": "string", "description": "cv超下限率"}, "cvStandardDeviation": {"type": "string", "description": "cv标准差"}, "cvStandardDeviationDecreaseRate": {"type": "string", "description": "cv标准差下降率"}, "cvLimitRate": {"type": "string", "description": "cv超限率"}, "countOperateFrequency": {"type": "string", "description": "cv先控操作频次"}}}}, "mvVarDetail": {"type": "array", "description": "控制器操作变量历史指标数据列表，具体列表包括 变量名称，时间段，分组，所属控制器，MV投运率，MV有效投运率，MV卡上限率，MV卡下限率，MV卡限率，MV先控操作频次，报表生成时间", "items": {"description": "控制器操作变量历史指标数据列表", "type": "object", "properties": {"name": {"type": "string", "description": "变量名称"}, "startTimeAndEndTime": {"type": "string", "description": "时间段"}, "groupName": {"type": "string", "description": "所属装置，所属分组"}, "controllerName": {"type": "string", "description": "所属控制器"}, "createTime": {"type": "string", "description": "报表生成时间"}, "mvOperateRate": {"type": "string", "description": "mv投运率"}, "mvUsableOperateRate": {"type": "string", "description": "mv有效投运率"}, "mvUpperRate": {"type": "string", "description": "mv卡上限率"}, "mvLowerRate": {"type": "string", "description": "mv卡下限率"}, "mvLimitRate": {"type": "string", "description": "mv卡限率"}, "countOperateFrequency": {"type": "string", "description": "mv先控操作频次"}}}}}}}, "required": ["success", "message"]}, "alias": ["控制器变量指标历史数据查询"], "catalog": "statistics"}, {"name": "get_watch_history_monitor_var_list", "description": "根据控制器名称获取该控制器的APC控制器性能监控变量，获取某个控制器的变量列表，同时这个列表中会有一些指标数据", "params": {"type": "object", "description": "controllerName是控制器名称不是装置名称，必填", "properties": {"controllerName": {"type": "string", "description": "控制器名称，而非装置名称"}}, "required": ["controllerName"]}, "result": {"type": "object", "description": "返回操作结果和控制器里列表，包括控制器名称，控制器状态，综合性能等级，控制器描述，操作频次，投运率，有效投运率，mv投运率，mv投运个数占比， cv投运率，cv投运个数占比， mv卡限率，cv超限率", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "array", "description": "控制器列表变量列表，包括变量名称，变量类型", "items": {"type": "object", "properties": {"variableName": {"type": "string", "description": "变量名称"}, "variableType": {"type": "string", "description": "变量类型"}}}}}, "required": ["success", "message"]}, "alias": ["APC控制器性能监控变量查询"], "catalog": "statistics"}, {"name": "get_controlProject", "description": "根据用户输入的控制器名称进行加载、运行、停止、卸载、重新加载操作", "params": {"type": "object", "description": "根据用户输入的控制器名称进行加载、运行、停止、卸载、重新加载操作", "properties": {"projectName": {"type": "string", "description": "控制器名称"}, "controlType": {"type": "integer", "description": "操作类型 1加载 2运行 3暂停 4重新加载 5卸载 6删除", "eumDesc": "{1:加载,2:运行,3:暂停,4:重新加载,5:卸载,6:删除}"}}, "required": ["projectName", "controlType"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "结果消息"}, "data": {"type": "boolean", "description": "true表示当前操作结果成功，false表示当前操作结果失败"}}, "required": ["success", "message"]}, "alias": ["APC控制器在线应用"], "catalog": "control"}, {"name": "get_Adcon_data_snap", "description": "根据控制器名称，获取工程类型为Adcon的应用工程快照数据信息，获取某个控制器的控制器运行参数信息，快照信息", "params": {"type": "object", "description": "根据控制器名称或工程名称，获取工程类型为Adcon的应用工程快照数据信息", "properties": {"projectName": {"type": "string", "description": "工程名称，控制器名称，不是装置名称"}}, "required": ["projectName"]}, "result": {"type": "object", "description": "返回应用工程的运行参数信息，快照信息", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "object", "description": "返回应用工程的运行参数信息，快照信息", "properties": {"onlineControllerBaseRunInfo": {"type": "object", "description": "返回应用工程的运行参数信息，快照信息，包括：工程状态，控制开关，控制器状态", "properties": {"projectName": {"type": "string", "description": "应用工程或者控制器名称"}, "projectStatus": {"type": "string", "description": "工程状态"}, "controllerSwitch": {"type": "string", "description": "控制开关"}, "controllerStatus": {"type": "string", "description": "控制器状态"}}}, "onlineControllerList": {"type": "array", "description": "返回应用工程的运行参数信息， 包括：类型，变量名，过程状态，开关，开关状态，运行状态", "items": {"type": "object", "description": "返回应用工程的运行参数信息", "properties": {"type": {"type": "string", "description": "类型"}, "paramName": {"type": "string", "description": "变量名"}, "processStatus": {"type": "string", "description": "过程状态"}, "switching": {"type": "string", "description": "开关"}, "switchStatus": {"type": "string", "description": "开关状态"}, "runStatus": {"type": "string", "description": "运行状态"}}}}}}}, "required": ["success", "message"]}, "alias": ["控制器运行参数快照查询"], "catalog": "statistics"}, {"name": "get_watch_history_monitor", "description": "查询某个控制器或某个装置的历史指标数据查询，支持 1全厂,2:装置,3:控制器,4:变量,5:自定义指标,6:回路等维度和界别下的历史指标数据", "params": {"type": "object", "description": "用户可以输入装置级和控制器级指标历史数据查询", "properties": {"deviceName": {"type": "string", "description": "用户输入的装置名称、分组名称、自定义指标名称，不是控制器的名称"}, "searchType": {"type": "integer", "description": "报表类型 1班次 2日 3周 4月", "eumDesc": "{1:班次,2:日,3:周,4:月}"}, "type": {"type": "integer", "description": "级别，维度，查询类型 1全厂 2装置 3控制器 4变量 5自定义指标 6回路", "eumDesc": "{1:全厂,2:装置,3:控制器,4:变量,5:自定义指标,6:回路}"}, "startTime": {"type": "string", "description": "开始时间，可以为空，默认是最近7天 (格式是：yyyy-MM-dd HH:mm:ss)"}, "endTime": {"type": "string", "description": "结束时间，可以为空，默认是最近7天 (格式是：yyyy-MM-dd HH:mm:ss)"}, "controllerName": {"type": "string", "description": "控制器名称、回路名称，而非装置名称"}}, "required": ["type"]}, "result": {"type": "object", "description": "返回全厂，装置，控制器，变量，自定义指标，回路等级别下的历史指标数据", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "object", "description": "返回全厂，装置，控制器，变量，自定义指标，回路等级别下的历史指标数据", "properties": {"allDeviceHistoryList": {"type": "array", "description": "返回全厂的历史指标数据裂列表", "items": {"type": "object", "properties": {"closeRate": {"type": "number", "description": "自控率"}, "countOperateFrequency": {"type": "integer", "description": "先控操作频次"}, "createTime": {"type": "string", "description": "报表生成时间"}, "name": {"type": "string", "description": "全厂名称"}, "operateFrequency": {"type": "integer", "description": "操作频次"}, "operateRate": {"type": "number", "description": "投运率"}, "startTimeAndEndTime": {"type": "string", "description": "时间段"}, "usableOperateRate": {"type": "number", "description": "有效投运率"}}}}, "controllerHistoryList": {"type": "array", "description": "返回控制器的历史指标数据", "items": {"description": "返回控制器的历史指标数据列表", "type": "object", "properties": {"controllerAllSwitchOperateRate": {"type": "number", "description": "总开关投运率"}, "countOperateFrequency": {"type": "integer", "description": "总先控操作频次"}, "createTime": {"type": "string", "description": "报表生成时间"}, "cvCountOperateFrequency": {"type": "integer", "description": "CV总先控操作频次"}, "cvOperatePercent": {"type": "number", "description": "CV投运个数百分比"}, "cvOperateRate": {"type": "number", "description": "CV平均投运速率"}, "cvUsableOperateRate": {"type": "number", "description": "CV平均有效投运速率"}, "groupName": {"type": "string", "description": "，所属装置名称，所属分组"}, "mvCountOperateFrequency": {"type": "integer", "description": "MV总先控操作频次"}, "mvOperatePercent": {"type": "number", "description": "MV投运个数百分比"}, "mvOperateRate": {"type": "number", "description": "MV平均投运速率"}, "mvUsableOperateRate": {"type": "number", "description": "MV平均有效投运速率"}, "name": {"type": "string", "description": "控制器名称"}, "operateRate": {"type": "number", "description": "投运率"}, "startTimeAndEndTime": {"type": "string", "description": "时间段"}, "usableOperateRate": {"type": "number", "description": "有效投运率"}}}}, "customHistoryList": {"type": "array", "description": "自定义指标历史指标数据", "items": {"type": "object", "description": "自定义指标历史指标数据列表", "properties": {"createTime": {"type": "string", "description": "报表生成时间"}, "groupName": {"type": "string", "description": "所属装置，所属分组"}, "metricValue": {"type": "number", "description": "计算结果"}, "name": {"type": "string", "description": "自定义指标名称"}, "startTimeAndEndTime": {"type": "string", "description": "时间段"}}}}, "deviceHistoryList": {"type": "array", "description": "返回的装置的历史指标数据", "items": {"type": "object", "description": "返回的装置的历史指标数据列表", "properties": {"closeRate": {"type": "number", "description": "装置自控率"}, "controllerAllSwitchOperatePercent": {"type": "number", "description": "装置控制器开关投运个数百分比"}, "countOperateFrequency": {"type": "integer", "description": "装置总先控操作频次"}, "createTime": {"type": "string", "description": "报表生成时间"}, "cvOperateRate": {"type": "number", "description": "装置CV投运率"}, "groupName": {"type": "string", "description": "所属装置，所属分组"}, "mvOperateRate": {"type": "number", "description": "装置MV投运率"}, "name": {"type": "string", "description": "装置名称，分组名称"}, "operateFrequency": {"type": "integer", "description": "装置操作频次"}, "operateRate": {"type": "number", "description": "装置投运率"}, "startTimeAndEndTime": {"type": "string", "description": "时间段"}, "usableOperateRate": {"type": "number", "description": "装置有效投运率"}}}}, "loopHistoryList": {"type": "array", "description": "返回回路级别下的某个装置的历史指标数据", "items": {"type": "object", "description": "返回回路级别下的某个装置的历史指标数据列表", "properties": {"closeRate": {"type": "number", "description": "自控率"}, "createTime": {"type": "string", "description": "报表生成时间"}, "groupName": {"type": "string", "description": "所属装置，所属分组"}, "name": {"type": "string", "description": "回路名称"}, "operateFrequency": {"type": "integer", "description": "操作频次"}, "startTimeAndEndTime": {"type": "string", "description": "时间段"}}}}}}}, "required": ["success", "message"]}, "alias": ["控制器历史指标数据查询", "装置指标历史数据查询"], "catalog": "statistics"}, {"name": "get_controller_name", "description": "根据adcon控制器名称对数据进行查询", "params": {"type": "object", "description": "根据adcon控制器名称对数据进行查询", "properties": {"adconName": {"type": "string", "description": "adcon控制器名称"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"data": {"type": "object", "description": "控制器列表数据信息"}}}, "alias": ["adcon控制器数据查询"], "catalog": "control"}, {"name": "get_customer_report_list", "description": "根据控制器名称对数据进行查询，生成自定义报表数据", "params": {"type": "object", "description": "根据控制器名称对数据进行查询，生成自定义报表数据", "properties": {"controllerNames": {"type": "array", "description": "控制器名称集合，支持同时查询单个或多个控制器"}, "indexType": {"type": "integer", "description": "报表指标类型 1:CV标准差下降率 2:MV投运率 3:CV投运率 4:MV有效投运率 5:CV有效投运率 6:自控率 7:操作频次", "eumDesc": "{1:CV标准差下降率,2:MV投运率,3:CV投运率,4:MV有效投运率,5:CV有效投运率,6:自控率,7:操作频次}"}, "afterserviceEndTime": {"type": "string", "description": "APC投运后结束时间 (时间格式：yyyy-mm-dd HH:mm:ss)"}, "afterserviceStartTime": {"type": "string", "description": "APC投运后开始时间 (时间格式：yyyy-mm-dd HH:mm:ss)"}, "preserviceEndTime": {"type": "string", "description": "APC投运前结束时间 (时间格式：yyyy-mm-dd HH:mm:ss)"}, "preserviceStartTime": {"type": "string", "description": "APC投运前开始时间 (时间格式：yyyy-mm-dd HH:mm:ss)"}}, "required": ["controllerNames", "indexType", "afterserviceEndTime", "afterserviceStartTime", "preserviceEndTime", "preserviceStartTime"]}, "result": {"type": "object", "description": "返回自定义报表各个指标数据", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "object", "description": "返回已加载和未加载的在线控制器列表", "properties": {"limitRateList": {"type": "array", "description": "装置APC投运前后标准差对比", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"variableName": {"type": "string", "description": "变量名称"}, "preserviceLimitDiff": {"type": "number", "description": "投运前标准差"}, "afterserviceLimitDiff": {"type": "number", "description": "投运后标准差"}, "limitDownRate": {"type": "number", "description": "标准差下下降率"}, "controllerName": {"type": "string", "description": "所属控制器"}}}}, "mvRateList": {"type": "array", "description": "装置MV投运率(装置操作变量投运率)", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"variableName": {"type": "string", "description": "变量名称"}, "operateRate": {"type": "number", "description": "投运率"}, "controllerName": {"type": "string", "description": "所属控制器"}}}}, "cvRateList": {"type": "array", "description": "装置CV投运率(装置被控变量投运率)", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"variableName": {"type": "string", "description": "变量名称"}, "operateRate": {"type": "number", "description": "投运率"}, "controllerName": {"type": "string", "description": "所属控制器"}}}}, "usableMVsList": {"type": "array", "description": "装置MV有效投运率(装置操作变量有效投运率)", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"variableName": {"type": "string", "description": "变量名称"}, "usableOperateRate": {"type": "number", "description": "投运率"}, "controllerName": {"type": "string", "description": "所属控制器"}}}}, "usableCVsList": {"type": "array", "description": "装置CV有效投运率(装置被控变量有效投运率)", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"variableName": {"type": "string", "description": "变量名称"}, "usableOperateRate": {"type": "number", "description": "投运率"}, "controllerName": {"type": "string", "description": "所属控制器"}}}}, "operateFrequenciesList": {"type": "array", "description": "装置操作频次", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"name": {"type": "string", "description": "回路名称"}, "operateFrequencyBefore": {"type": "number", "description": "APC投运前操作频次"}, "operateFrequencyAfter": {"type": "number", "description": "APC投运后操作频次"}}}}, "closeRateList": {"type": "array", "description": "装置自控率", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"name": {"type": "string", "description": "装置名称"}, "closeRateBefore": {"type": "number", "description": "APC投运前自控率"}, "closeRateAfter": {"type": "number", "description": "APC投运后自控率"}}}}, "metricDataList": {"type": "array", "description": "装置操自定义指标", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"name": {"type": "string", "description": "指标名称"}, "closeRateBefore": {"type": "number", "description": "APC投运前计算值"}, "closeRateAfter": {"type": "number", "description": "APC投运后计算值"}}}}}}}, "required": ["success", "message"]}, "alias": ["控制器监控自定义报表生成"], "catalog": "statistics"}, {"name": "get_operation_rate", "description": "根据用户输入的装置名称，获取该装置下面所有控制器投运率低于10%的控制器进行切除", "params": {"type": "object", "description": "根据用户输入的装置名称，获取该装置下面所有控制器投运率低于10%的控制器进行切除", "properties": {"DeviceName": {"type": "string", "description": "装置名称"}}, "required": ["DeviceName"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"children": {"type": "object", "description": "结果数据"}}, "required": ["children"]}, "alias": ["完成控制器投运和切除操作"], "catalog": "control"}, {"name": "turn_adcon_controller_mode", "description": "根据用户输入的控制器名称进行投运和切除", "params": {"type": "object", "description": "根据用户输入的控制器名称进行投运和切除", "properties": {"controllerName": {"type": "string", "description": "控制器名称"}, "type": {"type": "string", "description": "投运或切除操作", "eumDesc": "{0:投运,1:切除}"}}, "required": ["controllerName"]}, "result": {"type": "object", "description": "返回操作结果和装置列表信息", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "object", "description": "返回数据信息"}}, "required": ["success", "message"]}, "alias": ["控制器投运和切除"], "catalog": "control"}, {"name": "get_controller_id", "description": "根据用户输入的装置名称获取装置id", "params": {"type": "object", "description": "根据用户输入的装置名称获取装置id", "properties": {"DeviceName": {"type": "string", "description": "装置名称"}}, "required": ["DeviceName"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"sysGroupId": {"type": "integer", "description": "装置id"}, "list": {"type": "object", "description": "数据集合"}}}, "alias": ["APC装置信息查询-备份"], "catalog": "statistics"}, {"name": "get_apc_device_list", "description": "根据装置名称查询装置的信息，也可用于判断是否存在", "params": {"type": "object", "description": "根据用户输入的装置名称获取装置id", "properties": {"deviceName": {"type": "string", "description": "装置名称"}}}, "result": {"type": "object", "description": "返回操作结果和装置列表信息", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "array", "description": "装置信息列表，分组列表", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "装置唯一标识，装置id"}, "deviceName": {"type": "string", "description": "装置名称，分组名称"}}, "required": ["id", "deviceName"]}}}, "required": ["success", "message"]}, "alias": ["APC装置信息查询"], "catalog": "statistics"}, {"name": "get_config_node", "description": "获取APC装置列表数据信息", "params": {"type": "object", "description": "获取APC装置列表数据信息", "properties": {"DeviceName": {"type": "string", "description": "装置名称"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"children": {"type": "object", "description": "装置列表数据信息"}}, "required": ["children"]}, "alias": ["APC装置信息查询-ADCON"], "catalog": "statistics"}, {"name": "get_watch_realtime_monitor_controller_list", "description": "获取某个装置下的控制器列表(或者获取控制器性能指标)，包括控制器名称，控制器状态，综合性能等级，控制器描述，操作频次，投运率，有效投运率，mv投运率，mv投运个数占比， cv投运率，cv投运个数占比， mv卡限率，cv超限率", "params": {"type": "object", "description": "获取某个装置下的控制器列表（或者获取控制器性能指标），装置名称和控制器名称都可以为空则查询的是全厂级别下的数据，也可以都同时填写，同时填写就是查询某个控制器的数据，有装置而没有控制器参数则说明查询的是装置下的控制器", "properties": {"deviceName": {"type": "string", "description": "装置名称,而非控制器名称，装置和控制器不是同一个概念"}, "controllerNames": {"type": "array", "description": "控制器名称集合，而非装置名称 装置和控制器不是同一个概念k,支持同时查询单个或多个控制器"}, "countType": {"type": "integer", "description": "类型、维度、汇总维度、报表维度", "eumDesc": "{0:实时,1:班次,2:日,3:周,4:月}"}}}, "result": {"type": "object", "description": "返回操作结果和控制器里列表，包括控制器名称，控制器状态，综合性能等级，控制器描述，操作频次，投运率，有效投运率，mv投运率，mv投运个数占比， cv投运率，cv投运个数占比， mv卡限率，cv超限率", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "array", "description": "控制器列表,同时包含每个控制器状态、一些指标数据、一些变化数据", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "控制器唯一标识，控制器id"}, "controllerName": {"type": "string", "description": "控制器名称"}, "status": {"type": "string", "description": "控制器状态"}, "generalPerformance": {"type": "string", "description": "综合性能等级"}, "desc": {"type": "string", "description": "控制器描述"}, "apcOperationFrequency": {"type": "string", "description": "操作频次"}, "controllerOperationalRate": {"type": "string", "description": "投运率"}, "controllerOperationalRateChange": {"type": "string", "description": "投运率变化率"}, "controllerEffectiveOperationalRate": {"type": "string", "description": "有效投运率"}, "controllerEffectiveOperationalRateChange": {"type": "string", "description": "有效投运率变化率"}, "mvOperationalRate": {"type": "string", "description": "mv投运率"}, "mvOperationalRateChange": {"type": "string", "description": "mv投运率变化率"}, "mvOperationalPercent": {"type": "string", "description": "mv投运个数占比"}, "mvOperationalPercentChange": {"type": "string", "description": "mv投运个数占比变化率"}, "cvOperationalPercent": {"type": "string", "description": "cv投运率"}, "cvOperationalPercentChange": {"type": "string", "description": "cv投运率变化率"}, "cvOperationalRate": {"type": "string", "description": "cv投运个数占比"}, "cvOperationalRateChange": {"type": "string", "description": "cv投运个数占比变化率"}, "mvStuckLimitRate": {"type": "string", "description": "mv卡限率"}, "mvStuckLimitRateChange": {"type": "string", "description": "mv卡限率变化率"}, "cvOverLimitRate": {"type": "string", "description": "cv超限率"}, "cvOverLimitRateChange": {"type": "string", "description": "cv超限率变化率"}}}}}, "required": ["success", "message"]}, "alias": ["APC控制器性能监控查询"], "catalog": "statistics"}, {"name": "get_adcon_controller_online_list", "description": "获取Adcon在线控制器列表和控制器状态查询，获取某个装置下的控制器信息（在线控制器列表）", "params": {"type": "object", "description": "获取Adcon在线控制器列表和控制器状态查询", "properties": {"deviceName": {"type": "string", "description": "装置名称，不是控制器名称，只能是装置名称，如果不输入此参数则表明是查询的全厂下的在线控制器"}}}, "result": {"type": "object", "description": "反正是否正确表述以及返回已加载和未加载的在线控制器列表", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "object", "description": "返回已加载和未加载的在线控制器列表", "properties": {"loadProject": {"type": "array", "description": "加载的控制器列表，其中包括以下字段 应用工程(控制器名称)，工程类型，工程状态，控制开关，进程ID，端口，最后一次运行时间，最后一次加载时间，自动启动顺序，数据源名称", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"controllerSwitch": {"type": "string", "description": "控制开关"}, "dataSourceName": {"type": "string", "description": "数据源名称"}, "lastLoadingTime": {"type": "string", "description": "最后一次加载时间"}, "lastRunningTime": {"type": "string", "description": "最后一次运行时间"}, "priority": {"type": "string", "description": "自动启动顺序"}, "processId": {"type": "string", "description": "进程ID"}, "projectName": {"type": "string", "description": "控制器名称或者说是工程名称"}, "runState": {"type": "string", "description": "工程状态"}, "serverPort": {"type": "string", "description": "端口"}, "type": {"type": "string", "description": "工程类型"}}}}, "unLoadProject": {"type": "array", "description": "未加载的在线控制器列表，包含以下字段：应用工程（控制器名称），工程类型，工程状态，最后一次运行时间，最后一次加载时间，自动启动顺序，控制器，数据源名称", "items": {"description": "每个元素的字段说明", "type": "object", "properties": {"dataSourceName": {"type": "string", "description": "数据源名称"}, "lastLoadingTime": {"type": "string", "description": "最后一次加载时间"}, "lastRunningTime": {"type": "string", "description": "最后一次运行时间"}, "priority": {"type": "string", "description": "自动启动顺序"}, "projectName": {"type": "string", "description": "控制器名称，应用工程"}, "runState": {"type": "string", "description": "工程状态"}, "type": {"type": "string", "description": "工程类型"}}}}}}}, "required": ["success", "message"]}, "alias": ["APC控制器信息查询"], "catalog": "statistics"}, {"name": "get_controller_table_select", "description": "获取APC的性能监控控制器列表", "params": {"type": "object", "description": "获取APC的性能监控控制器列表", "properties": {"controllerName": {"type": "string", "description": "控制器名称"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"list": {"type": "object", "description": "控制器列表数据信息"}, "controllerName": {"type": "string", "description": "控制器名称"}, "controllerDesc": {"type": "string", "description": "控制器描述"}, "groupPath": {"type": "string", "description": "分组"}, "conditionSwitch": {"type": "integer", "description": "条件剔除 0否 1是"}, "controllerVariableNum": {"type": "string", "description": "控制器量级"}, "datasourceName": {"type": "string", "description": "所属数据源"}, "controllerTypeName": {"type": "string", "description": "控制器类型"}, "controllerRunCycle": {"type": "integer", "description": "控制器运行周期"}, "modifyTime": {"type": "string", "description": "最后一次修改时间"}}, "required": ["list"]}, "alias": ["APC控制器性能监控变量查询-备份（20250627）"], "catalog": "statistics"}, {"name": "get_project_log_list", "description": "控制器日志查询", "params": {"type": "object", "description": "控制器日志查询", "properties": {"projectName": {"type": "string", "description": "控制器名称,工程名称"}, "userName": {"type": "string", "description": "用户名称，操作员名称"}, "logSource": {"type": "string", "description": "日志来源", "eumDesc": "{Adcon:Adcon,ARC:ARC,Sensor:Sensor,Ident:Ident}"}, "tempContext": {"type": "string", "description": "事件，事件描述"}, "logType": {"type": "integer", "de,scription": "日志类别，日志类型", "eumDesc": "{1:APC操作日志,2:后台自动记录,3:其他}"}, "severity": {"type": "string", "description": "安全等级", "eumDesc": "{INFO:普通,WARNING:警告,ERROR:错误}"}, "logLevel": {"type": "integer", "description": "日志等级，日志级别", "eumDesc": "{1:普通,2:重要,3:关键}"}, "startTime": {"type": "string", "description": "开始时间 (格式是：yyyy-MM-dd HH:mm:ss)"}, "endTime": {"type": "string", "description": "结束时间 (格式是：yyyy-MM-dd HH:mm:ss)"}, "pageSize": {"type": "integer", "description": "用户输入的显示数据条数，显示、展示条数"}}}, "result": {"type": "object", "description": "返回操作结果和控制器里列表，包括控制器名称，控制器状态，综合性能等级，控制器描述，操作频次，投运率，有效投运率，mv投运率，mv投运个数占比， cv投运率，cv投运个数占比， mv卡限率，cv超限率", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "array", "description": "控制器列表,同时包含每个控制器状态、一些指标数据、一些变化数据", "items": {"type": "object", "properties": {"logLevel": {"type": "string", "description": "级别"}, "severity": {"type": "string", "description": "安全等级"}, "addTime": {"type": "string", "description": "创建时间"}, "context": {"type": "string", "description": "事件"}, "projectName": {"type": "string", "description": "应用工程或控制器名称"}, "logSource": {"type": "string", "description": "日志来源"}, "userName": {"type": "string", "description": "用户名称"}, "type": {"type": "string", "description": "类别"}}}}}, "required": ["success", "message"]}, "alias": ["APC运行日志查询"], "catalog": "statistics"}, {"name": "get_self_learning_tasks", "description": "根据用户输入的控制器名称创建自学习任务", "params": {"type": "object", "description": "根据用户输入的控制器名称创建自学习任务", "properties": {"controllerName": {"type": "string", "description": "用户输入的控制器名称"}}, "required": ["controllerName"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"data": {"type": "object", "description": "结果信息"}}}, "alias": ["创建自学习任务"], "catalog": "optimization"}, {"name": "get_task_query", "description": "查询当前自学习列表数据", "params": {"type": "object", "description": "查询当前自学习列表数据", "properties": {"taskName": {"type": "string", "description": "自学习任务名称"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"list": {"type": "object", "description": "自学习任务数据列表"}}, "required": ["list"]}, "alias": ["查询自学习任务"], "catalog": "statistics"}, {"name": "get_task_switch", "description": "根据自学习任务名称对任务进行运行和停止", "params": {"type": "object", "description": "根据自学习任务名称对任务进行运行和停止", "properties": {"taskName": {"type": "string", "description": "自学习任务名称"}, "status": {"type": "integer", "description": "1：运行 0：停止", "eumDesc": "{1:运行,0:停止}"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"message": {"type": "string", "description": "结果信息"}}, "required": ["message"]}, "alias": ["自学习任务在线应用"], "catalog": "optimization"}, {"name": "get_watch_realtime_monitor_controller", "description": "根据控制器名称查询控制器实时指标数据，包括控制器运行状态，投运开始时间，累计运行时长，累计剔除时长，综合性能，mv投运率，cv投运率，投运率，操作频次，控制器变量指标信息，MV总数，MV剔除个数，MV投运个数，MV操作频次，CV总数，CV剔除个数，CV投运个数，CV操作频次", "params": {"type": "object", "description": "根据用户输入的控制器名称查询实时指标数据信息", "properties": {"controllerNames": {"type": "array", "description": "控制器名称，而非装置名称，装置和控制器不是同一个概念"}, "selectTarget": {"type": "array", "items": {"type": "integer", "enum": [0, 1, 2, 3, 4, 5]}, "description": "控制器查询指标名称", "eumDesc": "{0:查询全部指标如果不指定指标则传入此值,1:投运开始时间,1:累计运行时长,1:累计剔除时长,1:控制器的运行状态,2:综合性能,2:cv投运率（控制器级别）,2:mv投运率（控制器级别）,2:操作频次（控制器级别）,2:投运率,3:CV操作频次,3:CV标准基准,3:CV超限率,3:CV标准差,3:CV标准基差下降率,3:CV有效投运率,3:CV投运率,3:CV运行状态,4:MV的操作频次或操作频次（变量级别）,4:MV有效投运率,4:MV卡限率,4:MV运行状态或运行状态（变量级别）,5:CV的操作频次（控制器级别）,5:CV剔除个数,5:CV投运个数,5:CV总数,5:MV操作频次（控制器级别）,5:MV剔除个数,5:MV投运个数,5:MV总数}"}}, "required": ["controllerNames", "selectTarget"]}, "result": {"type": "object", "description": "控制器实时指标数据指标信息", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "array", "description": "控制器实时指标数据返回体，此层没有具体数据，需要具体往下层数据结构获取", "items": {"type": "object", "description": "此层数据为能力具体返回数据，如果查询的是单个的控制器信息，直接取集合中第一个元素数据即可，如果是多个控制器，则需要遍历获取", "properties": {"controllerName": {"type": "string", "description": "控制器名称，跟此字段平级的都属于该控制器的指标,优先查看此字段,controllerBaseInfo,controllerRunBaseInfo,cvInfoDetail,mvInfoDetail,var_count_info_return等都是此控制器的指标"}, "controllerBaseInfo": {"type": "object", "description": "控制器基础信息，包括控制器运行状态，投运开始时间，累计运行时长，累计剔除时长", "properties": {"beginTime": {"type": "string", "description": "投运开始时间"}, "operationTime": {"type": "string", "description": "累计运行时长"}, "shutdownTime": {"type": "string", "description": "累计剔除时长"}, "status": {"type": "string", "description": "控制器的运行状态"}}}, "controllerRunBaseInfo": {"type": "object", "description": "控制器的运行基本指标，综合指标，包括综合性能，mv投运率，cv投运率，投运率，操作频次", "properties": {"baseInfoChange": {"type": "string", "description": "综合性能变化率"}, "baseInfoValue": {"type": "string", "description": "综合性能"}, "cvTylChange": {"type": "string", "description": "cv投运率变化率"}, "cvTylValue": {"type": "string", "description": "cv投运率"}, "mvTylChange": {"type": "string", "description": "mv投运率变化率"}, "mvTylValue": {"type": "string", "description": "mv投运率"}, "operationCount": {"type": "string", "description": "操作频次"}, "tylValue": {"type": "string", "description": "投运率"}, "tylValueChange": {"type": "string", "description": "投运率变化率"}}}, "cvInfoDetail": {"type": "array", "description": "控制器的被控变量列表，控制器的cv变量列表,其中包括变量名称，变量描述，运行状态，CV投运率(%)，CV有效投运率(%)，CV超限率(%)，CV标准基准，CV标准差，CV标准基差下降率(%)，CV操作频次", "items": {"description": "列表", "type": "object", "properties": {"apcOperationFrequency": {"type": "string", "description": "CV操作频次"}, "cvBasedStandardDeviation": {"type": "string", "description": "CV标准基准"}, "cvOverLimitRate": {"type": "string", "description": "CV超限率"}, "cvStandardDeviation": {"type": "string", "description": "CV标准差"}, "cvStandardDeviationDecreaseRate": {"type": "string", "description": "CV标准基差下降率"}, "effectiveOperationalRate": {"type": "string", "description": "CV有效投运率"}, "operationalRate": {"type": "string", "description": "CV投运率"}, "status": {"type": "string", "description": "CV运行状态"}, "variableDesc": {"type": "string", "description": "变量描述"}, "variableName": {"type": "string", "description": "变量名称"}}}}, "mvInfoDetail": {"type": "array", "description": "控制器的操作变量列表，控制器的mv变量列表 其中包括一下指标：变量名称，变量描述，mv运行状态，mv投运率(%)，mv有效投运率(%)，MV卡限率(%)，mv操作频次", "items": {"type": "object", "properties": {"apcOperationFrequency": {"type": "string", "description": "MV操作频次"}, "effectiveOperationalRate": {"type": "string", "description": "MV有效投运率"}, "mvStuckLimitRate": {"type": "string", "description": "MV卡限率"}, "operationalRate": {"type": "string", "description": "MV投运率"}, "status": {"type": "string", "description": "MV运行状态"}, "variableDesc": {"type": "string", "description": "变量描述"}, "variableName": {"type": "string", "description": "变量名称"}}}}, "var_count_info_return": {"type": "object", "description": "mv变量和cv变量一些统计信息其中包括：MV总数，MV剔除个数，MV投运个数，MV操作频次，CV总数，CV剔除个数，CV投运个数，CV操作频次", "properties": {"cvCountInfo": {"type": "object", "description": "cv变量一些统计信息其中包括：CV总数，CV剔除个数，CV投运个数，CV操作频次", "properties": {"cvApcOperationFrequency": {"type": "integer", "description": "CV操作频次"}, "cvConditionNum": {"type": "integer", "description": "CV剔除个数"}, "cvOperationalNum": {"type": "integer", "description": "CV投运个数"}, "cvTotalNum": {"type": "integer", "description": "CV总数"}}}, "mvCountInfo": {"type": "object", "description": "mv变量一些统计信息其中包括：MV总数，MV剔除个数，MV投运个数，MV操作频次", "properties": {"mvApcOperationFrequency": {"type": "integer", "description": "MV操作频次"}, "mvConditionNum": {"type": "integer", "description": "MV剔除个数"}, "mvOperationalNum": {"type": "integer", "description": "MV投运个数"}, "mvTotalNum": {"type": "integer", "description": "MV总数"}}}}}}, "required": ["controllerName"]}}}, "required": ["success", "message"]}, "alias": ["控制器实时指标数据查询"], "catalog": "statistics"}, {"name": "get_watch_realtime_monitor_device", "description": "获取某个装置的装置指标实时指标数据，可以根据不同维度，报表维度汇总,指标包括包括装置的投运开始时间，运行状态，累计运行时长，累计停工时长，控制器的运行状态，自控率，投运率，MV投运率，CV投运率，操作频次，控制器投运个数占比，cv投运个数占比，MV投运个数占比，APC切除次数，操作变量卡限率，操作变量投运率,被控变量超限率，被控变量标准差下降率等", "params": {"type": "object", "description": "deviceName 是装置名称而非控制器名称，必须传入，countType可以为空有默认值", "properties": {"deviceName": {"type": "string", "description": "装置名称，而非控制器名称，装置和控制器是两个级别"}, "selectTarget": {"type": "array", "items": {"type": "integer", "enum": [0, 1, 2, 3, 4, 5, 6, 7]}, "description": "查询指标名称", "eumDesc": "{0:全部指标包含（1,2,3,4,5,6,7），1:装置运行状态，1:投运开始时间，1:累计运行时长，1:累计停工时长，1:控制器的运行状态统计,2:自控率，2:投运率，2:mv投运率,2:cv投运率,2:操作频次,2:投运个数占比，2:cv投运个数占比，2:mv投运个数占比,3:操作变量卡限率,3:mv变量卡限率,4:操作变量投运率，4:mv变量投运率,5:被控变量超限率,5:cv变量超限率,6:被控变量标准差下降率,6:cv变量标准差下降率,7:APC切除次数}"}, "countType": {"type": "integer", "description": "类型、维度、汇总维度、报表维度", "eumDesc": "{0:实时,1:班次,2:日,3:周,4:月}"}, "orderType": {"type": "integer", "description": "排序方式（描述方式中带有TOP,Bottom,最高,最多,最大,最低,最少,最小）", "eumDesc": "{0:<PERSON><PERSON>,0:最高,0:最多,0:最大,1:Bottom,1:最低,1:最少,1:最小}"}}, "required": ["selectTarget"]}, "result": {"type": "object", "description": "返回的装置指标信息综合结果", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "object", "description": "返回的装置指标信息综合结果，可能包括包括装置的投运开始时间，运行状态，累计运行时长，累计停工时长，控制器的运行状态，自控率，控制器投运个数占比，cv投运个数占比，mv投运个数占比,操作频次，APC切除次数，操作变量卡限率，操作变量投运率,被控变量超限率，被控变量标准差下降率等数据", "properties": {"deviceStatusAndBaseData": {"type": "object", "description": "装置的基础面板，装置运行状态总览，包括装置的投运开始时间，运行状态，累计运行时长，累计停工时长，控制器的运行状态", "properties": {"status": {"type": "string", "description": "装置运行状态", "enum": ["开启", "关闭", "条件剔除"]}, "beginTime": {"type": "string", "description": "装置的投运开始时间,投运开始时间"}, "operationTime": {"type": "string", "description": "装置的累计运行时长,累计运行时长"}, "shutdownTime": {"type": "string", "description": "装置的累计停工时长,累计停工时长"}, "controllerStatus": {"type": "object", "description": "控制器的运行各个状态的统计信息", "properties": {"open_num": {"type": "integer", "description": "正常数量"}, "closed_num": {"type": "integer", "description": "关闭数量"}, "condition_num": {"type": "integer", "description": "剔除数量"}}}}}, "deviceRunInfo": {"type": "object", "description": "装置的基础运行指标其中包括自控率，投运率，mv投运率,cv投运率,操作频次,投运个数占比，cv投运个数占比，mv投运个数占比", "properties": {"operationSelfControlValue": {"type": "string", "description": "装置的自控率,自控率"}, "operationSelfControlChange": {"type": "string", "description": "装置的自控率变化率,自控率变化率"}, "tylValue": {"type": "string", "description": "装置的投运率,投运率"}, "tylValueChange": {"type": "string", "description": "装置的投运率变化率,投运率变化率"}, "mvTylValue": {"type": "string", "description": "装置的mv投运率变化率,mv投运率"}, "mvTylChange": {"type": "string", "description": "装置的mv投运率变化率,mv投运率变化率"}, "cvTylValue": {"type": "string", "description": "装置的cv投运率变化率,cv投运率"}, "cvTylChange": {"type": "string", "description": "装置的cv投运率变化率,cv投运率变化率"}, "operationCountText": {"type": "string", "description": "装置的操作频次,操作频次"}, "tylGsValue": {"type": "string", "description": "装置的投运个数占比,投运个数占比"}, "tylGsChange": {"type": "string", "description": "装置的投运个数占比变化率,投运个数占比变化率"}, "mvTylGsValue": {"type": "string", "description": "装置的mv投运个数占比,mv投运个数占比"}, "mvTylGsChange": {"type": "string", "description": "装置的mv投运个数占比变化率,mv投运个数占比变化率"}, "cvTylGsValue": {"type": "string", "description": "装置的cv投运个数占比,cv投运个数占比"}, "cvTylGsChange": {"type": "string", "description": "装置的cv投运个数占比变化率,cv投运个数占比变化率"}}}, "operationParamCardLineData": {"type": "array", "description": "装置的操作变量卡限率列表,装置的mv变量卡限率列表", "items": {"type": "object", "description": "装置的操作变量卡限率列表，是变量级别的，是该装置下额所有的控制器，所有变量的卡限率，按照值正序，或倒叙的排序", "properties": {"name": {"type": "string", "description": "操作变量（mv）名称"}, "controllerName": {"description": "该变量所属控制器"}, "value": {"type": "string", "description": "卡限率的值"}}}}, "putIntoOperationData": {"type": "array", "description": "装置的操作变量投运率列表，装置的mv变量投运率列表", "items": {"type": "object", "description": "装置的操作变量投运率列表，是变量级别的，是该装置下额所有的控制器，所有操作变量投运率，按照值正序，或倒叙的排序", "properties": {"name": {"type": "string", "description": "操作变量（mv）名称"}, "controllerName": {"description": "该变量所属控制器"}, "value": {"type": "string", "description": "投运率的值"}}}}, "overLimitRateData": {"type": "array", "description": "装置的被控变量超限率列表，装置的cv变量超限率列", "items": {"type": "object", "description": "装置的被控变量(cv)超限率列表，是变量级别的，是该装置下额所有的控制器，所有被控变量(cv)超限率，按照值正序，或倒叙的排序", "properties": {"name": {"type": "string", "description": "被控变量（cv）名称"}, "controllerName": {"description": "该变量所属控制器"}, "value": {"type": "string", "description": "超限率的值"}}}}, "declineDataRateData": {"type": "array", "description": "装置的被控变量标准差下降率列表，cv变量标准差下降率", "items": {"type": "object", "description": "装置的被控变量(cv)标准差下降率列表，是变量级别的，是该装置下额所有的控制器，所有被控变量(cv)标准差，按照值正序，或倒叙的排序", "properties": {"name": {"type": "string", "description": "被控变量（cv）名称"}, "controllerName": {"description": "该变量所属控制器"}, "value": {"type": "string", "description": "标准差下降率的值"}}}}, "removalFrequencyData": {"type": "array", "description": "装置的APC切除次数列表，控制器切除次数列表", "items": {"type": "object", "description": "装置的APC切除次数（或者控制器切除次数）列表，按照值正序，或倒叙的排序", "properties": {"name": {"type": "string", "description": "控制器名称"}, "value": {"type": "string", "description": "APC切除次数,切除次数，控制器切除次数"}}}}, "controllerRunDetailsData": {"type": "array", "description": "控制器的实时指 该列表不属于装置的指标，属于控制器的指标,标包括控制器名称，控制器描述，操作频次，投运率，有效投运率，mv投运率，mv投运个数占比，cv投运率，cv投运个数占比，mv卡限率，cv超限率，以及对应指标的一些变化率", "items": {"type": "object", "description": "列表中的元素", "properties": {"controllerName": {"type": "string", "description": "控制器名称"}, "apcOperationFrequency": {"type": "string", "description": "操作频次"}, "controllerOperationalRate": {"type": "string", "description": "投运率"}, "controllerOperationalRateChange": {"type": "string", "description": "投运率变化率"}, "controllerEffectiveOperationalRate": {"type": "string", "description": "有效投运率"}, "controllerEffectiveOperationalRateChange": {"type": "string", "description": "有效投运率变化率"}, "mvOperationalRate": {"type": "string", "description": "mv投运率"}, "mvOperationalRateChange": {"type": "string", "description": "mv投运率变化率"}, "mvOperationalPercent": {"type": "string", "description": "mv投运个数占比"}, "mvOperationalPercentChange": {"type": "string", "description": "mv投运个数占比变化率"}, "cvOperationalPercent": {"type": "string", "description": "cv投运个数占比"}, "cvOperationalPercentChange": {"type": "string", "description": "cv投运个数占比变化率"}, "cvOperationalRate": {"type": "string", "description": "cv投运率"}, "cvOperationalRateChange": {"type": "string", "description": "cv投运率变化率"}, "mvStuckLimitRate": {"type": "string", "description": "mv卡限率"}, "mvStuckLimitRateChange": {"type": "string", "description": "mv卡限率变化率"}, "cvOverLimitRate": {"type": "string", "description": "cv超限率"}, "cvOverLimitRateChange": {"type": "string", "description": "cv超限率变化率"}}}}}}}, "required": ["success", "message"]}, "alias": ["装置指标实时指标数据查询"], "catalog": "statistics"}, {"name": "get_online_project_model", "description": "根据用户输入的Adcon工程名称获取该工程下的模型数据信息", "params": {"type": "object", "description": "根据用户输入的Adcon工程名称获取该工程下的模型数据信息", "properties": {"projectName": {"type": "string", "description": "工程名称"}}, "required": ["projectName"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"data": {"type": "object", "description": "结果信息"}}}, "alias": ["Adcon控制器模型查询"], "catalog": "prediction"}, {"name": "get_simulation_trend_charts", "description": "根据用户输入的Adcon控制器名称获取该工程下仿真趋势图数据信息", "params": {"type": "object", "description": "根据用户输入的Adcon控制器名称获取该工程下仿真趋势图数据信息", "properties": {"ControllerName": {"type": "string", "description": "控制器名称"}}, "required": ["ControllerName"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"data": {"type": "object", "description": "结果信息"}}}, "alias": ["获取Adcon仿真曲线"], "catalog": "simulation"}, {"name": "adcon_config_create", "description": "创建控制器，并导入模型信息", "params": {"type": "object", "description": "创建控制器参数信息", "properties": {"adconName": {"type": "string", "description": "adcon名称，控制器名称"}}}, "result": {"type": "object", "description": "adcon组态返回信息", "properties": {"success": {"type": "boolean", "description": "操作是否成功，如果是true则说明是操作成功，如果是false则说明操作结果错误"}, "message": {"type": "string", "description": "操作结果描述，一般用于操作错误描述"}, "data": {"type": "boolean", "description": "adcon组态界面创建结果"}}, "required": ["success", "message"]}, "alias": ["adcon控制器组态"], "catalog": "statistics"}, {"name": "get_auto_ident", "description": "根据用户输入的输入位号、输出位号、开始时间、结束时间进行系统自动辨识功能", "params": {"type": "object", "description": "根据用户输入的输入位号、输出位号、开始时间、结束时间进行系统自动辨识功能", "properties": {"InputNames": {"type": "array", "description": "输入位号、输入变量", "items": {"type": "string"}}, "OutputNames": {"type": "array", "description": "输出位号、输出变量", "items": {"type": "string"}}, "startTime": {"type": "string", "description": "开始时间"}, "endTime": {"type": "string", "description": "结束时间"}, "runCycle": {"type": "integer", "description": "采样周期"}, "modelStepRespPVsLength": {"type": "integer", "description": "模型长度"}}, "required": ["InputNames", "OutputNames"]}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"data": {"type": "object", "description": "结果信息"}}}, "alias": ["模型辨识"], "catalog": "prediction"}, {"name": "get_process_getGoalList", "description": "根据用户输入的控制器名称进行全流程指导数据查询", "params": {"type": "object", "description": "根据用户输入的控制器名称进行全流程指导数据查询", "properties": {"adconName": {"type": "string", "description": "控制器名称、装置名称、设备名称"}, "devicenumber": {"type": "string", "description": "设备编号、装置编号"}}, "required": ["adconName", "devicenumber"]}, "result": {"type": "object", "description": "返回结果为0时，则进入新增控制器全流程指导，返回结果不为0时，则进入获取控制器全流程指导信息", "properties": {"baseProjectId": {"type": "integer", "description": "结果为0时，进入新增控制器全流程指导页面，结果不为0时，进入获取控制器全流程指导信息页面"}, "adconName": {"type": "string", "description": "控制器名称、装置名称、设备名称"}}, "required": ["baseProjectId", "adconName"]}, "alias": ["全流程指导控制器查询"], "catalog": "control"}, {"name": "get_configuration_data", "description": "根据用户输入的控制器名称进行全流程指导数据新增。默认 baseProjectId == 0 进行新增。", "params": {"type": "object", "description": "根据用户输入的控制器名称进行全流程指导数据新增", "properties": {"adconName": {"type": "string", "description": "控制器名称、装置名称、设备名称"}, "devicenumber": {"type": "string", "description": "设备编号、装置编号"}}, "required": ["adconName", "devicenumber"]}, "result": {"type": "object", "description": "返回控制器全流程数据添加结果", "properties": {"baseProjectId": {"type": "integer", "description": "返回装置ID、控制器ID"}}, "required": ["baseProjectId"]}, "alias": ["新增控制器全流程指导"], "catalog": "control"}, {"name": "get_configuration_edit", "description": "根据控制器id进行全流程指导数据编辑", "params": {"type": "object", "description": "根据控制器id进行全流程指导数据编辑", "properties": {"baseProjectId": {"type": "integer", "description": "控制器ID、装置ID"}, "devicenumber": {"type": "string", "description": "设备编号、装置编号"}}, "required": ["baseProjectId", "devicenumber"]}, "result": {"type": "object", "description": "返回控制器全流程数据编辑结果", "properties": {"baseProjectId": {"type": "integer", "description": "返回装置ID、控制器ID"}}, "required": ["baseProjectId"]}, "alias": ["获取控制器全流程指导信息"], "catalog": "control"}, {"name": "get_auto_identification_add", "description": "根据用户设置的模型矩阵，模型名称、输入变量、输出变量、开始时间、结束时间进行系统自动辨识", "params": {"type": "object", "description": "根据用户设置的模型矩阵，模型名称、输入变量、输出变量、开始时间、结束时间进行系统自动辨识", "properties": {"InputNames": {"type": ["array", "null"], "description": "输入位号、输入变量", "items": {"type": "string"}}, "OutputNames": {"type": ["array", "null"], "description": "输出位号、输出变量", "items": {"type": "string"}}, "startTime": {"type": ["string", "null"], "description": "开始时间"}, "endTime": {"type": ["string", "null"], "description": "结束时间"}, "runCycle": {"type": ["integer", "null"], "description": "采样周期"}, "modelStepRespPVsLength": {"type": ["integer", "null"], "description": "模型长度"}, "mvQuantity": {"type": ["integer", "null"], "description": "输入变量个数"}, "pvQuantity": {"type": ["integer", "null"], "description": "输出变量个数"}, "modelName": {"type": ["string", "null"], "description": "模型名称"}, "algorithmCallFlag": {"type": ["integer", "null"], "description": "根据用户输入的辨识方法，采用何种方式进行辨识 1:子空间 2:FIR"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"preProjectId": {"type": "string", "description": "结果信息preProjectId"}}}, "alias": ["辨识模型矩阵"], "catalog": "prediction", "skip_summary": true}, {"name": "get_auto_identification_result", "description": "根据preProjectId对辨识结果数据进行查看", "params": {"type": "object", "description": "根据preProjectId对辨识结果数据进行查看", "properties": {"preProjectId": {"type": "string", "description": "模型数据Id"}, "identEndFlag": {"type": "integer", "description": "辨识结果状态 0辨识中 1辨识成功 2辨识失败"}}}, "result": {}, "alias": ["辨识模型数据"], "catalog": "prediction", "skip_summary": true}, {"name": "get_auto_identification_query", "description": "根据用户输入的模型名称对辨识数据进行查看", "params": {"type": "object", "description": "根据用户输入的模型名称对辨识数据进行查看", "properties": {"modelName": {"type": "string", "description": "模型名称"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"output": {"type": "object", "description": "结果信息"}}}, "alias": ["辨识模型数据列表"], "catalog": "prediction"}, {"name": "get_auto_identification_edit", "description": "根据用户输入的模型名称对数据进行修改、删除、新增", "params": {"type": "object", "description": "根据用户输入的模型名称对数据进行修改、删除、新增", "properties": {"modelName": {"type": "string", "description": "模型名称"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"data": {"type": "object", "description": "结果信息"}}}, "alias": ["辨识模型修改"], "catalog": "prediction"}, {"name": "get_controlling", "description": "根据用户输入的装置名称生成控制方案", "params": {"type": "object", "description": "根据用户输入的装置名称生成控制方案", "properties": {"userinput": {"type": "string", "description": "用户输入信息"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"user_question": {"type": "string", "description": "用户输入信息"}, "process_tech_choice": {"type": "object", "description": "返回信息"}, "controlProblem": {"type": "string", "description": "返回信息"}, "coverage": {"type": "object", "description": "返回信息"}}}, "alias": ["控制方案设计信息确认"], "catalog": "control", "skip_summary": true}, {"name": "get_control_optimization", "description": "控制方案检索生成能力", "params": {"type": "object", "description": "控制方案检索生成能力", "properties": {"user_question": {"type": "string", "description": "用户输入信息"}, "process_tech_choice": {"type": "object", "description": "数据信息"}, "controlProblem": {"type": "string", "description": "数据信息"}, "coverage": {"type": "object", "description": "数据信息"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"Isover": {"type": "boolean", "description": "是否结束流程 false 需要进行下一步操作"}, "control_scheme_result": {"type": "object", "description": "返回数据信息"}, "file_name": {"type": "string", "description": "返回的文件名称"}}}, "alias": ["控制方案检索生成"], "catalog": "control", "skip_summary": true}, {"name": "get_control_scheme_generation", "description": "控制方案优化能力", "params": {"type": "object", "description": "控制方案优化能力", "properties": {"Isover": {"type": "boolean", "description": "是否结束流程 false 需要进行下一步操作"}, "control_scheme_result": {"type": "object", "description": "返回数据信息"}, "file_name": {"type": "string", "description": "返回的文件名称"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"code": {"type": "integer", "description": "结果信息code"}, "message": {"type": "string", "description": "结果信息message"}, "preProjectId": {"type": "string", "description": "结果信息preProjectId"}}}, "alias": ["控制方案优化"], "catalog": "control", "skip_summary": true}]}