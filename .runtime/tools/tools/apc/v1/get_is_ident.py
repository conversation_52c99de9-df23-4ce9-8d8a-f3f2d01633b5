from __runner__ import tool, Context


# 控制方案-控制方案已经生成，确认是否需要进行辨识
# add by liupeng 2025-07-01
@tool(private=True)
async def get_is_index(context: Context, params: any):
    result = await context.get_interaction("get_is_index")
    if result is None:
        context.require_interaction({
            "id": "get_is_index",
            "title": "检测到已生成多变量预测控制器，是否基于当前变量和模型关系对控制器进行模型辨识？",
            "type": "select",
            "mode": "single",
            "select": [
                {
                    "title": "当前生成的控制方案和控制器结构已满足需求，结束任务",
                    "data": 0,
                },
                {
                    "title": "继续进行模型辨识，获得控制器模型矩阵",
                    "data": 1,
                }
            ]
        })
        return {

        }
    else:
        tmp_result = {
            'data': result[0]['data']
        }
        return tmp_result
