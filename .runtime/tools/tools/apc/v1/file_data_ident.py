from __runner__ import tool, Context
import requests
import json
from furl import furl
import time


# add by liupeng 2025-05-14
@tool(private=True)
# 通过位号历史数据进行系统辨识 Edit by liupeng 2025-06-10
async def file_data_ident(context: Context, params):
    APC_URL = context.config["APC_URL"]
    await context.log_info(f"APC_URL:{APC_URL}")
    mv_list = []
    pv_list = []
    identProcessInputVariableList = []
    identProcessOutputVariableList = []
    dataSourceFlag = 0  # 数据源标识 默认0查询数据源 1导入文件数据
    algorithmCallFlag = 0  # 0调用2个算法进行辨识 1子空间算法 2FIR算法
    results = await context.call_tool("tuning_file_upload", params=params)  # 对位号历史数据进行上传
    await context.log_info(f"tuning_file_upload results={results}")
    if results is not None:
        dataSourceFlag = results['dataSourceFlag']
    result = await context.get_interaction("file_data_ident")
    # await context.log_info(f"file_data_ident result={result}")
    if result is None:
        response = requests.post(url=APC_URL + '/tpt/tag/importHistoryValue'
                                 , json={
                'bucket': results['file']["bucket"],
                'object': results['file']["object"],
                'name': results['file']["name"],
                'fileType': 0,
                'mode': 0
            }
                                 , headers={
                # 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Content-Type': 'application/json',
                'Cookie': 'tenant-id=0',
                'Accept-Language': 'zh-CN',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        return_data = json.loads(response.text)
        await context.log_info(f"return_data results={return_data}")
        if return_data['code'] != 200:
            await context.add_view(dict(format="markdown", content=f"""
## 位号历史数据上传失败：
#### 您选择的位号历史数据文件格式数据存在异常，系统无法正常进行解析，错误信息如下：
{return_data['message']}
"""))
            await context.add_view({
                "format": "card",
                "content": {
                    "type": 'markdown',
                    "title": '位号历史数据模板说明',
                    "details": """
## 位号历史数据模板说明
> **请根据文件模板填写数据后上传。请注意以下技术要求**：
> - （1）文件格式为csv；
> - （2）每列数据长度请保持一致,即每列行数相等；
> - （3）第一行第一列用作时间标题,其固定名称为:TIME；
> - （4）第一行第二列开始用作位号名称输入,其他行不能出现空行或空格；
> - （5）第二行第一列为空白,不需要填写数据信息；
> - （6）第二行第二列开始用作位号描述,其位号描述信息可以为空；
> - （7）第三行第一列开始用作时间数据,其时间格式为yyyy/mm/dd hh:mm:ss,不可为空；
> - （8）第三行第二列开始用作位号数据,数据列数以首行数据列数为准,数据为数值型；
> - （9）位号数据需包含操作变量和被控变量；
> - （10）为保证控制模型效果,上传的历史运行数据应具有代表性,最短数据长度建议不少于5000行,且应涵盖典型工况变化过程；
            """
                }
            })
            return response.reason
        # 新增对变量类型进行调整页面，add by liupeng 2025-07-08
        get_variable_modification_result = await  context.get_cache("get_variable_modification_result")
        if get_variable_modification_result is None:
            variable_result = await context.call_tool("get_variable_modification", params=return_data)
            await context.set_cache("get_variable_modification_result", variable_result)
            get_variable_modification_result = variable_result
        await context.log_info(f"get_variable_modification_result result={get_variable_modification_result}")

        # identProcessInputVariableList = return_data['result']['identProcessInputVariableList']
        # identProcessOutputVariableList = return_data['result']['identProcessOutputVariableList']

        identProcessInputVariableList = get_variable_modification_result['mvList']
        identProcessOutputVariableList = get_variable_modification_result['pvList']

        startTime = return_data['result']['identStartTime']
        endTime = return_data['result']['identEndTime']
        mvQuantity = len(identProcessInputVariableList)
        pvQuantity = len(identProcessOutputVariableList)
        modelName = results['model_name']
        id = return_data['result']['id']
        # 输入变量
        for s in identProcessInputVariableList:
            mv_list.append(s['paramName'])
        # 输出变量
        for s in identProcessOutputVariableList:
            pv_list.append(s['paramName'])
        runCycle = results['runCycle']
        modelStepRespPVsLength = results['modelStepRespPVsLength']
        # await context.log_info(f"33333333 result={return_data}")
        # 打开page
        # 创建 URL 对象并修改各部分
        f = furl()
        f.path = '/xpt-tpt-apc/modelMatrix'
        f.args['rowCount'] = mvQuantity  # 输入变量个数 行数
        f.args['colCount'] = pvQuantity  # 输出变量个数 列数
        f.args['modelName'] = modelName  # 模型名称
        f.args['sampleCylc'] = runCycle  # 模型周期
        f.args['modelLen'] = modelStepRespPVsLength  # 模型长度
        f.args['identStartTime'] = startTime  # 开始时间
        f.args['identEndTime'] = endTime  # 结束时间
        f.args['algorithmCallFlag'] = algorithmCallFlag  # 采用那种算法进行辨识 0两种算法 1子空间 2FIR算法
        f.args['dataSourceFlag'] = dataSourceFlag  # 数据源标识 默认0查询数据源 1导入文件数据
        f.args['ID'] = id
    if len(mv_list) > 0:
        f.args['mvList'] = str(mv_list).replace(" ", "")  # 输入变量
    if len(pv_list) > 0:
        f.args['pvList'] = str(pv_list).replace(" ", "")  # 输出变量
        # await context.log_info(f"33333333 result={identProcessInputVariableList}")
        input_ret = generate_batch_pretuning_form(identProcessInputVariableList)
        output_ret = generate_batch_pretuning_form(identProcessOutputVariableList)

        messages = {'key': '模型信息'}
        result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
        await context.add_view({
            "format": "markdown",
            "content": f"""
{result_message}
        """
        })

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '模型信息',
                "description": "包含模型基本信息、输入变量信息、输出变量信息",
                "details": f"""
## 模型信息
|   模型名称   |  输入变量  | 输出变量 |  模型周期  |  模型长度  |              数据时间范围       |
|:--------:|:------:|:------:|:------:|:------:|:---------------------------------------:|
|{modelName} |{mvQuantity}|{pvQuantity}|{runCycle}s|{modelStepRespPVsLength}|{startTime}~{endTime}| 
## 输入变量信息：
|          输入变量          |  变量描述   |
|:----------------------:|:------------:|
{input_ret}
## 输出变量信息：
|          输出变量          |  变量描述   |
|:----------------------:|:------------:|
{output_ret}
"""
            }

        })

        messages = {'key': '模型矩阵确认'}
        result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
        await context.add_view({
            "format": "markdown",
            "content": f"""
{result_message}
        """
        })
        context.require_interaction({
            "id": "file_data_ident",
            "title": "请打开辨识模型矩阵，确认数据信息后点击【下一步】按钮开始辨识",
            "type": "open_page",
            "page_type": "execute",
            "open_page": f.url
        })
        return {}
    else:
        # 循环和sleep轮询 获取辨识过程中产生的过程数据信息
        while True:
            time.sleep(5)  # 5秒钟（休眠）
            Id = result['result']
            response = requests.post(url=APC_URL + '/tpt/ident/getIdentLog'
                                     , json={
                    'id': Id
                }
                                     , headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
                })
            await context.log_info(f"getIdentLog返回数据：{response.text}")
            json_obj = json.loads(response.text)
            # if "modelName" in json_obj['result']:
            #     modelName = json_obj['result']['modelName']
            #
            # if "sampleCylc" in json_obj['result']:
            #     runCycle = json_obj['result']['sampleCylc']
            #
            # if "identStartTime" in json_obj['result']:
            #     startTime = json_obj['result']['identStartTime']
            #
            # if "identEndTime" in json_obj['result']:
            #     endTime = json_obj['result']['identEndTime']
            #
            # if "modelLen" in json_obj['result']:
            #     modelStepRespPVsLength = json_obj['result']['modelLen']

            identEndFlag = json_obj['result']['identEndFlag']
            if identEndFlag == "1" or identEndFlag == "2":  # 0是辨识中 1是辨识成功 2辨识错误 1或者2时代表辨识结束，需要进行下一步操作
                dataSegmentLogs = ''
                if len(json_obj['result']['dataSegmentLogs']) > 0:
                    tmp_dataSegmentLogs = json_obj['result']['dataSegmentLogs']
                    for val in tmp_dataSegmentLogs:
                        dataSegmentLogs += "- " + val + '\r\n'

                dataQualityLogs = ''
                if len(json_obj['result']['dataQualityLogs']) > 0:
                    tmp_dataQualityLogs = json_obj['result']['dataQualityLogs']
                    # for val in tmp_dataQualityLogs:
                    #     dataQualityLogs += "- " + val + '\r\n'
                    dataQualityLogs = generate_dataQualityLogs(tmp_dataQualityLogs)
                errorLog = '辨识成功！'
                if identEndFlag == "2":
                    errorLog = '辨识失败！'
                if "errorLog" in json_obj['result']:
                    errorLog = json_obj['result']['errorLog']

                messages = {'key': '辨识过程已完成'}
                result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
                await context.add_view({
                    "format": "markdown",
                    "content": f"""
{result_message}
        """
                })

                await context.add_view({
                    "format": "card",
                    "content": {
                        "type": 'markdown',
                        "title": '辨识过程信息展示',
                        "description": "过程信息小结(数据清洗、数据质量评价、模型辨识、模型质量评价)",
                        "details": f"""
## 辨识过程信息展示
- 首先，对数据集进行数据清洗，剔除数据中的坏值和异常值；
- 然后，调用自动切片算法，选取数据中激励充分的数据段，得到各个输出变量对应的优质数据段以及各数据段的质量评价结果如下：

|          输出变量       |  数据时间范围      |  质量评价  |
|:----------------------:|:---------------:|:--------:|
{dataQualityLogs}
- 数据质量评价：数据质量分为4个等级，质量高低顺序为：A>B>C>D，默认选择AB数据段进行建模；
- 数据质量评价完毕后，将选取质量等级为优、良的数据段进行建模；
- 辨识数据选取完毕后，将调用FIR辨识算法和子空间辨识算法，得到辨识模型，并输出模型的评价结果；
- 模型质量评价：模型质量分为4个等级，质量高低顺序为：A>B>C>D，根据不确定度指标和拟合度指标进行综合评价，推荐选择AB模型用于控制；
- 最后，根据由辨识得到的模型对全段数据进行仿真预测，输出仿真结果。
## 辨识结果：
{errorLog}
"""
                    }
                })
                break
    return {
        "preProjectId": Id,
        "identEndFlag": identEndFlag
    }


def generate_batch_pretuning_form(identProcessVariableList):
    ret = ""
    desc = ''
    for s in identProcessVariableList:
        ret = ret + f"|{s['paramName']}|{s['desc']}|\r\n"
        # if "paramName" in s:
        #     ret = ret + f"|{s['variableName']}|{s['paramName']}|\r\n"
        # else:
        #     ret = ret + f"|{s['variableName']}|{desc}|\r\n"
    return ret

def generate_dataQualityLogs(dataQualityLogsList):
    ret = ""
    for s in dataQualityLogsList:
        fruits = s.split(';')
        ret = ret + f"|{fruits[0]}|{fruits[1]}|{fruits[2]}|\r\n"
    return ret
