from __runner__ import tool, Context

from furl import furl

# 对控制方案矩阵信息进行更新
# add by liupeng 2025-06-27
@tool(private=True)
async def get_control_controlScheme(context: Context, params: any):
    # TPT_URL = context.config["TPT_URL"]
    # 创建 URL 对象并修改各部分
    fs = furl()
    fs.path = '/xpt-tpt-apc/controlSchemeConfig'
    fs.args['theme'] = 'dark'
    fs.args['name'] = params['matrix_data']['name']  # 名称
    fs.args['cv_num'] = params['matrix_data']['cv_num']  # cv个数
    fs.args['mv_num'] = params['matrix_data']['mv_num']  # mv个数
    fs.args['dv_num'] = params['matrix_data']['dv_num']  # dv个数
    fs.args['cv_list'] = str(params['matrix_data']['cv_list']).replace(" ", "")  #
    fs.args['mv_list'] = str(params['matrix_data']['mv_list']).replace(" ", "")  #
    fs.args['dv_list'] = str(params['matrix_data']['dv_list']).replace(" ", "")  #
    fs.args['matrix'] = str(params['matrix_data']['matrix']).replace(" ", "")  #
    tmp_check_result = await context.get_interaction("multivariable_controllers")
    await context.log_info(f"tmp_check_result111 result={tmp_check_result}")
    if tmp_check_result is None:
        await context.add_view({
            "format": "markdown",
            "content": """
现在您可以通过“编辑多变量预测控制器结构”，在窗口中查看当前控制器结构并进行编辑（包括变量属性修改、模型关系修改），确认提交后，我将据此生成最终控制器结构文件。
                        """
        })
        context.require_interaction({
            "id": "multivariable_controllers",
            "title": "编辑控制器结构",
            "type": "open_page",
            "page_type": "execute",
            "open_page": fs.url
        })
        return {}
    params['matrix_data'] = tmp_check_result
    f = furl()
    f.path = '/xpt-tpt-apc/controlScheme'
    f.args['theme'] = 'dark'
    f.args['name'] = tmp_check_result['name']  # 名称
    f.args['cv_num'] = tmp_check_result['cv_num']  # cv个数
    f.args['mv_num'] = tmp_check_result['mv_num']  # mv个数
    f.args['dv_num'] = tmp_check_result['dv_num']  # dv个数
    f.args['cv_list'] = str(tmp_check_result['cv_list']).replace(" ", "")  #
    f.args['mv_list'] = str(tmp_check_result['mv_list']).replace(" ", "")  #
    f.args['dv_list'] = str(tmp_check_result['dv_list']).replace(" ", "")  #
    f.args['matrix'] = str(tmp_check_result['matrix']).replace(" ", "")  #
    f.args['isview'] = False
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'page',
            "title": '输出调整后控制器结构信息',
            "description": "",
            "details": f.url
        }
    })
    return tmp_check_result
