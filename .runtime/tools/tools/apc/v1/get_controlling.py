import json

from __runner__ import tool, Context


def data_structure(results, tmp_user_question):
    controlProblem = ''  # 装置当前的自动化控制水平是？
    user_question = tmp_user_question
    if "file" in results['result']:
        if results['result']['file'] is not None  and results['result']['file']  != "":
            tmp_json = json.loads(results['result']['file'])
            process_choice = {
                "bucket_name": tmp_json['bucket'],
                "object_name": tmp_json['object']
            }
        else:
            process_choice = {
                "bucket_name": "",
                "object_name": ""
            }
    else:
        process_choice = {
            "bucket_name": "",
            "object_name": ""
        }
    # for item in results['result']['controlProblem']:
    #     if item == '1':
    #         controlProblem = '自控率较低，操作频次较高'
    #     elif item == '2':
    #         controlProblem = '自控率良好，关键工艺指标平稳性存在改善空间'

    if results['result']['controlProblem'] == '1':
        controlProblem = '自控率较低，操作频次较高'
    else:
        controlProblem = '自控率良好，关键工艺指标平稳性存在改善空间'

    # if results['result']['analyzer'] == "1":
    #     analyzer = "是"
    # else:
    #     analyzer = "否"
    #
    # if results['result']['concentration'] == "1":
    #     concentration = "配备水煤浆浓度分析仪"
    # else:
    #     concentration = "未配备水煤浆浓度分析仪"

    coverage_scope = {}
    # if results['result']['coverages']['coverage'] == '1':
    #     coverage_scope = {
    #         "type": "水煤浆气化炉",
    #         "value": results['result']['coverages']['spray_nozzle']
    #     }
    # elif results['result']['coverages']['coverage'] == '2':
    #     coverage_scope = {
    #         "type": "粉煤气化炉",
    #         "value": results['result']['coverages']['spray_nozzles']
    #     }
    # elif results['result']['coverages']['coverage'] == '3':
    #     coverage_scope = {
    #         "type": "其他",
    #         "value": results['result']['coverages']['other']
    #     }
    coverage_scope = {
        "type": results['result']['combinedField']['radioOption'],
        "value": results['result']['combinedField']['textInput']
    }

    # if results['data_Type'] == 1:  # 气化装置
    #     tmp_result = {"user_question": user_question, "process_tech_choice": process_choice,
    #                   "focus_control_issues": controlProblem, "coverage": coverage_scope,
    #                   "concentration": concentration, "analyzer": analyzer
    #                   }
    # else:
    #     tmp_result = {"user_question": user_question, "process_tech_choice": process_choice,
    #                   "controlProblem": controlProblem, "coverage": coverage_scope
    #                   }

    tmp_result = {"user_question": user_question, "process_tech_choice": process_choice,
                  "controlProblem": controlProblem, "coverage": coverage_scope
                  }
    return tmp_result


# 主要实现的功能是 控制方案设计信息确认
# add by liupeng 2025-06-11
@tool(version="*")
async def get_controlling(context: Context, params: any):
    user_question = ''  # 用户输入的问题信息
    if "userinput" in params:
        user_question = params['userinput']
    results = await context.call_tool("get_controlling_form", params=params)  # 控制方案入参数据信息
    tmp_result = data_structure(results, user_question)
    await context.log_info(f"tmp_result result={tmp_result}")
    return tmp_result
