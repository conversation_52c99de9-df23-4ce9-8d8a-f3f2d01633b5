from __runner__ import tool, Context

# 主要实现的功能是构造控制方案参数信息
# add by liupeng 2025-06-11
@tool(private=True)
async def get_variable_data(context: Context, params: any):
    result = await context.get_interaction("get_variable_data")
    # await context.log_info(f"get_variable_data333 result={result}")
    if result is None:
        # id，交互标识，区别同函数中的多个交互
        # mode，选择模式 multiple/single，默认 single
        # data, 与选项同时返回，方便代码处理选项
        context.require_interaction({
            "id": "get_variable_data",
            "title": "现已为您生成装置控制方案和对应控制器结构，若您提供对应变量数据，我将通过数据分析验证并进一步优化现有控制器结构，更新完善先进控制方案。",
            "type": "select",
            "mode": "single",
            "select": [
                {
                    "title": "继续上传数据",
                    "data": 0,
                },
                {
                    "title": "直接生成先进控制方案",
                    "data": 1,
                }
            ]
        })
        return {}
    else:
        tmp_result={
            'data':result[0]['data']
        }
        return tmp_result
