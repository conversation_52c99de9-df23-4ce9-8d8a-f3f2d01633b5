from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# 控制器变量指标历史数据查询

# 控制器变量指标历史数据查询
@tool(version="*")
async def get_watch_history_monitor_var_list(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    controllerName = params['controllerName']  # 用户输入的控制器名称
    ##查询控制器
    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/controller/list', headers={
        'Content-Type': 'application/json',
        'Authorization': APC_AGENT_TOKEN,
        'X-TPT-TOKEN': X_TPT_TOKEN,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    controllerJson = json.loads(response.text)
    controllerDataList = [emp for emp in controllerJson['list'] if
                          emp["controllerName"] == controllerName]  # 根据控制器名称去匹配
    controllerId = 0
    if len(controllerDataList) > 0:
        controllerId = controllerDataList[0]['id']  # 控制器id
    else:
        return format_response(
            success=False,
            message="未查询到该控制器(" + controllerName + ")"
        )
    varParam = {"controllerId": controllerId, "countType": 0}
    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/controller/variable/select'
                            , params=varParam, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(response.text)
    variableInfoList = var_info(json_obj)

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '控制器变量列表',
            "description": "控制器变量列表",
            "details": f"""
{variableInfoList["var_info_table"]}
"""
        }
    })
    return format_response(
        success=True,
        data=variableInfoList["var_info_detail_return"]
    )


## 控制器运行状态总览
## 变量指标信息--cv
def var_info(json_obj):
    dataDto = json_obj.get('data')
    var_info_detail = ''
    var_info_detail_return = []
    if dataDto:
        tempCount = 1
        if 'cvIndicatorDtoList' in dataDto:
            cvIndicatorDtoList = dataDto['cvIndicatorDtoList']
            if cvIndicatorDtoList:
                for index, s in enumerate(cvIndicatorDtoList, start=1):
                    variableType = '被控变量'
                    var_info_detail = var_info_detail + (
                        f"|{index}"
                        f"|{s.get('variableName', '')}"
                        f"|{variableType}\r\n"
                    )
                    tempCount = tempCount + 1
                    cv_var_info_detail_item = {"variableName": s.get('variableName', ''), "variableType": variableType}
                    var_info_detail_return.append(cv_var_info_detail_item)

        if 'mvIndicatorDtoList' in dataDto:
            mvIndicatorDtoList = dataDto['mvIndicatorDtoList']
            for index, s in enumerate(mvIndicatorDtoList, start=tempCount):
                variableType = '操作变量'
                var_info_detail = var_info_detail + (
                    f"|{index}"
                    f"|{s.get('variableName', '')}"
                    f"|{variableType}\r\n"
                )
                mv_var_info_detail_item = {"variableName": s.get('variableName', ''), "variableType": variableType}
                var_info_detail_return.append(mv_var_info_detail_item)

    var_info_table = f"""
## 控制器变量列表
| 序号| 变量名称 | 变量类型 |
|:--:|:--------:|:------:|
{var_info_detail}
"""
    return {"var_info_table": var_info_table, "var_info_detail_return": var_info_detail_return}



def format_response(data=None, success=True, message=""):
    if data is None:
        data = []
    return {
        "success": success,
        "message": message,
        "data": data
    }
