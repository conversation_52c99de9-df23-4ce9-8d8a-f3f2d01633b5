from __runner__ import tool, Context
import requests
import json
from furl import furl


# 对控制方案矩阵信息进行更新
# add by liupeng 2025-06-27
@tool(private=True)
async def get_control_update(context: Context, params: any):
    # TPT_URL = context.config["TPT_URL"]
    get_search_all_result = params['get_search_all_result']
    check_results = params['check_results']
    # await context.log_info(f"gparams22222 result={params}")
    # 创建 URL 对象并修改各部分
    fs = furl()
    fs.path = '/xpt-tpt-apc/controlScheme'
    fs.args['theme'] = 'dark'
    fs.args['name'] = get_search_all_result['name']  # 名称
    fs.args['cv_num'] = get_search_all_result['cv_num']  # cv个数
    fs.args['mv_num'] = get_search_all_result['mv_num']  # mv个数
    fs.args['dv_num'] = get_search_all_result['dv_num']  # dv个数
    fs.args['cv_list'] = str(get_search_all_result['cv_list']).replace(" ", "")  #
    fs.args['mv_list'] = str(get_search_all_result['mv_list']).replace(" ", "")  #
    fs.args['dv_list'] = str(get_search_all_result['dv_list']).replace(" ", "")  #
    fs.args['matrix'] = str(check_results['modelRelationshipOut']).replace(" ", "")  #
    tmp_check_result = await context.get_interaction("multivariable_controller")
    if tmp_check_result is None:
        await context.add_view({
            "format": "markdown",
            "content": """
现在您可以通过“编辑多变量预测控制器结构”，在窗口中查看当前控制器结构并进行编辑（包括变量属性修改、模型关系修改），确认提交后，我将据此生成最终控制器结构文件。
                                    """
        })
        context.require_interaction({
            "id": "multivariable_controller",
            "title": "编辑多变量预测控制器结构",
            "type": "open_page",
            "page_type": "execute",
            "open_page": fs.url
        })
        return {}
    await context.set_cache("suggestion_check_result", tmp_check_result)
    suggestion_check_result = tmp_check_result

    update_matrix = {
        'file_name': params['file_name'],
        'matrix_data': suggestion_check_result
    }
    # 更新控制方案文档
    get_update_matrix_result = await context.get_cache("get_update_matrix_result")
    if get_update_matrix_result is None:
        matrix_result = await context.call_tool("get_update_matrix", params=update_matrix)  # 更新控制方案文档
        f = furl()
        f.path = '/xpt-tpt-apc/controlScheme'
        f.args['theme'] = 'dark'
        f.args['name'] = update_matrix['matrix_data']['name']  # 名称
        f.args['cv_num'] = update_matrix['matrix_data']['cv_num']  # cv个数
        f.args['mv_num'] = update_matrix['matrix_data']['mv_num']  # mv个数
        f.args['dv_num'] = update_matrix['matrix_data']['dv_num']  # dv个数
        f.args['cv_list'] = str(update_matrix['matrix_data']['cv_list']).replace(" ", "")  #
        f.args['mv_list'] = str(update_matrix['matrix_data']['mv_list']).replace(" ", "")  #
        f.args['dv_list'] = str(update_matrix['matrix_data']['dv_list']).replace(" ", "")  #
        f.args['matrix'] = str(update_matrix['matrix_data']['matrix']).replace(" ", "")  #
        f.args['isview'] = False
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'page',
                "title": '更新控制器结构',
                "description": "",
                "details": f.url
            }
        })
        await context.set_cache("get_update_matrix_result", update_matrix)
        get_update_matrix_result = update_matrix

    return get_update_matrix_result
