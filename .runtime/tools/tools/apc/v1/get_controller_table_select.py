from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# 主要实现的功能是 获取APC的性能监控控制器列表
# 首先需要获取全厂ID
# add by liupeng 2025-04-08
@tool(version="*")
async def get_controller_table_select(context: Context, params: any):

    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']

    groupType = 7  # 写死
    current = 1
    pageSize = 99999999
    selectType = 0
    params = {'groupType': groupType}
    response_config = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all'
                                   , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj_config = json.loads(response_config.text)
    sysGroupId = json_obj_config['data']['id']  # 全厂ID
    if sysGroupId > 0:
        # 根据全厂id对控制器数据进行查询
        params_controller = {'current': current, 'pageSize': pageSize,'selectType':selectType,'sysGroupId':sysGroupId}
        response_controller = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/controller/table/select'
                                           , params=params_controller, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN' : X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_controller = json.loads(response_controller.text)
        return json_obj_controller['list']
    else:
        return 'APC的性能监控控制器列表查询失败！'
