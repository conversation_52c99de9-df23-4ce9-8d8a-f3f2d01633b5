from __runner__ import tool, Context
import requests
import json
import os

from pyexpat.errors import messages

#from apc_base_response_utils import APCBaseResponseUtil
from dotenv import load_dotenv



load_dotenv()




# add by liupeng 2025-04-07
# 获取控制器分组数据信息 包含全厂 装置信息
@tool(version="*")
async def get_apc_device_list(context: Context, params: any):

    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']

    if 'deviceName' in params:
        deviceName = params["deviceName"]  # 装置名称(用户输入)
    else:
        deviceName = ""
    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all?groupType=0'  # 该接口是获取所有装置数据
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    #平铺数据并展示数据 markdown
    if not json_obj['data']:
        # return ResponseUtil.format(
        #     success=False,
        #     message = "未找到相关装置"
        # )
        return None
    deviceList = visualize_tree(json_obj, deviceName)
    
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '装置列表',
            "description": "装置列表",
            "details": f"""
{deviceList}
"""
        }
    })
    #平铺数据并进行array化
    deviceJsonList = get_flattened_tree(json_obj, deviceName)
    if deviceJsonList:
        #return {"deviceJsonList":deviceJsonList}
        return format_response(
            data = deviceJsonList
        )
    else:
        return format_response(
            success=False,
            message = "未找到服务要求的装置"
        )
        #return None

# 递归生成树状结构的Markdown表格行
def generate_tree_table(data, indent=0, counter=[1]):
    """递归生成树状结构的Markdown表格行"""
    rows = ''
    # 生成当前节点行
    tempGroupPathName = data.get('groupPathName', '').replace('\\', '/')
    row = f"| {counter[0]} | {data.get('groupName', '')} | {tempGroupPathName} |\r\n"
    rows = rows + row
    counter[0] += 1

    # 处理子节点
    if 'children' in data and data['children']:
        for child in data['children']:
            rows = rows + (generate_tree_table(child, indent + 1, counter))
    return rows


# 平铺树状结构 并以表格形式输出
def visualize_tree(json_data, deviceName):
    """主函数：生成完整的Markdown表格"""
    data = json_data['data']
    if not deviceName:
        table_rows = generate_tree_table(data)
    else:
        table_rows = ''
        tempData = find_id_by_name(data, deviceName)
        if tempData:
            table_rows = f"| 1 | {tempData.get('groupName', '')} | {tempData.get('groupPathName', '')} |\r\n"

    table_info = f"""
## 装置列表
| 序号 | 装置名称 | 装置路径 |
|:----:|:------:|:------:|
{table_rows}
"""
    return table_info


# 归查找JSON结构中匹配target_name的第一个节点，返回其id
def find_id_by_name(data, target_name):
    """
    递归查找JSON结构中匹配target_name的第一个节点，返回其id
    :param data: 要搜索的字典/列表
    :param target_name: 要查找的name值
    :return: 匹配的id，未找到返回None
    """
    if isinstance(data, dict):
        if data.get('name') == target_name:
            return data
        if 'children' in data:
            return find_id_by_name(data['children'], target_name)
    elif isinstance(data, list):
        for item in data:
            result = find_id_by_name(item, target_name)
            if result is not None:
                return result
    return None



##平铺数据并返回list
def flatten_tree(data):
    """递归平铺树状结构，返回包含id和groupName的数组"""
    result = []

    # 添加当前节点
    result.append({
        'id': data.get('id'),
        'deviceName': data.get('groupName')
    })

    # 处理子节点
    if 'children' in data and data['children']:
        for child in data['children']:
            result.extend(flatten_tree(child))

    return result

##平铺数据并返回list
def get_flattened_tree(json_data, deviceName=None):
    """主函数：获取平铺后的树结构"""
    data = json_data['data']
    if not deviceName:
        return flatten_tree(data)
    else:
        tempData = find_id_by_name(data, deviceName)
        return [{
            'id': tempData.get('id'),
            'deviceName': tempData.get('groupName')
        }] if tempData else []


def format_response(data=None, success=True, message=""):
    if data is None:
        data = []
    return {
        "success": success,
        "message": message,
        "data": data
    }