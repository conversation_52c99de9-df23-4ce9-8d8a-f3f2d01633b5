from __runner__ import tool, Context
from furl import furl


# 主要实现的功能是 控制方案检索生成
# add by liupeng 2025-07-03
@tool(version="*")
async def get_control_optimization(context: Context, params: any):
    # 方案库及公开资料检索
    is_over = False  # 是否结束流程
    data_result = {}
    get_controlling_form_result = params
    # await context.log_info(f"sssssss result={get_controlling_form_result}")
    get_search_all_result = await context.get_cache("get_search_all_result")
    if get_search_all_result is None:
        search_result = await context.call_tool("get_search_all", params=get_controlling_form_result)  # 控制方案入参数据信息
        if search_result is not None:
            # await context.log_info(f"search_result result={search_result}")
            file_name = search_result['file_name']
            f = furl()
            f.path = '/xpt-tpt-apc/controlScheme'
            f.args['theme'] = 'dark'
            f.args['name'] = search_result['matrix_data']['name']  # 名称
            f.args['cv_num'] = search_result['matrix_data']['cv_num']  # cv个数
            f.args['mv_num'] = search_result['matrix_data']['mv_num']  # mv个数
            f.args['dv_num'] = search_result['matrix_data']['dv_num']  # dv个数
            f.args['cv_list'] = str(search_result['matrix_data']['cv_list']).replace(" ", "")  #
            f.args['mv_list'] = str(search_result['matrix_data']['mv_list']).replace(" ", "")  #
            f.args['dv_list'] = str(search_result['matrix_data']['dv_list']).replace(" ", "")  #
            f.args['matrix'] = str(search_result['matrix_data']['matrix']).replace(" ", "")  #
            f.args['isview'] = False
            await context.add_view({
                "format": "card",
                "content": {
                    "type": 'page',
                    "title": '输出控制器结构',
                    "description": "",
                    "details": f.url
                }
            })
        await context.set_cache("get_search_all_result", search_result)
        get_search_all_result = search_result

    # 用户是否需要对控制方案结构进行调整功能
    get_control_adjustment_result = await context.get_cache("get_control_adjustment_result")
    if get_control_adjustment_result is None:
        adjustment_result = await context.call_tool("get_control_adjustment", params=params)  # 获取数据信息
        await context.set_cache("get_control_adjustment_result", adjustment_result)
        get_control_adjustment_result = adjustment_result

    if get_control_adjustment_result['data'] == 1:  # 调整控制器结构
        get_control_scheme_result = await context.get_cache("get_control_scheme_result")
        if get_control_scheme_result is None:
            control_scheme_result = await context.call_tool("get_control_controlScheme",
                                                            params=get_search_all_result)  # 获取数据信息
            await context.set_cache("get_control_scheme_result", control_scheme_result)
            get_control_scheme_result = control_scheme_result
            # await context.log_info(f"get_control_scheme_result1 result={get_control_scheme_result}")
        data_result = {
            'Isover': is_over,
            'control_scheme_result': get_control_scheme_result,
            'file_name': get_search_all_result['file_name']
        }
    else:  # 控制器结构满足控制需求，下一步
        await  context.log_info(f"get_search_all_result result={get_search_all_result}")
        data_result = {
            'Isover': is_over,
            'control_scheme_result': get_search_all_result['matrix_data'],
            'file_name': get_search_all_result['file_name']
        }
    # await context.log_info(f"data_result result={data_result}")
    return data_result
