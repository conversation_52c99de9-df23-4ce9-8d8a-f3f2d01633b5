from __runner__ import tool, Context
import requests
import json
import os
import time
from dotenv import load_dotenv

load_dotenv()

# 根据控制器生成自定义报表
# 首先需要获取控制器信息，需要得到该控制器下有多少个CV和MV
@tool(version="*")
async def get_customer_report_list(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']

    indexType = 1  # 报表指标类型 1 CV标准差下降率 2 MV投运率 3 CV投运率 4 MV有效投运率 5 CV有效投运率 6自控率 7操作频次
    afterserviceEndTime = params["afterserviceEndTime"]  # APC投运后时间段 结束
    afterserviceStartTime = params["afterserviceStartTime"]  # APC投运后时间段 开始
    preserviceEndTime = params["preserviceEndTime"]  # APC投运前时间段 结束
    preserviceStartTime = params["preserviceStartTime"]  # APC投运前时间段 开始
    if 'indexType' in params:
        indexType = params["indexType"]
    controllerNames = params["controllerNames"]
    groupType = get_need_group_type(indexType)
    groupParams = {'groupType': groupType}
    # 获取全厂ID
    response_config = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all'
                                   , params=groupParams, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    ##根据指标类型获取到分组信息
    group_rep = json.loads(response_config.text)
    if group_rep['code']!=100000000:
        return format_response(
            success=False,
            message=group_rep['message']
        )
    # 解析分组，获取可用参数
    paramList = find_children_by_names(group_rep["data"],controllerNames)
    if not paramList:
        return format_response(
            success=False,
            message="未找到符合条件的自定义参数"
        )

    ##构建请求体重要参数
    customReportIndexDTOList = get_create_report_param_list(indexType,paramList)
    customReportMetricList = []
    reportName = time.strftime("%Y%m%d%H%M%S", time.localtime())  # 自定义报表名称，采用用户输入的名称加上当前时间到秒
    afterserviceEndTime = afterserviceEndTime  # APC投运后时间段 结束
    afterserviceStartTime = afterserviceStartTime  # APC投运后时间段 开始
    preserviceEndTime = preserviceEndTime  # APC投运前时间段 结束
    preserviceStartTime = preserviceStartTime  # APC投运前时间段 开始
    responsew = requests.post(
        url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/customerreport/add'
        , json={
            'reportName': reportName,
            'afterserviceEndTime': afterserviceEndTime,
            'afterserviceStartTime': afterserviceStartTime,
            'preserviceEndTime': preserviceEndTime,
            'preserviceStartTime': preserviceStartTime,
            'customReportMetricList': customReportMetricList,
            'customReportIndexDTOList': customReportIndexDTOList
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    # print(responsew.text)
    #CREATING
    json_objs = json.loads(responsew.text)
    if json_objs['code'] == 100000000:
        tempInfo = check_report_status(context,reportName,120)
        #message = reportName + tempInfo["message"]
        reportDetail = select_report_detail(context,tempInfo["reportId"])

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '自定义报表',
                "description": '自定义报表',
                "details": f"""
{reportDetail["tempView"]}
"""
            }
        })
        return format_response(
            success=True,
            message=reportDetail["tempReturn"]
        )
    else:
        return format_response(
            success=False,
            message=json_objs['message']
        )


# 状态码映射字典
#{1:CV标准差下降率,2:MV投运率,3:CV投运率,4:MV有效投运率,5:CV有效投运率,6:自控率,7:操作频次}
INDEX_AND_GROUP_MAPPING = {
    1: 5,
    2: 3,
    3: 5,
    4: 3,
    5: 5,
    6: 4,
    7: 4
}
# 根据输入的指标来确定查询分组信息的类型
def get_need_group_type (indexType):
    return INDEX_AND_GROUP_MAPPING.get(indexType, 5)



## 循环看报表是否添加成功
def check_report_status(context: Context,report_name, max_wait=120):

    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']

    """
    轮询检查报告状态
    :param report_name: 报告ID
    :param max_wait: 最大等待时间(秒)
    """
    start_time = time.time()
    params = {'current': 1, "pageSize": 20, "selectType": 1, "condition": report_name}
    while True:
        # 发送请求获取报告状态
        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/customerreport/list'
                                , params=params, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN' : X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_objt = json.loads(response.text)
        tempList = json_objt['list']
        if not tempList:
            #没有此报告 报告被删除
            return {"message":'报告被删除',"reportId":None}
        reportStatus = tempList[0]['reportStatus']
        reportId = tempList[0]['id']
        # 检查状态
        if reportStatus != "CREATING":
            if reportStatus == 'FAILURE':
                return {"message":'保存成功,状态:生成失败',"reportId":reportId}
            if reportStatus == 'COMPLETE':
                return {"message":'生成成功',"reportId":reportId}
            return {"message":'生成失败',"reportId":reportId}
        # 检查是否超时
        if time.time() - start_time > max_wait:
            # print("等待超时，停止轮询")
            return {"message":'等待超时',"reportId":None}
        # 等待1秒
        time.sleep(1)

def select_report_detail(context: Context,reportId):

    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']

    if reportId:
        params = {"id":reportId,"ratesDetail":False}
        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/customerreport/select/detail'
                                , params=params, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN' : X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj = json.loads(response.text)
        ##装置APC投运前后标准差对比情况
        limitRates = []
        ## 装置MV投运率
        mvsRates = []
        ## 装置CV投运率
        cvsRates = []
        ## 装置MV有效投运率
        usableMVs = []
        ## 装置CV有效投运率
        usableCVs = []
        ## 装置操作频次
        operateFrequencies = []
        ## 装置自控率
        closeRates=[]
        ## 装置自定义指标
        metricDataList = []
        if json_obj['code'] == 100000000:
            jsonData = json_obj["data"]
            if jsonData:
                ##装置APC投运前后标准差对比情况
                if "limitRates" in jsonData:
                    limitRates = jsonData["limitRates"]
                ## 装置MV投运率
                if "mvsRates" in jsonData:
                    mvsRates = jsonData["mvs"]
                ## 装置CV投运率
                if "cvs" in jsonData:
                    cvsRates = jsonData["cvs"]
                ## 装置MV有效投运率
                if "usableMVs" in jsonData:
                    usableMVs = jsonData["usableMVs"]
                ## 装置CV有效投运率
                if "usableCVs" in jsonData:
                    usableCVs = jsonData["usableCVs"]
                ## 装置操作频次
                if "operateFrequencies" in jsonData:
                    operateFrequencies = jsonData["operateFrequencies"]
                ## 装置自控率
                if "closeRates" in jsonData:
                    closeRates = jsonData["closeRates"]
                ## 装置自定义指标
                if "metricDataList" in jsonData:
                    metricDataList = jsonData["metricDataList"]
        #格式化输出
        limitRatesDetail = device_limit_rates(limitRates)
        mvRatesDetail = rates_detail(mvsRates,'装置MV投运率')
        cvRatesDetail = rates_detail(cvsRates,'装置CV投运率')
        usableMVsDetail = usable_rates_detail(usableMVs,"装置MV有效投运率")
        usableCVsDetail = usable_rates_detail(usableCVs,"装置CV有效投运率")
        operateFrequenciesDetail = operate_frequencies(operateFrequencies)
        closeRatesDetail = close_rates(closeRates)
        metricDataDetail = metric_data_detail(metricDataList)
        #返回字段
        tempView = (limitRatesDetail["limitRatesDetailTable"] + mvRatesDetail["mvRatesDetailTable"]
                    + cvRatesDetail["mvRatesDetailTable"] + usableMVsDetail["mvRatesDetailTable"]
                    + usableCVsDetail["mvRatesDetailTable"] + operateFrequenciesDetail["operateFrequenciesDetailTable"]
                    + closeRatesDetail["closeRatesDetailTable"] + metricDataDetail["metricDataDetailTable"])


        tempReturn = {"limitRateList":limitRatesDetail["limitRatesDetailReturn"],
                      "mvRateList":mvRatesDetail["mvRatesDetailReturn"],
                      "cvRateList":cvRatesDetail["mvRatesDetailReturn"],
                      "usableMVsList":usableMVsDetail["mvRatesDetailReturn"],
                      "usableCVsList":usableCVsDetail["mvRatesDetailReturn"],
                      "operateFrequenciesList":operateFrequenciesDetail["operateFrequenciesReturn"],
                      "closeRateList":closeRatesDetail["closeRatesReturn"],
                      "metricDataList":metricDataDetail["metricDataDetailReturn"],}
        return {"tempView":tempView,"tempReturn":tempReturn}

    else:
        return {"tempView":None,"tempReturn":None}


## 装置APC投运前后标准差对比情况
def device_limit_rates(limitRates):
    limitRatesDetail = ''
    limitRatesDetailReturn = []
    if limitRates:
        for tempData in limitRates:
            importDTOList = tempData["importDTOList"]
            controllerName = tempData["controllerName"]
            if importDTOList:
                for index, s in enumerate(importDTOList, start=1):
                    limitRatesDetail = limitRatesDetail + (
                        f"|{index}"
                        f"|{s.get('name', '')}"
                        f"|{s.get('preserviceLimitDiff', '')}"
                        f"|{s.get('afterserviceLimitDiff', '')}"
                        f"|{s.get('limitDownRate', '')}"
                        f"|{controllerName}\r\n"
                    )
                    limitRatesDetailReturnItem = {"variableName": s.get('name', ''),
                                                  "preserviceLimitDiff": s.get('preserviceLimitDiff', ''),
                                                  "afterserviceLimitDiff": s.get('afterserviceLimitDiff', ''),
                                                  "limitDownRate": s.get('limitDownRate', ''),
                                                  "controllerName": controllerName}
                    limitRatesDetailReturn.append(limitRatesDetailReturnItem)

    limitRatesDetailTable = f"""
## 装置APC投运前后标准差对比情况    
| 序号| 变量名称 | 投运前标准差 | 投运后标准差 | 标准差下下降率 | 所属控制器 |
|:--:|:--------|:----------:|:---------:|:------------:|:--------:|
{limitRatesDetail}
"""
    return {"limitRatesDetailTable": limitRatesDetailTable, "limitRatesDetailReturn": limitRatesDetailReturn}


## 装置MV投运率,装置CV投运率
def rates_detail(mvsRates,title):
    mvRatesDetail = ''
    mvRatesDetailReturn = []
    if mvsRates:
        for tempData in mvsRates:
            importDTOList = tempData["importDTOList"]
            controllerName = tempData["controllerName"]
            if importDTOList:
                for index, s in enumerate(importDTOList, start=1):
                    mvRatesDetail = mvRatesDetail + (
                        f"|{index}"
                        f"|{s.get('name', '')}"
                        f"|{s.get('operateRate', '')}"
                        f"|{controllerName}\r\n"
                    )
                    mvRatesDetailReturnItem = {"variableName": s.get('name', ''),
                                                  "operateRate": s.get('operateRate', ''),
                                                  "controllerName": controllerName}
                    mvRatesDetailReturn.append(mvRatesDetailReturnItem)

    mvRatesDetailTable = f"""
## {title}    
| 序号| 变量名称 | 投运率 | 所属控制器 |
|:--:|:--------|:----:|:---------:|
{mvRatesDetail}
"""
    return {"mvRatesDetailTable": mvRatesDetailTable, "mvRatesDetailReturn": mvRatesDetailReturn}

## 装置MV有效投运率,装置CV有效投运率
def usable_rates_detail(mvsRates,title):
    mvRatesDetail = ''
    mvRatesDetailReturn = []
    if mvsRates:
        for tempData in mvsRates:
            importDTOList = tempData["importDTOList"]
            controllerName = tempData["controllerName"]
            if importDTOList:
                for index, s in enumerate(importDTOList, start=1):
                    mvRatesDetail = mvRatesDetail + (
                        f"|{index}"
                        f"|{s.get('name', '')}"
                        f"|{s.get('usableOperateRate', '')}"
                        f"|{controllerName}\r\n"
                    )
                    mvRatesDetailReturnItem = {"variableName": s.get('name', ''),
                                               "usableOperateRate": s.get('usableOperateRate', ''),
                                               "controllerName": controllerName}
                    mvRatesDetailReturn.append(mvRatesDetailReturnItem)

    mvRatesDetailTable = f"""
## {title}    
| 序号| 变量名称 | 有效投运率 | 所属控制器 |
|:--:|:--------|:--------:|:---------:|
{mvRatesDetail}
"""
    return {"mvRatesDetailTable": mvRatesDetailTable, "mvRatesDetailReturn": mvRatesDetailReturn}



## 装置操作频次
def operate_frequencies(operateFrequencies):
    operateFrequenciesDetail = ''
    operateFrequenciesReturn = []
    if operateFrequencies:
        for index, s in enumerate(operateFrequencies, start=1):
            operateFrequenciesDetail = operateFrequenciesDetail + (
                f"|{index}"
                f"|{s.get('name', '')}"
                f"|{s.get('operateFrequencyBefore', '')}"
                f"|{s.get('operateFrequencyAfter', '')}\r\n"
            )
            operateFrequenciesReturnItem = {"name": s.get('name', ''),
                                            "operateFrequencyBefore": s.get('operateFrequencyBefore', ''),
                                            "operateFrequencyAfter": s.get('operateFrequencyAfter', ''),}
            operateFrequenciesReturn.append(operateFrequenciesReturnItem)


    operateFrequenciesDetailTable = f"""
## 装置操作频次   
| 序号| 回路名称 | APC投运前操作频次 | APC投运后操作频次 |
|:--:|:--------|:--------------:|:---------------:|
{operateFrequenciesDetail}
"""
    return {"operateFrequenciesDetailTable": operateFrequenciesDetailTable, "operateFrequenciesReturn": operateFrequenciesReturn}


## 装置自控率
def close_rates(closeRates):
    closeRatesDetail = ''
    closeRatesReturn = []
    if closeRates:
        for index, s in enumerate(closeRates, start=1):
            closeRatesDetail = closeRatesDetail + (
                f"|{index}"
                f"|{s.get('name', '')}"
                f"|{s.get('closeRateBefore', '')}"
                f"|{s.get('closeRateAfter', '')}\r\n"
            )
            closeRatesReturnItem = {"name": s.get('name', ''),
                                            "closeRateBefore": s.get('closeRateBefore', ''),
                                            "closeRateAfter": s.get('closeRateAfter', ''),}
            closeRatesReturn.append(closeRatesReturnItem)


    closeRatesDetailTable = f"""
## 装置自控率   
| 序号| 装置名称 | APC投运前自控率(%) | APC投运后自控率(%) |
|:--:|:--------|:----------------:|:----------------:|
{closeRatesDetail}
"""
    return {"closeRatesDetailTable": closeRatesDetailTable, "closeRatesReturn": closeRatesReturn}


## 装置操自定义指标
def metric_data_detail(metricDataList):
    metricDataDetail = ''
    metricDataDetailReturn = []
    if metricDataList:
        for index, s in enumerate(metricDataList, start=1):
            metricDataDetail = metricDataDetail + (
                f"|{index}"
                f"|{s.get('metricName', '')}"
                f"|{s.get('preMetricValue', '')}"
                f"|{s.get('afterMetricValue', '')}\r\n"
            )
            metricDataDetailReturnItem = {"name": s.get('metricName', ''),
                                            "preMetricValue": s.get('preMetricValue', ''),
                                            "afterMetricValue": s.get('afterMetricValue', ''),}
            metricDataDetailReturn.append(metricDataDetailReturnItem)


    metricDataDetailTable = f"""
## 装置操自定义指标   
| 序号| 指标名称 | APC投运前计算值 | APC投运后计算值 |
|:--:|:--------|:------------:|:-------------:|
{metricDataDetail}
"""
    return {"metricDataDetailTable": metricDataDetailTable, "metricDataDetailReturn": metricDataDetailReturn}





#根据返回的分组信息获取根据业务需求查找合适的param
def find_children_by_names(data, target_names):
    """
    递归查找JSON结构中匹配target_names且controllerFlag为True的节点，返回其children列表
    :param data: 要搜索的字典/列表
    :param target_names: 要查找的name值集合(set)
    :return: 所有匹配节点的children列表合并结果
    """
    result = []
    if isinstance(data, dict):
        if data.get('name') in target_names and data.get('controllerFlag') is True:
            if 'children' in data:
                result.extend(data['children'])
        if 'children' in data:
            result.extend(find_children_by_names(data['children'], target_names))

    elif isinstance(data, list):
        for item in data:
            result.extend(find_children_by_names(item, target_names))

    return result


##构建 新增自定义报表customReportIndexDTOList字段
def get_create_report_param_list(indexType,paramList):
    customReportIndexDTOList = []
    for tempParam in paramList:

        dto = {
            'paramId': tempParam['id'],
            'indexType': indexType,
            'isCompared': 1
        }
        if indexType == 6 or indexType ==7:
            dto["controllerId"] = tempParam['parent_id']
        else:
            dto["sysGroupId"] = tempParam['parent_id']

        customReportIndexDTOList.append(dto)
    return customReportIndexDTOList



def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }