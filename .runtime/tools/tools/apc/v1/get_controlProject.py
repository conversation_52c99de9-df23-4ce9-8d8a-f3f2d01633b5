import sys

sys.path.append("../../")
from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

# add by liupeng 2025-04-01
# 根据应用工程名称对应用工程进行加载、运行、停止、卸载、重新加载操作
#
load_dotenv()

@tool(version="*")
async def get_controlProject(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']

    projectName = params["projectName"]  # 用户输入控制器名称
    controlType = params["controlType"]  # 操作类型 1加载 2运行 3暂停 4重新加载 5卸载 6删除
    groupType = 8  # 写死
    params = {'groupType': groupType}
    # 获取全厂ID
    response_config = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all'
                                   , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    if response_config.status_code != 200:
        return response_config.reason
    json_obj_config = json.loads(response_config.text)
    if json_obj_config['code']!=100000000:
       return  json_obj_config['message']
    # 解析数据 得到全厂id
    groupId = json_obj_config['data']['id']
    params_config = {'groupId': groupId}
    # 根据全厂ID对控制器信息进行查询
    response_projecttree = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/project/data/projecttree'
                                        , params=params_config, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj_projecttree = json.loads(response_projecttree.text)
    dataList = [emp for emp in json_obj_projecttree['data']['loadedProjectList'] if
                emp["projectName"] == projectName]  # 根据工程名称进行筛选（已加载数据）
    if len(dataList) > 0:
        projectId = dataList[0]['projectId']
        params = {'projectId': projectId, 'controlType': controlType}
        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/project/data/control'
                                , params=params, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN' : X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        #print(response.text)
        json_obj = json.loads(response.text)
        data = json_obj['data']
        return format_response(
            success=True,
            data=data
        )
    else:
        dataList = [emp for emp in json_obj_projecttree['data']['unLoadedProjectList'] if
                    emp["projectName"] == projectName]  # 根据工程名称进行筛选(未加载数据)
        if len(dataList) > 0:
            projectId = dataList[0]['projectId']
            params = {'projectId': projectId, 'controlType': controlType}
            response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/project/data/control'
                                    , params=params, headers={
                    'Content-Type': 'application/json',
                    'Authorization': APC_AGENT_TOKEN,
                    'X-TPT-TOKEN' : X_TPT_TOKEN,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
                })
            #print(response.text)
            json_obj = json.loads(response.text)
            data = json_obj['data']
            return format_response(
                success=True,
                data=data
            )
        else:
            #return {"success":True,'message': projectName+"'无法进行相关操作！'", 'data': False}
            return format_response(
                success=False,
                message=projectName+"无法进行相关操作",
                data=False
            )


def format_response(data=None, success=True, message=""):
    if data is None:
        data = []
    return {
        "success": success,
        "message": message,
        "data": data
    }

