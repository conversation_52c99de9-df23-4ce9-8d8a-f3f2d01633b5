from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# 主要实现的功能是:获取APC的装置列表数据信息
# add by liupeng 2025-04-08
@tool(version="*")
async def get_config_node(context: Context, params: any):
    groupType = 7  # 写死
    params = {'groupType': groupType}
    response = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/config/node/all'
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj['data']['children']

