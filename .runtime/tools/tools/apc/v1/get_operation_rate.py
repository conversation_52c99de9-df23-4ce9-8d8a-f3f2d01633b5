from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# add by liupeng 2025-04-07
# 0、主要实现的功能是 切除XXX装置投运率低于10%的控制器
# 1、要获取所有的装置信息
# 2、根据用户输入的装置名称去匹配，得到装置ID
# 3、根据装置ID获取该装置下的所有控制器数据信息
@tool(version="*")
async def get_operation_rate(context: Context, params: any):
    sysGroupId = 0  # 获取装置ID
    sysGroupId = await context.call_tool("get_controller_id", params=params)  # 根据用户输入的装置名称对装置ID进行获取
    if sysGroupId > 0:
        countType = 0  # 0实时 1班 2日 3周 4月
        params = {'countType': countType, 'sysGroupId': sysGroupId}
        # 该接口是获取所有该装置下的控制器信息
        controllerResponse = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/monitor/device/controller/select'
                                          , params=params, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_inner_token,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_controller = json.loads(controllerResponse.text)
        if len(json_obj_controller['list']) > 0:
            for item in json_obj_controller['list']:
                controllerOperationalRate = item['controllerOperationalRate'].replace("%", "")
        return json_obj_controller['list']
    else:
        return params["DeviceName"] + '装置不存在！'
