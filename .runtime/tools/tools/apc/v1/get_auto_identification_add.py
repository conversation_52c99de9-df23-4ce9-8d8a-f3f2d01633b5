from __runner__ import tool, Context
import os

# 主要实现的功能是 系统自动辨识
# add by liupeng 2025-05-14
@tool(version="*")
async def get_auto_identification_add(context: Context, params: any):
    mvQuantity = 0
    pvQuantity = 0
    await context.log_info(f"系统辨识1 result={params}")
    if "mvQuantity" in params and params['mvQuantity'] is not None:
        mvQuantity = params['mvQuantity']
    if "pvQuantity" in params and params['pvQuantity'] is not None:
        pvQuantity = params['pvQuantity']
    if "InputNames" in params and params['InputNames'] is not None:
        mvQuantity = len(params['InputNames'])
    if "OutputNames" in params and params['OutputNames'] is not None:
        pvQuantity = len(params['OutputNames'])
    if mvQuantity > 0 and pvQuantity > 0:
        results = await context.call_tool("data_source_ident", params=params)  # 根据数据源的位号数据进行系统辨识
    else:
        results = await context.call_tool("file_data_ident", params=params)  # 根据用户上传的位号历史数据进行系统辨识
    return results
