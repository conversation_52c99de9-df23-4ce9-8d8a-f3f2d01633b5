from __runner__ import tool, Context
import requests
import json


# import os
# from dotenv import load_dotenv
#
# load_dotenv()
#
# APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为APC_Inner_token的值
# APC_URL = os.getenv("APC_URL")  # 读取网址信息


# 主要实现的功能是 调用大语言实现搜索功能
# add by liupeng 2025-06-17
@tool(private=True)
async def get_search_all(context: Context, params: any):
    LLM_URL = context.config["LLM_URL"]
    await context.log_info(f"给到大语言的数据信息：{params}")
    titles = ''
    datas = ''
    types = 'markdown'
    matrix_data = ''
    file_name = ''
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '思考过程',
            "description": "规划控制方案文档",
            "details": """
#### 我将规划生成该装置/设备控制方案的相关步骤。
我将基于用户补充的装置信息，使用搜索工具来研究用户装置的历史控制方案、装置工艺特性、装置控制难点与需求；进一步我将基于检索到的相关内容，生成可能的控制器结构与先进控制方案；如果用户能够提供控制器相关的变量数据，我将进一步通过数据验证并优化现有控制器结构，更新完善控制方案；如果用户需要控制器的辨识模型，我将基于优化的控制器结构与用户上传的数据，辨识模型，提供APC控制器所需模型文件。待办清单：
一. **控制方案检索与生成**
检索目标装置/设备的工艺特性、控制难点、公开的历史控制方案。
明确控制器变量范围，生成初步的APC控制器模型结构。
基于检索结果和分析，撰写先进控制方案初稿。
二. **控制方案优化与调整**
（如果有数据） 结合补充的控制器变量数据，进行特征数据分析，提出控制方案的调整优化建议。
结合用户对控制器结构的反馈或修改意见，进一步更新和完善先进控制方案。
三. **辨识模型生成**
（如果需要且数据可用） 基于最终确定的控制器结构和用户提供的变量数据，自动辨识控制器模型关系，生成并提供APC控制器所需的模型文件。
构思的先进控制方案大纲如下：
1. 前言
1.1. 编制目的
1.2. 术语
1.3. 方案设计依据
2. 项目概述
2.1. 项目背景
2.2. 装置现状
3. 项目建设范围与目标
4. 先进控制系统设计
4.1. 技术路线及关键技术
4.2. 总体功能架构
4.3. 先进控制系统各子系统控制器
5. 安全策略
6. 实施方案
7. 运行环境与要求
8. 方案小结
"""
        }
    })
    response = requests.post(url=LLM_URL + '/api/RAGAndWebsearch'
                             , json=params
                             , headers={
            'Content-Type': 'application/json',
            'Cookie': 'tenant-id=0',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
        },
                             stream=True)
    for line in response.iter_lines():
        if line:
            if line:
                try:
                    line = line.decode("utf-8")
                except Exception as e:
                    # print(f"解析{e}-{line}报错")
                    # await context.log_info(f"解析报错 result={e}")
                    types = 'error'
                    continue
                line = line[6:]
                if line:
                    try:
                        line = json.loads(line)
                        titles = line['title']
                        datas = line['data']
                        if line['type'] == "navigate":
                            types = 'search'
                        elif line['type'] == "web_search":
                            types = 'webresult'
                        elif line['type'] == "append_text":
                            types = 'append'
                        elif line['type'] == "file":
                            types = 'file'
                            file_name = datas['object']  # 只读取文件路径和名称
                        elif line['type'] == "matrix":
                            matrix_data = datas
                            types = 'matrix'
                        else:
                            types = 'markdown'

                        if types == 'append':
                            await context.append_view({
                                "details": datas
                            })
                        elif types == 'file':
                            await context.add_view({
                                "format": "card",
                                "content": {
                                    "type": 'file',
                                    "title": '输出控制方案文档',
                                    "details": datas,
                                    "description": "控制方案文档"
                                }
                            })
                        elif types != 'matrix' or types != 'error':
                            await context.add_view({
                                "format": "card",
                                "content": {
                                    "type": types,
                                    "title": titles,
                                    "description": "",
                                    "details": datas
                                }
                            })
                    except Exception as e:
                        # await context.log_info(f"解析{e}-{line}报错")
                        types = 'error'
                        continue
    return {
        'file_name': file_name,
        'matrix_data': json.loads(matrix_data)
    }
