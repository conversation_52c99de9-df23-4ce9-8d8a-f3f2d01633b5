from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

#递归寻找第一个名称
@tool(private=True)
def find_id_by_name(data, target_name):
    """
    递归查找JSON结构中匹配target_name的第一个节点，返回其id
    :param data: 要搜索的字典/列表
    :param target_name: 要查找的name值
    :return: 匹配的id，未找到返回None
    """
    if isinstance(data, dict):
        if data.get('name') == target_name:
            return data.get('id')
        if 'children' in data:
            return find_id_by_name(data['children'], target_name)
    elif isinstance(data, list):
        for item in data:
            result = find_id_by_name(item, target_name)
            if result is not None:
                return result
    return None

# add by liupeng 2025-04-07
# 获取控制器分组数据信息 包含全厂 装置信息
@tool(private=True)
async def get_device_id_by_name(context:Context,params: any):

    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']

    if 'deviceName' in params:
        deviceName = params["deviceName"]  # 装置名称(用户输入)
    else:
        deviceName = ""
    groupType = 0
    if 'customGroupType' in params:
        groupType = params["customGroupType"]
    tempParam = {"groupType": groupType}
    response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all'  # 该接口是获取所有装置数据
                            , params=tempParam, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN' : X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if len(deviceName) > 0:
        temp_id = find_id_by_name(json_obj['data'],deviceName)
        if temp_id is not None:
            return temp_id
        else:
            return -1
    else:
        return -1
