from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# 主要实现的功能是读取所有Adcon组态控制器数据信息
# add by liupeng 2025-04-14
@tool(version="*")
async def get_simulation_trend_charts(context: Context, params: any):
    ControllerName = params['ControllerName']
    dataList = await context.call_tool("get_configuration", params=params)
    if len(dataList) > 0:
        Tmp_data_List = [emp for emp in dataList if emp["adconName"] == ControllerName]  # 根据adcon控制器名称进行筛选
        if len(Tmp_data_List) > 0:
            baseProjectId = Tmp_data_List[0]['id']
        else:
            return ControllerName + ' 仿真数据查询失败！'
    else:
        return ControllerName + ' 仿真数据查询失败！'
    # 根据控制器ID对仿真趋势图数据进行查询
    params = {'baseProjectId': baseProjectId, 'filterRole': 1}
    response = requests.get(url=APC_URL + '/inter-api/apc-project/v1/simulation/controller/params/list'
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if "data" in json_obj:
        return json_obj['data']['controllerParams']
    else:
        return ControllerName + ' 仿真数据查询失败！'