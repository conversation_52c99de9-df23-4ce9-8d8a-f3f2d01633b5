from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# add by liupeng 2025-04-14
# 根据Adcon控制器名称获取该控制器下的模型数据信息
@tool(version="*")
async def get_online_project_model(context: Context, params: any):
    projectName = params["projectName"]  # 用户输入工程名称
    # 获取全厂ID
    params = {'groupType': 6}
    response_config = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/config/node/all'
                                   , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj_config = json.loads(response_config.text)
    # 解析数据 得到全厂id
    groupId = json_obj_config['data']['id']
    params_config = {'groupId': groupId}
    # 根据全厂ID对控制器信息进行查询
    response_projecttree = requests.get(url=APC_URL + '/inter-api/apc-dashboard/v1/project/data/projecttree'
                                        , params=params_config, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj_projecttree = json.loads(response_projecttree.text)
    dataList = [emp for emp in json_obj_projecttree['data']['loadedProjectList'] if
                emp["projectName"] == projectName]  # 根据工程名称进行筛选（已加载数据）
    if len(dataList) > 0:
        baseProjectId = dataList[0]['id']
    else:
        dataList = [emp for emp in json_obj_projecttree['data']['unLoadedProjectList'] if
                    emp["projectName"] == projectName]  # 根据工程名称进行筛选(未加载数据)
        if len(dataList) > 0:
            baseProjectId = dataList[0]['id']
        else:
            return projectName + '无法进行相关操作！'
    # 根据控制器Adcon控制器id获取该控制器下的模型数据信息
    params = {'baseProjectId': baseProjectId}
    response = requests.get(url=APC_URL + '/inter-api/apc-project/v1/online/project/model'
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    message = json_obj['message']
    data = json_obj['data']
    return {'message': message, 'data': data}
