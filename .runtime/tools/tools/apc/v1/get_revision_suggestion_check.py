from __runner__ import tool, Context


def find_positive_positions(grid):
    positions = []
    for i in range(len(grid)):
        for j in range(len(grid[i])):
            if grid[i][j] > 0:
                positions.append([i, j])
    return positions


# 主要实现的功能是构造控制方案参数信息
# add by liupeng 2025-06-11
@tool(private=True)
async def get_revision_suggestion_check(context: Context, params: any):
    form_result = await context.get_interaction("get_revision_suggestion_check")
    if form_result is None:
        all_properties = {}
        orders = []
        cv_list = params['matrix_data']['cv_list']
        mv_dv_list = params['matrix_data']['mv_list'] + params['matrix_data']['dv_list']
        suggestionFlag = params['data_information']['suggestionFlag']
        index_result = find_positive_positions(params['data_information']['suggestionFlag'])
        #         await context.add_view({
        #             "format": "markdown",
        #             "content": f"""
        # ## get_revision_suggestion_check：
        # {params}
        # """
        #         })
        #
        #         await context.add_view({
        #             "format": "markdown",
        #             "content": f"""
        # ## 指标位置信息：
        # {index_result}
        # """
        #         })
        #         await context.add_view({
        #             "format": "markdown",
        #             "content": f"""
        # ## mv_dv_list：
        # {mv_dv_list}
        # """
        #         })
        for coord in index_result:
            i = coord[0]  # 行索引
            j = coord[1]  # 列索引
            x_val = cv_list[j]  # 注意：这里列索引j对应x的第j个元素
            y_val = mv_dv_list[i]  # 行索引i对应y的第i个元素
            tmp_data = str([i, j])
            orders.append(tmp_data)
            values_data = ''
            enum_names=[]
            if suggestionFlag[i][j] == 1:
                values_data = f'数据分析显示{x_val}变量与{y_val}变量相关性较低，表明无明显干扰影响，建议去除该影响关系，我推测可能在日常控制过程中，{x_val}变量增大/减小时，{y_val}变量没有明显变化，您是否同意？'
                enum_names = ["同意，上述变量之间没有明显影响，控制器结构中去除该控制关系",
                             "不同意，上述变量之间有明显影响，控制器结构中保留该控制关系"]
            elif suggestionFlag[i][j] == 2:
                values_data = f'数据分析显示{x_val}变量与{y_val}变量时滞相关性较高，表明存在明显干扰影响，建议增加该影响关系，我推测可能在日常控制过程中，{x_val}变量增大/减小时，{y_val}变量有明显变化，您是否同意？'
                enum_names = ["同意，上述变量之间有明显影响，控制器结构中添加该影响关系",
                              "不同意，上述变量之间没有明显影响，控制器结构中不添加该影响关系"]
            elif suggestionFlag[i][j] == 3:
                values_data = f'数据分析表明{x_val}变量阀门存在一定的粘滞，建议去除该{x_val}变量对{y_val}变量的控制关系，我推测在日常控制过程中，可能存在阀门动作迟滞或响应不灵敏的现象，您是否同意？'
                enum_names = ["同意，上述变量阀门动作迟滞或不灵敏，控制器结构中去除该控制关系",
                              "不同意，上述变量阀门动作正常，控制器结构中保留该控制关系"]
            elif suggestionFlag[i][j] == 4:
                values_data = f'数据分析显示{x_val}变量与{y_val}变量的相关性较低且调节次数极少，表明该控制关系可能不成立，建议去除，我推测可能在日常控制过程中，调节{x_val}变量时，{y_val}变量没有明显变化，您是否同意？'
                enum_names = [f"同意，调节{x_val}变量时，{y_val}变量没有明显变化，控制器结构中去除该控制关系",
                              f"不同意，调节{x_val}变量时，{y_val}变量会有明显变化，控制器结构中保留该控制关系"]
            elif suggestionFlag[i][j] == 5:
                values_data = f'数据分析显示{x_val}变量与{y_val}变量的相关性较高且频繁调节，表明这可能是常用的控制手段，建议增加该控制关系，我推测可能在日常控制过程中，当{y_val}变量变化时，会用xx变量进行调节，您是否同意？'
                enum_names = [f"同意，{y_val}变量变化时会用{x_val}变量进行调节，控制器结构中添加该控制关系",
                              f"不同意，{y_val}变量变化时不会用{x_val}变量进行调节，控制器结构中不添加该控制关系"]
                # 添加数据信息
            all_properties[tmp_data] = {
                "type": "string",
                "title": values_data,
                "enum": ["0", "1"],
                "enumNames": enum_names,
                "widget": "radio",
                "default": '0'
            }
        #         await context.add_view({
        #             "format": "markdown",
        #             "content": f"""
        # ## all_properties数据信息：
        # {all_properties}
        # ## orders数据信息：
        # {orders}
        # """
        #         })
        await context.add_view({
            "format": "markdown",
            "content": """
根据控制器结构合理性检查结果，我将提供控制器变量关系优化建议的详细描述，如果您不同意我的判断，请修改选项，并提交应用本次修改，我将根据您确认后的信息再次调整控制器结构。
        """
        })
        # 完整实现 - 动态构建多个属性
        form = {
            "schema": {
                "description": "",
                "type": "object",
                "order": orders,
                "properties": {},
                "required": []
            }
        }
        # 动态添加属性
        for key, config in all_properties.items():
            form["schema"]["properties"][key] = config

        # 完整实现 - 动态构建多个属性
        context.require_interaction({
            "id": "get_revision_suggestion_check",
            "title": "请输入以下内容",
            "type": "form",
            "form": form
        })
        return {}
    else:
        index_result = find_positive_positions(params['data_information']['suggestionFlag'])
        tmp_modelRelationshipOut = params['data_information']['modelRelationshipOut']
        for coord in index_result:
            i = coord[0]  # 行索引
            j = coord[1]  # 列索引
            tmp_data = str([i, j])
            if form_result[tmp_data] == '1':
                tmp_modelRelationshipOut[i][j] = 1 - tmp_modelRelationshipOut[i][j]

        #         await context.add_view({
        #                 "format": "markdown",
        #                 "content": f"""
        # ## tmp_modelRelationshipOut数据信息：
        # {tmp_modelRelationshipOut}
        # """
        #             })

        return {
            "modelRelationshipOut": tmp_modelRelationshipOut
        }
