from __runner__ import tool, Context
from dateutil.parser import parse
# 日期格式化
@tool(private=True)
async def custom_format_time(context:Context,time_str):
    try:
        # 使用dateutil更灵活地解析各种格式
        dt = parse(time_str)
        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        # print(f"格式化日期出错: {time_str} - 错误: {str(e)}")
        return time_str
    return formatted_time