import json
import requests
import os
from __runner__ import tool, Context, validator
from datetime import datetime


@tool(private=True)
async def tuning_file_upload(context: Context, params: any):
    form_result = await context.get_interaction("tuning_file_form")
    APC_URL = context.config["APC_URL"]
    # add by liupeng 2025-06-03
    template_list = []
    tmp_template = {'label': '位号历史数据模板说明', 'url': f"{APC_URL}/tpt/tag/historyValueTemplateExplainExport"}
    template_list.append(tmp_template)
    tmp_template = {'label': '位号数据模板', 'url': f"{APC_URL}/tpt/tag/historyValueTemplateExport"}
    template_list.append(tmp_template)
    tmp_template = {'label': '位号历史数据样板', 'url': f"{APC_URL}/tpt/tag/historyValueSampleTemplateExport"}
    template_list.append(tmp_template)

    # await context.log_info(f"template_list result={template_list}")

    if form_result is None:
        # await context.add_view({
        #     "format": "card",
        #     "content": {
        #         "type": 'file',
        #         "title": '数据样板',
        #         "details": f"{APC_URL}/tpt/tag/historyValueSampleTemplateExport",
        #         "description": "位号历史数据样板.csv"
        #     }
        # })
        # await context.add_view({
        #     "format": "card",
        #     "content": {
        #         "type": 'file',
        #         "title": '数据模板',
        #         "details": f"{APC_URL}/tpt/tag/historyValueTemplateExport",
        #         "description": "位号数据模板.csv"
        #     }
        # })
        messages = {'key': '模型矩阵参数'}
        result_message = await context.call_tool("get_result_message", params=messages)  # 根据key获取信息
        await context.add_view({
            "format": "markdown",
            "content": f"""
{result_message}
        """
        })

        #         await context.add_view({
        #             "format": "card",
        #             "content": {
        #                 "type": 'markdown',
        #                 "title": '位号历史数据模板说明',
        #                 "details": """
        # ## 位号历史数据模板说明
        # > **位号历史数据样板说明**：
        # > - 提供一份位号历史数据样板数据，可以下载这份位号历史数据进行用于辨识；
        # >
        # > **请根据文件模板填写数据后上传。请注意以下技术要求**：
        # > - （1）每列数据长度请保持一致,即每列行数相等；
        # > - （2）第一行第一列用作时间标题,其固定名称为:TIME；
        # > - （3）第一行第二列开始用作位号名称输入,其他行不能出现空行或空格；
        # > - （4）第二行第一列为空白,不需要填写数据信息；
        # > - （5）第二行第二列开始用作位号描述,其位号描述信息可以为空；
        # > - （6）第三行第一列开始用作时间数据,其时间格式为yyyy/mm/dd hh:mm:ss,不可为空；
        # > - （7）第三行第二列开始用作位号数据,数据列数以首行数据列数为准,数据为数值型；
        # > - （8）位号数据需包含操作变量和被控变量；
        # > - （9）为保证控制模型效果,上传的历史运行数据应具有代表性,最短数据长度建议不少于5000行,且应涵盖典型工况变化过程；
        # >
        # """
        #             }
        #         })
        context.require_interaction({
            "id": "tuning_file_form",
            "title": "请输入以下内容",
            "type": "form",
            "form": {
                "form_type": "",
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        "model_name": {
                            "type": "string",
                            "title": "模型名称",
                            "description": "请输入模型名称"
                        },
                        "runCycle": {
                            "type": "number",
                            "title": "模型周期(s)",
                            "description": "即控制器的执行周期，建议取数采周期的整数倍，可取5s-60s，默认推荐为30s",
                            "minimum": 0
                        },
                        "modelStepRespPVsLength": {
                            "type": "number",
                            "title": "模型长度",
                            "description": "模型阶跃响应的长度，需要确保覆盖对象的动态特性，可取30-400点，默认推荐为200点，可根据对象特性适当减少或增加",
                            "minimum": 0
                        },
                        "file": {
                            "title": "位号历史数据文件(.csv)",
                            "type": "string",
                            "format": "file-object",
                            "description": '位号历史数据文件',
                            "widget": "tptfile",
                            "template": template_list,
                            "x-validator": "tag_data_check_files",
                        }
                    },
                    "required": [
                        "file",
                        "model_name",
                        "runCycle",
                        "modelStepRespPVsLength"
                    ]
                },
                "default": {
                    'model_name': datetime.now().strftime("%Y%m%d%H%M%S"),
                    'runCycle': 30,
                    'modelStepRespPVsLength': 200
                }
            }
        })
        return {}
    else:
        # 处理文件内容, 必须先解析
        file_info = json.loads(form_result.get("file"))
        # file_content_bytes = await context.get_file(file_info, "bytes")  # 获取文件内容，字节格式
        # await context.log_info(f"file_content_bytes: {file_content_bytes}")
        # file_contents = str(file_content_bytes, encoding='utf-8', errors='strict')
        # await context.log_info(f"file_info22222 result={file_info}")

        return {
            'file': file_info,
            'model_name': form_result['model_name'],
            'runCycle': form_result['runCycle'],
            'modelStepRespPVsLength': form_result['modelStepRespPVsLength'],
            'dataSourceFlag': 1  # 数据源标识 默认0查询数据源 1导入文件数据
        }


@validator(version="*")
async def tag_data_check_files(context: Context, target: any, payload: any = None):
    APC_URL = context.config["APC_URL"]
    # target 格式即为上述实际值格式
    if target is None or target == "":
        raise ValueError("请上传有效的csv文件进行系统自动辨识！")
    file_info = json.loads(target)
    object_path = file_info["object"]
    _, extension = os.path.splitext(object_path)
    # 清洗扩展名：移除点号 + 转小写
    clean_extension = extension.lstrip('.').lower() if extension else None
    if clean_extension != "csv":
        raise ValueError("文件格式仅支持.csv，请重新上传文件！")
    response = requests.post(url=APC_URL + '/tpt/control/fileVerify'
                             , json={
            'bucket': file_info["bucket"],
            'object': file_info["object"],
            'name': file_info["name"],
            'list': [],
            'datatype': 0  # 0是系统辨识 1是控制方案
        }
                             , headers={
            'Content-Type': 'application/json',
            'Cookie': 'tenant-id=0',
            'Accept-Language': 'zh-CN',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    return_data = json.loads(response.text)
    if return_data['code'] != 200:
        # 错误结果，抛出异常
        raise ValueError(f"您上传的文件不符合要求，错误信息如下: {return_data['message']}")
    # 直接返回即为成功
    return
