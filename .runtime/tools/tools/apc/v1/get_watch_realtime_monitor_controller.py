from __runner__ import tool, Context
import requests
import json
import os
import time
from datetime import datetime
from dotenv import load_dotenv
from dateutil.parser import parse

load_dotenv()

# 根据控制器名称查询实时指标数据
# add by liupeng 2025-04-10
@tool(version="*")
async def get_watch_realtime_monitor_controller(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    controllerCurrentRunInfo = ''
    controllerCurrentRunInfoReturnList = []
    try:
        selectTarget = params['selectTarget']
        controllerNames = params['controllerNames']  # 用户输入的控制器名称
        current = 1
        pageSize = 99999999
        selectType = 0
        sysGroupId = 0
        params = {'deviceName': '全厂'}
        device_id = await context.call_tool("get_device_id_by_name", params=params)  # 根据用户输入的装置名称对装置ID进行获取
        if device_id != -1:
            sysGroupId = device_id
        params = {'current': current, 'pageSize': pageSize, 'selectType': selectType, 'sysGroupId': sysGroupId}
        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/controller/table/select'
                                , params=params, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj = json.loads(response.text)
        #dataList = [emp for emp in json_obj['list'] if emp["controllerName"] == controllerName]  # 根据控制器名称去匹配
        dataList = filter_controllers_by_name(json_obj['list'],controllerNames)
        if len(dataList) > 0:
            for tempController in dataList:
                controllerId = tempController["id"]
                controllerName = tempController["controllerName"]
                params_controller = {'controllerId': controllerId}
                # 列表数据
                response_controller = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/controller/select'
                                                   , params=params_controller, headers={
                        'Content-Type': 'application/json',
                        'Authorization': APC_AGENT_TOKEN,
                        'X-TPT-TOKEN': X_TPT_TOKEN,
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
                    })
                json_obj_controller = json.loads(response_controller.text)
                # # 趋势图数据
                # response_charts = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/controller/charts/select'
                #                                , params=params_controller, headers={
                #         'Content-Type': 'application/json',
                #         'Authorization': APC_AGENT_TOKEN,
                #         'X-TPT-TOKEN' : X_TPT_TOKEN,
                #         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
                #     })
                # json_obj_charts = json.loads(response_charts.text)
                # dataCharts = json_obj_charts['data']
                # return {'DataList': DataList, 'DataCharts': DataCharts}
                dataList = json_obj_controller['data']
                # 控制器基本信息
                controller_base_info_temp = controller_base_info(dataList)
                # 控制器运行基本指标信息
                controller_run_base_info_temp = controller_run_base_info(dataList)
                # cv 被控变量指标信息
                cv_info_temp = cv_info(dataList)
                # mv 操作变量指标信息
                mv_info_temp = mv_info(dataList)

                controller_var_count_info_temp = controller_var_count_info(dataList)

                controllerCurrentRunInfo = (controller_base_info_temp["controller_base_info_table"] +
                                            controller_run_base_info_temp["device_data_detail_table"] +
                                            cv_info_temp["cv_info_table"] +
                                            mv_info_temp["mv_info_table"] +
                                            controller_var_count_info_temp["mv_info_table"])

                # controllerCurrentRunInfoReturn = {
                #     "controllerName":controllerName,
                #     "controllerBaseInfo": controller_base_info_temp["controller_base_info_return"],
                #     "controllerRunBaseInfo": controller_run_base_info_temp["device_data_detail_return"],
                #     "cvInfoDetail": cv_info_temp["cv_info_detail_return"],
                #     "mvInfoDetail": mv_info_temp["mv_info_detail_return"],
                #     "var_count_info_return": controller_var_count_info_temp["var_count_info_return"]
                # }
                controllerCurrentRunInfoReturn = {"controllerName": controllerName}
                if 0 in selectTarget:
                    controllerCurrentRunInfoReturn = {
                        "controllerName":controllerName,
                        "controllerBaseInfo": controller_base_info_temp["controller_base_info_return"],
                        "controllerRunBaseInfo": controller_run_base_info_temp["device_data_detail_return"],
                        "cvInfoDetail": cv_info_temp["cv_info_detail_return"],
                        "mvInfoDetail": mv_info_temp["mv_info_detail_return"],
                        "var_count_info_return": controller_var_count_info_temp["var_count_info_return"]
                    }
                else:
                    if 1 in selectTarget:
                        controllerCurrentRunInfoReturn["controllerBaseInfo"] =  controller_base_info_temp["controller_base_info_return"]
                    if 2 in selectTarget:
                        controllerCurrentRunInfoReturn["controllerRunBaseInfo"] = controller_run_base_info_temp["device_data_detail_return"]
                    if 3 in selectTarget:
                        controllerCurrentRunInfoReturn["cvInfoDetail"] = cv_info_temp["cv_info_detail_return"]
                    if 4 in selectTarget:
                        controllerCurrentRunInfoReturn["mvInfoDetail"] = mv_info_temp["mv_info_detail_return"]
                    if 5 in selectTarget:
                        controllerCurrentRunInfoReturn["var_count_info_return"] = controller_var_count_info_temp["var_count_info_return"]

                controllerCurrentRunInfoReturnList.append(controllerCurrentRunInfoReturn)
                tempTitle = f"""{controllerName}查询控制器实时指标数据"""
                await context.add_view({
                    "format": "card",
                    "content": {
                        "type": 'markdown',
                        "title": tempTitle,
                        "description": tempTitle,
                        "details": f"""
{controllerCurrentRunInfo}
"""
                    }
                })
            return format_response(
                success=True,
                data=controllerCurrentRunInfoReturnList
            )
        else:

            return format_response(
                success=False,
                message="控制器实时指标数据查询失败"
            )
    except Exception as e:
        # print("控制器实时指标数据查询失败:APC_AGENT_URL:" + APC_AGENT_URL + str(e))
        raise Exception(f"控制器实时指标数据查询失败，错误信息:" + str(e))
        # return {"error": "控制器实时指标数据查询失败,参数信息:"+DeviceName}
        pass


def custom_format_time(time_str):
    try:
        # 使用dateutil更灵活地解析各种格式
        dt = parse(time_str)
        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        # print(f"格式化日期出错: {time_str} - 错误: {str(e)}")
        return time_str
    return formatted_time


## 控制器运行状态总览
def controller_base_info(DataList):
    runingStatusAndTimeDto = DataList.get('runingStatusAndTimeDto')
    controller_base_info_text = ''
    controller_base_info_return = {}
    try:
        if runingStatusAndTimeDto:
            ##运行状态
            temp_status = runingStatusAndTimeDto.get('status')
            status_text = '开启'
            if temp_status != 1:
                status_text = '停止'
            ##开始时间
            begin_time_text = runingStatusAndTimeDto.get('beginTime')
            begin_time_text = custom_format_time(begin_time_text)

            ##累计运行时长
            operation_time_text = runingStatusAndTimeDto.get('operationTime')
            ##累计停工时长
            shutdown_time_text = runingStatusAndTimeDto.get('shutdownTime')
            ##控制器运行状态
            controller_base_info_text = controller_base_info_text + (
                f"|{status_text}"
                f"|{begin_time_text}"
                f"|{operation_time_text}"
                f"|{shutdown_time_text}\r\n"
            )
            controller_base_info_return = {"status": status_text, "beginTime": begin_time_text,
                                           "operationTime": operation_time_text, "shutdownTime": shutdown_time_text}

    except Exception as e:
        pass
        # print("解析控制器运总览出错:" + str(e))
    controller_base_info_table = f"""
## 控制器运行状态总览
|  运行状态 |  投运开始时间 | 累计运行时长(小时) | 累计剔除时长(小时) |
|:-------:|:-----------:|:---------------:|:---------------:|
{controller_base_info_text}
"""
    return {"controller_base_info_table": controller_base_info_table,
            "controller_base_info_return": controller_base_info_return}


def filter_controllers_by_name(controller_list, controllerNames):
    """
    根据controllerNames集合筛选控制器对象
    :param controller_list: 原始控制器列表 [{'controllerName': 'CTRL1'}, ...]
    :param controllerNames: 要匹配的控制器名称集合（允许None/空集合/空字符串）
    :return: 包含匹配对象的新列表（当controllerNames为空时返回原列表的副本）
    """
    if not controllerNames:  # 处理None/空集合/空字符串等情况
        return controller_list.copy()  # 返回副本避免修改原列表

    # 统一处理字符串输入（兼容单个名称的情况）
    names_set = {controllerNames} if isinstance(controllerNames, str) else set(controllerNames)

    return [controller for controller in controller_list
            if controller.get('controllerName') in names_set]



## 控制器运行指标
def controller_run_base_info(dataList):
    device_data_detail = ''
    device_data_detail_return = {}
    try:
        # 综合性能
        base_info_value = ''
        base_info_change = ''
        # 投运率
        tyl_value = ''
        tyl_value_change = ''
        # MV投运率
        mv_tyl_value = ''
        mv_tyl_change = ''
        # CV投运率
        cv_Tyl_value = ''
        cv_Tyl_change = ''
        # 操作频次
        operation_count_text = ''
        operation_indicator_list = dataList.get('operationIndicatorDtoList')
        if operation_indicator_list:
            # 综合性能
            base_info = operation_indicator_list[0]
            base_info_value = base_info.get("value")
            base_info_change = base_info.get("change")
            # 投运率
            tyl = operation_indicator_list[1]
            tyl_value = tyl.get("value")
            tyl_value_change = tyl.get("change")
            # MV投运率
            mvTyl = operation_indicator_list[2]
            mv_tyl_value = mvTyl.get("value")
            mv_tyl_change = mvTyl.get("change")
            # CV投运率
            cv_Tyl = operation_indicator_list[3]
            cv_Tyl_value = cv_Tyl.get("value")
            cv_Tyl_change = cv_Tyl.get("change")

            # 操作频次
            operation_count = operation_indicator_list[4]
            operation_count_text = operation_count.get("value")

            device_data_detail = device_data_detail + (
                f"|{base_info_value}({base_info_change})"
                f"|{tyl_value}({tyl_value_change})"
                f"|{mv_tyl_value}({mv_tyl_change})"
                f"|{cv_Tyl_value}({cv_Tyl_change})"
                f"|{operation_count_text}\r\n"
            )
            device_data_detail_return = {"baseInfoValue": base_info_value, "baseInfoChange": base_info_change,
                                         "tylValue": tyl_value, "tylValueChange": tyl_value_change,
                                         "mvTylValue": mv_tyl_value, "mvTylChange": mv_tyl_change,
                                         "cvTylValue": cv_Tyl_value, "cvTylChange": cv_Tyl_change,
                                         "operationCount": operation_count_text}
    except Exception as e:
        pass
        # print("解析控制器运行指标出错:" + str(e))
    # tempCountType = get_count_type(countType)
    device_data_detail_table = f"""
## 控制器运行指标
| 综合性能(%) | 投运率(%) | MV投运率(%) | CV投运率(%) | 操作频次 |
|:---------:|:--------:|:----------:|:----------:|:-------:|
{device_data_detail}
"""
    return {"device_data_detail_table": device_data_detail_table,
            "device_data_detail_return": device_data_detail_return}


## 变量指标信息--cv
def cv_info(dataList):
    variableIndicatorInformationDto = dataList.get('variableIndicatorInformationDto')
    cv_info_detail = ''
    cv_info_detail_return = []
    if variableIndicatorInformationDto:
        cvIndicatorDtoList = variableIndicatorInformationDto.get('cvIndicatorDtoList')
        if cvIndicatorDtoList:
            for index, s in enumerate(cvIndicatorDtoList, start=1):

                temp_status = s.get('status', '')
                status_text = '开启'
                if temp_status != 1:
                    status_text = '关闭'
                cv_info_detail = cv_info_detail + (
                    f"|{index}"
                    f"|{s.get('variableName', '')}"
                    f"|{s.get('variableDesc', '')}"
                    f"|{status_text}"
                    f"|{s.get('operationalRate', '')}"
                    f"|{s.get('effectiveOperationalRate', '')}"
                    f"|{s.get('cvOverLimitRate', '')}"
                    f"|{s.get('cvBasedStandardDeviation', '')}/{s.get('cvStandardDeviation', '')}"
                    f"|{s.get('cvStandardDeviationDecreaseRate', '')}"
                    f"|{s.get('apcOperationFrequency', '')}\r\n"
                )
                cv_info_detail_return_item = {"variableName": s.get('variableName', ''),
                                              "variableDesc": s.get('variableDesc', ''),
                                              "status": status_text,
                                              "operationalRate": s.get('operationalRate', ''),
                                              "effectiveOperationalRate": s.get('effectiveOperationalRate', ''),
                                              "cvOverLimitRate": s.get('cvOverLimitRate', ''),
                                              "cvBasedStandardDeviation": s.get('cvBasedStandardDeviation', ''),
                                              "cvStandardDeviation": s.get('cvStandardDeviation', ''),
                                              "cvStandardDeviationDecreaseRate": s.get(
                                                  'cvStandardDeviationDecreaseRate', ''),
                                              "apcOperationFrequency": s.get('apcOperationFrequency', '')}
                cv_info_detail_return.append(cv_info_detail_return_item)

    cv_info_table = f"""
## 变量指标信息-被控变量    
| 序号| 变量名称 | 变量描述 | 运行状态 | 投运率(%) | 有效投运率(%) | CV超限率(%) | CV标准基准/CV标准差 | CV标准基差下降率(%) | 操作频次 |
|:--:|:--------|:------:|:-------:|:-------:|:------------:|:----------:|:----------------:|:----------------:|:-------:|
{cv_info_detail}
"""
    return {"cv_info_table": cv_info_table, "cv_info_detail_return": cv_info_detail_return}


## 变量指标信息--mv
def mv_info(dataList):
    variableIndicatorInformationDto = dataList.get('variableIndicatorInformationDto')
    mv_info_detail = ''
    mv_info_detail_return = []
    if variableIndicatorInformationDto:
        cvIndicatorDtoList = variableIndicatorInformationDto.get('mvIndicatorDtoList')
        if cvIndicatorDtoList:
            for index, s in enumerate(cvIndicatorDtoList, start=1):
                temp_status = s.get('status', '')
                status_text = '开启'
                if temp_status != 1:
                    status_text = '关闭'
                mv_info_detail = mv_info_detail + (
                    f"|{index}"
                    f"|{s.get('variableName', '')}"
                    f"|{s.get('variableDesc', '')}"
                    f"|{status_text}"
                    f"|{s.get('operationalRate', '')}"
                    f"|{s.get('effectiveOperationalRate', '')}"
                    f"|{s.get('mvStuckLimitRate', '')}"
                    f"|{s.get('apcOperationFrequency', '')}\r\n"
                )
                mv_info_detail_return_item = {"variableName": s.get('variableName', ''),
                                              "variableDesc": s.get('variableDesc', ''),
                                              "status": status_text,
                                              "operationalRate": s.get('operationalRate', ''),
                                              "effectiveOperationalRate": s.get('effectiveOperationalRate', ''),
                                              "mvStuckLimitRate": s.get('mvStuckLimitRate', ''),
                                              "apcOperationFrequency": s.get('apcOperationFrequency', '')}
                mv_info_detail_return.append(mv_info_detail_return_item)

    mv_info_table = f"""
## 变量指标信息-操作变量    
| 序号| 变量名称 | 变量描述 | 运行状态 | 投运率(%) | 有效投运率(%) | MV卡限率(%) | 操作频次 |
|:--:|:--------|:--------:|:-------:|:-------:|:-------------:|:----------:|:-------:|
{mv_info_detail}
"""
    return {"mv_info_table": mv_info_table, "mv_info_detail_return": mv_info_detail_return}


## 控制器信息
def controller_var_count_info(dataList):
    controllerTotalInformationDto = dataList.get('controllerTotalInformationDto')
    mv_info_detail = ''
    var_count_info_return = {}
    if controllerTotalInformationDto:
        mvTotalNum = controllerTotalInformationDto.get('mvTotalNum', '')
        cvTotalNum = controllerTotalInformationDto.get('cvTotalNum', '')
        mv_info_detail = mv_info_detail + (
            f"|MV总数"
            f"|{mvTotalNum}"
            f"|CV总数"
            f"|{cvTotalNum}\r\n"
        )
        mvConditionNum = controllerTotalInformationDto.get('mvConditionNum', '')
        cvConditionNum = controllerTotalInformationDto.get('cvConditionNum', '')
        mv_info_detail = mv_info_detail + (
            f"|MV剔除个数"
            f"|{mvConditionNum}"
            f"|CV剔除个数"
            f"|{cvConditionNum}\r\n"
        )
        mvOperationalNum = controllerTotalInformationDto.get('mvOperationalNum', '')
        cvOperationalNum = controllerTotalInformationDto.get('cvOperationalNum', '')
        mv_info_detail = mv_info_detail + (
            f"|MV投运个数"
            f"|{mvOperationalNum}"
            f"|CV投运个数"
            f"|{cvOperationalNum}\r\n"
        )
        mvApcOperationFrequency = controllerTotalInformationDto.get('mvApcOperationFrequency', '')
        cvApcOperationFrequency = controllerTotalInformationDto.get('cvApcOperationFrequency', '')
        mv_info_detail = mv_info_detail + (
            f"|MV投运个数"
            f"|{mvApcOperationFrequency}"
            f"|CV投运个数"
            f"|{cvApcOperationFrequency}\r\n"
        )
        mvCountInfo = {"mvTotalNum": mvTotalNum, "mvConditionNum": mvConditionNum, "mvOperationalNum": mvOperationalNum,
                       "mvApcOperationFrequency": mvApcOperationFrequency}

        cvCountInfo = {"cvTotalNum": cvTotalNum, "cvConditionNum": cvConditionNum, "cvOperationalNum": cvOperationalNum,
                       "cvApcOperationFrequency": cvApcOperationFrequency}

        var_count_info_return = {"mvCountInfo": mvCountInfo, "cvCountInfo": cvCountInfo}

    mv_info_table = f"""
## 控制器信息
| 操作变量  |  数量 | 被控变量 | 数量 |
|:---------|:----:|:-------|:-----:|
{mv_info_detail}
"""
    return {"mv_info_table": mv_info_table, "var_count_info_return": var_count_info_return}


def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }
