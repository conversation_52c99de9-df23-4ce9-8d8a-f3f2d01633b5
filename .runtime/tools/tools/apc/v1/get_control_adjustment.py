from __runner__ import tool, Context

# 控制方案-用户针对大语言生成的控制器结构进行确认是否需要调整
# add by liupeng 2025-07-02
@tool(private=True)
async def get_control_adjustment(context: Context, params: any):
    result = await context.get_interaction("get_control_adjustment")
    if result is None:
        context.require_interaction({
            "id": "get_control_adjustment",
            "title": "现已为您生成控制方案和对应控制器结构，若控制器结构中的变量范围与模型关系与现场装置控制需求存在偏差，请进一步调整控制器结构",
            "type": "select",
            "mode": "single",
            "select": [
                {
                    "title": "控制器结构满足控制需求，下一步",
                    "data": 0,
                },
                {
                    "title": "调整控制器结构",
                    "data": 1,
                }
            ]
        })
        return {

        }
    else:
        tmp_result = {
            'data': result[0]['data']
        }
        return tmp_result
