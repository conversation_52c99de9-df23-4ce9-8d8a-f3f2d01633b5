from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv
from dateutil.parser import parse
from urllib.parse import urlparse

load_dotenv()

# APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
# APC_URL = os.getenv("APC_URL")  # 读取网址信息



#主要实现apc中adcon的创建
#目前打开一个页面
# 创建apc，如果用户输入名称则修改用户名称 然后把页面打开出来即可
# add by mncg 2025-07-14
@tool(version="*")
async def adcon_config_create(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    adconName = ""
    if "adconName" in params:
        adconName  = params["adconName"]

    isCreate = create_adcon(context,adconName)
    if not isCreate:
        return format_response(success=False,message="创建控制器失败",data=False)

    #这里现在的方案是暂时先替换成https
    url = normalize_url(APC_AGENT_URL)
    await context.log_info("adcon地址:"+url)

    context.require_interaction(
        {"id": "adcon_config_model_page",
         "title": "adcon组态界面",
         "type": "open_page",
         "open_page": f"{url}/adcon?ticket={APC_AGENT_TOKEN}",
         "page_type": "view"
         })


    return format_response(success=True,data=True)

def create_adcon(context: Context,adconName):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    adconCreateParam = {"adconName":adconName}
    createAdconRep = requests.post(url=APC_AGENT_URL + '/inter-api/apc-project/v1/configuration/save'
                                        , json=adconCreateParam, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    createAdconRepJson = json.loads(createAdconRep.text)
    if createAdconRepJson['code'] == 100000000:
        return True
    else:
        return False

#http变成https
def normalize_url(url):
    parsed = urlparse(url)
    if parsed.scheme != 'https':
        netloc = parsed.netloc.split(':')  # 只保留主机名
        url = 'https://' + netloc[0] + parsed.path + parsed.query + parsed.fragment
    return url


def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }

