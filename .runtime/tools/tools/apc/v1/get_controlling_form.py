from __runner__ import tool, Context, validator
import json
import os


# 主要实现的功能是构造控制方案参数信息
# add by liupeng 2025-06-11
@tool(private=True)
async def get_controlling_form(context: Context, params: any):
    user_question = ''
    tmp_content = '基于用户指令与输出要求,输出用户指令中明确提到的生产装置或设备范围中,先进控制方案控制器可能涉及的核心工艺设备名称及可变数量关键属性列表,具体步骤如下:1.首先基于用户指令中的生产装置名称或设备名称获得设备列表,颗粒度到多变量预测控制器涉及的反应器、加热炉、装置包含的多个可能包含的具体分离塔设备、重要的脱盐罐等大型核心设备,不需要包括阀门、储罐、储槽等细节控制对象(比如气化炉生成粉煤气化炉、水煤浆气化炉等设备列表、乙烯装置生成裂解炉、高压脱丙烷塔、脱乙烷塔、乙烯塔、丙烯塔等设备列表)2.根据第一步生成的设备列表,按照以下要求进一步获得可能存在的对应可变数量属性,可变数量属性描述对于气化炉类设备属性为喷嘴个数,加热炉裂解炉等设备属性为支路个数、塔类设备属性为侧线个数，其余设备如反应器等不输出属性。3.将设备列表中设备名称与可能存在的属性描述通过逗号拼接作为列表中的元素代替原本的设备名称,如果没有可变属性保留该设备在列表中。4.特殊条件判断:如果用户指令包含气化炉,输出["粉煤气化炉，喷嘴个数:", "水煤浆气化炉，喷嘴个数:"]；如果用户指令包含氯碱与电解槽关键字,输出["离子膜电解槽,个数:"]5.输出格式要求:以气化炉为例,输出设备列表格式为["粉煤气化炉,喷嘴个数:","水煤浆气化炉,喷嘴个数:"],禁止其他列表外内容生成。/no_think'
    if "userinput" in params:
        user_question = params['userinput']
    tmp_params = {
        'content': user_question,
        'content_data': tmp_content
    }
    get_chat_completions_result = await context.get_cache("get_chat_completions_result")
    if get_chat_completions_result is None:
        result_data = await context.call_tool("get_chat_completions", params=tmp_params)  # 控制方案入参数据信息
        await context.set_cache("get_chat_completions_result", result_data)
    is_show = "hidden"
    form_result = await context.get_interaction("get_controlling_form")
    if form_result is None:
        await context.add_view({
            "format": "markdown",
            "content": """
为了更好地完成您的任务，我需要与您确认一下任务的具体要求，以确保我们对控制方案的期望是一致的。
        """
        })
        context.require_interaction({
            "id": "get_controlling_form",
            "title": "请输入以下内容",
            "type": "form",
            "form": {
                "form_type": "",
                "schema": {
                    "description": "",
                    "type": "object",
                    "order": ["file", "controlProblem", "combinedField", "concentration", "analyzer"],
                    "properties": {
                        "file": {
                            "title": "1. 如果您选择上传操作规程等工艺资料（支持doc、docx与pdf格式），将帮助我们了解您所在装置具体的工艺特点与控制需求，以便生成针对性的装置控制方案。",
                            "type": "string",
                            "format": "file-object",
                            "description": '',
                            "widget": "tptfile",
                            "x-validator": "data_check_file",
                        },
                        "controlProblem": {
                            "type": "string",
                            "title": "2. 装置当前的自动化控制水平是？",
                            "enum": ["1", "2"],
                            "enumNames": ["自控率较低，操作频次较高", "自控率良好，关键工艺指标平稳性存在改善空间"],
                            "widget": 'radio',
                            "default": '1'
                        },
                        # "coverages": {
                        #     "$ref": "#/definitions/person",
                        # },
                        # "concentrations": {
                        #     "type": "string",
                        #     "title": "3. 控制方案覆盖范围：",
                        #     "enum": result_data,
                        #     "enumNames": result_data,
                        #     "widget": 'radio',
                        #     "default": result_data[0]
                        # },
                        "combinedField": {
                            "type": "object",
                            "title": '',
                            "properties": {
                                "radioOption": {
                                    "type": "string",
                                    "title": "3. 控制方案覆盖范围：",
                                    "enum": result_data,
                                    "enumNames": result_data,
                                    "widget": 'radio',
                                    "default": result_data[0]
                                },
                                "textInput": {
                                    "type": "number",
                                    "title": "控制方案覆盖设备关键属性值（如支路个数、喷嘴数量、设备个数；若选项中没有对应属性无需修改）",
                                    "default": 1,
                                    "minimum": 0
                                }
                            },
                            "required": ["textInput", "radioOption"]
                        },

                        # "concentrations": {
                        #     "type": "string",
                        #     "title": "3. 控制方案覆盖范围：",
                        #     "enum": result_data,
                        #     "enumNames": result_data,
                        #     "widget": 'radio',
                        #     "default": result_data[0]
                        # },
                        # "spray_nozzle": {
                        #     "type": "number",
                        #     "title": "对应喷嘴个数",
                        #     "description": "",
                        #     "default": 2,
                        #     "minimum": 0
                        # },
                        # "concentration": {
                        #     "type": "string",
                        #     "title": "4. 是否配备水煤浆浓度分析仪",
                        #     "enum": ["1", "2"],
                        #     "enumNames": ["配备水煤浆浓度分析仪", "未配备水煤浆浓度分析仪"],
                        #     "widget": is_show,
                        #     "default": '1'
                        # },
                        # "analyzer": {
                        #     "type": "string",
                        #     "title": "5. 是否定期对煤质进行人工化验",
                        #     "enum": ["1", "2"],
                        #     "enumNames": ["是", "否"],
                        #     "widget": is_show,
                        #     "default": '1'
                        # },
                    },
                    "required": [
                        "controlProblem"
                    ],
                    # "definitions": {
                    #     "person": {
                    #         "title": "",
                    #         "type": "object",
                    #         "properties": {
                    #             "coverage": {
                    #                 "title": "3. 控制方案覆盖范围：",
                    #                 "type": "string",
                    #                 "enum": ["1", "2", "3"],
                    #                 "default": "1",
                    #                 "enumNames": ["水煤浆气化炉", "粉煤气化炉", "其他"],
                    #                 "widget": "radio",
                    #             }
                    #         },
                    #         "required": [
                    #             "coverage"
                    #         ],
                    #         "dependencies": {
                    #             "coverage": {
                    #                 "oneOf": [
                    #                     {
                    #                         "properties": {
                    #                             "coverage": {
                    #                                 "enum": [
                    #                                     "1"
                    #                                 ]
                    #                             },
                    #                             "spray_nozzle": {
                    #                                 "type": "number",
                    #                                 "title": "对应喷嘴个数",
                    #                                 "description": "",
                    #                                 "default": 2,
                    #                                 "minimum": 0
                    #                             }
                    #                         },
                    #                         "required": [
                    #                             "spray_nozzle"
                    #                         ]
                    #                     },
                    #                     {
                    #                         "properties": {
                    #                             "coverage": {
                    #                                 "enum": [
                    #                                     "2"
                    #                                 ]
                    #                             },
                    #                             "spray_nozzles": {
                    #                                 "type": "number",
                    #                                 "title": "对应喷嘴个数",
                    #                                 "description": "",
                    #                                 "default": 2,
                    #                                 "minimum": 0
                    #                             }
                    #                         },
                    #                         "required": [
                    #                             "spray_nozzles"
                    #                         ]
                    #                     },
                    #                     {
                    #                         "properties": {
                    #                             "coverage": {
                    #                                 "enum": [
                    #                                     "3"
                    #                                 ]
                    #                             },
                    #                             "other": {
                    #                                 "type": "string",
                    #                                 "title": "请输入以下信息",
                    #                                 "description": "",
                    #                             }
                    #                         },
                    #                         "required": [
                    #                             "other"
                    #                         ]
                    #                     }
                    #                 ]
                    #             }
                    #         }
                    #     }
                    # }
                },
            },
        })
        return {}
    else:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "正在基于补充信息联网搜索公开的历史方案和工艺信息，生成先进控制方案及控制器结构，预计需要2-5分钟，请在生成后及时确认控制器信息。",
                "description": "",
                "details": ""
            }
        })
        return {
            "result": form_result,
        }


@validator(version="*")
async def data_check_file(context: Context, target: any, payload: any = None):
    if target is None or target == "":
        return
    file_info = json.loads(target)
    object_path = file_info["object"]
    _, extension = os.path.splitext(object_path)
    # 清洗扩展名：移除点号 + 转小写
    clean_extension = extension.lstrip('.').lower() if extension else None
    if clean_extension != "docx" and clean_extension != "doc" and clean_extension != "pdf":
        raise ValueError("文件格式仅支持.docx/.doc/.pdf，请重新上传文件！")
    # 直接返回即为成功
    return
