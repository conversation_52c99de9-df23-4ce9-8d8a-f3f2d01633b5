from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# 控制器变量指标历史数据查询

# 控制器变量指标历史数据查询
@tool(version="*")
async def get_watch_history_monitor_var(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    controllerList = []
    device_id = 0
    if "deviceName" in params:
        ##装置级别  装置级别是查询这个装置下面的所有的控制器，然后循环控制器的信息出结果
        tempDeviceName = params["deviceName"]
        groupParams = {"deviceName":tempDeviceName}
        device_id = await context.call_tool("get_device_id_by_name", params=groupParams)  # 根据用户输入的装置名称对装置ID进行获取

        if device_id == -1:
            return format_response(
                success=False,
                message="未找到该装置:"+tempDeviceName
            )
        controllerListParam = {"id":device_id}
        controllerListResponse = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/history/controller/list',
                                params=controllerListParam,headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
        controllerListJson = json.loads(controllerListResponse.text)
        controllerList = controllerListJson["data"]
    else:
        ##控制器级别
        controllerName = params['controllerName']  # 用户输入的控制器名称
        ##查询控制器
        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/controller/list', headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
        controllerJson = json.loads(response.text)
        controllerList = [emp for emp in controllerJson['list'] if
                              emp["controllerName"] == controllerName]  # 根据控制器名称去匹配
        # controllerId = 0
        # groupType = 0
        # if len(controllerDataList) > 0:
        #     controllerId = controllerDataList[0]['id']  # 控制器id
        #     groupType = controllerDataList[0]['sysGroupId']  # 控制器id
        # else:
        #     return format_response(
        #         success=False,
        #         message="未查询到该控制器(" + controllerName + ")"
        #     )
    if not controllerList:
        return format_response(
            success=False,
            message="无符合条件的控制器"
        )

    startTime = params["startTime"]
    endTime = params["endTime"]
    # 查询结果类型，枚举类：1.投运率 2.有效投运率 3.(MV)卡上限率 4.(MV)卡下限率 5.(CV)超上限率 6.(CV)超下限率 7.(CV)标准差 8.(CV)标准差下降率 11.卡限率 12.超限率 13.先控操作频次
    resultTypes = None
    if 'resultType' in params:
        resultTypes = params["resultType"]

    # 报告类型，枚举类： 1.实时/当前 2.日报 3.周报 4.月报
    reportType = 1
    if 'reportType' in params:
        reportType = params["reportType"]
    ##默认变量类型 3 操作变量 5 被控变量
    variableType = 3
    if 'variableType' in params:
        variableType = params["variableType"]

    ## 转化变量类型为中文 并校验
    variableTypeZh = transform_zh_variable_type(variableType)
    if not variableTypeZh:
        return format_response(
            success=False,
            message="变量类型描述错误，目前仅支持：[操作变量,被控变量]"
        )
    ## 如果变量名称不为空则需要根据位号名称查询位号id
    variableInfoTable = ""
    variableInfoList = []

    for controllerData in controllerList:
        historyParam = {}
        controllerId = controllerData["id"]
        if 'sysGroupId' in controllerData:
            groupType = controllerData['sysGroupId']
        else:
            groupType = device_id
        if 'variableName' in params:
            variableName = params["variableName"]
            variableListParam = {'type': variableType, 'id': controllerId}
            response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/history/variable/list'
                                    , params=variableListParam, headers={
                    'Content-Type': 'application/json',
                    'Authorization': APC_AGENT_TOKEN,
                    'X-TPT-TOKEN': X_TPT_TOKEN,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
                })
            variableListRep = json.loads(response.text)
            ##变量id
            variableId = find_matching_variable(variableListRep['data'], variableName)
            if not variableId:
                return format_response(
                    success=False,
                    message="该控制器下不存在此" + variableTypeZh + "[" + variableName + "]"
                )
            historyParam = {'current': 1, "pageSize": 99999, "groupType": groupType, "controller": controllerId,
                            "id": variableId,
                            "searchType": reportType, "variableType": variableType, "startTime": startTime,
                            "endTime": endTime, "type": 4}
        else:
            historyParam = {'current': 1, "pageSize": 99999, "groupType": groupType, "controller": controllerId,
                            "searchType": reportType, "variableType": variableType, "startTime": startTime,
                            "endTime": endTime, "type": 4}

        response = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/history/list'
                                , params=historyParam, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj = json.loads(response.text)
        ##返回结果
        tempVariableInfo = generate_markdown_report(json_obj['list'], variableType, resultTypes, reportType)
        tempVariableInfoTable = tempVariableInfo["var_detail_table"]
        ##叠加markdown
        variableInfoTable = variableInfoTable + tempVariableInfoTable
        tempVariableInfoList= tempVariableInfo["var_info_detail_return"]
        ##叠加变量历史数据
        if tempVariableInfoList:
            for tempData in tempVariableInfoList:
                variableInfoList.append(tempData)

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '控制器变量指标历史数据查询',
            "description": "控制器变量指标历史数据查询",
            "details": f"""
{variableInfoTable}
"""
        }
    })
    #格式化输出JSON
    varKey = None
    if variableType == 3:
        varKey = "mvVarDetail"
    if variableType == 5:
        varKey = "cvVarDetail"
    variableDetail = variableInfoList
    if varKey:
        return format_response(
            success=True,
            data={varKey:variableDetail}
        )
    else:
        return format_response(
            success=False,
            message="变量类型错误"
        )

##找到符合条件的名称对应的位号id
def find_matching_variable(data_list, target_name):
    """
    从列表中查找variableName匹配的第一个元素
    :param data_list: 数据列表
    :param target_name: 要匹配的变量名
    :return: 匹配的第一个字典或None
    """
    for item in data_list:
        if item.get('variableName') == target_name:
            return item.get("id")
    return None


def transform_zh_variable_type(variable_type):
    variable_type_zh = None
    if variable_type == 3:
        variable_type_zh = "操作变量"
    if variable_type == 5:
        variable_type_zh = "被控变量"
    return variable_type_zh


### 根据输入信息变量
def generate_markdown_report(data_list, variable_type, result_types=None, report_type=None):
    """
    生成动态表头的Markdown报表
    :param data_list: 数据列表
    :param variable_type: 变量类型('MV'或'CV')
    :param result_types: 结果类型列表(可选，为空时返回全部指标)
    :param report_type: 报表类型(可选)
    :return: Markdown格式的报表字符串
    """
    # 基础字段映射
    base_headers = ["变量名称", "时间段", "分组", "变量描述", "所属控制器"]
    base_fields = ["name", "time_range", "groupName", "desc", "controllerName"]

    # 类型特定字段映射(包含所有可能的指标)
    type_mapping = {
        3: {
            1: ('MV投运率(%)', 'mvOperateRate'),
            2: ('MV有效投运率(%)', 'mvUsableOperateRate'),
            3: ('MV卡上限率(%)', 'mvUpperRate'),
            4: ('MV卡下限率(%)', 'mvLowerRate'),
            11: ('MV卡限率(%)', 'mvLimitRate'),
            13: ('MV先控操作频次', 'countOperateFrequency')
        },
        5: {
            1: ('CV投运率(%)', 'cvOperateRate'),
            2: ('CV有效投运率(%)', 'cvUsableOperateRate'),
            5: ('CV超上限(%)', 'cvUpperRate'),
            6: ('CV超下限(%)', 'cvLowerRate'),
            7: ('CV标准差', 'cvStandardDeviation'),
            8: ('CV标准差下降率(%)', 'cvStandardDeviationDecreaseRate'),
            12: ('CV超限率(%)', 'cvLimitRate'),
            13: ('CV先控操作频次', 'countOperateFrequency')
        }
    }
    mapping = type_mapping.get(variable_type, {})

    tempReportType = "未知"
    if report_type == 1:
        tempReportType = "班次"
    if report_type == 2:
        tempReportType = "日"
    if report_type == 3:
        tempReportType = "周"
    if report_type == 4:
        tempReportType = "月"

    # 处理result_types为空的情况
    if not result_types:
        result_types = list(mapping.keys())

    # 构建动态表头
    dynamic_headers = []
    dynamic_fields = []
    for rt in sorted(result_types):  # 按类型编号排序
        if rt in mapping:
            header, field = mapping[rt]
            dynamic_headers.append(header)
            dynamic_fields.append(field)

    # 添加固定尾部字段
    tail_headers = ["报表生成时间"]
    tail_fields = ["createTime"]
    if report_type is not None:
        tail_headers.append("报表类型")
        tail_fields.append("type")

    # 合并所有字段
    all_headers = base_headers + dynamic_headers + tail_headers
    all_fields = base_fields + dynamic_fields + tail_fields

    # 生成Markdown表格
    header_line = "|" + "|".join(all_headers) + "|"
    separator_line = "|" + "|".join([":---:"] * len(all_headers)) + "|"

    data_lines = []
    var_info_detail_return = []
    for item in data_list:
        startTimeAndEndTime = f"{item.get('startTime', '')}-{item.get('endTime', '')}"
        row_data = [
            item.get('name', ''),
            startTimeAndEndTime,
            item.get('groupName', ''),
            item.get('desc', ''),
            item.get('controllerName', '')
        ]
        var_info_detail_return_item = {"name": item.get('name', ''), "startTimeAndEndTime": startTimeAndEndTime,
                                       "groupName": item.get('groupName', ''),
                                       "controllerName": item.get('controllerName', ''),
                                       "createTime": item.get('createTime', '')}
        for field in dynamic_fields:
            tempValue = str(item.get(field, ''))
            row_data.append(tempValue)
            var_info_detail_return_item[field] = tempValue

        row_data.append(item.get('createTime', ''))
        var_info_detail_return.append(var_info_detail_return_item)

        if report_type is not None:
            row_data.append(tempReportType)

        data_lines.append("|" + "|".join(row_data) + "|")

    var_detail_table = f"{header_line}\n{separator_line}\n" + "\n".join(data_lines)

    return {"var_detail_table": var_detail_table, "var_info_detail_return": var_info_detail_return}


def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }
