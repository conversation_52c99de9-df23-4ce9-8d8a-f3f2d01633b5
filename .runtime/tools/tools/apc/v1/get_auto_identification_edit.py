from __runner__ import tool, Context
import os
import requests
import json
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为APC_Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息

# 对模型数据信息进行操作
# add by liupeng 2025-05-14
@tool(version="*")
async def get_auto_identification_edit(context: Context, params: any):
    modelName = params['modelName']  # 模型名称
    tmp_params = {'modelName': modelName}
    # 根据模型名称对数据进行查询
    # dataList = await context.call_tool("get_configuration", params=tmp_params)
    dataList = get_configuration()
    if len(dataList) > 0:
        return dataList
    else:
        return modelName + ' 模型数据查询失败！'

def get_configuration():
    response = requests.post(
        url=APC_URL + '/inter-api/apc-project/v1/configuration/list'
        , json={
            'selectType': 0
        }
        , headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    return json_obj['data']
