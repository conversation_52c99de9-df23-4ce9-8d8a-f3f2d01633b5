from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv
load_dotenv()

# 主要实现的功能是 APC性能监控控制器查询  实时值页面
# 首先需要获取全厂ID
# add by liupeng 2025-04-08
@tool(version="*")
async def get_watch_realtime_monitor_controller_list(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    countType = 0
    if 'countType' in params:
        countType = params["countType"]  # 装置名称(用户输入)
    groupType = 7  # 写死
    controllerNames = []
    if 'controllerNames' in params:
        controllerNames = params['controllerNames']

    deviceName = ''
    if 'deviceName' in params:
        deviceName = params['deviceName']
    if deviceName == '':
        deviceName = '全厂'

    deviceParam = {'deviceName' : deviceName}
    try:
        # params = {'groupType': groupType}
        # response_config = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all'
        #                                , params=params, headers={
        #         'Content-Type': 'application/json',
        #         'Authorization': APC_AGENT_TOKEN,
        #         'X-TPT-TOKEN' : X_TPT_TOKEN,
        #         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        #     })
        # json_obj_config = json.loads(response_config.text)
        sysGroupId = await context.call_tool("get_device_id_by_name", params=deviceParam)  # 根据用户输入的装置名称对装置ID进行获取
        if sysGroupId > 0:
            # 根据全厂id对控制器数据进行查询
            params_controller = {'sysGroupId': sysGroupId, 'countType': countType}
            response_controller = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/monitor/device/controller/select'
                                               , params=params_controller, headers={
                    'Content-Type': 'application/json',
                    'Authorization': APC_AGENT_TOKEN,
                    'X-TPT-TOKEN' : X_TPT_TOKEN,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
                })
            json_obj_controller = json.loads(response_controller.text)
            #return json_obj_controller['list']
            controllerRunDetail = controller_run_detail(json_obj_controller['list'], countType, controllerNames)
            if isinstance(controllerRunDetail, dict):
                return controllerRunDetail

            await context.add_view({
                "format": "card",
                "content": {
                    "type": 'markdown',
                    "title": 'APC控制器性能监控查询',
                    "description": "APC控制器性能监控查询",
                    "details": f"""
{controllerRunDetail}
"""
                }
            })

            controllerJsonList = controller_run_detail_return(json_obj_controller['list'],controllerNames)
            return format_response(
                success=True,
                data=controllerJsonList
            )
        else:
            return format_response(
                success=False,
                message = "APC控制器性能监控查询和控制器状态查询失败"
            )
    except Exception as e:
        # print("APC控制器性能监控查询和控制器状态查询失败:APC_AGENT_URL:"+APC_AGENT_URL + str(e))
        raise Exception(f"APC控制器性能监控查询和控制器状态查询失败，错误信息:"+str(e))
        #return {"error": "控制器实时指标数据查询失败,参数信息:"+DeviceName}
        pass



def get_count_type(count_type):
    """
    根据统计类型返回对应的时间单位
    :param count_type: 1-班次 2-日 3-周 4-月
    :return: 对应的时间单位字符串
    """
    count_type_mapping = {
        0: "实时",
        1: "班次",
        2: "日",
        3: "周",
        4: "月"
    }
    return count_type_mapping.get(count_type, "未知")




def filter_controllers_by_name(controller_list, controllerNames):
    """
    根据controllerNames集合筛选控制器对象
    :param controller_list: 原始控制器列表 [{'controllerName': 'CTRL1'}, ...]
    :param controllerNames: 要匹配的控制器名称集合（允许None/空集合/空字符串）
    :return: 包含匹配对象的新列表（当controllerNames为空时返回原列表的副本）
    """
    if not controllerNames:  # 处理None/空集合/空字符串等情况
        return controller_list.copy()  # 返回副本避免修改原列表

    # 统一处理字符串输入（兼容单个名称的情况）
    names_set = {controllerNames} if isinstance(controllerNames, str) else set(controllerNames)

    return [controller for controller in controller_list
            if controller.get('controllerName') in names_set]

## 装置下---控制器指标信息
def controller_run_detail(controller_run_detail_list, countType,controllerNames):
    controller_run_details = ''
    if controller_run_detail_list:
        if controllerNames:
            controller_run_detail_list = filter_controllers_by_name(controller_run_detail_list,controllerNames)
        if not controller_run_detail_list:
            return ""

        for index, s in enumerate(controller_run_detail_list, start=1):
            statusDesc = get_status_description(s.get('status', ''))
            generalPerformance = grade_performance(s.get('generalPerformance', ''))
            controller_run_details = controller_run_details + (
                f"|{index}"
                f"|{s.get('controllerName', '')}"
                f"|{statusDesc}"
                f"|{generalPerformance}"
                f"|{s.get('desc', '')}"
                f"|{s.get('apcOperationFrequency', '')}"
                f"|{s.get('controllerOperationalRate', '')}({s.get('controllerOperationalRateChange', '')})"
                f"|{s.get('controllerEffectiveOperationalRate', '')}({s.get('controllerEffectiveOperationalRateChange', '')})"
                f"|{s.get('mvOperationalRate', '')}({s.get('mvOperationalRateChange', '')})"
                f"|{s.get('mvOperationalPercent', '')}({s.get('mvOperationalPercentChange', '')})"
                f"|{s.get('cvOperationalPercent', '')}({s.get('cvOperationalPercentChange', '')})"
                f"|{s.get('cvOperationalRate', '')}({s.get('cvOperationalRateChange', '')})"
                f"|{s.get('mvStuckLimitRate', '')}({s.get('mvStuckLimitRateChange', '')})"
                f"|{s.get('cvOverLimitRate', '')}({s.get('cvOverLimitRateChange', '')})\r\n"
            )

    tempCountType = get_count_type(countType)
    controller_run_detail_table = f"""
## 控制器指标信息-{tempCountType}     
| 序号| 控制器名称 | 控制器状态 | 综合性能等级 |控制器描述 | 操作频次 | 投运率 |有效投运率 | mv投运率 | mv投运个数占比 | cv投运率 | cv投运个数占比 | mv卡限率 | cv超限率 |
|:--:|:--------:|:--------:|:----------:|:-------:|:------:|:-----:|:-------:|:-------:|:------------:|:-------:|:-----------:|:--------:|:-------:|
{controller_run_details}
"""
    return controller_run_detail_table


## 装置下---控制器指标信息
def controller_run_detail_return(controller_run_detail_list,controllerNames):
    controller_run_details = ''
    if controller_run_detail_list:
        if controllerNames:
            controller_run_detail_list = filter_controllers_by_name(controller_run_detail_list,controllerNames)
        if not controller_run_detail_list:
            #return {"error":"无此控制器("+controllerName+")"}
            return []
        for index, s in enumerate(controller_run_detail_list, start=1):
            statusDesc = get_status_description(s.get('status', ''))
            generalPerformance = grade_performance(s.get('generalPerformance', ''))
            s["generalPerformance"] = str(generalPerformance)  # 保持字符串类型
            s["status"] = str(statusDesc)
    return controller_run_detail_list



#根据字符串或数字分数返回性能评级
def grade_performance(score):
    """
    根据字符串或数字分数返回性能评级
    参数:
        score (str/int/float): 待评分的数值(支持"66.67"字符串格式)
    返回:
        str: 优/良/中/差
    异常:
        ValueError: 当输入无法转换为数字或超出0-100范围时
    """
    try:
        num = float(score) if isinstance(score, str) else float(score)
    except (ValueError, TypeError):
        return score

    if 90 <= num <= 100:
        return "优"
    elif 80 <= num < 90:
        return "良"
    elif 60 <= num < 80:
        return "中"
    elif 0 <= num < 60:
        return "差"
    else:
        return '-'

#获取状态码对应的描述信息
def get_status_description(status):
    """
    获取状态码对应的描述信息
    参数:
        status (int/str): 状态值(0/1/2)
    返回:
        str: 状态描述
    异常:
        ValueError: 当输入不是有效状态码时
    """
    status_mapping = {
        0: "关闭",
        1: "开启",
        2: "条件剔除"
    }

    try:
        # 处理字符串类型的数字输入
        status_code = int(status)
    except (ValueError, TypeError):
        raise ValueError("状态码必须是数字或数字字符串")

    if status_code in status_mapping:
        return status_mapping[status_code]
    else:
        return '-'

def format_response(data=None, success=True, message=""):
    if data is None:
        data = []
    return {
        "success": success,
        "message": message,
        "data": data
    }