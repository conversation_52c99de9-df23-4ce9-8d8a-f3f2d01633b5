from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息


# add by liupeng 2025-04-22
# 根据用户输入的精馏塔控制器名称对数据进行查询
@tool(version="*")
async def get_process_getGoalList(context: Context, params: any):
    adconName = params["adconName"]  # 全流程精馏塔控制器名称(用户输入)
    response = requests.get(url=APC_URL + '/inter-api/apc-project/v1/process/getGoalList'  # 获取全流程精馏塔控制器组态数据信息
                            , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_inner_token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj = json.loads(response.text)
    if 'status' in json_obj:
        return json_obj['message']
    else:
        dataList = [emp for emp in json_obj['list'] if emp["adconName"] == adconName]  # 全流程精馏塔控制器名称
        if len(dataList) > 0:
            # 获取全流程精馏塔控制器ID
            baseProjectId = dataList[0]['baseProjectId']  # 全流程精馏塔控制器ID
            return {'baseProjectId': baseProjectId, 'adconName': adconName}
        else:
            return {'baseProjectId': 0, 'adconName': adconName}
