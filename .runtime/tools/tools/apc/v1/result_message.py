from __runner__ import tool, Context
import random


# 根据KEY对数据进行查询
# add by liupeng 2025-06-04

@tool(private=True)
async def get_result_message(context: Context, params: any):
    messages = []
    message = {'message': '根据补充信息填写完成，可以点击【模型信息】查看小结信息。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '在填写完补充信息后，你可以点击【模型信息】来查看小结。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '根据提供的附加信息完成填写，之后通过点击【模型信息】按钮访问总结。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '一旦你根据补充数据填写完毕，就能点击指定区域查看概要信息。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '填写基于额外信息的表格后，选择【模型信息】选项以获取总结。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '完成补充信息的输入后，用户可通过点击【模型信息】来查阅小结。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '依据补充资料填写完成后，点击【模型信息】即可浏览摘要。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '在补充信息被完整填写后，点击【模型信息】部分以查看总结内容。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '填写补充信息完毕后，访问【模型信息】链接来获得小结。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '基于附加信息完成所有填写后，使用【模型信息】功能查看概要。', 'key': '模型信息'}
    messages.append(message)
    message = {'message': '当所有补充信息都输入完成后，通过点击【模型信息】来显示总结。', 'key': '模型信息'}
    messages.append(message)

    message = {'message': '辨识模型矩阵参数信息已构造完成，以下为您提供位号历史数据模板说明和需要您填写的补充数据信息.',
               'key': '模型矩阵参数'}
    messages.append(message)
    message = {'message': '模型辨识矩阵参数已成功构建，接下来为您展示位号历史数据模板解析及所需补充的数据内容.',
               'key': '模型矩阵参数'}
    messages.append(message)
    message = {'message': '系统已完成辨识模型矩阵参数构造，现在提供给您位号历史数据格式说明以及待填写的额外数据项目.',
               'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '辨识模型的矩阵参数构造环节已完成，下方是位号历史数据模板的详细说明和您需要提供的补充信息表格.',
        'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '矩阵参数辨识模型构建工作已结束，以下分别是位号历史数据模板指引和需要您补充填写的相关数据字段.',
        'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '系统已构建完成辨识模型的矩阵参数，接下来请您参考位号历史数据模板说明，并完成补充数据的填写工作.',
        'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '辨识模型的矩阵参数信息构造已完毕，随附提供的是位号历史数据的模板指南和等待您填写的补充数据表单.',
        'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '系统完成了辨识模型矩阵参数的构建过程，现向您提供位号历史数据的模板解释和需要您输入的附加数据说明.',
        'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '辨识模型矩阵参数已全部构建完毕，下方包含位号历史数据的模板解读以及您需要补充提交的数据信息清单.',
        'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '模型辨识的矩阵参数信息已构建完整，现在为您呈现位号历史数据模板的使用说明及所需填写的补充数据部分.',
        'key': '模型矩阵参数'}
    messages.append(message)
    message = {
        'message': '系统已构造完成辨识模型的矩阵参数结构，以下是位号历史数据模板的详细说明与您需要填写的补充数据要求.',
        'key': '模型矩阵参数'}
    messages.append(message)

    message = {'message': '辨识过程已完成，已为您对辨识过程信息进行小结，您可点击【辨识过程信息展示】查看辨识过程信息.',
               'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '系统辨识过程已顺利完成，我们已将辨识全过程要点进行了归纳提炼，您可通过点击【辨识过程信息展示】查阅完整辨识历程.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '辨识过程工作圆满结束，平台已为您整理了完整的辨识流程概要，请点击【辨识过程信息展示】浏览全过程数据与分析.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '模型辨识环节已成功执行，系统已自动汇总了关键辨识信息，您可点击【辨识过程信息展示】获取详细辨识记录.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '辨识过程任务处理完毕，我们已对整个辨识链路进行了系统梳理，点击【辨识过程信息展示】即可查看完整操作与结果.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {'message': '辨识分析已全部完成，平台已自动生成辨识过程摘要报告，请点击【辨识过程信息展示】访问完整辨识历程.',
               'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '系统过程已完成全部辨识计算，并已为您准备了辨识流程的关键节点总结，点击【辨识过程信息展示】了解完整辨识情况.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '辨识过程程序运行结束，我们已将辨识各阶段信息整合为简明概览，您可通过【辨识过程信息展示】查询完整执行细节.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '模型辨识过程已处理完成，平台已生成辨识步骤与结果的综合说明，请点击【辨识过程信息展示】获取全部辨识过程.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '辨识过程工作已全部结束，系统已为您准备了辨识全流程的关键信息摘要，点击【辨识过程信息展示】可访问详细辨识数据.',
        'key': '辨识过程已完成'}
    messages.append(message)
    message = {
        'message': '模型参数辨识过程已完成，我们已对整个辨识过程进行了要点提炼，您可点击【辨识过程信息展示】阅览完整辨识信息.',
        'key': '辨识过程已完成'}
    messages.append(message)

    message = {
        'message': '最后，根据用户对模型结果进行确认后生成辨识结果文件和辨识报告，以下为您提供【辨识结果和辨识报告说明】查看、【辨识结果】预览和下载、【辨识报告】预览和下载.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '流程终点，当您对模型性能评估结束后，平台将输出辨识模型文件和专业评估报告，我们为您准备了【辨识结果和辨识报告说明】阅读、【辨识结果】查看和下载、【辨识报告】浏览和下载.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '工作完成后，根据您的模型质量评估，平台将生成最终辨识参数集和专业评估报告，为您提供【辨识结果和辨识报告说明】参考、【辨识结果】查看与导出、【辨识报告】浏览与存储.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '在您完成模型验证环节后，系统将自动创建辨识成果文件和详尽分析报告，您可访问【辨识结果和辨识报告说明】参考、【辨识结果】在线查看与下载、【辨识报告】预览与下载.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '最终，在用户验证模型表现后，系统将生成辨识结果数据集和分析报告，您可以通过【辨识结果和辨识报告说明】了解、【辨识结果】在线查看和存储、【辨识报告】浏览和下载.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '完成后，待您审核模型输出数据并确认无误，平台会自动创建辨识成果文件与详细报告，我们为您提供【技术文档说明】阅览、【模型数据】查看与导出、【模型报告】预览与导出.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '最后一步，根据您对辨识精度的评估与接受，系统将输出最终的模型参数文件和综合分析报告，您可以通过【参数说明文档】参考、【模型结果文件】预览及下载、【分析总结报告】查看及下载.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '在您确认模型拟合效果达到要求后，平台会生成标准格式的辨识输出文件和详尽评估报告，为您准备了【格式说明】查阅、【辨识数据】浏览和获取、【报告文档】阅读和导出.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '处理完成，您对模型性能评估满意后，系统将创建结构化的辨识结果与专业分析报告，您可访问【辨识结果和辨识报告说明】了解、【辨识结果】在线预览并下载、【辨识报告】查看和下载.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '流程结束，当您核准了模型输出质量，平台将自动生成辨识数据包和完整技术报告，我们提供【辨识结果和辨识报告说明】查看、【辨识结果】浏览与下载、【辨识报告】阅读与下载.',
        'key': '结果输出'}
    messages.append(message)
    message = {
        'message': '最后阶段，待您确认模型表现符合预期，系统会输出标准辨识结果文件和综合分析报告，您可以查阅【辨识结果和辨识报告说明】、预览并下载【辨识结果】、浏览并下载【辨识报告】.',
        'key': '结果输出'}
    messages.append(message)

    # message = {
    #     'message': '最后，根据用户对模型结果进行确认后生成辨识结果文件和辨识报告，以下为您提供【辨识报告】预览和下载.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '流程终点，当您对模型性能评估结束后，平台将输出辨识模型文件和专业评估报告，我们为您准备了【辨识报告】浏览和下载.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '工作完成后，根据您的模型质量评估，平台将生成最终辨识参数集和专业评估报告，为您提供【辨识报告】浏览与存储.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '在您完成模型验证环节后，系统将自动创建辨识成果文件和详尽分析报告，您可访问【辨识报告】预览与下载.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '最终，在用户验证模型表现后，系统将生成辨识结果数据集和分析报告，您可以通过【辨识报告】浏览和下载.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '完成后，待您审核模型输出数据并确认无误，平台会自动创建辨识成果文件与详细报告，我们为您提供【辨识报告】预览与导出.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '最后一步，根据您对辨识精度的评估与接受，系统将输出最终的模型参数文件和综合分析报告，您可以通过【辨识报告】查看及下载.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '在您确认模型拟合效果达到要求后，平台会生成标准格式的辨识输出文件和详尽评估报告，为您准备了【辨识报告】阅读和导出.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '处理完成，您对模型性能评估满意后，系统将创建结构化的辨识结果与专业分析报告，您可访问【辨识报告】查看和下载.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '流程结束，当您核准了模型输出质量，平台将自动生成辨识数据包和完整技术报告，我们提供【辨识报告】阅读与下载.',
    #     'key': '结果输出'}
    # messages.append(message)
    # message = {
    #     'message': '最后阶段，待您确认模型表现符合预期，系统会输出标准辨识结果文件和综合分析报告，您可以查阅并下载【辨识报告】.',
    #     'key': '结果输出'}
    # messages.append(message)

    message = {
        'message': '辨识模型矩阵参数信息已输入完成，需要您进一步对辨识模型矩阵参数信息进行确认.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '辨识模型矩阵参数录入工作已完成，请您对参数信息进行最终确认.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '模型矩阵参数配置已录入系统，需由您进行参数校验与确认.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '您好，辨识模型的矩阵参数信息已全部输入完毕，请您审核确认这些参数设置.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '当前步骤已完成模型矩阵参数的输入，请继续执行参数确认操作.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '任务提醒：辨识模型矩阵参数输入已完成，请及时进行参数确认以避免影响后续流程.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '模型矩阵参数录入完毕，请进行最终确认.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '辨识模型的矩阵参数信息已完整输入，请您核查确认.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '模型矩阵参数输入已完成，下一步需您审阅并确认这些信息.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '模型矩阵参数信息输入工作已告一段落，恳请您予以确认.',
        'key': '模型矩阵确认'}
    messages.append(message)
    message = {
        'message': '当前步骤：模型矩阵参数输入完成。下一步：请您确认参数信息.',
        'key': '模型矩阵确认'}
    messages.append(message)

    message = {
        'message': '辨识模型数据已生成，需要您进一步对辨识结果进行确认，并生成模型结果数据文件和辨识结果报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '辨识模型计算已完成，当前结果已生成，请进行最终确认并导出浏览模型数据文件及辨识报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '模型辨识数据已就绪，请检查结果并生成对应的模型文件及分析报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '模型辨识已完成，请确认结果并导出模型数据与辨识报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '模型数据已生成 → 下一步：请审核结果并生成最终文件.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '辨识任务执行完毕，结果已就绪，请确认并生成模型输出文件及分析报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '模型计算完成，结果文件待生成，请确认后输出模型数据与辨识报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '模型辨识过程已完成，结果数据已生成，请进行审核并输出最终模型文件及分析报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '当前阶段：模型辨识完成 → 请确认结果并生成标准化的模型数据文件及辨识报告.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '模型辨识完成，请确认结果并生成相关文件.',
        'key': '辨识结果确认'}
    messages.append(message)
    message = {
        'message': '辨识结果已生成，请检查并导出模型文件及报告.',
        'key': '辨识结果确认'}
    messages.append(message)

    result = [item for item in messages if item["key"] == params["key"]]
    # 随机选择一个字典
    random_message = random.choice(result)
    return random_message['message']
