from __runner__ import tool, Context
from datetime import datetime
from furl import furl


# 控制方案-控制合理性检查
@tool(private=True)
async def get_control_rationality_check(context: Context, params: any):
    # TPT_URL = context.config["TPT_URL"]
    cv_num = params['matrix_data']['cv_num']  # cv个数
    mv_num = params['matrix_data']['mv_num']  # mv个数
    dv_num = params['matrix_data']['dv_num']  # dv个数
    cv_list = params['matrix_data']['cv_list'],  # CV集合数据
    mv_list = params['matrix_data']['mv_list'],  # MV集合数据
    dv_list = params['matrix_data']['dv_list'],  # DV集合数据
    matrix = params['data_information']['modelRelationshipOut']  #
    suggestion_flag = params['data_information']['suggestionFlag']  #

    result = await context.get_interaction("get_control_rationality_check")
    if result is None:
        # id，交互标识，区别同函数中的多个交互
        # mode，选择模式 multiple/single，默认 single
        # data, 与选项同时返回，方便代码处理选项
        await context.add_view({
            "format": "markdown",
            "content": "我已完成方案相关变量的数据分析，我将结合变量间模型关系及工艺数据，完成数据波动性检验、时滞相关性及因果检验、阀门粘滞检测，接着进行控制器结构的合理性检查，根据检查校验后的优化建议对控制器结构进行更新完善。"
        })
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '正在进行控制合理性检查',
                "description": ""
            }
        })
        f = furl()
        f.path = '/xpt-tpt-apc/controlScheme'
        f.args['theme'] = 'dark'
        f.args['cv_num'] = cv_num  # cv个数
        f.args['mv_num'] = mv_num  # mv个数
        f.args['dv_num'] = dv_num  # dv个数
        f.args['cv_list'] = str(cv_list).replace(" ", "")  #
        f.args['mv_list'] = str(mv_list).replace(" ", "")  #
        f.args['dv_list'] = str(dv_list).replace(" ", "")  #
        f.args['matrix'] = str(matrix).replace(" ", "")  #
        f.args['suggestionflag'] = str(suggestion_flag).replace(" ", "")  #
        f.args['isview'] = False
        await context.add_view({
            "format": "card",
            "content": {
                "type": 'page',
                "title": '修改后的控制器结构',
                "description": "",
                "details": f.url
            }
        })
        context.require_interaction({
            "id": "get_control_rationality_check",
            "title": "控制合理性检查",
            "type": "select",
            "mode": "single",
            "select": [
                {
                    "title": "接受修改建议",
                    "data": 0,
                },
                {
                    "title": "查看并修改详细修改建议",
                    "data": 1,
                }
            ]
        })
        return {

        }
    else:
        tmp_result = {
            'data': result[0]['data']
        }
        return tmp_result
