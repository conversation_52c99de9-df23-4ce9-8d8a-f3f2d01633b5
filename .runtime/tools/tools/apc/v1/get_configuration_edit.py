from __runner__ import tool, Context
import requests
import json
import os
from furl import furl
from dotenv import load_dotenv

load_dotenv()

APC_inner_token = os.getenv('APC_inner_token')  # 读取env文件中的key值为Inner_token的值
APC_URL = os.getenv("APC_URL")  # 读取网址信息


# 根据控制器名称进行组态新增操作(仅适合全流程操作)
# add by liupeng 2025-04-22
@tool(version="*")
async def get_configuration_edit(context: Context, params: any):
    baseProjectId = params["baseProjectId"]  # 控制器组态的ID
    devicenumber = params["devicenumber"]  # 用户输入的设备编号、装置编号
    # 打开page
    # 创建 URL 对象并修改各部分
    f = furl()
    f.path ='/xpt-tpt-apc/guidance/distillation/' + baseProjectId
    f.args['apc-token'] = APC_inner_token
    f.args['devicenumber'] = devicenumber #设备编号、装置编号
    result = await context.get_interaction("open_page")
    if result is None:
        context.require_interaction({
            "id": "open_page",
            "title": "打开页面",
            "type": "open_page",
            "open_page": f.url
        })
        return {}
    else:
        return {
            "output": result
        }
