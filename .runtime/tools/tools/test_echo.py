from __tester__ import load_module, run
from __runner__ import Context

greet_module = load_module("demo/1.0.0/greet.py")

context = Context()
context.config = {"key": "value"}
params = {"name": "ronbb"}
expected_result = {"output": "hello, ronbb"}

result = run(greet_module.greet, context, params)
assert result == expected_result

# 测试不传context参数
params = {"name": "world"}
expected_result = {"output": "hello, world"}
result = run(greet_module.greet, params=params)
assert result == expected_result

print("测试通过!")
