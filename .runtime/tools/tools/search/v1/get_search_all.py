from __runner__ import tool, Context
import requests
import json
import os

# 主要实现的功能是 调用大语言实现搜索功能
# add by liupeng 2025-06-17
@tool(private=True)
async def get_search_all(context: Context, params: any):
    # response = requests.post(url=APC_URL + '/tpt/tag/importHistoryValue'
    #                          , json=params
    #                          , headers={
    #         'Content-Type': 'application/json',
    #         'Cookie': 'tenant-id=0',
    #         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
    #     })
    return {
        'code':200,
        'message':'测试数据信息成功'
    }
