{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "get_search_all", "description": "跟大语言对接，实现搜索功能", "params": {"type": "object", "description": "跟大语言对接，实现搜索功能", "properties": {"name": {"type": "string", "description": "变量名称"}}, "required": []}, "result": {"type": "object", "description": "搜索结果输出", "properties": {"list": {"type": "sting", "description": "结果输出"}}, "required": ["list"]}, "alias": ["搜索"], "catalog": "statistics"}]}