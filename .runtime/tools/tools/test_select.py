from __tester__ import load_module, run
from __runner__ import Context, InteractionError

module = load_module("demo/1.0.0/select.py")

context = Context()

has_interaction = False

try:
    result = run(module.select, context, {})
except InteractionError as e:
    print(f"Interaction: {e}")
    has_interaction = True

assert has_interaction, "Interaction error was expected but not raised."

context.cache["select"] = [1, 2]

result = run(module.select, context, {})

assert result == { "output": [1, 2] }, f"Expected [1, 2], but got {result}"

