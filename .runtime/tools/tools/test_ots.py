import time
import pytest

from time import sleep

from __tester__ import load_module, run
from __runner__ import Context

readvalue = load_module("ots/v1/readvalue.py").readvalue
writevalue = load_module("ots/v1/writevalue.py").writevalue
startmodel = load_module("ots/v1/startmodel.py").startmodel
stopmodel = load_module("ots/v1/stopmodel.py").stopmodel
loadmodel = load_module("ots/v1/loadmodel.py").loadmodel
getmodelinfo = load_module("ots/v1/getmodelinfo.py").getmodelinfo
loadsnapshot = load_module("ots/v1/loadsnapshot.py").loadsnapshot

@pytest.fixture
def context():
    ctx = Context()
    ctx.config = {"key":"value"}
    return ctx

# context = Context()
# context.config = {"key": "value"}

def test_ots_workflow(context, capsys):
    result = run(getmodelinfo, context, params={})
    print(result)
    sleep(1)
    result = run(loadmodel, context, params={"modelname":"B1"})
    print(result)
    sleep(1)
    result = run(getmodelinfo, context, params={})
    print(result)
    sleep(1)
    result = run(startmodel, context, params={})
    print(result)
    sleep(1)
    result = run(getmodelinfo, context, params={})
    print(result)
    sleep(1)
    params = {"tagnames": ["S868.MFT", "T602.P[0]", "T602.T[0]", "S856.T", "T602.T[26]", "T602.LH[26]"]}
    result = run(readvalue, context, params)
    print(result)
    sleep(1)
    result = run(loadsnapshot, context, params={"index":"user_2019_04_14_08_39_41_0748.se"})
    print(result)
    sleep(1)
    result = run(getmodelinfo, context, params={})
    print(result)
    sleep(1)
    params = {"tagnames": ["S868.MFT", "T602.P[0]", "T602.T[0]", "S856.T", "T602.T[26]", "T602.LH[26]"]}
    result = run(readvalue, context, params)
    print(result)
    sleep(1)
    params = {"tags":[
        {"tagName":"S856.T", "dataType":2, "value":388},
        {"tagName":"S868.MFT", "dataType":2, "value":0.01},
        {"tagName":"T602.P[0]", "dataType":2, "value":474765.0},
        {"tagName":"T602.T[0]", "dataType":2, "value":317.0},
        {"tagName":"T602.T[26]", "dataType":2, "value":371.0},
        {"tagName":"T602.LH[26]", "dataType":2, "value":0.01}
    ]}
    result = run(writevalue, context, params)
    print(result)
    sleep(1)
    params = {"tagnames": ["S868.MFT", "T602.P[0]", "T602.T[0]", "S856.T", "T602.T[26]", "T602.LH[26]"]}
    result = run(readvalue, context, params)
    print(result)
    sleep(1)
    result = run(stopmodel, context, params={})
    print(result)
    sleep(1)
    result = run(getmodelinfo, context, params={})
    print(result)
    sleep(1)

    params = {"tagnames": ["S868.MFT", "T602.P[0]", "T602.T[0]", "S856.T", "T602.T[26]", "T602.LH[26]"]}
    result = run(readvalue, context, params)
    print(result)
    sleep(1)
    print("测试通过!")

    # 断言最后一步的结果不为空（根据实际情况调整）
    assert result is not None