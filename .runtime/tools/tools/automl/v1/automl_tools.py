from __runner__ import tool, Context,validator
import requests
import json
import os
import tempfile
from pathlib import Path
import pypandoc

#调用LLM进行润色总结
@tool(private=True)
async def get_llm_summary(context: Context, params: any):
    success = 1
    llm_resp = ""
    try:
        llm_params = {
            "model": context.config["llm_model_file"],
            "messages": [{
                "role": "user",
                "content": params["prompt_str"]
            }]
        }
        response = requests.post(
            url= context.config["llm_api_url"], 
            data=json.dumps(llm_params, indent=2, ensure_ascii=False), 
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
        if response.status_code == 200:
            llm_resp = response.json().get("choices")[0].get("message").get("content")
        else:
            success = -1
            llm_resp = response.text 
    except Exception as e:
         success = -1
         llm_resp = "调用LLM总结出现异常:" + str(e)
    output_result  = {
        "success":success,
        "llm_resp":llm_resp
    }
    return output_result

#使用pypandoc进行markdown转docx
@tool(private=True)
async def markdown_to_docx_file(context: Context, params: any):
    success = 1
    try:
        with tempfile.NamedTemporaryFile(suffix='.docx',dir='./', delete=False) as temp_file:
                temp_path = temp_file.name
        # 模板路径（所有的公用内容均在automl/v1文件夹中）
        # 获取当前脚本所在目录
        current_script_path = os.path.dirname(os.path.abspath(__file__))
        template_file_path = os.path.join(current_script_path,"target_template.docx")
        #temp_path =  os.path.join(current_script_path,"report.docx")
        pypandoc.convert_text(
                params["mk_str"],
                'docx',
                format='md',
                outputfile= temp_path,  # 返回字节数据而不是文件
                extra_args=[f"--reference-doc={template_file_path}"]
            )
        with open(temp_path, 'rb') as f:
                docx_data = f.read()
        # 清理临时文件
        os.unlink(temp_path)
    except Exception as e:
        success = -1
        docx_data = str(e)
        if os.path.exists(temp_path):
             os.unlink(temp_path)

    output_result  = {
        "success":success,
        "file_info":docx_data
    }
    return output_result