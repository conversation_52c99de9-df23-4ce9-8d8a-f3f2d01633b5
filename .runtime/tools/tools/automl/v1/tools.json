{"$schema": "../../../schema/tools.schema.json", "tools": [{"skip_summary": true, "alias": ["建模问题定义与标准化", "建模问题与标准化"], "name": "aml_question_definition_standard", "catalog": "prediction", "description": "跟据用户提问的问题，识别问题的场景:优化/预警/预测/回归等任务，并从中提取关键信息", "params": {"type": "object", "description": "", "required": ["task_type", "domain_knowledge", "target_variables", "task_objective_description", "user_specified_input_variables", "specified_algorithms", "prediction_input_time_period", "prediction_output_time_period"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "根据用户问题判断任务类型，其中0表示RTO,优化，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；异常检测任务关键词：“检测历史异常情况”，“检测异常”，“历史是否存在异常值”，“识别异常/故障”，“离群点分析”等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "target_variables": {"type": "array", "description": "目标变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称,如果没有为空"}, "description": {"type": "string", "description": "变量描述"}}}}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "user_specified_input_variables": {"type": "array", "description": "指定输入变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称,如果没有为空"}, "description": {"type": "string", "description": "变量描述"}}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型,默认为0"}, "prediction_input_time_period": {"type": "number", "description": "预测需要输入的数据时间长度（分钟）,默认10分钟"}, "prediction_output_time_period": {"type": "number", "description": "预测输出的数据时间长度（分钟），默认2分钟"}}}, "result": {"type": "object", "description": "返回用户确认补全后的信息", "required": ["task_type", "domain_knowledge", "target_variables", "task_objective_description", "user_specified_input_variables", "specified_algorithms", "prediction_input_time_period", "prediction_output_time_period", "train_device_type"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "target_variables": {"type": "array", "description": "目标变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "user_specified_input_variables": {"type": "array", "description": "指定输入变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型。"}, "prediction_input_time_period": {"type": "number", "description": "预测需要输入的数据时间长度（分钟）"}, "prediction_output_time_period": {"type": "number", "description": "预测输出的数据时间长度（分钟）"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}}}}, {"skip_summary": true, "alias": ["建模数据上传"], "name": "aml_offline_file_upload", "catalog": "prediction", "description": "上传需要的位号的历史数据文件，csv格式", "params": {"type": "object", "description": "上传成功后返回的文件数据", "required": ["task_type", "domain_knowledge", "target_variables", "task_objective_description", "user_specified_input_variables", "specified_algorithms", "prediction_input_time_period", "prediction_output_time_period", "train_device_type"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "target_variables": {"type": "array", "description": "目标变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "user_specified_input_variables": {"type": "array", "description": "指定输入变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型。"}, "prediction_input_time_period": {"type": "number", "description": "预测需要输入的数据时间长度（分钟）"}, "prediction_output_time_period": {"type": "number", "description": "预测输出的数据时间长度（分钟）"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}}}, "result": {"type": "object", "description": "上传成功后返回的文件数据", "required": ["file", "task_type", "domain_knowledge", "target_variables", "task_objective_description", "user_specified_input_variables", "specified_algorithms", "prediction_input_time_period", "prediction_output_time_period", "file_variables", "recommended_tags", "train_device_type"], "properties": {"file": {"type": "object", "description": "用户上传csv文件存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "task_type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "target_variables": {"type": "array", "description": "目标变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "user_specified_input_variables": {"type": "array", "description": "指定输入变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型。"}, "prediction_input_time_period": {"type": "number", "description": "预测需要输入的数据时间长度（分钟）"}, "prediction_output_time_period": {"type": "number", "description": "预测输出的数据时间长度（分钟）"}, "file_variables": {"type": "array", "description": "数据集中所有变量的列表", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}}}}, {"skip_summary": true, "alias": ["建模参数预设"], "name": "aml_parse_file_upload", "catalog": "prediction", "description": "解析数据集文件，通过大模型识别目标变量、相关变量等信息", "params": {"type": "object", "description": "上传成功后返回的文件数据", "required": ["file", "task_type", "domain_knowledge", "target_variables", "task_objective_description", "user_specified_input_variables", "specified_algorithms", "prediction_input_time_period", "prediction_output_time_period", "file_variables", "recommended_tags", "train_device_type"], "properties": {"file": {"type": "object", "description": "用户上传csv文件存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "task_type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "target_variables": {"type": "array", "description": "目标变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "user_specified_input_variables": {"type": "array", "description": "指定输入变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型。"}, "prediction_input_time_period": {"type": "number", "description": "预测需要输入的数据时间长度（分钟）"}, "prediction_output_time_period": {"type": "number", "description": "预测输出的数据时间长度（分钟）"}, "file_variables": {"type": "array", "description": "数据集中所有变量的列表", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}}}, "result": {"type": "object", "description": "通过大模型解析数据集数据，识别TPT模型微调需要的参数信息并确认", "required": ["file", "task_type", "target_variables", "specified_algorithms", "prediction_input_time_period", "prediction_output_time_period", "input_variables", "train_device_type", "recommended_tags"], "properties": {"file": {"type": "object", "description": "用户上传csv文件存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "task_type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归"}, "target_variables": {"type": "array", "description": "目标变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型。"}, "prediction_input_time_period": {"type": "number", "description": "预测需要输入的数据时间长度（分钟）"}, "prediction_output_time_period": {"type": "number", "description": "预测输出的数据时间长度（分钟）"}, "input_variables": {"type": "array", "description": "数据集中所有变量的列表", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}}}}, {"skip_summary": true, "alias": ["模型微调训练"], "name": "aml_exec_file_upload", "catalog": "prediction", "description": "根据识别的参数进行TPT模型微调", "params": {"type": "object", "description": "模型微调需要的参数信息", "required": ["file", "task_type", "target_variables", "specified_algorithms", "prediction_input_time_period", "prediction_output_time_period", "input_variables", "train_device_type", "recommended_tags"], "properties": {"file": {"type": "object", "description": "用户上传csv文件存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "task_type": {"type": "integer", "enum": [0, 1, 2, 3, 4], "description": "任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归"}, "target_variables": {"type": "array", "description": "目标变量", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型。"}, "prediction_input_time_period": {"type": "number", "description": "预测需要输入的数据时间长度（分钟）"}, "prediction_output_time_period": {"type": "number", "description": "预测输出的数据时间长度（分钟）"}, "input_variables": {"type": "array", "description": "数据集中所有变量的列表", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}}}, "result": {"type": "object", "description": "根据任务类型，执行不同的算法接口，返回训练过程及结果", "required": ["output"], "properties": {"output": {"type": "object", "description": "模型训练结果及推理结果", "required": ["train_result", "inference"], "properties": {"train_result": {"type": "object", "description": "模型训练结果及推理结果", "required": [], "properties": {}}, "inference": {"type": "object", "description": "模型训练结果及推理结果", "required": [], "properties": {}}}}}}}]}