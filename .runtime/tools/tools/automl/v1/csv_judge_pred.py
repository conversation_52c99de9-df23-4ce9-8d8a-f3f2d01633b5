import json
from datetime import datetime
# import s3fs
import io
import pandas as pd
from __runner__ import tool, Context, validator

# fs = s3fs.S3FileSystem(
#     key='admin',  # 你的访问密钥
#     secret='Supcon1304',  # 你的秘密密钥
#     client_kwargs={
#         'endpoint_url': 'http://seak8sm2.supcon5t.com:30350'  # MinIO 服务地址
#     }
# )

@validator(version="*")
async def csv_judge_pred(context: Context, target: any):
    # file_path = "s3://"+file["bucket"]+"/"+ file["object"]
    # df = pd.read_csv(fs.open(file_path, 'rb'))
    # Check if the CSV has at least two rows
    file_info = json.loads(target)
    file = await context.get_file(file_info,"bytes") #字节格式，默认string，utf-8编码
    df = pd.read_csv(io.BytesIO(file))
    # df = pd.read_csv(fs.open(file_info, 'rb'))
    first_row = df.iloc[0]
    second_row = df.iloc[1]
    for col in df.columns:
        if not isinstance(first_row[col], str) or not isinstance(second_row[col], str):
            raise ValueError(f"第1或第2行中列'{col}'不是位号或位号名称")
    # 2. 取出除前两行之外的数据
    data_part = df.iloc[2:].reset_index(drop=True)
    if data_part.empty:
        raise ValueError("数据部分为空")
    # 3. 检查第一列是否可以转换为时间类型
    try:
        pd.to_datetime(data_part.iloc[:, 0])
    except Exception as e:
        raise ValueError("数据部分的第一列无法解析为时间") from e

    # 4. 检查其余列是否为浮点数
    for col in data_part.columns[1:]:
        try:
            # 尝试将每列转为 float 类型
            pd.to_numeric(data_part[col], errors='raise')
        except Exception as e:
            raise ValueError(f"列 '{col}' 数据格式有问题") from e

    if len(df) < 100:
        raise ValueError("数据必须至少含有100条数据")
    return "检验通过"


