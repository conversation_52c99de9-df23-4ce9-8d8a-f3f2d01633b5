import json.tool
from __runner__ import tool, Context,validator
import requests
import base64
import pandas as pd
import io
import json
import os
from datetime import datetime
import ast
import tempfile

# 建模问题定义与标准化
@tool(version="*")
async def aml_question_definition_standard(context: Context, params: any):
    formResult = await context.get_interaction("aml_question_define_form")
    if formResult is None:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "需要确认您的详细需求，请补充信息：",
                "description": "",
                "details": ""
            }
        })
        await context.add_view({
            "format": "markdown",
            "content":"经过模型的分析与识别获取了一些关键的信息，现在请确认这些信息，以便更好的生成解决方案。"
        })

        target_vars =params["target_variables"]
        target_describs = [item["description"] for item in target_vars if isinstance(item, dict) and "description" in item]
        target_vars_describ_str = "、".join(target_describs) if target_describs else ""

        user_specified_input_vars=params["user_specified_input_variables"]
        user_specified_input_vars_describs = [item["description"] for item in user_specified_input_vars if isinstance(item, dict) and "description" in item]
        user_specified_input_vars_describ_str = "、".join(user_specified_input_vars_describs) if user_specified_input_vars_describs else ""
        
        context.require_interaction({
            "id": "aml_question_define_form",
            "title": "关键信息补充与确认",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        # "industry": {
                        #     "title": "所属行业",
                        #     "type": "string"
                        # },
                        # "device_type": {
                        #     "title": "装置类型",
                        #     "type": "string"
                        # },
                        "target_variables": {
                            "title": "您希望重点预测的目标变量",
                            "type": "string"
                        },
                        # "prediction_input_time_period": {
                        #     "title": f"您希望样本多少时长的数据（分钟，非必填）",
                        #     "type": "number"
                        # },
                        #  "prediction_output_time_period": {
                        #     "title": f"您希望预测未来多少时长的数据（分钟，非必填）",
                        #     "type": "number"
                        # },
                        "user_specified_input_variables": {
                            "title": f"您认为对这些预测目标变量影响的因素有哪些？(非必填)",
                            "type": "string"
                        }
                    #    "specified_algorithms":{
                    #         "title": "默认使用TPT时序大模型进行微调训练，您是否需要使用传统的机器学习方法：",
                    #         "type": "integer",
                    #         "enum": [0, 1],
                    #         "enumNames": ["TPT时序大模型", "传统机器学习模型"],
                    #         "widget": "radio",
                    #     },
                    #     "train_device_type":{
                    #         "title": "默认使用GPU进行微调训练，您是否需要使用CPU：",
                    #         "type": "string",
                    #         "enum": ["gpu", "cpu"],
                    #         "enumNames": ["GPU", "CPU"],
                    #         "widget": "radio",
                        # },
                        # "train_device_type":{
                        #     "title": "默认使用GPU基于TPT时序大模型进行微调训练，您是否需要使用CPU：",
                        #     "type": "string",
                        #     "enum": ["gpu", "cpu"],
                        #     "enumNames": ["GPU", "CPU"],
                        #     "widget": "radio",
                        # },
                    },
                    "order":["industry","device_type","target_variables","prediction_input_time_period","prediction_output_time_period","user_specified_input_variables","specified_algorithms","train_device_type"],
                    "required": ["industry","device_type","target_variables"]
                },
                "default": {
                    "industry":params["domain_knowledge"]["industry"],
                    "device_type": params["domain_knowledge"]["device_type"],
                    "target_variables":target_vars_describ_str,
                    "user_specified_input_variables":user_specified_input_vars_describ_str,
                    # "prediction_input_time_period" : params["prediction_input_time_period"],
                    # "prediction_output_time_period": params["prediction_output_time_period"],
                    "prediction_input_time_period" : 10,
                    "prediction_output_time_period": 2,
                    "specified_algorithms" :0,
                    "train_device_type" : "gpu"
                }
            }
        })   
    else:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "请稍作等待，后续需要您上传数据",
                "description": "",
                "details": ""
            }
        })
        output_result = params
        target_vars_items  = formResult["target_variables"].split("、")
        user_specified_input_vars_items  = formResult["user_specified_input_variables"].split("、")
        output_result["domain_knowledge"] = {
            "industry":formResult["industry"],
            "device_type": formResult["device_type"]
        }
        output_result["target_variables"] = [{"tag": "", "description": item.strip()} for item in target_vars_items]
        output_result["user_specified_input_variables"]= [{"tag": "", "description": item.strip()} for item in user_specified_input_vars_items]
        output_result["prediction_input_time_period"] = formResult["prediction_input_time_period"]
        output_result["prediction_output_time_period"]= formResult["prediction_output_time_period"]
        output_result["specified_algorithms"] = formResult["specified_algorithms"]
        output_result["train_device_type"] = formResult["train_device_type"]

        prompt_str  =f"""
# 角色
你是一个专业的数据分析师，能够对输入的json数据进行精准解读与分析。你要根据json结构中的不同数据组合情况，按照对应的填充规则进行替换和总结分析，并确保总结内容总字数少于200字。

## 技能
### 技能 1: 分析json数据
1. 接收用户输入的json数据，数据结构如下：
{output_result}
2. 根据json数据中的不同情况进行分析总结：
    - 如果“prediction_output_time_period”与“user_specified_input_variables”都有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法预测[3]变量未来[4]分钟趋势，明确关键的影响因素为[5]。
    - 如果“prediction_output_time_period”与“user_specified_input_variables”都没有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法预测[3]变量未来一段时间趋势，不清楚关键影响因素，未填写内容后续我将根据您上传的数据自动判断。
    - 如果“prediction_output_time_period”有内容填写，“user_specified_input_variables”没有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法预测[3]变量未来[4]分钟趋势，不清楚关键影响因素，未填写内容后续我将根据您上传的数据自动判断。
    - 如果“prediction_output_time_period”没有内容填写，“user_specified_input_variables”有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望基于[6]算法预测[3]变量未来一段趋势，明确关键的影响因素为[5]，未填写内容后续我将根据您上传的数据自动判断。
3. 填充规则:
    - `[0]`: 对应 `task_type`，其中，"0"表示RTO，"1"表示预警，"2"表示时间序列预测，"3"表示回归；返回数字表示。(最后回答时候，用对应的中文表示)
    - `[1]`: 对应 `domain_knowledge.industry`。(最后回答时候，用对应的中文表示)
    - `[2]`: 对应 `domain_knowledge.device_type`。(最后回答时候，用对应的中文表示)
    - `[6]`: 对应 `specified_algorithms`，0是TPT时序大模型，1是AutoML。最后回答时候，用对应的中文表示
    - `[3]`: 对应 `target_variables`的"description"内容，如果多个用顿号连接。
    - `[4]`: 对应 `prediction_output_time_period`。
    - `[5]`: 对应 `user_specified_input_variables` 的"description"内容，如果多个用顿号连接。

## 限制:
- 只围绕输入的json数据进行分析解读，拒绝回答与数据结构分析无关的话题。
- 总结分析内容必须按照给定的示例格式进行组织，不能偏离框架要求。
- 总结分析部分不能超过200字。
- 回答内容不要产生多余字眼，[]对应的内容替换完整，不要出现中括号。
/no_think
        """
        #调用LLM润色
        prompt_str_obj = {
            "prompt_str" :prompt_str
        }
        llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj)
        if llm_resp_obj["success"] == 1:
            llm_resp = llm_resp_obj["llm_resp"]
        else:
             llm_resp = f"""
已明确您的意图，您所属行业为{output_result["domain_knowledge"]["industry"]}，装置类型是{output_result["domain_knowledge"]["device_type"]}，希望基于{"TPT时序大模型" if output_result["specified_algorithms"] == 0 else "传统机器学习模型"}算法解决{output_result["task_objective_description"]}问题，不清楚的关键影响因素及未填写内容后续我将根据您上传的数据自动判断。
"""     
        #用LLM润色返回的问题
        output_result["task_objective_description"] = llm_resp
        await context.add_view({
            "format": "markdown",
            "content":llm_resp
        })

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '数据源匹配',
                "content":"识别到数据未接入到实时数据系统中，且未找到已经训练好的模型，需要您上传离线数据文件以进行模型调整。",
                "description": "",
                "details": ""
           }
        })

        return output_result

# 建模数据上传
@tool(version="*")
async def aml_offline_file_upload(context: Context, params: any):
    formResult = await context.get_interaction("aml_offline_file_upload_form")
    if formResult is None:
        # url = context.config["llm_agent_recommend_params"]     
        # agent_params= {
        #     "question": params["task_objective_description"],
        #     "task_type": params["task_type"]
        # }
        # # 发送 POST 请求
        # response = requests.post(url, json=agent_params)
        # # 检查响应状态码
        # if response.status_code == 200:
        #     # 输出响应内容（JSON 格式）
        #     output = response.json()
        # else:
        #     return f"调用参数预设推荐请求失败，状态码: {response.status_code}"
        
        # await context.add_view({
        #     "format": "markdown",
        #     "content": output["text"]
        # })
        #将推荐参数预存在cache中
        output_parameters = ["低甲酸性气流量","酸性气预热器进口压力","混合气流量","混合气预热器进口压力","氧气流量","助燃空气流量"] 
        await context.set_cache("recommended_tags" , output_parameters)
#         data = output["parameters"]
#         rows = (len(data) + 1) // 2
#         # 构建表头
#         markdown = "| 序号 | 名称  | 序号 | 名称  |\n"
#         markdown += "|------|-------|------|-------|\n"
#         # 构建每一行
#         for i in range(rows):
#             index1 = i + 1
#             name1 = data[i] if i < len(data) else ""
#             index2 = i + rows + 1
#             name2 = data[i + rows] if (i + rows) < len(data) else ""
#             markdown += f"| {index1}    | {name1}   | {index2}    | {name2}   |\n"
#         recommended_tags_str = f"""
# 已分析找到影响问题的关键因素，推荐您上传包含以下参数的数据文件：
# {markdown}
# """
#         await context.add_view({
#             "format": "tip",
#             "content": {
#                 "type": 'default',
#                 "title": '',
#                 "content": recommended_tags_str,
#                 "description": "",
#                 "details": ""
#             }
#         })
        context.require_interaction({
            "id": "aml_offline_file_upload_form",
            "title": "请上传位号历史数据文件",
            "type": "form",
            "form": {
                "form_type": "file",
                "schema": {
                    "type": "object",
                    "description": "请上传位号历史数据文件",
                    "properties": {
                        "file": {
                            "title": "位号历史数据文件(.csv)，仅支持utf-8编码的.csv格式文件",
                            "type": "string",
                            "format": "file-object",
                            "widget": "tptfile",
                            # "x-validator": "csv_judge_pred"
                        }
                    },
                    "required": ["file"]
                },
                "default": {}
            }
        })
    else:{}
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "请稍作等待，后续需要您进行相关信息确认",
            "description": "",
            "details": ""
        }
    })
    file = json.loads(formResult["file"])
    # todo 调用数据解析脚本进行解析，得到报告内容，调用算法管理发布的算法
    try:
        alg_params = {
            "input_file" : file,
            "scenario_id"  : params["task_type"]
        }
        response = requests.post(
            url = context.config["ts_data_eval_alg_exec_runtime_url"], 
            data = json.dumps(alg_params,indent=2, ensure_ascii=False),
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        if response.status_code == 200:
            json_obj = json.loads(response.text)
            read_data = json.loads(json_obj["data"])
            try:
                frequency = int(pd.to_timedelta(read_data['time_statistics']['dominant_frequency']).total_seconds())
            except Exception as e:
                frequency = 5
        else:
           return f"调用数据解析请求失败，状态码: {response.status_code}" 
    except Exception as e:
        return "数据质量分析出现异常：" + str(e)
    
    prompt_str  =f"""
# 角色
你是一位专业的数据质量评估师，擅长对各类数据进行深入分析和评估，能够根据数据的各项指标，精准判断数据质量状况，并给出针对性的总结和建议。
## 技能
### 技能 1: 数据质量评估分析
1. 接收输入的类似以下格式的json数据：
{read_data}
2. 针对每一个字段名称，依据标准差、散度（变异系数）、缺失占比、重复值比例、自相关性、偏自相关等重点指标，进行数据质量评估分析。
### 技能 2: 输出总结文字
根据数据质量评估分析结果，输出一段总结，清晰描述数据质量整体状况（好、中、差）。例如：整体来看，数据在缺失情况方面表现良好，但重复值问题较为突出，尤其是 “混合气流量指示控制” 字段。部分字段的自相关性和偏自相关较高，有利于时间序列分析，但也需注意潜在风险。综合考虑，数据质量处于中等水平，在使用这些数据进行分析前，建议对重复值进行处理，以提高数据质量和分析结果的准确性。
## 限制:
- 仅围绕数据质量评估相关内容进行分析和总结，拒绝回答无关话题。
- 总结需逻辑清晰、简洁明了，全面反映数据质量状况。 
/no_think
"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj)
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp=""
    markdown_info_1 = ""
    markdown_info_2 = ""

    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_1 += f"| {read_data['en_columns'][i]} | {k} | {v['missing_percentage']} | {v['missing_count']} | {v['duplicate_ratio']} | {v['duplicate_count']} |\n"
        i += 1
    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_2 += f"| {read_data['en_columns'][i]} | {k} | {v['min']} | {v['max']} | {v['mean']} | {v['std']} | {v['divergence']} | {v['q1']} | {v['q2']} | {v['q3']} | {v['iqr']} | {v['autocorrelation_lag1']} | {v['autocorrelation_lag5']} | {v['partial_autocorrelation_lag1']} | {v['partial_autocorrelation_lag5']} | \n"
        i += 1
    markdownRet = f"""
# 数据质量评估报告

## 一、数据概况

### 1. 基本信息

在开始评估之前，我们首先梳理数据集的基本情况如下：

- **文件名称**：{file["name"]}  
- **数据量**：{read_data['row_count']} 行，{len(read_data["feature_statistics"])} 列  
- **时间范围**：{read_data['time_statistics']['min_time']}———{read_data['time_statistics']['max_time']}
- **数据采集频次**：每 {f"{frequency}s"} 采集一次  
- **主要字段说明**：{list(read_data["feature_statistics"].keys())}

### 2. 完整性和唯一性
从完整性和唯一性两个维度分析数据，完整性表示数据是否存在缺失，唯一性表示数据是否存在重复记录，具体情况如下表：
| 位号 | 位号描述 | 缺失值比例 | 缺失值数量 | 重复值比例 | 重复值数量 |
| ------- |-------- | --- | --- | --- | --- |
{markdown_info_1}
### 3. 统计分析
对数据进行统计分析，快速了解数据分布情况，为后续数据清洗和模型训练，重点展示数据的最小值、最大值、分位数等关键统计指标。
计算方法：
标准差：衡量数据离散程度的统计量，表示数据集合中每个数据点与平均值的偏离程度。值越大，数据分布越发散，值越小，数据越集中。
散度：衡量数据分布或者离散程度的相对统计量，消除不同数据集量纲影响，离散系数大，说明数据的离散程度大，携带的信息越多。
第一四分位数(Q1)：25%分位数，将数据分为前25%
第二四分位数(Q2)：50%分位数，即中位数，数据的中间值
第三四分位数(Q3)：75%分位数，将数据分为前75%
自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，反映了同一时间序列在不同时间点的相关程度。
偏自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，同时消除了其他滞后阶数的影响。
| 位号 | 位号描述 | 最小值 | 最大值 | 均值 | 标准差 | 散度 | 第一四分位数 | 中位数 | 第三四分位数 | 四分位距 | 自相关性（滞后1阶）| 自相关性（滞后5阶） | 偏自相关（滞后1阶）| 偏自相关（滞后5阶）|
| ------- |-------- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | 
{markdown_info_2}      
{llm_resp}  

"""

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '数据集解析',
            "description": "数据集解析信息展示",
            "details": markdownRet
        }
    })

    output_result = params
    output_result["file"] = file
    output_result["recommended_tags"] = await context.get_cache("recommended_tags")
    output_result["file_variables"] = [{"tag": tag, "description": desc} for tag, desc in zip(read_data["en_columns"], read_data["zn_columns"])]

    return output_result

# 建模参数信息获取
@tool(version="*")
async def aml_parse_file_upload(context: Context, params: any):
    result = await context.get_interaction("open_aml_param_page")
    if result is None:  
        await context.add_view({
            "format": "markdown",
            "content":f"""
现在将这些信息提供给大模型，基于模型的推理获取解决此问题的变量、数据等信息。
"""
        })

        #调用参数预设推荐
        agnent_params = {
            "task_type": params["task_type"],
            "user_requirement": params["task_objective_description"],
            "file_variables": params["file_variables"],
            "recommended_tags": params["recommended_tags"]
        }
        response = requests.post(
            url= context.config["llm_agent_auto_model_rec"], 
            data=json.dumps(agnent_params, indent=2, ensure_ascii=False), headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        if response.status_code == 200 :
            #不规则的模版解析出来的json结构存在问题 调用出错
            json_obj = json.loads(response.text)
            params["target_variables"] = json_obj["target_variables"]
            params["input_variables"] = json_obj["input_variables"]
        else:
            return f"调用参数预设推荐请求失败，状态码: {response.status_code}"
        
        await context.add_view({
        "format": "markdown",
        "content":f"""
根据用户提问，结合选择数据中的位号描述、历史数据，识别出的变量如界面所示，请确认
"""
        })
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "已根据您的补充信息与上传数据，生成TPT训练与计算的必须信息，请您确认",
                "description": "",
                "details": ""
            }
        })
        context.require_interaction({
            "id": "open_aml_param_page",
            "title": "模型微调参数信息",
            "type": "open_page",
            "open_page": "/tpt-auto-model/configForm",
            "page_type":"execute",
            #增加页面传参，保持回填数据一致
            "page_data":params
        })
    else: {}
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "TPT计算所需要的信息和数据已补齐，下一步将调用TPT进行训练和计算，预计需要7分钟，您可以关闭此会话离开，如有结果将及时通知您！",
            "description": "",
            "details": ""
        }
    })
    json_obj= {
        "file": params['file'],
        "task_type":params["task_type"],
        "target_variables": result["target_variables"],
        "specified_algorithms":result["specified_algorithms"],
        "prediction_input_time_period":result["prediction_input_time_period"],
        "prediction_output_time_period": result["prediction_output_time_period"],
        "input_variables": result["input_variables"],
        "train_device_type":params["train_device_type"],
        "recommended_tags":params["recommended_tags"]
    }

    return json_obj

# 建模算法执行
@tool(version="*")
async def aml_exec_file_upload(context: Context, params: any):
    task_type =  params["task_type"]  
    file = params["file"]
    alg_name = "tpt"
    if params["specified_algorithms"] ==1:
        alg_name = "AutoML"
    params["alg_name"] = alg_name

    origrin_params_input_vars = params["input_variables"]
    origrin_params_target_vars = params["target_variables"]
    input_vars = [item["tag"] for item in params["input_variables"]]
    target_vars = [item["tag"] for item in params["target_variables"]]
    params["input_variables"]  = input_vars  #给模型训练用
    params["target_variables"] = target_vars #给模型训练用
    params["prediction_input_time_period"] = params["prediction_input_time_period"]*60 #训练模型及推理使用秒为单位
    params["prediction_output_time_period"] =  params["prediction_output_time_period"]*60 #训练模型及推理使用秒为单位

    await context.add_view({
    "format": "markdown",
    "content":f"""
开始对数据进行预处理，获取干净数据，为后续分析和应用奠定良好基础：  
"""
})
    alg_params = {
        "input_file" : file,
        "scenario_id" : params["task_type"],
        "features": input_vars,
        "targets": target_vars
    }
    response = requests.post(
        url = context.config["ts_data_process_alg_exec_runtime_url"], 
        data = json.dumps(alg_params,indent=2, ensure_ascii=False),
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    if response.status_code == 200:
        json_obj = json.loads(response.text)
        #数据处理后产生的新的数据文件
        file = json.loads(json_obj["data"])
    else:
        return f"调用数据预处理请求失败，状态码: {response.status_code}" 
    
    # todo 数据预处理完，对预处理的数据继续进行数据分析，此时 "scenario_id" 默认为9
    alg_params = {
        "input_file" : file,
        "scenario_id"  : 9
    }

    response = requests.post(
        url = context.config["ts_data_eval_alg_exec_runtime_url"], 
        data = json.dumps(alg_params,indent=2, ensure_ascii=False),
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    if response.status_code == 200:
        json_obj = json.loads(response.text)
        read_data = json.loads(json_obj["data"])
        try:
            frequency = int(pd.to_timedelta(read_data['time_statistics']['dominant_frequency']).total_seconds())
        except Exception as e:
            frequency = 5
    else:
        return f"调用预处理后的数据解析请求失败，状态码: {response.status_code}" 
    
    prompt_str  =f"""
# 角色
你是一位专业的数据质量评估师，擅长对各类数据进行深入分析和评估，能够根据数据的各项指标，精准判断数据质量状况，并给出针对性的总结和建议。
## 技能
### 技能 1: 数据质量评估分析
1. 接收输入的类似以下格式的json数据：
{read_data}
2. 针对每一个字段名称，依据标准差、散度（变异系数）、缺失占比、重复值比例、自相关性、偏自相关等重点指标，进行数据质量评估分析。
### 技能 2: 输出总结文字
根据数据质量评估分析结果，输出一段总结，清晰描述数据质量整体状况（好、中、差）。例如：整体来看，数据在缺失情况方面表现良好，但重复值问题较为突出，尤其是 “混合气流量指示控制” 字段。部分字段的自相关性和偏自相关较高，有利于时间序列分析，但也需注意潜在风险。综合考虑，数据质量处于中等水平，在使用这些数据进行分析前，建议对重复值进行处理，以提高数据质量和分析结果的准确性。
## 限制:
- 仅围绕数据质量评估相关内容进行分析和总结，拒绝回答无关话题。
- 总结需逻辑清晰、简洁明了，全面反映数据质量状况。 
/no_think
"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj)
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp=""
    
    markdown_info_1 = ""
    markdown_info_2 = ""
    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_1 += f"| {read_data['en_columns'][i]} | {k} | {v['missing_percentage']} | {v['missing_count']} | {v['duplicate_ratio']} | {v['duplicate_count']} |\n"
        i += 1
    i = 0    
    for k, v in read_data["feature_statistics"].items():
        markdown_info_2 += f"| {read_data['en_columns'][i]} | {k} | {v['min']} | {v['max']} | {v['mean']} | {v['std']} | {v['divergence']} | {v['q1']} | {v['q2']} | {v['q3']} | {v['iqr']} | {v['autocorrelation_lag1']} | {v['autocorrelation_lag5']} | {v['partial_autocorrelation_lag1']} | {v['partial_autocorrelation_lag5']} | \n"
        i += 1
    markdownRet = f"""
# 数据质量评估报告

## 一、数据概况

### 1. 基本信息

在开始评估之前，我们首先梳理数据集的基本情况如下：

- **文件名称**：{file["name"]}  
- **数据量**：{read_data['row_count']} 行，{len(read_data["feature_statistics"])} 列  
- **时间范围**：{read_data['time_statistics']['min_time']}———{read_data['time_statistics']['max_time']}
- **数据采集频次**：每 {f"{frequency}s"} 采集一次  
- **主要字段说明**：{list(read_data["feature_statistics"].keys())}

### 2. 完整性和唯一性
从完整性和唯一性两个维度分析数据，完整性表示数据是否存在缺失，唯一性表示数据是否存在重复记录，具体情况如下表：
| 位号 | 位号描述 | 缺失值比例 | 缺失值数量 | 重复值比例 | 重复值数量 |
| ------- |-------- | --- | --- | --- | --- |
{markdown_info_1}
### 3. 统计分析
对数据进行统计分析，快速了解数据分布情况，为后续数据清洗和模型训练，重点展示数据的最小值、最大值、分位数等关键统计指标。
计算方法：
标准差：衡量数据离散程度的统计量，表示数据集合中每个数据点与平均值的偏离程度。值越大，数据分布越发散，值越小，数据越集中。
散度：衡量数据分布或者离散程度的相对统计量，消除不同数据集量纲影响，离散系数大，说明数据的离散程度大，携带的信息越多。
第一四分位数(Q1)：25%分位数，将数据分为前25%
第二四分位数(Q2)：50%分位数，即中位数，数据的中间值
第三四分位数(Q3)：75%分位数，将数据分为前75%
自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，反映了同一时间序列在不同时间点的相关程度。
偏自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，同时消除了其他滞后阶数的影响。
| 位号 | 位号描述 | 最小值 | 最大值 | 均值 | 标准差 | 散度 | 第一四分位数 | 中位数 | 第三四分位数 | 四分位距 | 自相关性（滞后1阶）| 自相关性（滞后5阶） | 偏自相关（滞后1阶）| 偏自相关（滞后5阶）|
| ------- |-------- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | 
{markdown_info_2}    
{llm_resp}  

"""
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '数据预处理',
            "description": "数据预处理质量评估",
            "details": markdownRet
            }
    })

    #模型训练
    await context.add_view({
        "format": "markdown",
        "content":f"""
接下来，我将选择并构建合适的模型，开始进行模型训练‌：
"""
    })
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": 'TPT模型微调',
            "description": "微调过程",
            "details": """
模型训练日志：   
"""
        }
    })
    
    #用数据处理后的file执行
    params["file"] = file
    csv_path = "s3://" + file["bucket"] + "/" + file["object"]
    params["csv_path"] = csv_path  
    params["frequency"] = frequency

    train_alg_name = ""
    if(task_type==0):
         train_alg_name = ""
    elif(task_type==1):
         train_alg_name = ""
    elif(task_type==2):
        train_alg_name = ""
        try:
            alg_result = await context.call_tool("execute_predict_alg",context = context,params=params)
            if(alg_result["executeStatus"] ==1):
                try:
                    output = alg_result["result"][0]
                except Exception as e:
                    return f"读取模型训练结果失败，返回信息如: {str(e)}"
            else :
                return f"模型微调过程失败，返回信息如: {alg_result["message"]}"
        except Exception as e: 
                return f"执行模型微调出现异常，返回信息如: {str(e)}"
    elif(task_type==3):
         train_alg_name = ""
    
    output_datas = [
    {
        "format": "text",
        "content": f"""
使用{output["method"]}算法，已经训练好模型文件{output["model_file"]}，模型评估结果如下所示：  
| 平均绝对误差MAE | 均方误差MSE | 均方根误差RMSE | 决定系数R2 | 平均绝对百分比误差MAPE |
|-----|-----|-----|-----|-----|
| {output['eval']['MAE']} | {output['eval']['MSE']}  | {output['eval']['RMSE']} | {output['eval']['R2']} | {output['eval']['MAPE']} |   
        
"""
    }]
    if output["loss"] is not None:
        loss_data = output["loss"]
        #生成x轴标签（）
        xAxisData = []
        for i in range(len(loss_data)):
            xAxisData.append(f'{i + 1}')

        echart_options={
            "title": {
                "text": "训练损失 (Loss) 曲线",
            },
            "legend": {
                "data": ["Loss"],
                "top": 10,
                "right": 10
            },
            "tooltip": {
                "trigger": "axis",
                "formatter": """function(params) {return 'Loss: ' + params[0].value.toFixed(3) + '<br/>' +'Epoch: ' + (params[0].dataIndex + 1);}"""
            },
            "grid": {
                "left": "3%",
                "right": "10%",
                "bottom": "3%",
                "containLabel": True
            },
            "xAxis": {
                "name":"",
                "type": "category",
                "boundaryGap": False,
                "data": xAxisData
            },
            "yAxis": {
                "type": "value",
                "scale": True,
                "name": "Loss值",
                "splitLine": {
                    "show": True
                },
                "min": "function (value) {return (parseFloat(value.min)-Math.abs(parseFloat(value.min)) * 0.05).toFixed(2);}",
                "max": "function (value) {return (parseFloat(value.max)+Math.abs(parseFloat(value.max)) * 0.05).toFixed(2);}"
            },
            "series": [
                {
                    "name": "Loss",
                    "type": "line",
                    "smooth": True,
                    "showSymbol": False,
                    "lineStyle": {
                        "type": "solid",
                        "width": 1
                    },
                    "markPoint": {
                        "data": [
                            { "type": "min", "name": "最小值", "symbolSize": 60 },
                            { "type": "max", "name": "最大值" }
                        ],
                        "label": {
                            "formatter": """function(params) {return params.value.toFixed(3);}"""
                        }
                    },
                    "markLine": {
                        "data": [
                            { "type": "average", "name": "平均值" }
                        ],
                        "label": {
                            "formatter": """function(params) {return "平均: " + params.value.toFixed(3);}"""
                        }
                    },
                    "data": loss_data
                }
            ]
        }
        charts_content = {
            "format": "echarts",
            "content": [
                {
                    "chartTheme": 'light',
                    "chartConfig": echart_options,
                },
                {
                    "chartTheme": 'dark',
                    "chartConfig": echart_options,
                }] # 注意保持数组格式
        }
        output_datas.append(charts_content)

    await context.add_view({
        "format": "card",
        "content": {
            "type": "markdown",
            "title": 'TPT模型微调',
            "description": "微调结果展示",
            "details": output_datas
         }
    })

    # object_name = "automl/" + context.session_id + "_模型微调结果.md"
    # bucket_name = "reports"
    # file_info = await context.add_file(f"{bucket_name}/{object_name}", str(output_datas))
    # await context.add_view({
    #     "format": "card",
    #     "content": {
    #         "type": 'summary_file',
    #         "title": '模型微调结果',
    #         "content": "模型微调结果已成功生成，请点击 模型微调结果 查看",
    #         "details": file_info,
    #         "description": "模型微调结果.md"
    #     }
    # })
    # todo 调用推理脚本，将返回结果输入大模型进行润色，最终输出：图片+结论文字
    try:
        alg_params = {
            "csv_path": csv_path,
            "methods": alg_name,
            "model_file": output["model_file"],
            "prediction_input_time_period": params["prediction_input_time_period"],
            "prediction_output_time_period": params["prediction_output_time_period"],
            "frequency": frequency,
            "input_variables": input_vars,
            "target_variables": target_vars
        }

        if alg_params["methods"] == "tpt":
            url= context.config["tpt_automl_alg_exec_runtime_url"]
        else:
            url= context.config["automl_alg_exec_runtime_url"]
        response = requests.post(
            url = url,
            data=json.dumps(alg_params, ensure_ascii=False),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        if response.status_code == 200:
            json_obj = json.loads(response.text)
            json_obj = json.loads(json_obj["data"]["result"]) 
            past_values = json_obj["pastValue"]
            pre_values = json_obj["preValue"] 
        else:
            return f"调用推理API出现异常，异常代码：{response.status_code}"
    except Exception as e:
        return "执行模型微调验证推理出现异常：" +  str(json.dumps(alg_params,  ensure_ascii=False))

    prompt_str = f"""
# 角色
你是一位专业的预测结果分析大师，擅长对用户输入的特定格式数据进行深入分析，精准判断预测数据未来的走势，并以清晰易懂的语言呈现分析结果。

## 技能
### 技能 1: 分析预测数据走势
1. 当用户输入类似以下格式的数据:
{json_obj}
仔细分析数据。其中variable: [位号名称列表，可以有多个], xtime: [预测的时间戳列表], preValue: [[预测值列表，与位号和时间戳对应]], xpastTime: [输入的过去时间戳列表], pastValue: [[过去的实际值列表，与位号对应]]
2. 根据数据特征，判断每个位号对应的预测数据未来的走势。
3. 按照以下格式给出回答：
基于您输入数据训练完成的模型，预测未来趋势如下，其中，[位号名称]将在未来[X]分钟内呈现[上升/下降/平稳等走势描述]趋势，[X]分钟后达到[具体数值或范围]。

## 限制:
- 仅依据用户输入的数据进行分析和回答，不涉及其他无关内容。
- 回答必须按照规定的格式组织语言，清晰准确地呈现分析结果。 
/no_think
"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj)
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp=""

    output_alg_datas = [
        {
            "format": "text",
            "content": llm_resp  
        }
    ]

    try :    
        #生成x轴标签（）
        xAxisData = []
        xAxisData = json_obj["xpastTime"]+json_obj["xtime"]
        xAxisData = [datetime.strptime(ts, "%Y-%m-%d %H:%M:%S").strftime("%H:%M:%S") for ts in xAxisData]
        for i, item in enumerate(json_obj["variable"]):
            past_data = [v[i] for v in past_values] + [""] * len(pre_values)
            pre_data = [""] * (len(past_values)-1)+[past_values[-1][i]] + [v[i] for v in pre_values]
            echart_options={
                "title": {
                    "text": item,
                },
                "legend": {
                    "data": ["实际值","预测值"], 
                    "top": 10,
                    "right":10
                },
                "tooltip": {
                    "trigger": "axis", 
                    "axisPointer": {
                        "type": "cross"
                    },
                    "formatter": """function (params) {
                        let result= `${params[0].axisValue} <br/>`;
                        var isAdded = false;
                        params.slice(0, 2).forEach((item) =>{
                        if(item.value && isAdded == false)
                        {
                            result +=`${item.marker} ${item.seriesName}: <b>${parseFloat(`${item.value}`).toFixed(2)}</b><br/>`;        
                            isAdded = true;
                        }
                        else
                        {
                            isAdded = false;
                        }
                    });
                        return result;
                    }
                    """
                },
                "grid": {
                    "left": "3%",
                    "right": "10%",
                    "bottom": "3%",
                    "containLabel": True
                },
                "xAxis": {
                    "name":"时间",
                    "type": "category",
                    "boundaryGap": False,
                    "data": xAxisData
                },
                "yAxis": {
                    "type": "value",
                    "scale": True,
                    "name": "值",
                    "splitLine": {
                        "show": True
                    },
                    "min": """function (value) {return (parseFloat(value.min)-Math.abs(parseFloat(value.min)) * 0.05).toFixed(2);}""",
                    "max": """function (value) {return (parseFloat(value.max)+Math.abs(parseFloat(value.max)) * 0.05).toFixed(2);}""",
                    "axisLabel": {
                        "formatter": """function(value) {return parseFloat(`${value}`).toFixed(2);}
                        """
                    }
                },
                "series": [
                    {
                        "name": "实际值",
                        "type": "line",
                        "smooth": True,
                        "showSymbol": False,
                        "lineStyle": {
                            "type": "solid",
                            "width": 2
                        },
                        "data": past_data
                    },
                    {
                        "name": "预测值",
                        "type": "line",
                        "smooth": True,
                        "showSymbol": False,
                        "lineStyle": {
                            "type": "dashed",
                            "width": 2
                        },
                        "data": pre_data 
                    }
                ]
            }
            charts_content = {
                "format": "echarts",
                "content": [
                    {
                        "chartTheme": 'light',
                        "chartConfig": echart_options,
                    },
                    {
                        "chartTheme": 'dark',
                        "chartConfig": echart_options,
                    }] # 注意保持数组格式
            }
            output_alg_datas.append(charts_content)      
    except Exception as e:
        return "模型推理结果图形化展示出现异常!"+ str(e)
    
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '微调模型推理',
            "description": "模型推理结果展示",
            "details": output_alg_datas
         }
    })
    # object_name = "automl/" + context.session_id + "_模型推理结果.md"
    # bucket_name = "reports"
    # file_info = await context.add_file(f"{bucket_name}/{object_name}", str(output_alg_datas))
    # await context.add_view({
    #     "format": "card",
    #     "content": {
    #         "type": 'summary_file',
    #         "title": '模型推理结果',
    #         "content": "模型推理结果已成功生成，请点击 模型推理结果 查看",
    #         "details": file_info,
    #         "description": "模型推理结果.md"
    #     }
    # })

    llm_agl_param = {
        "method" : output["method"],
        "model_file" : output["model_file"],
        "input_variables": origrin_params_input_vars,
        "target_variables": origrin_params_target_vars,
        "recommended_tags": params["recommended_tags"],
        "preResult": llm_resp
    }

    prompt_str  =f"""
你是一个工业自动化建模解决方案专家，需要根据提供的硫回收装置预测数据（target_variables、preResult、input_variables等字段），生成一份结构化技术报告。报告需包含动态生成的预测结果分析，并严格遵循以下格式要求。
{llm_agl_param}

根据上述内容，以target_variables中所有“description”为预测目标参数，生成如下报告：

硫回收装置预测SO2与H2S浓度趋势解决方案（根据用户输入的target_variables中“description”作为关键词生成报告名，字体为报告中最大的）

1.硫回收工艺背景
根据自身对相应工艺的了解，简单叙述，尽可能贴近报告名称的内容。
2.预测影响因素分析
根据自身了解，科普可能存在的影响因素，最终引导至recommended_tags中的位号
3.预测模型搭建
为解决预测“target_variables”中所有“description”趋势的问题，通过查找历史数据库案例及阅读文献，建立了以“input_variables”中所有“description”为输入，“target_variables”中所有“description”为输出的“specified_algorithms”模型，并进行微调训练，最终模型为 “model_file”。要求所有值都用输入的中文对应值取代。
组件            配置
输入变量        “input_variables”中所有“description”,多个用、号连接
输出目标        “target_variables”中所有“description”,多个用、号连接
算法架构        {"TPT时序大模型" if  alg_name == "tpt" else "传统机器学习模型"}
模型版本        {llm_agl_param["model_file"] }
4.预测结果
基于历史运行数据，我构建了“method”预测模型，预测未来趋势结果表明：
输出“preResult”的内容，分段描述

后续建议：
1.验证装置中“target_variables”中所有“description”趋势预测方案的可行性，
2.验证装置中“target_variables”中所有“description”预测值与实际值的偏差率，
3.当“target_variables”中所有“description”超过0.25%时触发模型再训练机制，更新 “model_file”的参数，
4.在交接班记录中增加“target_variables”中所有“description”的预测/实际值对比曲线。

"""     
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj)
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]

    report_file_path = "reports/预测总结报告.md"
    mk_str_object = {
        "mk_str" : llm_resp
    }
    convert_file = await context.call_tool("markdown_to_docx_file",context = context,params=mk_str_object)
    if(convert_file["success"]==1):
        docx_data = convert_file["file_info"]
        report_file_path = "reports/预测总结报告.docx"
        file_info = await context.add_file(report_file_path, docx_data)
    else:
        file_info = await context.add_file(report_file_path, llm_resp)
    await context.add_view({
            "format": "card",
            "content": {
                "type": 'summary_file',
                "title": '预测总结报告',
                "content": "预测总结报告已成功生成，请点击 预测总结报告 查看",
                "details": file_info,
                "description": "预测总结报告"
            }
        })
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "所有任务执行完毕，请确认优化结果",
            "description": "",
            "details": ""
        }
    })

    json_obj = {
        "output":{
            "train_result":output,
            "inference":json_obj
        }
    }
    
    return json_obj