from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("工艺模型启动仿真模拟失败，未找到工艺模型1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("工艺模型启动仿真模拟失败，未找到工艺模型2")
        except ValueError:
            raise ValueError("工艺模型启动仿真模拟异常，未找到工艺模型")
            
        return server_ip, server_port

    except Exception as e:
        return None

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

@tool(version="*")
async def startmodel(context: Context, params: any):
    
    loaded_model = await context.get_cache("loaded_model")
    if not loaded_model:
        loaded_model = params.get("loaded_model", "").strip()
    
    # url = "http://localhost:9999/api/startmodel"
    # 默认的 URL
    url = "http://localhost:9999/api/startmodel"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/startmodel"
            else:
                raise ValueError("工艺模型启动模拟仿真计算失败，未找到工艺模型位置")
    except ValueError as ve:
        raise ValueError("工艺模型启动模拟仿真计算失败，工艺模型位置坐标解析失败")
    except Exception as e:
        raise ValueError("工艺模型启动模拟仿真计算异常，寻找工艺模型位置发生错误")

    headers = {"Content-Type": "application/json"}

    payload = {"runstate": 1} # 数值 1 表示通知服务端根据已加载的工艺模型开始运行仿真计算

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=5
            ) as response:
                response.raise_for_status()
                result = await response.json()

                result_state = result.get("state")
                if result_state is None or result_state != 0:
                    error_code = result_state if result_state is not None else -3
                    raise ValueError(f"错误码：{error_code}，工艺模型启动仿真计算失败, 请重新生成工艺模型")

                if loaded_model:
                    await context.set_cache("running_model", loaded_model)
                # if result_state == 0:
                #     await context.set_cache("running_state", 1)
                
                await add_card(
                    context, 
                    "tip", 
                    "正在运行模型，请稍后。。。", 
                    "正在运行模型"
                    )
                await asyncio.sleep(10)

                await add_card(
                    context, 
                    "summary", 
                    f"工艺模型已成功开启模拟仿真计算，现在可以进一步观察工艺模型设备仿真计算过程中设备状态变化。", 
                    f"成功启动工艺模型仿真模拟"
                    )

                return {
                    "state": result_state,
                    "running_model":loaded_model
                }

        except aiohttp.ClientError as e:
            raise ValueError(f"state:-1，工艺模型启动仿真计算出错，请重新生成工艺模型重试")
        except asyncio.TimeoutError:
            raise ValueError(f"state:-2， 工艺模型启动仿真计算超时未响应，请重新生成工艺模型重试")