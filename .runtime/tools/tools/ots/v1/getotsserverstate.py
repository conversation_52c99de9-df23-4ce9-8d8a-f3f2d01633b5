from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import xml.etree.ElementTree as ET
import os
import ipaddress

# 视图展示字段
L_TYPE_MAP = {
    0: "模型未启动",
    1: "模型正在运行",
    2: "模型暂停仿真",
    3: "模型终止仿真",
    4: "正在加载快照",
    5: "正在重演"
}

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成仿真模拟工艺模型失败1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成仿真模拟工艺模型失败2")
        except ValueError:
            raise ValueError("生成仿真模工艺模型异常")
            
        return server_ip, server_port

    except Exception as e:
        # print("工艺模型位置解析异常")
        return None

# 临时方案，现在去查x-source时，必须是工作流第一个节点，所有需要从向量库查询的东西，都先放到第一个节点
# 从向量库查询出来的数据先不要用，直接放到缓存里
async def cache_user_inputs(context: Context, params: dict):
    """缓存用户输入参数"""
    # 缓存用户模型
    user_model = params.get("user_model", [])
    if user_model:
        await context.set_cache("user_model", user_model)
    
    # 缓存用户工况
    user_snapshot = params.get("user_snapshot", [])
    if user_snapshot:
        await context.set_cache("user_snapshot", user_snapshot)
    
    # 缓存运行模型标签
    running_model_tags = params.get("running_model_tags", [])
    if running_model_tags:
        await context.set_cache("running_model_tags", running_model_tags)

@tool(version="*")
async def getotsserverstate(context: Context, params: any):
    # 先看缓存里有没有，有的话不用设置，因为此处不用，主要是如果该接口作为工作流的第一个调用接口，要解析文本
    # user_model = await context.get_cache("user_model")
    # if not user_model:
    #     user_model = params.get("user_model", "").strip()
    #     if not user_model:
    #         await context.set_cache("user_model", user_model)

    # 1. 缓存所有用户输入参数,临时方案
    await cache_user_inputs(context, params)
    
    # 默认的 URL
    url = "http://localhost:9999/api/getmodelinfo"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/getmodelinfo"
            else:
                raise ValueError("生成工艺模型服务失败，未找到工艺模型位置")
    except ValueError as ve:
        raise ValueError("生成工艺模型服务失败，工艺模型位置坐标解析失败")
    except Exception as e:
        raise ValueError("生成工艺模型服务异常，寻找工艺模型位置发生错误")

    headers = {"Content-Type": "application/json"}

    payload = {"timeout": 30}

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=30
            ) as response:
                response.raise_for_status()
                result = await response.json()

                result_state = result.get("state", -1)
                result_model = result.get("modelname")

                if result_model:
                    await context.set_cache("loaded_model", result_model)

                # state为[0,5]表示服务端状态正常，参照schema定义
                # 服务端是否已加载工艺模型，根据modelname是否为空判断，modelname为空时表示为服务端尚未加载工艺模型
                # user_model 为用户文本提取模型名称，这里返回是作为工作流上下文参数传递
                if result_state >= 0 and result_state <=5:
                    status = L_TYPE_MAP.get(result_state, f"未知状态{result_state}")
                    # modelmsg = f"已关联工艺模型 {result_model}" if result_model and str(result_model).strip() else "未关联工艺模型"
                    await add_card(
                    context, 
                    "summary", 
                    "工艺模型服务已生成并正常运行，现在可以准备进一步模拟操作了。", 
                    "已生成工艺模型服务，完成状态检测"
                    )
                    return {
                        "state": result_state,
                        "loaded_model" : result_model
                    }
                else:
                    raise ValueError(f"生成工艺模型服务异常: {result_state}")
                    # return {
                    #     "state": -1,
                    #     "modelname" : ""
                    # }

        except aiohttp.ClientError as e:
            raise ValueError(f"错误码：{e}, state:-3, 已生成工艺模型服务，启动失败")
        except asyncio.TimeoutError:
            raise ValueError(f"state:-2, 已生成工艺模型服务，启动超时")
            # return {
            #     "state": -2,
            #     "modelname" : ""
            # }