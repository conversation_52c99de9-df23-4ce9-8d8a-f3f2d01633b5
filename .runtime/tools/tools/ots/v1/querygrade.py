from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import requests
from typing import Any, Dict
from collections import OrderedDict
from datetime import datetime

import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_train_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['agentserver_host'] or ""
        server_port = context.config['trainserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("配置中的主机无效")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("配置中的端口无效")
        except ValueError:
            raise ValueError(f"端口号不是有效的整数: {server_port}")
            
        return server_ip, server_port

    except Exception as e:
        return None

async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

def format_timestamp(ts):
    """将时间戳转换为可读格式"""
    return datetime.fromtimestamp(ts/1000).strftime('%Y-%m-%d %H:%M:%S')

@tool(version="*")
async def querygrade(context: Context, params: Dict[str, Any]) -> Any:
    ip_address = "localhost"
    port="1920"
    try:
        config = get_ots_train_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                ip_address, port = config
            else:
                raise ValueError("配置格式不正确")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        raise ValueError(f"值错误: {ve}")
    except Exception as e:
        # print(f"发生错误: {e}")
        raise ValueError(f"发生错误: {e}")

    url = f"http://{ip_address}:{port}/TranServer/WebQueryScoreCache"
    # 1. 动态处理参数
    required_key = "param"
    if required_key not in params:
        return {
            "state": 0,
            "info":"输入参数不正确"
        }

    projurl=f"http://{ip_address}:{port}/TranServer/cloudQueryOTSProject"
    payload = {}
    headers = {}
    proj_response = requests.request("POST", projurl, headers=headers, data=payload)
    proj_rawtext = proj_response.text
    proj_text = json.loads(proj_rawtext)
    proj_json = json.loads(proj_text)
    proj_data = proj_json.get("data", [])

    param = params.get('param', {})
    proj_name = param.get('project', None)
    subject_name = param.get('km', None)
    proj_exists = any(item['name'] == proj_name for item in proj_data)

    if not proj_exists:
        proj_list = list({d["name"]: d for d in proj_data}.values())
        form_schema = {
            "type": "object",
            "properties": {
                "projectname": {
                    "type": "string",
                    "title": "工程列表",
                    "enum": [item['name'] for item in proj_list],
                    "enumNames":  [item['name'] for item in proj_list]
                }
            },
            "required": ["projectname"]
        }
        proj_interaction_id = "selectproj_form"
        form_result = await context.get_interaction(proj_interaction_id)
        if form_result is None:
            proj_default_name = proj_list[0]["name"]
            context.require_interaction({
                "id": proj_interaction_id,
                "title": "选择需要查询的工程",
                "type": "form",
                "form": {
                    "schema": form_schema,
                    "default": {"projectname": proj_default_name}
                }
            })
            return {}
        param['project'] = form_result["projectname"]


    new_proj_name = param.get('project', None)
    suburl = f"http://{ip_address}:{port}/TranServer/cloudQueryOTSSubject"
    payload = json.dumps({
        "param": json.dumps({
            "projectName": new_proj_name
        })
    })
    headers = {
        'Content-Type': 'application/json'
    }

    sub_response = requests.request("POST", suburl, headers=headers, data=payload)
    sub_rawtext = sub_response.text
    sub_text = json.loads(sub_rawtext)
    sub_json = json.loads(sub_text)
    sub_data = sub_json.get("data", [])

    subject_name = param.get('km', None)
    sub_exists = any(item['name'] == subject_name for item in sub_data)

    if not sub_exists:
        sub_list = list({d["name"]: d for d in sub_data}.values())
        form_schema = {
            "type": "object",
            "properties": {
                "subjectname": {
                    "type": "string",
                    "title": "科目列表",
                    "enum": [item['name'] for item in sub_list],
                    "enumNames":  [item['name'] for item in sub_list]
                }
            },
            "required": ["subjectname"]
        }
        sub_interaction_id = "selectsub_form"
        form_result = await context.get_interaction(sub_interaction_id)
        if form_result is None:
            sub_default_name = sub_list[0]["name"]
            context.require_interaction({
                "id": sub_interaction_id,
                "title": "选择需要查询的科目",
                "type": "form",
                "form": {
                    "schema": form_schema,
                    "default": {"subjectname": sub_default_name}
                }
            })
            return {}
        param['subject'] = form_result["subjectname"]

    data = {
        "param": params[required_key]  # 直接使用传入的值
    }
    # 1. 将内部字典转换为 JSON 字符串
    inner_json_str = json.dumps(data['param'], ensure_ascii=False)  # 确保中文字符不被转义

    # 2. 更新外层字典的值为字符串
    data['param'] = inner_json_str

    # 3. 将外层字典转换为 JSON 字符串（带转义符）
    final_json_str = json.dumps(data, ensure_ascii=False)
    payload=final_json_str
    headers = {
        'Content-Type': 'application/json'
    }
    # print(payload)
    # 生成表格内容
    table_content = '|学员姓名|学员编号|考生得分|开始时间|结束时间|试卷总分|\n'
    table_content += '| :----: | :----: | :----: | :----: | :----: | :----: |\n'
    result = {
        "state": 1,
        "info": "查询成绩成功！",
        "data": []
    }
    # 使用 aiohttp 发送异步请求
    async with aiohttp.ClientSession() as session:
        async with session.post(url, data=payload, headers=headers) as response:
            # 打印响应内容
            raw_text = await response.text()
            exam_json = json.loads(raw_text)
            # print("Parsed JSON:", exam_json)
            exam_data = exam_json.get("data", [])

            for exam in exam_data:
                try:
                    students = json.loads(exam['students'])['StudentList']
                except (json.JSONDecodeError, KeyError):
                    students = []

                for student in students:
                    student_name = student.get('StudentName', '')
                    student_id = student.get('StudentID', '')
                    score = exam.get('score', '')
                    start_time = format_timestamp(exam.get('start', 0))
                    end_time = format_timestamp(exam.get('end', 0))
                    total_score = exam.get('totalScore', '')
                    exam_record = {
                        "stuname": student_name,
                        "stuid": student_id,
                        "score": score,
                        "start": start_time,
                        "end": end_time,
                        "totalScore": total_score,
                        "km": exam.get('km', ''),
                        "examno": exam.get('examno', ''),
                        "project": exam.get('project', '')
                    }
                    result["data"].append(exam_record)
                    table_content += f"|{student_name}|{student_id}|{score}|{start_time}|{end_time}|{total_score}|\n"

    await add_card(context, "card", table_content, title="考试成绩信息")

    return result
