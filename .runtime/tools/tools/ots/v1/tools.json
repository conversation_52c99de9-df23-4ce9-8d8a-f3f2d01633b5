{"$schema": "../../../schema/tools.schema.json", "tools": [{"skip_summary": true, "name": "getmodelinfo", "alias": ["获取当前模型"], "catalog": "simulation", "description": "获取工艺模型运行状态信息的能力", "params": {"type": "object", "description": "无参数", "properties": {}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "工艺模型详细信息", "properties": {"state": {"type": "integer", "description": "模型状态，正常状态要>=0, 模型状态：0-未启动，1-运行中，2-停止仿真，3-已终止，4-加载快照中，5-重演状态", "minimum": 0, "maximum": 5, "enum": [0, 1, 2, 3, 4, 5], "enumDescriptions": ["模型未启动", "模型正在运行", "模型停止仿真", "模型终止状态", "正在加载快照", "重演状态"]}, "speed": {"type": "number", "description": "计算速度，加速倍数，每秒需要仿真的轮次，与步长一起控制仿真快慢", "minimum": 0}, "step": {"type": "number", "description": "计算步长,仿真一轮次需要消耗的时间，单位毫秒", "minimum": 0}, "calcnum": {"type": "integer", "description": "计算次数，模型当前已执行的仿真计算轮次", "minimum": 0}, "runtime": {"type": "number", "description": "运行时间，模型已进行仿真计算消耗的时间，单位是毫秒", "minimum": 0}, "modelname": {"type": "string", "description": "模型名称"}, "cursnapshot": {"type": "string", "description": "当前工况存储路径,可以为空", "format": "uri-reference"}, "curreview": {"type": "string", "description": "当前重演存储路径,可以为空", "format": "uri-reference"}, "currecode": {"type": "string", "description": "当前记录存储路径,可以为空", "format": "uri-reference"}}, "required": ["state", "speed", "step", "calcnum", "runtime", "modelname"]}}, {"skip_summary": true, "name": "getotsserverstate", "alias": ["检测工艺模型服务状态"], "catalog": "simulation", "description": "检测当前工艺模型服务器运行状态，如果已正常运行，是否正在模拟工艺模型执行仿真计算", "params": {"type": "object", "description": "检测工艺模型服务状态，以及工艺模型信息，工况信息和工艺模型关联设备参数配置信息", "properties": {"user_model": {"type": "array", "description": "用户询问文本中提取的工艺模型名称列表，用户没有输入工艺模型名称时，列表可以为空数组", "items": {"type": "object", "description": "工艺模型名称，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成", "properties": {"likemodel": {"type": "string", "description": "工艺模型名称，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成"}}, "required": ["likemodel"]}, "x-source": ["milvus_m3_modellist_ots"]}, "user_snapshot": {"type": "array", "description": "用户询问文本提取的工况名，用户没有输入时默认为空数组", "items": {"type": "object", "description": "工况元素定义", "properties": {"likesnapshot": {"type": "string", "description": "工况名称"}}, "required": ["likesnapshot"]}, "x-source": ["milvus_m3_modelsnapshot_ots"]}, "running_model_tags": {"type": "array", "description": "根据用户问题中的模型名称，获取与该工艺模型关联的设备参数配置信息列表，如果都不符合可以为空数组", "items": {"type": "object", "description": "带设备参数配置信息的工艺模型元素定义", "properties": {"running_model": {"type": "string", "description": "工艺模型名称modelname，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成"}, "tagsconfig": {"type": "array", "description": "用户询问的工艺模型所关联的设备参数配置信息列表，即tags，可以为空数组", "items": {"type": "object", "description": "单个设备参数配置信息元素定义", "properties": {"name": {"type": "string", "description": "设备参数名称"}, "datatype": {"type": "integer", "description": "数据类型,0：布尔值，1：整型，2：浮点型，3：字符串", "enum": [0, 1, 2, 3], "enumDescriptions": ["布尔值", "整型", "浮点型", "字符串"]}, "desc": {"type": "string", "description": "设备参数描述"}, "unit": {"type": "string", "description": "数据单位"}}, "required": ["name", "datatype"], "additionalProperties": false}}}, "required": ["running_model"], "additionalProperties": false}, "x-source": ["milvus_m3_modeltags_ots"]}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "当前工艺模型服务器状态", "properties": {"state": {"type": "integer", "description": "模型服务状态，正常状态要>=0, 模型状态：0-未启动，1-运行中，2-停止仿真，3-已终止，4-加载快照中，5-重演状态，其他未明确说明的状态表示服务器状态异常", "minimum": 0, "maximum": 5, "enum": [0, 1, 2, 3, 4, 5], "enumDescriptions": ["模型未启动", "模型正在运行", "模型停止仿真", "模型终止状态", "正在加载快照", "重演状态"]}, "loaded_model": {"type": "string", "description": "模型名称，如果当前服务运行正常，且已加载工艺模型，则为工艺模型名称，否则为空字符串"}}, "required": ["state", "loaded_model"]}}, {"skip_summary": true, "name": "getevaluatestate", "alias": ["检测评分服务状态"], "catalog": "simulation", "description": "检测当前评分服务是否正常", "params": {"type": "object", "description": "无参数", "properties": {}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "当前评分服务状态", "properties": {"state": {"type": "integer", "description": "评分服务状态是否正常，0为不正常，1为正常"}}, "required": ["state"]}}, {"skip_summary": true, "name": "loadmodel", "ignore_converter_cache": true, "alias": ["加载模型"], "catalog": "simulation", "description": "加载工艺模型的能力", "params": {"type": "object", "description": "加载模型需要的参数", "properties": {"user_model": {"type": "array", "description": "用户输入文本中提取的工艺模型名称列表，用户没有输入工艺模型名称时，列表可以为空数组", "items": {"type": "object", "description": "工艺模型名称，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成", "properties": {"likemodel": {"type": "string", "description": "工艺模型名称，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成"}}, "required": ["likemodel"]}, "x-source": ["milvus_m3_modellist_ots"]}, "server_modellist": {"type": "array", "description": "从工艺模型服务获得的工艺模型列表，列表数据可以由成功执行获取模型列表后传入，未传时该列表可以为空数组", "items": {"type": "object", "description": "工艺模型元素定义", "properties": {"modelname": {"type": "string", "description": "工艺模型名称"}}, "required": ["modelname"]}}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "工艺模型加载结果", "properties": {"state": {"type": "integer", "description": "模型加载是否成功,只有0表示成功", "enum": [0]}, "loaded_model": {"type": "string", "description": "最新加载的工艺模型名称"}}, "required": ["state", "loaded_model"]}}, {"skip_summary": true, "name": "startmodel", "alias": ["运行模型"], "catalog": "simulation", "description": "启动工艺模型并开始仿真计算的能力", "params": {"type": "object", "description": "运行模型的参数", "properties": {"loaded_model": {"type": "string", "description": "工艺模型服务已加载的工艺模型名称"}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "工艺模型启动模拟仿真计算的操作结果", "properties": {"state": {"type": "integer", "description": "模型是否成功启动,只有0表示成功", "enum": [0]}, "running_model": {"type": "string", "description": "工艺模型服务已成功启动模拟仿真计算的工艺模型名称"}}, "required": ["state", "running_model"]}}, {"skip_summary": true, "name": "pausemodel", "alias": ["冻结模型"], "catalog": "simulation", "description": "冻结工艺模型模拟仿真计算的能力", "params": {"type": "object", "description": "无参数", "properties": {}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "冻结工艺模型仿真计算的操作结果", "properties": {"state": {"type": "integer", "description": "冻结模型是否成功,只有0表示成功", "enum": [0]}}, "required": ["state"]}}, {"skip_summary": true, "name": "<PERSON><PERSON><PERSON><PERSON>", "alias": ["查看流程图"], "catalog": "simulation", "description": "打开流程图查看界面", "params": {"type": "object", "description": "无参数,不需要转换", "properties": {}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "打开并查看流程图是否成功", "properties": {"state": {"type": "integer", "description": "打开流程图是否成功，0为成功", "enum": [0, 1, -1, -2], "enumDescriptions": ["成功", "处理中", "参数错误", "网络错误"]}, "hmiurl": {"type": "string", "description": "流程图界面地址，仅在state=0时有效"}}}, "required": ["state"]}, {"skip_summary": true, "name": "stopmodel", "alias": ["停止模型"], "catalog": "simulation", "description": "停止工艺模型模拟仿真计算的能力", "params": {"type": "object", "description": "无参数", "properties": {}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "停止工艺模型模拟仿真计算的操作结果", "properties": {"state": {"type": "integer", "description": "停止模型是否成功,只有0表示成功", "enum": [0]}}, "required": ["state"]}}, {"skip_summary": true, "name": "loadsnapshot", "ignore_converter_cache": true, "alias": ["加载工况"], "catalog": "simulation", "description": "复原之前截取生成的场景快照，即复原工况", "params": {"type": "object", "description": "加载工况需要的参数", "properties": {"user_snapshot": {"type": "array", "description": "用户提问文本中提取的工况名列表，没有输入时列表默认为空数组", "items": {"type": "object", "description": "工况元素定义", "properties": {"likesnapshot": {"type": "string", "description": "工况名称"}}, "required": ["likesnapshot"]}, "x-source": ["milvus_m3_modelsnapshot_ots"]}, "server_snapshotlist": {"type": "array", "description": "从工艺模型服务查询获取的工况列表，可以为空数组", "items": {"type": "object", "description": "单个工况元素定义", "properties": {"ltype": {"type": "integer", "description": "工况类型，2-手动类型工况，3-自动类型工况，4-标准类型工况", "enum": [2, 3, 4], "enumDescriptions": ["手动类型工况", "自动类型工况", "标准类型工况"]}, "id": {"type": "string", "description": "工况序列号，唯一标识"}, "name": {"type": "string", "description": "工况名称"}}, "required": ["ltype", "id", "name"]}}, "running_model": {"type": "string", "description": "正在模拟仿真计算的工艺模型名称，外部没有传入时默认为空"}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "加载工况结果", "properties": {"state": {"type": "integer", "description": "工艺模型加载工况是否成功,只有0表示成功", "enum": [0]}, "loaded_snapshot": {"type": "string", "description": "已经加载成功的工况名称"}, "running_model": {"type": "string", "description": "正在模拟仿真计算的工艺模型名称"}}, "required": ["state", "loaded_snapshot"]}}, {"skip_summary": true, "name": "writevalue", "ignore_converter_cache": true, "alias": ["仿真设备写值"], "catalog": "simulation", "description": "外部设置模型设备参数值并写入仿真设备", "params": {"type": "object", "description": "准备写入的设备参数值", "properties": {"tags": {"type": "array", "description": "设备参数列表，用户提问文本中未输入设备参数名称时可以为空数组", "items": {"type": "object", "description": "带值的模型设备参数定义", "allOf": [{"type": "object", "properties": {"tagName": {"type": "string", "description": "设备参数名称"}}, "required": ["tagName"], "additionalProperties": false}, {"type": "object", "properties": {"dataType": {"type": "integer", "description": "数据类型", "enum": [0, 1, 2, 3], "example": 1}, "value": {"description": "设备参数值数据，需与dataType匹配", "type": ["integer", "boolean", "number", "string"]}}, "required": ["dataType", "value"], "additionalProperties": false}], "if": {"properties": {"dataType": {"const": 0}}}, "then": {"properties": {"value": {"type": "boolean"}}}, "else": {"if": {"properties": {"dataType": {"const": 1}}}, "then": {"properties": {"value": {"type": "integer"}}}, "else": {"if": {"properties": {"dataType": {"const": 2}}}, "then": {"properties": {"value": {"type": "number"}}}, "else": {"if": {"properties": {"dataType": {"const": 3}}}, "then": {"properties": {"value": {"type": "string"}}}}}}}}}, "required": ["tags"], "additionalProperties": false}, "result": {"type": "object", "description": "设置设备参数值并写入仿真设备操作结果", "properties": {"state": {"type": "integer", "description": "设置设备参数值是否成功,只有0表示成功", "enum": [0]}}, "required": ["state"]}}, {"skip_summary": true, "name": "readvalue", "ignore_converter_cache": true, "alias": ["获取设备参数值"], "catalog": "simulation", "description": "读取工艺模型设备的参数值", "params": {"type": "object", "description": "准备查询的设备参数", "properties": {"user_tagnames": {"type": "array", "description": "设备参数名列表，用户提问文本输入，用户未输入时该列表可为空数组", "items": {"type": "object", "description": "设备参数基础定义", "properties": {"tagName": {"type": "string", "description": "设备参数名称"}}, "required": ["tagName"], "additionalProperties": false}}, "user_duration": {"type": "integer", "description": "用户指定要持续读值的时长，用户文本未输入时默认为0", "default": 0}, "loaded_snapshot": {"type": "string", "description": "已经加载的工况名称，上下文未曾发生成功加载工况时可为空字符串"}, "running_model_tags": {"type": "array", "description": "根据用户问题中输入的模型名称，以及与该工艺模型关联的设备参数配置信息列表，如果都不符合可以为空数组", "items": {"type": "object", "description": "带设备参数配置信息的工艺模型元素定义", "properties": {"running_model": {"type": "string", "description": "工艺模型名称modelname，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成"}, "tagsconfig": {"type": "array", "description": "用户询问的工艺模型所关联的设备参数配置信息列表，即tags，可以为空数组", "items": {"type": "object", "description": "单个设备参数配置信息元素定义", "properties": {"name": {"type": "string", "description": "设备参数名称"}, "datatype": {"type": "integer", "description": "数据类型,0：布尔值，1：整型，2：浮点型，3：字符串", "enum": [0, 1, 2, 3], "enumDescriptions": ["布尔值", "整型", "浮点型", "字符串"]}, "desc": {"type": "string", "description": "设备参数描述"}, "unit": {"type": "string", "description": "数据单位"}}, "required": [], "additionalProperties": false}}}, "required": ["running_model"], "additionalProperties": false}, "x-source": ["milvus_m3_modeltags_ots"]}, "pre_tagsvalue": {"type": "array", "description": "上一次成功获取的设备参数模拟数据数组，此次是第一次操作获取设备参数值时可以为空数组", "items": {"type": "object", "description": "带值的设备参数元素定义", "properties": {"tagName": {"type": "string", "description": "设备参数名称"}, "dataType": {"type": "integer", "description": "数据类型", "enum": [0, 1, 2, 3]}, "value": {"description": "设备参数数据,需与dataType匹配", "type": ["integer", "boolean", "number", "string"]}}, "required": ["tagName", "dataType", "value"], "additionalProperties": false, "if": {"properties": {"dataType": {"const": 0}}}, "then": {"properties": {"value": {"type": "boolean"}}}, "else": {"if": {"properties": {"dataType": {"const": 1}}}, "then": {"properties": {"value": {"type": "integer"}}}, "else": {"if": {"properties": {"dataType": {"const": 2}}}, "then": {"properties": {"value": {"type": "number"}}}, "else": {"if": {"properties": {"dataType": {"const": 3}}}, "then": {"properties": {"value": {"type": "string"}}}}}}}}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "获取设备参数值结果", "properties": {"state": {"type": "integer", "description": "获取设备参数值是否成功,只有0表示成功", "enum": [0]}, "running_model": {"type": "string", "description": "正在模拟仿真计算的工艺模型"}, "tagsvalue": {"type": "array", "description": "设备参数值数据数组", "minItems": 1, "items": {"type": "object", "description": "带值的设备参数定义", "properties": {"tagName": {"type": "string", "description": "设备参数名称"}, "dataType": {"type": "integer", "description": "数据类型", "enum": [0, 1, 2, 3]}, "value": {"description": "设备参数值，需与dataType匹配", "type": ["integer", "boolean", "number", "string"]}, "timestamp": {"type": "string", "format": "date-time", "description": "数据采集时间"}}, "required": ["tagName", "dataType", "value"], "additionalProperties": false, "if": {"properties": {"dataType": {"const": 0}}}, "then": {"properties": {"value": {"type": "boolean"}}}, "else": {"if": {"properties": {"dataType": {"const": 1}}}, "then": {"properties": {"value": {"type": "integer"}}}, "else": {"if": {"properties": {"dataType": {"const": 2}}}, "then": {"properties": {"value": {"type": "number"}}}, "else": {"if": {"properties": {"dataType": {"const": 3}}}, "then": {"properties": {"value": {"type": "string"}}}}}}}}}, "required": ["state", "tagsvalue"]}}, {"skip_summary": true, "name": "changesubject", "alias": ["切换科目"], "catalog": "simulation", "description": "切换科目的能力", "params": {"type": "object", "description": "切换科目的参数", "properties": {"subname": {"type": "string", "description": "需要切换的科目,必须传入"}}, "required": ["subname"], "additionalProperties": false}, "result": {"type": "object", "description": "切换科目的结果", "properties": {"state": {"type": "integer", "description": "切换科目是否成功,只有0表示成功", "enum": [0]}, "subname": {"type": "string", "description": "切换的科目名称"}}, "required": ["state"]}}, {"skip_summary": true, "name": "publishexam", "alias": ["下发考试"], "catalog": "simulation", "description": "下发考试的能力", "params": {"type": "object", "description": "下发考试的参数", "properties": {"subname": {"type": "string", "description": "考试的科目,必须传入"}, "examtime": {"type": "string", "description": "考试时长"}}, "required": ["subname"], "additionalProperties": false}, "result": {"type": "object", "description": "下发考试的结果", "properties": {"state": {"type": "integer", "description": "下发考试是否成功,只有0表示成功", "enum": [0]}, "subname": {"type": "string", "description": "考试的科目名称"}}, "required": ["state"]}}, {"skip_summary": true, "name": "querygrade", "ignore_converter_cache": true, "alias": ["查询考试成绩"], "catalog": "simulation", "description": "查询考试成绩历史数据的能力", "params": {"type": "object", "description": "准备查询的成绩", "properties": {"param": {"type": "object", "description": "查询成绩的参数", "properties": {"project": {"type": "string", "description": "考试的工程名称，非必要字段，可以为空"}, "start": {"type": "integer", "format": "int64", "description": "开始时间，UTC 时间转换为的毫秒级时间戳，自 1970-01-01 00:00:00 UTC 以来的毫秒数，非必要字段，可以为空"}, "end": {"type": "integer", "format": "int64", "description": "结束时间，UTC 时间转换为的毫秒级时间戳，自 1970-01-01 00:00:00 UTC 以来的毫秒数，非必要字段，可以为空"}, "km": {"type": "string", "description": "考试的科目名称，非必要字段，可以为空"}, "examo": {"type": "string", "description": "考试的场次，非必要字段，可以为空"}}}}, "additionalProperties": false}, "result": {"type": "object", "description": "查询成绩结果", "properties": {"state": {"type": "integer", "description": "查询成绩的结果，0为查询成功，1为查询失败"}, "info": {"type": "string", "description": "如果查询失败，提示查询失败的原因"}, "data": {"type": "array", "description": "成绩数据列表", "items": {"type": "object", "properties": {"start": {"type": "string", "description": "开始时间"}, "end": {"type": "string", "description": "结束时间"}, "stuid": {"type": "string", "format": "uuid", "description": "考试学员学号"}, "stuname": {"type": "string", "format": "uuid", "description": "考试学员姓名"}, "km": {"type": "string", "description": "考试科目名称"}, "project": {"type": "string", "description": "考试工程名"}, "score": {"type": "number", "description": "考试实际得分"}, "totalScore": {"type": "number", "description": "试卷总分"}, "examno": {"type": "string", "description": "考试场次编号"}}}}, "total": {"type": "integer", "description": "考试总数"}}}}, {"skip_summary": true, "name": "getsnapshotlist", "ignore_converter_cache": true, "alias": ["获取工况列表"], "catalog": "simulation", "description": "查看已截取保存的指定类型工况", "params": {"type": "object", "description": "获取工况列表需要的参数", "properties": {"snapshottype": {"type": "integer", "description": "工况类型，1为获取所有类型工况，2为获取手动类型工况，3为获取自动类型工况，4为获取标准类型工况", "minimum": 1, "maximum": 4, "enum": [1, 2, 3, 4], "enumDescriptions": ["获取所有类型工况", "获取手动类型工况", "获取自动类型工况", "获取标准类型工况"], "default": 4}}, "additionalProperties": false}, "result": {"type": "object", "description": "工艺模型获取工况列表结果", "properties": {"state": {"type": "integer", "description": "工艺模型获取工况列表是否成功,只有0表示成功", "enum": [0]}, "server_snapshotlist": {"type": "array", "description": "指定类型的工况列表", "minItems": 1, "items": {"type": "object", "description": "获取得到的工况元素定义", "properties": {"ftime": {"type": "string", "format": "date-time", "description": "快照创建时间"}, "ltime": {"type": "number", "description": "仿真时间，精确到豪秒,如果要进行单位转换，注意1秒等于1000毫秒，1分钟等于60秒，1小时等于60分钟，1天等于24小时", "minimum": 0}, "ltype": {"type": "integer", "description": "工况类型,1-所有类型工况，2-手动类型工况，3-自动类型工况，4-标准类型工况", "enum": [1, 2, 3, 4]}, "lsourcetype": {"type": "integer", "description": "工况来源，由哪个角色创建，预留，暂不验证"}, "id": {"type": "string", "description": "工况序列号，唯一标识"}, "name": {"type": "string", "description": "工况名称"}, "scenename": {"type": "string", "description": "工况关联的场景模型名称"}, "desc": {"type": "string", "description": "工况的描述信息"}}, "required": ["ltime", "ltype", "id", "name", "scenename", "desc"]}}}, "required": ["state", "server_snapshotlist"]}}, {"skip_summary": true, "name": "getmodellist", "ignore_converter_cache": true, "alias": ["获取模型列表"], "catalog": "simulation", "description": "从工艺模型服务查询获取所有已创建的工艺模型", "params": {"type": "object", "description": "获取模型列表需要的参数", "properties": {"timeout": {"type": "integer", "description": "获取模型列表的超时时间，外部有传入时使用传入值，外部没有传入时使用默认值，默认是60秒", "default": 60}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "从工艺模型服务查询获取所有已创建的工艺模型结果，以列表形式展示", "properties": {"state": {"type": "integer", "description": "从工艺模型服务获取模型列表是否成功,只有0表示成功", "enum": [0]}, "server_modellist": {"type": "array", "description": "从服务端获取的已创建的所有工艺模型的列表", "minItems": 1, "items": {"type": "object", "description": "单个工艺模型元素定义", "properties": {"modelname": {"type": "string", "description": "工艺模型名称"}}, "required": ["modelname"]}}}, "required": ["state", "server_modellist"]}}, {"skip_summary": true, "name": "createandrunmodel", "alias": ["生成工艺模型并执行"], "catalog": "simulation", "description": "检测当前工艺模型运行状态，如果已正常运行，加载模型并运行模型，模拟设备仿真计算", "params": {"type": "object", "description": "检测工艺模型状态，以及工艺模型信息，工况信息和工艺模型关联设备参数配置信息", "properties": {"user_model": {"type": "array", "description": "用户询问文本中提取的工艺模型名称列表，用户没有输入工艺模型名称时，列表可以为空数组", "items": {"type": "object", "description": "工艺模型名称，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成", "properties": {"likemodel": {"type": "string", "description": "工艺模型名称，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成"}}, "required": ["likemodel"]}, "x-source": ["milvus_m3_modellist_ots"]}, "user_snapshot": {"type": "array", "description": "用户询问文本提取的工况名，用户没有输入时默认为空数组", "items": {"type": "object", "description": "工况元素定义", "properties": {"likesnapshot": {"type": "string", "description": "工况名称"}}, "required": ["likesnapshot"]}, "x-source": ["milvus_m3_modelsnapshot_ots"]}, "running_model_tags": {"type": "array", "description": "根据用户问题中的模型名称，获取与该工艺模型关联的设备参数配置信息列表，如果都不符合可以为空数组", "items": {"type": "object", "description": "带设备参数配置信息的工艺模型元素定义", "properties": {"running_model": {"type": "string", "description": "工艺模型名称modelname，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成"}, "tagsconfig": {"type": "array", "description": "用户询问的工艺模型所关联的设备参数配置信息列表，即tags，可以为空数组", "items": {"type": "object", "description": "单个设备参数配置信息元素定义", "properties": {"name": {"type": "string", "description": "设备参数名称"}, "datatype": {"type": "integer", "description": "数据类型,0：布尔值，1：整型，2：浮点型，3：字符串", "enum": [0, 1, 2, 3], "enumDescriptions": ["布尔值", "整型", "浮点型", "字符串"]}, "desc": {"type": "string", "description": "设备参数描述"}, "unit": {"type": "string", "description": "数据单位"}}, "required": ["name", "datatype"], "additionalProperties": false}}}, "required": ["running_model"], "additionalProperties": false}, "x-source": ["milvus_m3_modeltags_ots"]}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "当前工艺模型状态", "properties": {"state": {"type": "integer", "description": "模型状态，正常状态要>=0, 模型状态：0-未启动，1-运行中，2-停止仿真，3-已终止，4-加载快照中，5-重演状态，其他未明确说明的状态表示服务器状态异常", "minimum": 0, "maximum": 5, "enum": [0, 1, 2, 3, 4, 5], "enumDescriptions": ["模型未启动", "模型正在运行", "模型停止仿真", "模型终止状态", "正在加载快照", "重演状态"]}, "running_model": {"type": "string", "description": "已成功启动模拟仿真计算的工艺模型名称"}}, "required": ["state", "running_model"]}}, {"skip_summary": true, "name": "simureadvalue", "ignore_converter_cache": true, "alias": ["模拟设备参数值"], "catalog": "simulation", "description": "模拟并读取工艺模型设备的参数值", "params": {"type": "object", "description": "准备模拟参数值的设备参数", "properties": {"user_tagnames": {"type": "array", "description": "设备参数名列表，用户提问文本输入，用户未输入时该列表可为空数组", "items": {"type": "object", "description": "设备参数基础定义", "properties": {"tagName": {"type": "string", "description": "设备参数名称"}}, "required": ["tagName"], "additionalProperties": false}}, "user_duration": {"type": "integer", "description": "用户指定要持续读值的时长，用户文本未输入时默认为0", "default": 0}, "loaded_snapshot": {"type": "string", "description": "已经加载的工况名称，上下文未曾发生成功加载工况时可为空字符串"}, "running_model_tags": {"type": "array", "description": "根据用户问题中输入的模型名称，以及与该工艺模型关联的设备参数配置信息列表，如果都不符合可以为空数组", "items": {"type": "object", "description": "带设备参数配置信息的工艺模型元素定义", "properties": {"running_model": {"type": "string", "description": "工艺模型名称modelname，模型名称可以是中文，中文可以转换为拼音全拼，或者转换为拼音首字母组成"}, "tagsconfig": {"type": "array", "description": "用户询问的工艺模型所关联的设备参数配置信息列表，即tags，可以为空数组", "items": {"type": "object", "description": "单个设备参数配置信息元素定义", "properties": {"name": {"type": "string", "description": "设备参数名称"}, "datatype": {"type": "integer", "description": "数据类型,0：布尔值，1：整型，2：浮点型，3：字符串", "enum": [0, 1, 2, 3], "enumDescriptions": ["布尔值", "整型", "浮点型", "字符串"]}, "desc": {"type": "string", "description": "设备参数描述"}, "unit": {"type": "string", "description": "数据单位"}}, "required": [], "additionalProperties": false}}}, "required": ["running_model"], "additionalProperties": false}, "x-source": ["milvus_m3_modeltags_ots"]}, "pre_tagsvalue": {"type": "array", "description": "上一次成功获取的设备参数模拟数据数组，此次是第一次操作获取设备参数值时可以为空数组", "items": {"type": "object", "description": "带值的设备参数元素定义", "properties": {"tagName": {"type": "string", "description": "设备参数名称"}, "dataType": {"type": "integer", "description": "数据类型", "enum": [0, 1, 2, 3]}, "value": {"description": "设备参数数据,需与dataType匹配", "type": ["integer", "boolean", "number", "string"]}}, "required": ["tagName", "dataType", "value"], "additionalProperties": false, "if": {"properties": {"dataType": {"const": 0}}}, "then": {"properties": {"value": {"type": "boolean"}}}, "else": {"if": {"properties": {"dataType": {"const": 1}}}, "then": {"properties": {"value": {"type": "integer"}}}, "else": {"if": {"properties": {"dataType": {"const": 2}}}, "then": {"properties": {"value": {"type": "number"}}}, "else": {"if": {"properties": {"dataType": {"const": 3}}}, "then": {"properties": {"value": {"type": "string"}}}}}}}}}, "required": [], "additionalProperties": false}, "result": {"type": "object", "description": "获取模拟设备参数值的结果", "properties": {"state": {"type": "integer", "description": "获取模拟设备参数值是否成功,只有0表示成功", "enum": [0]}, "running_model": {"type": "string", "description": "正在模拟仿真计算的工艺模型"}, "tagsvalue": {"type": "array", "description": "设备参数值数据数组", "minItems": 1, "items": {"type": "object", "description": "带值的设备参数定义", "properties": {"tagName": {"type": "string", "description": "设备参数名称"}, "dataType": {"type": "integer", "description": "数据类型", "enum": [0, 1, 2, 3]}, "value": {"description": "设备参数值，需与dataType匹配", "type": ["integer", "boolean", "number", "string"]}, "timestamp": {"type": "string", "format": "date-time", "description": "数据采集时间"}}, "required": ["tagName", "dataType", "value"], "additionalProperties": false, "if": {"properties": {"dataType": {"const": 0}}}, "then": {"properties": {"value": {"type": "boolean"}}}, "else": {"if": {"properties": {"dataType": {"const": 1}}}, "then": {"properties": {"value": {"type": "integer"}}}, "else": {"if": {"properties": {"dataType": {"const": 2}}}, "then": {"properties": {"value": {"type": "number"}}}, "else": {"if": {"properties": {"dataType": {"const": 3}}}, "then": {"properties": {"value": {"type": "string"}}}}}}}}}, "required": ["state", "tagsvalue"]}}]}