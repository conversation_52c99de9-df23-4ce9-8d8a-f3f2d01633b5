from __runner__ import tool, Context
import aiohttp
import json
import asyncio
import logging

from typing import Dict, Any, List
from pydantic import BaseModel, Field
import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成工艺模型列表失败，未找到工艺模型1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成工艺模型列表失败，未找到工艺模型2")
        except ValueError:
            raise ValueError("生成工艺模型列表异常，未找到工艺模型")
            
        return server_ip, server_port

    except Exception as e:
        # print("工艺模型位置解析异常")
        return None

class modelItem(BaseModel):
    modelname: str

class modellistResponse(BaseModel):
    state: int
    modellist: List[modelItem]

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

# 创建工艺模型列表视图
def create_model_list_table(models: List[modelItem]):
    """展示模型表格视图"""
    table_content = f"### 已生成工艺模型列表（共 {len(models)} 条）\n| 名称 |\n|------|\n"
    table_content += "\n".join(f"| {m.modelname} |" for m in models if m.modelname)
    
    return table_content
    

@tool(version="*")
async def getmodellist(context: Context, params: any):
    # 先看缓存里有没有，有的话不用设置，因为此处不用，主要是如果该接口作为工作流的第一个调用接口，要解析文本
    # user_model = await context.get_cache("user_model")
    # if not user_model:
    #     user_model = params.get("user_model", "").strip()
    #     if not user_model:
    #         await context.set_cache("user_model", user_model)
    
    user_timeout = params.get("timeout", 60)

    # 3. 发送HTTP请求
    # 默认的 URL
    url = "http://localhost:9999/api/getmodellist"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/getmodellist"
            else:
                raise ValueError("生成工艺模型列表失败，未找到工艺模型位置")
    except ValueError as ve:
        raise ValueError("生成工艺模型列表失败，工艺模型位置坐标解析失败")
    except Exception as e:
        raise ValueError("生成工艺模型列表异常，寻找工艺模型位置发生错误")

    headers = {"Content-Type": "application/json"}

    payload = {"timeout":user_timeout}

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url,
                json=payload,
                headers=headers,
                timeout=user_timeout+5
            ) as response:
                response.raise_for_status()
                response_data = await response.json()
                # 响应数据校验
                try:
                    validated_response = modellistResponse(**response_data)
                except Exception as e:
                    # print("[ERROR] 生成工艺模型列表错误")
                    return {"state": -4, "server_modellist": []}
                
                await add_card(
                context, 
                "summary", 
                f"已成功获取所有生成工艺模型，详情 请点击 工艺模型列表 进行查看。", 
                "成功生成模型列表"
                )

                # 创建模型列表视图
                table_content = create_model_list_table(validated_response.modellist)
                
                # 展示模型列表
                await add_card(context, "card", table_content, title="工艺模型列表")

                # 结果标准化处理
                return {
                    "state": 0,
                    "server_modellist": [
                        {
                            "modelname": item.modelname
                        }
                        for item in validated_response.modellist
                    ]
                }
    
        except asyncio.TimeoutError:
            # print("生成工艺模型列表超时")
            return{
                "state": -2,
                "server_modellist": []
            }            
        except aiohttp.ClientError as e:
            # print("生成工艺模型列表失败")
            return {
                "state": -1,
                "server_modellist": []
            }
        except Exception as e:
            # context.log.debug(f"[ERROR] 未知错误: {e}")  # 替换logger
            # print(f"[ERROR] 未知错误: {e}")
            return {"state": -3, "server_modellist": []}