from __runner__ import tool, Context
import aiohttp
import json
import asyncio
import logging

from typing import Dict, Any, List, Union, Literal
from pydantic import BaseModel,Field
import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成模型工况列表失败1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成模型工况列表失败2")
        except ValueError:
            raise ValueError("生成模型工况列表异常")
            
        return server_ip, server_port

    except Exception as e:
        return None

# 位号值定义
class TagValue(BaseModel):
    tagName: str
    dataType: Literal[0, 1, 2, 3]
    value: Union[bool, int, float, str]

# 定义与 Schema 严格匹配的请求响应模型
class SnapshotRequest(BaseModel):
    snapshottype: int = Field(
        default=4,
        ge=1,
        le=4,
        description="工况类型: 1=全部,2=手动,3=自动,4=标准"
    )

class SnapshotItem(BaseModel):
    ftime: str
    ltime: float
    ltype: int
    lsourcetype: int
    id: str
    name: str
    scenename: str
    desc: str

class SnapshotResponse(BaseModel):
    state: int
    snapshotlist: List[SnapshotItem]


# 类型映射字典
TYPE_MAPPING = {
    "全部工况": 1,
    "手动工况": 2,
    "自动工况": 3,
    "标准工况": 4
}


# 展示动态表单
async def show_snapshot_selection_form(
    context,
    default_value: int,
    interaction_id: str = "getsnapshotlist_form"
):
    # # 构造动态表单schema
    form_schema = {
        "type": "object",
        "properties": {
            "snapshottype": {
                "type": "string",
                "title": "工况类型选择",
                "enum":list(TYPE_MAPPING.keys()),
                "enumNames":["所有类型工况","手动类型工况","自动类型工况","标准类型工况"],
                "default":next(k for k,v in TYPE_MAPPING.items() if v == default_value)
            }
        },
        "required": ["snapshottype"]
    }
    
    form_result = await context.get_interaction(interaction_id)
    if not form_result:
        # 弹出表单交互
        await context.require_interaction({
            "id": interaction_id,
            "title": "工况类型选择",
            "type": "form",
            "form": {
                "schema": form_schema,
                "default": {"snapshottype": next(k for k,v in TYPE_MAPPING.items() if v == default_value)}
            }
        })
        return {}
    
    return TYPE_MAPPING[form_result["snapshottype"]]


# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

# 创建工艺模型列表视图
def create_snapshot_list_table(snapshotlist: List[SnapshotItem]):
    """展示模型表格视图"""
    # 添加视图展示
    L_TYPE_MAP = {
        2: "手动",
        3: "自动",
        4: "标准"
    }

    table_content = f"""
### 已生成工况列表（共 {len(snapshotlist)} 条）
| 类型 | 名称 | ID | 仿真时间 | 创建时间 |
|----------|----|------|----------|----------|
"""
    for item in snapshotlist:
        # 处理可能为空的数据
        name = item.name or "未命名"
        ftime = item.ftime or "未知时间"
        ltime = f"{item.ltime:.2f}" if item.ltime is not None else "N/A"
        type_name = L_TYPE_MAP.get(item.ltype, f"未知({item.ltype})")
        
        table_content += f"| {type_name} | {name} | `{item.id[:8]}...` | {ltime} | {ftime} |\n"
    
    table_content += f"""
# 添加详细说明
> **说明**：
> - **ID**：显示前8位，完整ID请查看原始数据
> - **类型**：{', '.join([f'{k}={v}' for k, v in L_TYPE_MAP.items()])}
> - **仿真时间**：单位为豪秒
"""
    
    return table_content

@tool(version="*")
async def getsnapshotlist(context: Any, params: Dict[str, Any]) -> Dict[str, Any]:

    # 参数传递，无需验证
    params = params or {}
    running_model = params.get("running_model", "")

    # 弹出动态表单（带默认值处理）
    # default_value = TYPE_MAPPING.get(params.get("snapshottype", "标准工况"), 4)
    # selected_type  = await show_snapshot_selection_form(context, default_value)
    # if not selected_type:
    #     return {"status": "pending_form"} # 等待用户交互
    # request_data = {"snapshottype": selected_type}

    # 处理表单数据
    selected_type = "标准工况"
    request_data = {"snapshottype": TYPE_MAPPING[selected_type]}
        
    # 1. 参数验证
    try:
        validated_request = SnapshotRequest(**request_data)
    except Exception as e:
        # print(f"[ERROR] 生成工况列表错误")
        return {"state": -5, "snapshotlist": []}
    
    # 3. 发送HTTP请求
    # 默认的 URL
    url = "http://localhost:9999/api/getsnapshotlist"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/getsnapshotlist"
            else:
                raise ValueError("模型工况列表生成失败，未找到模型工况位置")
    except ValueError as ve:
        raise ValueError("模型工况列表生成失败，工艺模型位置坐标解析失败")
    except Exception as e:
        raise ValueError("模型工况列表生成异常，寻找工艺模型位置发生错误")

    headers = {"Content-Type": "application/json"}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url,
                json=validated_request.model_dump(),
                headers=headers,
                timeout=5
            ) as response:
                response.raise_for_status()
                response_data = await response.json()
                # 响应数据校验
                try:
                    validated_response = SnapshotResponse(**response_data)
                except Exception as e:
                    # print("[ERROR] 模型工况列表生成失败")
                    return {"state": -4, "server_snapshotlist": [], "pre_tagsvalue":[]}
                
                await add_card(
                context, 
                "summary", 
                f"已成功生成{selected_type}列表，详情 请点击 {selected_type}列表 进行查看。", 
                f"成功生成{selected_type}列表"
                )
                
                table_content = create_snapshot_list_table(validated_response.snapshotlist)
                # 展示模型列表
                await add_card(context, "card", table_content, title=f"{running_model}{selected_type}列表")

                server_snapshotlist = [item.model_dump() for item in validated_response.snapshotlist]
                
                if not server_snapshotlist:
                    await context.set_cache("server_snapshotlist", server_snapshotlist)
                # 结果标准化处理
                return {
                    "state": 0,
                    "server_snapshotlist": server_snapshotlist
                }
    
        except asyncio.TimeoutError:
            # print("生成模型工况列表超时")
            return{
                "state": -2,
                "server_snapshotlist": []
            }            
        except aiohttp.ClientError as e:
            # print(f"生成模型工况列表失败")
            return {
                "state": -1,
                "server_snapshotlist": []
            }
        except Exception as e:
            # context.log.debug(f"[ERROR] 未知错误: {e}")  # 替换logger
            # print(f"[ERROR] 未知错误")
            return {"state": -3, "server_snapshotlist": []}