from __runner__ import tool, Context

import aiohttp
import json
import asyncio
from typing import Any, Dict

import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_train_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['agentserver_host'] or ""
        server_port = context.config['trainserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("配置中的主机无效")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("配置中的端口无效")
        except ValueError:
            raise ValueError(f"端口号不是有效的整数: {server_port}")
            
        return server_ip, server_port
    
    except Exception as e:
        return None

@tool(version="*")
async def publishexam(context: Any, params: Dict[str, Any]) -> Any:
    # 默认的 URL
    url = "http://localhost:1920/TranServer/WebPostMSgToEvaEngine"
    try:
        config = get_ots_train_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/TranServer/WebPostMSgToEvaEngine"
            else:
                raise ValueError("配置格式不正确")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        raise ValueError(f"值错误: {ve}")
    except Exception as e:
        # print(f"发生错误: {e}")
        raise ValueError(f"发生错误: {e}")

    # 1. 动态处理参数
    required_key = "subname"
    if required_key not in params:
        raise ValueError(f"Missing required parameter: '{required_key}'")

    subname=params["subname"]
    examtime=params["examtime"]
    obj = {
        "cmd": "MCPStartExam",
        "param": {
            "ip": "127.0.0.1",
            "port": "51000",
            "info": {
                "subject": subname,
                "examtime": examtime
            }
        }
    }
    data = {
        "param": obj  # 直接使用传入的值
    }
    # 1. 将内部字典转换为 JSON 字符串
    inner_json_str = json.dumps(data['param'], ensure_ascii=False)  # 确保中文字符不被转义

    # 2. 更新外层字典的值为字符串
    data['param'] = inner_json_str

    # 3. 将外层字典转换为 JSON 字符串（带转义符）
    final_json_str = json.dumps(data, ensure_ascii=False)
    payload = final_json_str
    headers = {
        'Content-Type': 'application/json'
    }
    # print(payload)
    # 使用 aiohttp 发送异步请求
    async with aiohttp.ClientSession() as session:
        async with session.post(url, data=payload, headers=headers) as response:
            # 打印响应内容
            raw_text = await response.text()
            response_json = json.loads(raw_text)
            # print("Parsed JSON:", response_json)
            return response_json
