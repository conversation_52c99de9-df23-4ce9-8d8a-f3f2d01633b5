from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import xml.etree.ElementTree as ET
import os
import ipaddress
import requests

'''
需求：（无法实现，废弃）
工程名不要手动填写，工程名精确匹配提问词直接执行能力，模糊匹配提供交互列表供用户选择和确认

设计：(废弃，过于复杂，大模型无法准确识别)
4个入参含义：
user_modelname: 用户文本抽取词，可以为空
user_modellist: 向量库返回的跟user_modelname有模糊匹配关系的工艺模型列表，可以为空，原始值是以user_modelname内容为基础，大模型处理后返回的模糊匹配数据
server_modellist: 输入的从服务端得到的工艺模型列表，可以为空，基本上是上一个能力节点获取工艺模型列表的输出
loaded_modelname: 输入的服务端已加载的工艺模型名称，可以为空，这个参数主要是防止上下文参数填写覆盖掉user_modelname

判断逻辑：（废弃，过于复杂，大模型调用接口时无法准确识别传参）
1、如果user_modelname不为空，server_modellist也不为空：
   （1）、如果server_modellist能直接找到user_modelname，则直接开始加载；
   （2）、如果server_modellist找不到user_modelname，则需提供动态表单交互：
        （1）、如果user_modellist不为空，则user_modellist作为动态表单下拉框，user_modellist的第一个元素作为默认项显示；
        （2）、如果user_modellist为空，则server_modellist作为动态表单下拉框，server_modellist的第一个元素作为默认项显示；
2、如果user_modelname不为空，server_modellist为空：
    （1）、如果user_modellist为空，则直接使用user_modelname尝试加载；
    （2）、如果user_modellist不为空，则显示动态表单交互，user_modellist作为动态表单下拉框，user_modellist的第一个元素作为默认项显示；
3、如果user_modelname为空，server_modellist不为空：
    （1）、提供动态表单交互，server_modellist作为动态表单下拉框，server_modellist的第一个元素作为默认项显示；
4、如果user_modelname为空，server_modellist为空：
    （1）、如果user_modellist不为空，则user_modellist作为动态表单下拉框，user_modellist的第一个元素作为默认项显示；
    （2）、如果user_modellist为空，则直接从向量库获取milvus_m3_modellist_ots集合的所有项：
        （1）、如果向量库查询失败或者返回项为空，则接口直接失败；
        （2）、如果向量库查询成功，则显示动态表单交互，查询得到的数据列表作为动态表单下拉框，列表的第一个元素作为默认项显示；

'''

'''
新需求：
工程名不要手动填写，模糊匹配提供交互列表供用户选择和确认
设计：
3个入参含义：
user_model: 向量库返回的跟用户提取词有模糊匹配关系的工艺模型列表，可以为空，大模型处理后返回的模糊匹配数据
server_modellist: 输入的从服务端得到的工艺模型列表，可以为空，基本上是上一个能力节点获取工艺模型列表的输出
#参数废弃 loaded_modelname: 输入的服务端已加载的工艺模型名称，可以为空，这个参数主要是防止上下文参数填写覆盖掉user_modellist

判断逻辑：
1、如果user_model不为空，则user_model作为动态表单下拉框，user_model的第一个元素作为默认项显示；
2、如果user_model为空，server_modellist不为空，则server_modellist作为动态表单下拉框，server_modellist的第一个元素作为默认项显示；
3、如果user_model为空，server_modellist为空，则弹出交互表单，默认显示为空，需要手动填写；
# 废弃3、如果user_model为空，server_modellist为空，则直接从向量库获取milvus_m3_modellist_ots集合的所有项；
    # 废弃（1）、如果向量库查询失败或者返回项为空，则接口直接失败；
    # 废弃（2）、如果向量库查询成功，则显示动态表单交互，查询得到的数据列表作为动态表单下拉框，列表的第一个元素作为默认项显示；
'''

from typing import List, Dict, Union, Tuple
import random
from pydantic import BaseModel

# tpt 上使用时，加载向量模型的地址未知，暂无法接口中直接调用向量库查询，这段代码先注掉
# from pymilvus.model.hybrid import BGEM3EmbeddingFunction

# from pymilvus import (
#     FieldSchema,
#     CollectionSchema,
#     DataType,
#     Collection,
#     connections,
#     MilvusClient
# )

# bge_m3_ef = BGEM3EmbeddingFunction(
#             model_name='../bge-m3',  # Specify the model name
#             device='cpu',  # Specify the device to use, e.g., 'cpu' or 'cuda:0'
#             use_fp16=False  # Specify whether to use fp16. Set to `False` if `device` is `cpu`.
#         )

# client = MilvusClient(
#             uri="http://10.30.71.149:19530",
#             token="root:Milvus"
#         )

# async def _full_scan_random(
#         collection_name: str,
#         limit: int
# ) -> List[Dict]:
#     """小数据量全量扫描（绕过向量计算）"""
#     count = client.get_collection_stats(collection_name)["row_count"]
#     if count <= 10000:  # 小数据量直接全量
#         results = client.query(
#             collection_name=collection_name,
#             filter="",
#             output_fields=["metadata"],
#             limit=limit
#         )
#     else:  # 大数据量随机采样
#         offset = random.randint(0, max(0, count - limit))
#         results = client.query(
#             collection_name=collection_name,
#             filter="",
#             output_fields=["metadata"],
#             limit=limit,
#             offset=offset
#         )
#     return [_format_result(item) for item in results]


# def _format_result(item: Union[Dict, "Hit"]) -> Dict:
#     """统一结果格式"""
#     if "entity" in item:  # 向量搜索结果
#         metadata = item.entity.get("metadata", {})
#         return {
#             "name": metadata.get("modelname", ""),  # 必须包含name字段
#             "metadata": metadata  # 保留完整元数据
#         }
#     else:  # 标量查询结果
#        return {
#             "name": item.get("metadata", {}).get("modelname", ""),  # 从metadata提取模型名
#             "metadata": item.get("metadata", {})  # 保持数据结构一致
#         }

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("加载工艺模型失败，未找到工艺模型位置1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("加载工艺模型失败，未找到工艺模型位置2")
        except ValueError:
            raise ValueError("加载工艺模型异常，未找到工艺模型位置")
            
        return server_ip, server_port

    except Exception as e:
        return None

class modelItem(BaseModel):
    modelname: str

class modellistResponse(BaseModel):
    state: int
    modellist: List[modelItem]

async def get_modellist_from_server(context: Context)->list:
    # 1. 获取工艺模型列表
    # 默认的 URL
    list_url = "http://localhost:9999/api/getmodellist"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                list_url = f"http://{host}:{port}/api/getmodellist"
            else:
                raise ValueError("生成工艺模型列表失败1，未找到工艺模型位置")
    except ValueError as ve:
        return []
    except Exception as e:
        return []

    try:
        async with aiohttp.ClientSession() as session:
            list_response = await session.post(
                list_url,
                json={"timeout":60},
                timeout=60+5
            )
            list_response.raise_for_status()
            list_data = await list_response.json()
            model_list = list_data.get("modellist", [])
            
            # 数据验证与转换
            validated_data = [modelItem(**item).model_dump() for item in model_list if isinstance(item, dict)]
            # validated_data = [
            #             {
            #                 "modelname": item.modelname
            #             }
            #             for item in model_list]
            
            return validated_data

    except aiohttp.ClientError as e:
        return []

# 参数转换
def switch_to_model_names(model_list: List[Dict], key: str = "modelname") -> List[str]:
    """从模型列表中提取名称"""
    return [
        item[key] 
        for item in model_list 
        if isinstance(item, dict) and key in item
    ]

# 动态表单的默认值和下拉列表
async def choice_model_selection(
    context: Context,
    user_model_key: str,
    user_models: List[Dict], 
    server_model_key: str,
    server_models: List[Dict]
) -> Tuple[List[str], str]:
    """
    确定模型选择列表和默认值
    返回: (可选模型列表, 默认选中模型)
    """
    # await context.add_view({
    #     "format": "debug",
    #     "content": {
    #         "server_models": server_models,
    #         "user_models" : user_models
    #     }
    # })
    server_model_names = switch_to_model_names(server_models, server_model_key)

    # user_model中元素必须要在server_models中存在
    new_user_models = []
    if user_models and server_model_names:
        for user_item in user_models:
            if user_item[user_model_key] in server_model_names:
                new_user_models.append(user_item)

    user_model_names = switch_to_model_names(new_user_models, user_model_key)
    
    # 确定候选列表（优先用户模型）
    candidate_model_names = user_model_names if user_model_names else server_model_names
    
    # 处理一下去重
    seen_names = set()
    unique_candidates = [x for x in candidate_model_names if not (x in seen_names or seen_names.add(x))]
    # await context.add_view({
    #     "format": "debug",
    #     "content": {
    #         "unique_candidates": {
    #             "count": len(unique_candidates),
    #             "values": unique_candidates
    #         }
    #     }
    # })
    # 设置默认选中第一个
    default_name = unique_candidates[0] if unique_candidates else ""
    
    return unique_candidates, default_name

# 展示动态表单
async def show_model_selection_form(
    context,
    model_options: List[str],
    default_model: str,
    interaction_id: str = "loadmodel_form"
) -> Dict:
    """展示模型选择表单"""
    form_schema = {
        "type": "object",
        "properties": {
            "modelname": {
                "type": "string",
                "title": "工艺模型",
                "description": "请选择要加载的工艺模型",
                "enum": model_options,
                "enumNames": model_options
            }
        },
        "required": ["modelname"]
    }
    
    form_result = await context.get_interaction(interaction_id)
    if form_result:
        return form_result

    await context.require_interaction({
        "id": interaction_id,
        "title": "加载模型配置",
        "type": "form",
        "form": {
            "schema": form_schema,
            "default": {"modelname": default_model}
        }
    })
    return {}

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })


async def get_hmiproject_from_server(context: Context, ots_project:str, hmilist:List[str]):
    # 1. 获取与工艺模型匹配的hmi工程
    headers = {"Content-Type": "application/json"}
    payload = {
        "hmiprojectlist": [{"hmiproject": hmip} for hmip in hmilist],
        "otsProject": ots_project
    }
    # 默认的 URL
    list_url = "http://localhost:9999/api/gethmiprojectinfo"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                list_url = f"http://{host}:{port}/api/gethmiprojectinfo"
            else:
                raise ValueError("加载工艺模型失败2，未找到匹配工艺模型")
    except ValueError as ve:
        return ""
    except Exception as e:
        return ""

    try:
        async with aiohttp.ClientSession() as session:
            hmiproject_response = await session.post(
                list_url,
                json=payload,
                headers=headers,
                timeout=60+5
            )
            hmiproject_response.raise_for_status()
            hmi_project_info = await hmiproject_response.json()
            hmi_project = hmi_project_info.get("hmiProject", "")
            
            return hmi_project

    except aiohttp.ClientError as e:
        return ""

def get_ots_web_server_config(context: Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['webserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("加载工艺模型失败3，未找到匹配工艺模型1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("加载工艺模型失败3，未找到匹配工艺模型2")
        except ValueError:
            raise ValueError("加载工艺模型异常3，未找到匹配工艺模型")
            
        return server_ip, server_port

    except Exception as e:
        return None

def get_hmi_list(context: Context):
    ip_address = "localhost"
    port = "8121"

    try:
        config = get_ots_web_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                ip_address, port = config
            else:
                raise ValueError("加载工艺模型失败，未找到匹配工艺模型位置5")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        pass
    except Exception as e:
        # print(f"发生错误: {e}")
        pass

    # 发送 HTTP POST 请求
    url = f"http://{ip_address}:{port}/queryProjectList"
    payload = {}
    headers = {}

    try:
        response = requests.post(url, headers=headers, data=payload)
        # 检查请求是否成功
        if response.status_code == 200:
            # 解析 JSON 响应
            data = response.json()

            # 检查结果是否为成功
            if data['result'] == 'success':
                # 解析 param 字段中的 JSON 字符串
                projects = json.loads(data['param'])
                # 提取每个项目的 name 属性
                hmilist = [project['name'] for project in projects if 'name' in project]
                return hmilist
            else:
                # print("加载匹配工艺模型失败5")
                pass
        else:
            # print(f"加载匹配工艺模型失败5，状态码: {response.status_code}")
            pass
    except requests.exceptions.RequestException as e:
        # print("加载匹配工艺模型异常5")
        pass

    return None

def switch_hmi_proj(context: Context, hmi_proj:str):
    ip_address = "localhost"
    port = "8121"

    try:
        config = get_ots_web_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                ip_address, port = config
            else:
                raise ValueError("加载工艺模型失败，未找到工艺模型位置4")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        pass
    except Exception as e:
        # print(f"发生错误: {e}")
        pass

    # 发送 HTTP GET 请求
    url = f"http://{ip_address}:{port}/switchProject"
    payload = f'projName={hmi_proj}'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        # 检查请求是否成功
        if response.status_code == 200:
            # 解析 JSON 响应
            data = response.json()

            # 检查结果是否为成功
            if data['result'] == 'success':
                return
            else:
                # print("加载匹配工艺模型失败4")
                pass
        else:
            # print(f"加载匹配工艺模型失败4，状态码: {response.status_code}")
            pass
    except requests.exceptions.RequestException as e:
        # print("加载匹配工艺模型异常4")
        pass


@tool(version="*")
async def loadmodel(context: Context, params: any):
    # 1. 解析输入参数
    params = params or {}
    user_modellist = await context.get_cache("user_model") or []
    if not user_modellist:
        user_modellist = params.get("user_model", [])
    user_model_key = "likemodel"
    # server_modellist = params.get("server_modellist", [])
    server_model_key = "modelname"

    # if not server_modellist:
    server_modellist = await get_modellist_from_server(context)

    # 2. 确定模型选项列表和默认值
    model_options = []
    default_model = ""
    model_options, default_model = await choice_model_selection(context, user_model_key, user_modellist, server_model_key, server_modellist)
    
    if not model_options:
        await add_card(
            context, 
            "summary", 
            "生成工艺模型失败", 
            "无可用工艺模型"
            )
        raise ValueError(f"state:{-3}, 需要加载工艺模型未明确, 生成工艺模型失败")

    # 如果返回的model_options只有一个，则直接执行，不用用户确认
    if len(model_options) == 1:
        selected_model  = default_model
    else:
        # 在调用 show_selection_form 前检查是否已有交互结果, 仅首次展示提示
        if not await context.get_interaction("loadmodel_form"):
            # 要弹出动态表单需要用户确认前，提示用户需要确认的原因
            # promptmsg = "已生成多个可用工艺模型" if user_modellist else "用户未输入工艺模型"
            await add_card(
                context, 
                "tip", 
                "已生成多个可用模型，请从中选择一个模型加载", 
                ""
                )

        # 4. 构造动态表单schema
        form_result  = await show_model_selection_form(context, model_options, default_model)
        if not form_result:
            await add_card(
            context, 
            "summary", 
            "用户未选择工艺模型，加载模型失败", 
            "未选择模型"
            )
            raise ValueError(f"state:{-4}, 需要加载工艺模型未明确, 用户未选择工艺模型，加载模型失败")
         
        # 处理表单数据
        selected_model  = form_result["modelname"]
    
    # url = "http://localhost:9999/api/loadmodel"
    # 默认的 URL
    url = "http://localhost:9999/api/loadmodel"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/loadmodel"
            else:
                raise ValueError("加载工艺模型失败，未找到工艺模型位置1")
    except ValueError as ve:
        raise ValueError("加载工艺模型失败，工艺模型位置坐标解析失败1")
    except Exception as e:
        raise ValueError("加载工艺模型异常，寻找工艺模型位置发生错误1")

    headers = {"Content-Type": "application/json; charset=utf-8"}

    payload = {"modelname": selected_model}

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=30
            ) as response:
                response.raise_for_status()
                result = await response.json()
                resultstat = result.get("state")
                if resultstat is None or resultstat != 0:
                    error_code = resultstat if resultstat is not None else -6
                    raise ValueError(f"错误码：{error_code}, 加载工艺模型失败")
                
                if selected_model:
                    await context.set_cache("loaded_model", selected_model)
                
                await add_card(
                    context, 
                    "tip", 
                    "正在加载模型，请稍后。。。", 
                    "正在加载模型"
                    )
                await asyncio.sleep(15)

                await add_card(
                    context, 
                    "summary", 
                    "工艺模型加载成功，现在可以准备模拟设备仿真计算了。", 
                    "工艺模型加载完成"
                    )
                # 5. 加载HMI模型, hmi为空有可能是没有配置hmi，不用对用户展示
                hmi_list = get_hmi_list(context)
                await context.add_view({
                    "format": "debug",
                    "content": {
                        "text" : "得到hmi工程列表",
                        "hmi_list": hmi_list,
                        "selected_model" : selected_model
                    }
                })
                hmi_proj = await get_hmiproject_from_server(context, selected_model, hmi_list)
                # await context.add_view({
                #     "format": "debug",
                #     "content": {
                #         "text" : "确定准备切换的hmi工程",
                #         "hmi_proj": hmi_proj,
                #         "selected_model" : selected_model
                #     }
                # })
                # 不能在这里切换hmi工程，需要ots工程运行以后才能切换hmi工程
                if hmi_proj:
                    await context.set_cache("match_hmiproject", hmi_proj)
                    # switch_hmi_proj(hmi_proj)

                return {
                    "state": resultstat,
                    "loaded_model": selected_model
                }

        except aiohttp.ClientError as e:
            # print(f"请求失败: {e}")
            raise ValueError(f"错误码：{e}, state:-1, 加载模型失败")
        except asyncio.TimeoutError:
            # print("请求超时")
            raise ValueError(f"state:-2, 加载模型超时")