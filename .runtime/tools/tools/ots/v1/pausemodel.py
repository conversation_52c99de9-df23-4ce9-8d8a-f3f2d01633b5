from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成仿真模拟工艺服务器失败1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成仿真模拟工艺服务器失败2")
        except ValueError:
            raise ValueError("生成仿真模工艺服务器异常")
            
        return server_ip, server_port

    except Exception as e:
        return None

@tool(version="*")
async def pausemodel(context: Context, params: any):
    url = "http://localhost:9999/api/pausemodel"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/pausemodel"
            else:
                raise ValueError("配置格式不正确")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        raise ValueError(f"值错误: {ve}")
    except Exception as e:
        # print(f"发生错误: {e}")
        raise ValueError(f"发生错误: {e}")

    headers = {"Content-Type": "application/json"}

    payload = {}
    payload["runstate"] = 2

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=5
            ) as response:
                response.raise_for_status()
                result = await response.json()
                return {
                    "state": result.get("state")
                }

        except aiohttp.ClientError as e:
            # print(f"请求失败: {e}")
            return {
                "state": -1
            }
        except asyncio.TimeoutError:
            # print("请求超时")
            return {
                "state": -2
            }