{"$schema": "../../../schema/tools.schema.json", "$id": "tag-definitions.schema.json", "title": "位号类型定义", "description": "设备位号的基础数据类型定义", "$defs": {"tagDefinition": {"type": "object", "description": "位号基础定义", "properties": {"tagName": {"type": "string", "description": "位号名称", "example": ["Temperature", "Pressure"]}}, "required": ["tagName"], "additionalProperties": false}, "tagWithValue": {"type": "object", "description": "带值的位号定义", "allOf": [{"$ref": "#/$defs/tagDefinition"}, {"properties": {"dataType": {"type": "integer", "description": "位号数据类型", "enum": [0, 1, 2, 3], "example": 1}, "value": {"description": "位号值,需与dataType匹配", "type": ["integer", "boolean", "number", "string"]}}, "required": ["dataType", "value"], "additionalProperties": false}], "if": {"properties": {"dataType": {"const": 0}}}, "then": {"properties": {"value": {"type": "boolean"}}}, "else": {"if": {"properties": {"dataType": {"const": 1}}}, "then": {"properties": {"value": {"type": "integer"}}}, "else": {"if": {"properties": {"dataType": {"const": 2}}}, "then": {"properties": {"value": {"type": "number"}}}, "else": {"if": {"properties": {"dataType": {"const": 3}}}, "then": {"properties": {"value": {"type": "string"}}}}}}}}}