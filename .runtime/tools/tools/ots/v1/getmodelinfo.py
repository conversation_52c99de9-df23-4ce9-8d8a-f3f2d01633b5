from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成仿真模拟工艺模型失败，无法查询工艺模型状态信息1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成仿真模拟工艺模型失败，无法查询工艺模型状态信息2")
        except ValueError:
            raise ValueError("生成仿真模工艺模型异常，无法查询工艺模型状态信息")
            
        return server_ip, server_port

    except Exception as e:
        # print("工艺模型位置解析异常")
        return None

@tool(version="*")
async def getmodelinfo(context: Context, params: any):
    # 默认的 URL
    url = "http://localhost:9999/api/getmodelinfo"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/getmodelinfo"
            else:
                raise ValueError("查询工艺模型状态信息失败，未找到工艺模型位置")
    except ValueError as ve:
        raise ValueError("查询工艺模型状态信息失败，工艺模型位置坐标解析失败")
    except Exception as e:
        raise ValueError("查询工艺模型状态信息异常，寻找工艺模型位置发生错误")

    headers = {"Content-Type": "application/json"}

    payload = {}
    payload["timeout"]=5
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=5
            ) as response:
                response.raise_for_status()
                result = await response.json()
                return {
                    "state": result.get("state"),
                    "speed": result.get("speed"),
                    "step": result.get("step"),
                    "calcnum": result.get("calcnum"),
                    "runtime": result.get("runtime"),
                    "modelname": result.get("modelname"),
                    "cursnapshot": result.get("cursnapshot"),
                    "curreview": result.get("curreview"),
                    "currecode": result.get("currecode"),
                    "message"  : result.get("message")
                }

        except aiohttp.ClientError as e:
                # print(f"请求失败: {e}")
                return {
                    "state": -1,
                    "speed": 0,
                    "step": 0,
                    "calcnum": 0,
                    "runtime": 0,
                    "modelname": "",
                    "cursnapshot": "",
                    "curreview": "",
                    "currecode": "",
                }
        except asyncio.TimeoutError:
                # print("请求超时")
                return {
                    "state": -2,
                    "speed": 0,
                    "step": 0,
                    "calcnum": 0,
                    "runtime": 0,
                    "modelname": "",
                    "cursnapshot": "",
                    "curreview": "",
                    "currecode": "",
                }