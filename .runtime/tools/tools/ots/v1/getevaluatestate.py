from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_train_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['agentserver_host'] or ""
        server_port = context.config['trainserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("配置中的主机无效")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("配置中的端口无效")
        except ValueError:
            raise ValueError(f"端口号不是有效的整数: {server_port}")
            
        return server_ip, server_port

    except Exception as e:
        # print(f"An error occurred: {e}")
        return None


@tool(version="*")
async def getevaluatestate(context: Context, params: any):
    # 默认的 URL
    url = "http://localhost:1920/TranServer/Get"
    try:
        config = get_ots_train_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/TranServer/Get"
            else:
                raise ValueError("配置格式不正确")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        raise ValueError("值错误")
    except Exception as e:
        # print(f"发生错误: {e}")
        raise ValueError("发生异常")

    async with aiohttp.ClientSession() as session:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        # print(f"URL {url} is valid and accessible.")
                        return {
                            "state": 1
                        }
                    else:
                        # print(f"URL {url} returned status code: {response.status}")
                        return {
                            "state": 0
                        }
        except aiohttp.ClientError as e:
            # print(f"An error occurred while trying to access the URL {url}: {e}")
            return {
                "state": 0
            }
