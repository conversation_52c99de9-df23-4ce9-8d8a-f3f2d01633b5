from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import requests
import webbrowser

import xml.etree.ElementTree as ET
import os
import ipaddress
from typing import List
from docxtpl import DocxTemplate
from docx import Document
from docx.shared import Mm
import io
import time
import base64


def get_ots_web_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['webserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成流程图失败，未找到流程图信息1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成流程图失败，未找到流程图信息2")
        except ValueError:
            raise ValueError("生成流程图异常，未找到流程图信息")
            
        return server_ip, server_port

    except Exception as e:
        return None

def get_ots_web_client_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['webclient_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成流程图失败，未找到流程图资源1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成流程图失败，未找到流程图资源2")
        except ValueError:
            raise ValueError("生成流程图异常，未找到流程图资源")
            
        return server_ip, server_port

    except Exception as e:
        # print("流程图资源位置解析异常")
        return None

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成流程图失败，未找到工艺模型信息1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成流程图失败，未找到工艺模型信息2")
        except ValueError:
            raise ValueError("生成流程图异常，未找到工艺模型位置")
            
        return server_ip, server_port

    except Exception as e:
        # print("工艺模型位置解析异常")
        return None

async def get_hmiproject_from_server(context: Context, ots_project:str, hmilist:List[str]):
    # 1. 获取与工艺模型匹配的hmi工程
    headers = {"Content-Type": "application/json"}
    payload = {
        "hmiprojectlist": [{"hmiproject": hmip} for hmip in hmilist],
        "otsProject": ots_project
    }
    # 默认的 URL
    list_url = "http://localhost:9999/api/gethmiprojectinfo"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                list_url = f"http://{host}:{port}/api/gethmiprojectinfo"
            else:
                raise ValueError("生成流程图失败2，未拿到足够的模型位置信息")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        raise ValueError("生成流程图失败2，工艺模型位置坐标解析失败")
    except Exception as e:
        # print(f"发生错误: {e}")
        raise ValueError("生成流程图异常2，寻找工艺模型位置发生错误")

    try:
        async with aiohttp.ClientSession() as session:
            hmiproject_response = await session.post(
                list_url,
                json=payload,
                headers=headers,
                timeout=60+5
            )
            hmiproject_response.raise_for_status()
            hmi_project_info = await hmiproject_response.json()
            hmi_project = hmi_project_info.get("hmiProject", "")
            
            return hmi_project

    except aiohttp.ClientError as e:
        return ""

def get_hmi_list(context:Context):
    ip_address = "localhost"
    port = "8121"

    try:
        config = get_ots_web_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                ip_address, port = config
            else:
                raise ValueError("生成流程图失败，未找到匹配工艺模型位置5")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        pass
    except Exception as e:
        # print(f"发生错误: {e}")
        pass

    # 发送 HTTP POST 请求
    url = f"http://{ip_address}:{port}/queryProjectList"
    payload = {}
    headers = {}

    try:
        response = requests.post(url, headers=headers, data=payload)
        # 检查请求是否成功
        if response.status_code == 200:
            # 解析 JSON 响应
            data = response.json()

            # 检查结果是否为成功
            if data['result'] == 'success':
                # 解析 param 字段中的 JSON 字符串
                projects = json.loads(data['param'])
                # 提取每个项目的 name 属性
                hmilist = [project['name'] for project in projects if 'name' in project]
                return hmilist
            else:
                # print("生成流程图失败5")
                pass
        else:
            # print(f"生成流程图失败5，状态码: {response.status_code}")
            pass
    except requests.exceptions.RequestException as e:
        # print("生成流程图异常5")
        pass

    return None

def switch_hmi_proj(context:Context, hmi_proj:str):
    ip_address = "localhost"
    port = "8121"

    try:
        config = get_ots_web_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                ip_address, port = config
            else:
                raise ValueError("生成流程图失败，未找到工艺模型位置4")
    except ValueError as ve:
        # print(f"值错误: {ve}")
        pass
    except Exception as e:
        # print(f"发生错误: {e}")
        pass

    # 发送 HTTP GET 请求
    url = f"http://{ip_address}:{port}/switchProject"
    payload = f'projName={hmi_proj}'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        # 检查请求是否成功
        if response.status_code == 200:
            # 解析 JSON 响应
            data = response.json()

            # 检查结果是否为成功
            if data['result'] == 'success':
                return
            else:
                # print("生成流程图失败4")
                pass
        else:
            # print(f"生成流程图失败4，状态码: {response.status_code}")
            pass
    except requests.exceptions.RequestException as e:
        # print("生成流程图异常4")
        pass

async def tryto_switch_hmiproject(context: Context):
    matching_hmiproject = await context.get_cache("match_hmiproject") or ""
    if not matching_hmiproject:
       loaded_model = await context.get_cache("loaded_model") or ""
       hmi_list = get_hmi_list(context)
       if not hmi_list or not loaded_model:
           raise ValueError("生成流程图失败6，未找到工艺模型资源")
       matching_hmiproject = await get_hmiproject_from_server(context, loaded_model, hmi_list)

    if not matching_hmiproject:
        raise ValueError("生成流程图失败6，未找到流程图资源")
    
    await context.set_cache("match_hmiproject", matching_hmiproject)
    switch_hmi_proj(context, matching_hmiproject)

    # 添加辅助日志
    # await context.log_info(f"get matching_hmiproject={matching_hmiproject}")
    await context.add_view({
        "format": "debug",
        "content": {
            "matching_hmiproject": matching_hmiproject
        }
    })

    result = await context.get_interaction("open_hmi_page")
    if result is None:
        await add_card(
                        context, 
                        "tip", 
                        f"正在生成流程图，请稍候。。。", 
                        f"正在生成流程图"
                        )
        await asyncio.sleep(5) 

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card" and card_type != "chart" and card_type != "summary_file":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

async def display_flow_chart(context:Context, hmiurl:str):
    interaction_id = "open_hmi_page"
    form_result = await context.get_interaction(interaction_id)
    if form_result:
        return {"state": 0, "message": "流程图已查看"}
    
    # 首次调用展示
    await add_card(
        context, 
        "tip", 
        f"流程图已生成，点击下方按钮查看。", 
        f"流程图准备就绪"
        )

    # 触发交互
    await context.require_interaction({
        "id": interaction_id,
        "title": "查看流程图",  # 添加title字段
        "type": "open_page",
        "open_page": hmiurl
    }) 
    return {"state": 1, "pending": True}

def combine_data(
        tags:List[dict],
        config_tags:dict[str,dict]
):
    # 格式化浮点数，保留6位小数
    def format_value(value):
        if isinstance(value, (float, int)):
            return f"{value:.6f}"
        return value
    
    tagsvalue = []
    for tag in tags:
        if not isinstance(tag, dict) or "tagName" not in tag:
            continue
        param = {
            "unit": config_tags.get(tag["tagName"], {}).get("unit","N/A"),
            "device": tag["tagName"],
            "desc": config_tags.get(tag["tagName"], {}).get("desc", "N/A"),
            "value": format_value(tag["value"]),
            "standard": "N/A"
        }
        if None in param.values():
            raise ValueError(f"无效的标签数据: {tag}")
        tagsvalue.append(param)

    return tagsvalue
# 按格式合并数据
def get_simulation_data(
    model_name:str,
    snapshot_name:str,
    initsnapshot_tags:List[dict],
    lastsnapshot_tags:List[dict],
    config_tags:List[dict]    
):
    if not all(isinstance(x, list) for x in [initsnapshot_tags, lastsnapshot_tags, config_tags]):
        raise ValueError("输入数据必须是列表类型")
    #是一个字典对象
    simulation_data = {
        "model_name": model_name,
        "initial_state": "初始工况",
        "steady_state": f"{snapshot_name}工况",
        "init_params": [],
        "steady_params": []
    }
    for model in config_tags:
        if not isinstance(model, dict) or model.get("running_model") != model_name:
            continue
        tagsconfig = {tag["name"]: tag for tag in model.get("tagsconfig", []) 
                     if isinstance(tag, dict) and "name" in tag}
        
        simulation_data["init_params"] = combine_data(initsnapshot_tags, tagsconfig)
        simulation_data["steady_params"] = combine_data(lastsnapshot_tags, tagsconfig)
        # 找到model就break
        break

    if not simulation_data["init_params"] or not simulation_data["steady_params"]:
        raise ValueError("生成仿真报告失败，数据源信息不完整")
    
    return simulation_data

#输出ots工艺仿真验证报告
async def generate_report(context:Context, hmiurl:str):
    await add_card(
        context, 
        "tip", 
        f"模拟仿真任务执行完成，正在生成报告，请稍等。。。 ", 
        f"仿真报告生成中"
        )
    # 从缓存中获取动态内容
    model_name  = await context.get_cache("running_model") or ""
    loaded_snapshot = await context.get_cache("loaded_snapshot") or ""
    pre_tagsvalue = await context.get_cache("pre_tagsvalue") or []
    last_tagsvalue = await context.get_cache("lastest_tagsvalue") or []
    running_model_tags = await context.get_cache("running_model_tags") or []
    screenshot_hmi = await context.call_tool("screenshot", params={
                                            "url": hmiurl,
                                            "full_page":True,
                                            "delay": 15
                                        }, module_name="basic")
    screenshot_base64 = screenshot_hmi.get("image", "")
    # 按照容器大小缩放图片，设置最大宽度和高度为100%，自适应容器
    # await context.add_view({
    #     "format": "html",
    #     "content": f"<img src='data:image/png;base64,{screenshot_base64}' style='max-width:100%;max-height:100%;display:block;margin:auto;' />"
    # })
    # 截图失败不返回，继续逻辑
    # if not screenshot_base64:
    #     raise ValueError("截图获取失败")
    await context.add_view({
        "format": "debug",
        "content": {
            "model_name": model_name,
            "loaded_snapshot": loaded_snapshot,
            "has_pre_tagsvalue": bool(pre_tagsvalue),
            "has_last_tagsvalue": bool(last_tagsvalue),
            "has_running_model_tags": bool(running_model_tags)
            # "screenshot_base64":screenshot_base64
        }
    })
    if not all([model_name, loaded_snapshot, pre_tagsvalue, last_tagsvalue, running_model_tags]):
        raise ValueError("生成仿真报告失败，未找到报告数据源信息")
    
    # 先组装合成
    docx_data = get_simulation_data(model_name, loaded_snapshot, pre_tagsvalue, last_tagsvalue, running_model_tags)

    # 加载模板
    # 获取当前脚本所在的目录
    current_script_path = os.path.dirname(os.path.abspath(__file__))
    template_file_path = os.path.join(current_script_path,"ots_report_template.docx")
    doc = DocxTemplate(template_file_path)

    # temp_image_path = os.path.join(current_script_path, "temp_screenshot.png")
    if screenshot_base64:
        # image_data = base64.b64decode(screenshot_base64.split(",")[1])
        try:
            image_data = base64.b64decode(screenshot_base64)
            # with open(temp_image_path, "wb") as f:
            #     f.write(image_data)
            # doc.replace_pic("pic_placeholder", image_data)
            # # image_stream = io.BytesIO(image_data)
            # docx_data["screenshot"] = {
            #     "image": image_data,  # 必须传入二进制数据
            #     "width": 500,  # 像素宽度（或使用Mm(150)等）
            #     "type": "png"  # 明确指定图片类型
            # }
            docx_data["screenshot"] = image_data
        except Exception as e:
            docx_data["screenshot"] = "（无截图）"
            # docx_data["screenshot"] = f'<img src="data:image/png;base64,{screenshot_base64}" width="500" />'
    else:
        docx_data["screenshot"] = "（无截图）"

    # doc.replace_pic("{{screenshot_placeholder}}", image_stream)  # 替换模板中的占位图
    # 强制图片替换（核心改进）
    if docx_data["screenshot"]:
        for paragraph in doc.get_docx().paragraphs:
            if "{{screenshot}}" in paragraph.text:
                paragraph.clear()  # 清空段落
                run = paragraph.add_run()
                run.add_picture(io.BytesIO(docx_data["screenshot"]), width=Mm(150))  # 直接插入二进制流

    # 填充数据
    doc.render(docx_data)
    # 删除临时图片
    # os.remove(temp_image_path)
    # doc = Document("./ots_report_template.docx")
    # formatted_data = {f"{{{{{key}}}}}": value for key, value in docx_data.items()}
    # formatted_data = {k: str(v) for k, v in formatted_data.items()}

    # 保存到内存
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    # 输出报告的二进制内容
    file_bytes = file_stream.read()

    object_name = f"reports/ots/{model_name}_{int(time.time())}_工艺仿真验证输出报告.docx"
    file_info = await context.add_file(f"{object_name}", file_bytes)
    # /tpt-app/chat-too-work/api/file?bucket=${bucket}&object=${object}
    # minio文件服务器的地址
    # minio_url = f"/tpt-app/chat-too-work/api/file?bucket=${file_info['bucket']}&object=${file_info['object']}"
    minio_obj = {
        "bucket":file_info['bucket'],
        "object":file_info['object']
    }

    await context.add_view({
        "format": "debug",
        "content": {
            "minio_obj": minio_obj
        }
    })

    await add_card(
        context, 
        "summary", 
        f"模拟仿真报告已成功生成，请点击 工艺仿真报告 查看。", 
        f"仿真报告生成成功"
        )

    await context.add_view({
            "format": "card",
            "content": {
                "type": 'summary_file',
                "title": '工艺仿真报告',
                "details": minio_obj,
                "description": "工艺仿真验证输出报告.docx"
            }
        })

@tool(version="*")
async def gethmiurl(context: Context, params: any):
    # 尝试切换hmi工程
    await tryto_switch_hmiproject(context)

    try:
        # 1. 获取服务器配置
        ip_address, port = ("localhost", "8121")
        if config := get_ots_web_server_config(context):
            ip_address, port = config

        # 2. 查询项目列表
        url = f"http://{ip_address}:{port}/queryProjectList"
        async with aiohttp.ClientSession() as session:
            # 查询项目列表
            async with session.post(
                url=url,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status != 200:
                    return {"state": -2, "error": "生成流程图信息失败"}
                
                data = await response.json()
                if data['result'] != 'success':
                    return {"state": -2, "error": "生成流程图信息未成功"}

                projects = json.loads(data['param'])

                # 3. 查找正在使用的项目
                active_project = next((p for p in projects if p['isUsed'] == '1'), None)
                if not active_project:
                    return {"state": -2, "error": "没有活动的工艺模型"}

                platformid = active_project['platformid']
                url = f"http://{ip_address}:{port}/queryPlatformInfo"
                data = {"platformid": platformid}

                # 4. 查询平台信息
                async with session.post(
                    url=url,
                    data=data
                ) as platform_response:
                    if platform_response.status != 200:
                        return {"state": -2, "error": "工艺模型信息查询失败"}
                    
                    platform_data = await platform_response.json()
                    if platform_data['result'] != 'success':
                        return {"state": -2, "error": "工艺模型信息查询未成功"}

                    platform_info = json.loads(platform_data['param'])
                    platformtype = platform_info['platformtype']
                    # 5. 获取客户端配置
                    client_ip, client_port = ("localhost", "8801")
                    if client_config := get_ots_web_client_config(context):
                        client_ip, client_port = client_config

                    # 6. 构建HMI url
                    hmiurl = f"/vxlaunch-tpt/ViewPage/{platformtype}/ViewContainer?frameVisible=show&tpt=true"
                    # hmiurl = f"http://{client_ip}:{client_port}/#/ViewPage/{platformtype}/ViewContainer?frameVisible=show&tpt=true"

                    result = await context.get_interaction("open_hmi_page")
                    if result is None:
                        await add_card(
                            context, 
                            "tip", 
                            "流程图已生成，点击下方按钮查看。", 
                            "流程图准备就绪"
                            )
                        await context.add_view({
                            "format": "tip",
                            "content": {
                                "type": 'default',
                                "title": '',
                                "content": "流程图已准备就绪，请点击下方按钮查看确认",
                                "description": "",
                                "details": ""
                            }
                        })
                        await context.require_interaction({
                            "id": "open_hmi_page",
                            "title": "查看流程图",  # 添加title字段
                            "type": "open_page",
                            "page_type": "once_view", # 单次执行，流程图只能打开一次
                            "description": "流程图确认后，结果在报告中展示，链接失效",
                            "open_page": hmiurl,
                        })
                        
                        return {}
                    
                    await context.add_view({
                        "format": "tip",
                        "content": {
                            "type": 'default',
                            "title": '',
                            "content": "TPT计算所需要的信息和数据已补齐，下一步将调用TPT进行训练和计算，预计需要3分钟，您可以关闭此回话离开，如有结果将及时通知您！",
                            "description": "",
                            "details": ""
                        }
                    })
                    await generate_report(context, hmiurl)
                    
                    return {
                        "state": 0,
                        "hmiurl":hmiurl
                    }
          
    except Exception as e:
        # print(f"处理过程中发生错误: {e}")
        return {"state": -1, "error": str(e)}


