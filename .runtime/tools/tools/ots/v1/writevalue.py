from __runner__ import tool, Context
import aiohttp
import json
import asyncio

import xml.etree.ElementTree as ET
import os
import ipaddress

from typing import Dict, Any, List,Literal, Union
from pydantic import BaseModel, ValidationError, field_validator, Field, model_validator  

from dataclasses import field

from enum import IntEnum


def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("模拟参数修改值失败，未找到工艺模型1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("模拟参数修改值失败，未找到工艺模型2")
        except ValueError:
            raise ValueError("模拟参数修改值异常，未找到工艺模型")
            
        return server_ip, server_port
    
    except Exception as e:
        return None

class DataType(IntEnum):
    """设备参数数据类型枚举"""
    BOOL = 0
    INT = 1
    DOUBLE = 2
    STRING = 3

# 定义与 schema 匹配的 Pydantic 模型
class TagDefinition(BaseModel):
    """设备参数基础定义（请求参数）"""
    tagName: str = Field(..., description="设备参数名称", example="Temperature")

class TagWithValue(TagDefinition):
    """带值的设备参数定义（响应结果）"""
    dataType: DataType = Field(..., description="设备参数类型，0:bool, 1:int, 2:double, 3:string", example=DataType.INT)
    value: Union[int, bool, float, str]

    @model_validator(mode="before")
    @classmethod
    def validate_value_before(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """模型级验证（替代字段级验证）"""
        value = data.get("value")
        data_type = data.get("dataType")

        if isinstance(value, str) and data_type is not None:
            try:
                if data_type == DataType.BOOL:
                    data["value"] = value.lower() in ('true', '1', 't', 'y', 'yes')
                elif data_type == DataType.INT:
                    data["value"] = int(value)
                elif data_type == DataType.DOUBLE:
                    data["value"] = float(value)
            except ValueError as e:
                raise ValueError(f"类型转换失败: {str(e)}")

        return data

    @model_validator(mode="after")
    def validate_value_after(self):
        """后置类型验证"""
        type_map = {
            DataType.BOOL: bool,
            DataType.INT: int,
            DataType.DOUBLE: (float, int),
            DataType.STRING: str
        }
        if not isinstance(self.value, type_map[self.dataType]):
            raise ValueError(f"数据类型 {self.dataType} 的值类型不匹配")
        return self

class WriteValueParams(BaseModel):
    """设置设备参数值请求参数（匹配JSON Schema）"""
    tags: List[TagWithValue] = Field(
        ...,
        min_items=1,
        description="设备参数列表"
    )

class WriteValueResult(BaseModel):
    state: int

"""
设置工艺模型设备的参数值

Args:
    context: 执行上下文（日志、配置等）
    params: 设备参数列表，每个元素应包含:
        - tagName: 设备参数名称 (str)
        - dataType: 设备参数类型 (int/bool/double/string)
        - value: 设备参数值（需与dataType匹配）

Returns:
    {
        "state": 0/1 表示失败/成功
    }

Raises:
    ValueError: 参数验证失败时抛出
    aiohttp.ClientError: HTTP请求失败时抛出
"""

@tool(version="*")
async def writevalue(context: Any, params: Dict[str, Any]) -> Dict[str, Any]:
    # 新增动态表单，当调用接口未传参时，弹出表单让用户输入
    # 构造动态表单schema
    form_schema = {
        "type": "object",
        "properties": {
            "tags": {
                "type": "array",
                "title": "设备参数配置列表",
                "items": {
                    "type": "object",
                    "properties": {
                        "tagName": {
                            "type": "string",
                            "title": "设备参数名称"
                        },
                        "dataType": {
                            "type": "integer",
                            "title": "数据类型",
                            "enum": [0, 1, 2, 3],
                            "enumNames": ["BOOL", "INT", "DOUBLE", "STRING"]
                        },
                        "value": {
                            "type": "string",
                            "title": "值",
                            "description": "根据类型输入：true/false | 数字 | 字符串"
                        }
                    },
                    "required": ["tagName", "dataType", "value"]
                }
            }
        },
        "required": ["tags"]
    }
 
    # 获取交互结果或初始化表单
    interaction_id = "writevalue_form"
    form_result = await context.get_interaction(interaction_id)
    
    if form_result is None:
        # 填充默认值（当有传参时）
        default_tags = []
        if params and "tags" in params:
            for tag in params["tags"]:
                default_tags.append({
                    "tagName": tag.get("tagName", ""),
                    "dataType": tag.get("dataType", 1),  # 默认INT类型
                    "value": str(tag.get("value", ""))    # 保持字符串类型方便编辑
                })
 
        # 弹出表单交互
        context.require_interaction({
            "id": interaction_id,
            "title": "工艺模型设备参数配置",
            "type": "form",
            "form": {
                "schema": form_schema,
                "default": {
                    "tags": default_tags
                }
            }
        })
        return {}  # 等待用户输入
 
    # 处理表单提交
    try:
        # 类型转换逻辑
        processed_tags = []
        for item in form_result["tags"]:
            # 类型转换
            dtype = DataType(item["dataType"])
            raw_value = item["value"]
            
            if dtype == DataType.BOOL:
                value = raw_value.lower() in ('true', '1', 't', 'y', 'yes')
            elif dtype == DataType.INT:
                value = int(raw_value)
            elif dtype == DataType.DOUBLE:
                value = float(raw_value)
            else:
                value = raw_value  # STRING类型直接保留
 
            processed_tags.append(TagWithValue(
                tagName=item["tagName"],
                dataType=dtype,
                value=value
            ))
 

    # 1. 参数验证
    # try:
        # validated_params = WriteValueParams.model_validate(params, strict=False)
        validated_params = WriteValueParams(tags=processed_tags)
    except ValidationError as e:
        # context.logger.error(f"参数验证失败: {str(e)}")
        # print(f"参数验证失败: {str(e)}")
        raise ValueError(f"Invalid parameters: {e.errors()}") from e

    # 2. 准备请求数据
    request_data = [tag.model_dump() for tag in validated_params.tags]
    
    # 3. 发送HTTP请求
    url = "http://localhost:9999/api/writevalue"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                url = f"http://{host}:{port}/api/writevalue"
            else:
                raise ValueError("设置设备参数值失败，未找到工艺模型位置")
    except ValueError as ve:
        raise ValueError("设置设备参数值失败，工艺模型位置坐标解析失败")
    except Exception as e:
        raise ValueError("设置设备参数值异常，寻找工艺模型位置发生错误")

    headers = {"Content-Type": "application/json"}  

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url, 
                json=request_data, 
                headers=headers,
                timeout=5
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return {
                        "state": result.get("state")
                    }
        
        except asyncio.TimeoutError:
            # print("请求超时")
            return{
                "state": -2
            }            
        except aiohttp.ClientError as e:
            # print(f"请求失败：{e}")
            return {
                "state": -1
            }
        except Exception as e:
            # print(f"未知错误: {str(e)}")
            # context.logger.error(f"未知错误: {str(e)}")
            raise