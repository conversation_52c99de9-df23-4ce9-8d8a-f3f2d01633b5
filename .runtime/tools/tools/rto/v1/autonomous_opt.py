import json.tool
import requests
import json
import os
import time
import io
from __runner__ import tool, Context
import base64
import pandas as pd
from io import StringIO
import ast
from urllib.parse import urlencode, quote
import tempfile
# from pypandoc import convert_text

# from minio import Minio

# 初始化MinIO客户端
# minio_client = Minio(
#     "seak8sm2.supcon5t.com:30350",  # MinIO地址ip:port
#     access_key="admin",
#     secret_key="Supcon1304",
#     secure=False  # 使用HTTP设为False
# )
 
### 运行时算法返回结构多了"\"转换,全局方法，其它模块也引用
def convert_runtime_json(params: any):
    json_str = params
    try:
        # 先解析外层JSON
        outer_json = json.loads(json_str)
        if 'data' in outer_json:
            # 解析内层JSON字符串
            inner_data = json.loads(outer_json['data'])
            # 重新构建JSON对象
            outer_json['data'] = inner_data
            return json.dumps(outer_json, indent=2, ensure_ascii=False)
        return json_str
    except json.JSONDecodeError as e:
        # print(f"JSON解析错误: {e}")
        pass
    return json_str


# 主要实现的功能是 稳态/优化算法的参数查询、算法执行、稳态率查询
### 稳态检测算法部分
@tool(version="*")
async def query_steady_params(context: Context, params: any):
    node_name = params['nodeName']  # 输入工厂子节点
    in_params = {'nodeName': node_name}
    in_data = {
        "data": in_params
    }
    autonomous_opt_url = context.config["autonomous_opt_url"]
    response = requests.post(url=autonomous_opt_url + '/api/rto/querySteadyParams'
                             , data=json.dumps(in_data), headers={
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(response.text)
    json_obj["content"]["nodeName"] = node_name
    return json_obj


@tool(version="*")
async def exec_steady(context: Context, params: any):
    node_name = params['nodeName']  # 输入工厂子节点
    in_params = {'nodeName': node_name}
    in_data = {
        "data": in_params
    }
    autonomous_opt_url = context.config["autonomous_opt_url"]
    response = requests.post(url=autonomous_opt_url + '/api/rto/execSteady'
                             , data=json.dumps(in_data), headers={
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(response.text)
    return json_obj


@tool(version="*")
async def steady_config_page(context: Context, params: any):
    address = "/operate-optimization-web/operate-optimization/detection"
    result = await context.get_interaction("open_page")
    if result is None:
        context.require_interaction({
            "id": "open_page",
            "title": "打开稳态装置配置页面",
            "type": "open_page",
            "open_page": address
        })
        return {}
    else:
        return {
            "output": result
        }


### 优化算法部分
# 查询优化算法参数
@tool(version="*")
async def query_opt_params(context: Context, params: any):
    node_name = params['nodeName']  # 输入工厂子节点
    in_params = {'nodeName': node_name}
    in_data = {
        "data": in_params
    }
    autonomous_opt_url = context.config["autonomous_opt_url"]
    response = requests.post(url=autonomous_opt_url + '/api/rto/queryOptParams'
                             , data=json.dumps(in_data), headers={
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

    json_obj = json.loads(response.text)
    # 融合接口
    # 1、如果返回值存在配置，返回结果
    # 2、如果返回值不存在配置，则需要跳转配置界面，调用opt_config_page接口
    json_obj["content"]["nodeName"] = node_name
    isExist = json_obj["content"]['isExist']
    if isExist is False:
        opt_config_page(context, params)
    return json_obj


# 执行优化算法
@tool(version="*")
async def exec_opt(context: Context, params: any):
    node_name = params['nodeName']  # 输入工厂子节点
    in_params = {'nodeName': node_name}
    in_data = {
        "data": in_params
    }
    autonomous_opt_url = context.config["autonomous_opt_url"]
    response = requests.post(url=autonomous_opt_url + '/api/rto/execOpt'
                             , data=json.dumps(in_data), headers={
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    # print(response.text)
    json_obj = json.loads(response.text)
    return json_obj


@tool(version="*")
async def opt_config_page(context: Context, params: any):
    address = "/operate-optimization-web/operate-optimization/optimize"
    result = await context.get_interaction("open_page")
    if result is None:
        context.require_interaction({
            "id": "open_page",
            "title": "打开装置优化配置页面",
            "type": "open_page",
            "open_page": address
        })
        return {}
    else:
        return {
            "output": result
        }


@tool(version="*")
async def opt_file_upload(context: Context, params: any):
    formResult = await context.get_interaction("file_upload_form")
    if formResult is None:
        pre_info = "在使用TPT大模型进行优化前，需要上传离线数据集，可以选择一个已有的数据集，也可以重新上传一个新的数据集。" \
                   "通过下面的文件上传界面进行上传或选择已有的数据集后，点击提交按钮。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })
        context.require_interaction({
            "id": "file_upload_form",
            "title": "请上传位号历史数据文件",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "请上传位号历史数据文件",
                    "properties": {
                        "file": {
                            "title": "位号历史数据文件(.csv)，仅支持utf-8编码的.csv格式文件",
                            "type": "string",
                            "format": "data-url"
                        }
                    },
                    "required": ["file"]
                },
                "default": {}
            }
        })
    else:
        if 'file' not in formResult:
            return {
                'fileId': -1
            }
        else:
            pre_info = "现在我会对选择的数据集进行识别和分析，获取此数据集内包含的变量和数据信息。"
            await context.add_view({
                "format": "markdown",
                "content": pre_info
            })
            ## 暂停3s
            time.sleep(3)
            await context.add_view({
                "format": "card",
                "content": {
                    "type": 'markdown',
                    "title": '数据集解析',
                    "description": "数据集解析信息展示",
                    "details": """
通过解析数据，可知当前数据集数据的开始时间为：2024/9/26 13:21:00，结束时间为2024/12/2 18:46:00，数据量为：819，数据频次为：2h。具体信息如下：
## 数据集解析信息表
>   
| 位号描述 | 平均值 | 最小值 | 最大值 |
| --- | --- | --- | --- |
| LJ.三期电解碱密度 | 1.31 | 1.31 | 1.31 |
| A线三期264浓度计浓度 | 205.20 | 196.98 | 215.59 |
| A线.三期电解碱液循环罐进口浓度（修正后） | 32.16 | 32.12 | 32.20 |
| A线.电槽A单槽电压显示 | 488.38 | 455.92 | 497.18 |
| A线.电槽B单槽电压显示 | 477.57 | 445.77 | 483.39 |
| A线.电槽C单槽电压显示 | 479.14 | 445.63 | 484.87 |
| A线.电槽D单槽电压显示 | 476.20 | -79.97 | 502.50 |
| A线.电槽E单槽电压显示 | 479.86 | 451.14 | 486.94 |
| A线.电槽F单槽电压显示 | 492.75 | 462.50 | 501.67 |
| A线.电槽G单槽电压显示 | 483.70 | 453.80 | 492.49 |
| A线.电槽H单槽电压显示 | 480.35 | 450.42 | 487.84 |
| FV_2A211A（进电解槽A盐酸流量调节）测量值 | 590.19 | 317.56 | 644.60 |
| FV_2A211B（进电解槽B盐酸流量调节）测量值 | 548.13 | 211.87 | 620.31 |
| FV_2A211C（进电解槽C盐酸流量调节）测量值 | 519.28 | 191.06 | 643.85 |
| FV_2A211D（进电解槽D盐酸流量调节）测量值 | 564.03 | 0.15 | 672.01 |
| FV_2A211E（进电解槽E盐酸流量调节）测量值 | 624.86 | 313.52 | 678.60 |
| FV_2A211F（进电解槽F盐酸流量调节）测量值 | 265.58 | 104.92 | 359.77 |
| FV_2A211G（进电解槽G盐酸流量调节）测量值 | 562.18 | 264.01 | 677.18 |
| FV_2A211H（进电解槽H盐酸流量调节）测量值 | 627.86 | 342.15 | 676.40 |
| FV_2A221A（进电解槽纯水流量调节）测量值 | 22.31 | 12.40 | 24.90 |
| FV_2A231A（进电解槽A盐水流量调节）测量值 | 39.21 | 21.09 | 42.82 |
| FV_2A231B（进电解槽B盐水流量调节）测量值 | 37.92 | 19.76 | 40.15 |
| FV_2A231C（进电解槽C盐水流量调节）测量值 | 40.01 | 20.95 | 42.26 |
| FV_2A231D（进电解槽D盐水流量调节）测量值 | 33.68 | 0.01 | 42.08 |
| FV_2A231E（A线电解槽E盐水调节）测量值 | 39.02 | 23.29 | 41.99 |
| FV_2A231F（进电解槽F盐水流量调节）测量值 | 38.62 | 22.77 | 41.16 |
| FV_2A231G（进电解槽G盐水流量调节）测量值 | 39.41 | 23.61 | 42.48 |
| FV_2A231H（进电解槽H盐水流量调节）测量值 | 40.15 | 23.65 | 42.83 |
| FV_2A232A（进电解槽A碱液流量调节）测量值 | 49.20 | 49.16 | 49.21 |
| FV_2A232B（进电解槽B碱液流量调节）测量值 | 49.20 | 49.19 | 49.21 |
| FV_2A232C（A线电解槽C碱液调节）测量值 | 49.20 | 49.17 | 49.24 |
| FV_2A232D（进电解槽D碱液流量调节）测量值 | 48.31 | 0.01 | 49.25 |
| FV_2A232E（A线电解槽E碱液调节）测量值 | 49.20 | 49.15 | 49.26 |
| FV_2A232F（进电解槽F碱液流量调节）测量值 | 49.20 | 49.14 | 49.21 |
| FV_2A232G（进电解槽G碱液流量调节）测量值 | 49.20 | 49.15 | 49.42 |
| FV_2A232H（A线电解槽H碱液调节）测量值 | 49.20 | 49.12 | 49.21 |
| FV_2A265A（进电解槽淡盐水流量调节）测量值 | 43.10 | 26.11 | 46.92 |
| FV_2A425A（进电解槽盐酸总管盐酸流量调节）测量值 | 2218.54 | 1097.72 | 2503.68 |
| A线.三期去外管成品碱流量 | 64.97 | 39.30 | 71.35 |
| A线.进电槽纯水流量 | 2112.89 | 1045.44 | 2384.88 |
| A线.电槽A交流电流显示 | 14.75 | 7.94 | 16.11 |
| A线.电槽B交流电流显示 | 15.20 | 7.92 | 16.10 |
| A线.电槽C交流电流显示 | 15.38 | 8.05 | 16.24 |
| A线.电槽D交流电流显示 | 12.77 | -0.09 | 16.00 |
| A线.电槽E交流电流显示 | 15.01 | 8.96 | 16.15 |
| A线.电槽F交流电流显示 | 15.18 | 8.95 | 16.18 |
| A线.电槽G交流电流显示 | 15.01 | 8.99 | 16.18 |
| A线.电槽H交流电流显示 | 15.10 | 8.89 | 16.11 |
| A线.进精盐水罐PH检测 | 9.18 | 9.07 | 9.43 |
| A线.三期A槽出槽淡盐水PH | 3.53 | 2.37 | 5.16 |
| A线.三期B槽出槽淡盐水PH | 3.80 | 3.72 | 3.83 |
| A线.三期C槽出槽淡盐水PH | 3.37 | 2.91 | 3.57 |
| A线.三期D槽出槽淡盐水PH | 3.65 | 3.00 | 11.37 |
| A线.三期E槽出槽淡盐水PH | 3.08 | 2.98 | 3.36 |
| A线.三期F槽出槽淡盐水PH | 3.80 | 3.71 | 3.82 |
| A线.三期G槽出槽淡盐水PH | 3.80 | 3.69 | 3.95 |
| A线.三期H槽出槽淡盐水PH | 3.11 | 2.90 | 3.45 |
| A线.阳极循环罐出口PH | 3.50 | 3.39 | 3.58 |
| A线.电槽A单槽槽温显示 | 84.93 | 76.85 | 86.85 |
| A线.电槽B单槽槽温显示 | 84.25 | 76.65 | 86.53 |
| A线.电槽C单槽槽温显示 | 84.52 | 76.58 | 86.69 |
| A线.电槽D单槽槽温显示 | 84.24 | 44.21 | 87.79 |
| A线.电槽E单槽槽温显示 | 84.55 | 77.42 | 86.67 |
| A线.电槽F单槽槽温显示 | 85.72 | 78.63 | 87.81 |
| A线.电槽G单槽槽温显示 | 85.42 | 78.12 | 87.25 |
| A线.电槽H单槽槽温显示 | 84.64 | 77.49 | 86.85 |
| conc_HCl | 31.40 | 31.40 | 31.40 |
| 氯碱.一期威胜.3#整流变.组合有功总电能 | 18506.42 | 0 | 24850 |
| 氯碱.一期威胜.2#整流变.组合有功总电能 | 17626.06 | 0 | 25200 |
| 氯碱.一期威胜.1#整流变.组合有功总电能 | 18984.20 | 0 | 25200 |
| 氯碱.一期威胜.0#整流变.组合有功总电能 | 22917.95 | 0 | 25200 |
| 进槽碱液浓度 | 30.33 | 30.12 | 31.14 |
| A槽入口酸度 | 0.07 | 0.07 | 0.07 |
| A槽出口酸度 | 0.00 | 0.00 | 0.00 |
| A槽阳极效率 | 95.00 | 94.99 | 95.13 |
| A槽吨碱电耗 | 2101.05 | 1961.04 | 2138.92 |
| B槽入口酸度 | 0.07 | 0.05 | 0.07 |
| B槽出口酸度 | 0.00 | 0.00 | 0.00 |
| B槽阳极效率 | 95.30 | 95.00 | 96.02 |
| B槽吨碱电耗 | 2048.00 | 1897.27 | 2070.72 |
| C槽入口酸度 | 0.06 | 0.04 | 0.07 |
| C槽出口酸度 | 0.00 | 0.00 | 0.00 |
| C槽阳极效率 | 95.48 | 95.01 | 96.27 |
| C槽吨碱电耗 | 2050.77 | 1891.87 | 2075.55 |
| D槽入口酸度 | 0.08 | 0.00 | 0.11 |
| D槽出口酸度 | 0.00 | 0.00 | 0.00 |
| D槽阳极效率 | 94.82 | 94.22 | 98.35 |
| D槽吨碱电耗 | 2054.59 | -332.65 | 2162.86 |
| E槽入口酸度 | 0.07 | 0.06 | 0.08 |
| E槽出口酸度 | 0.00 | 0.00 | 0.00 |
| E槽阳极效率 | 94.89 | 94.84 | 95.45 |
| E槽吨碱电耗 | 2066.68 | 1932.67 | 2096.98 |
| F槽入口酸度 | 0.03 | 0.02 | 0.04 |
| F槽出口酸度 | 0.00 | 0.00 | 0.00 |
| F槽阳极效率 | 96.75 | 96.36 | 97.32 |
| F槽吨碱电耗 | 2081.40 | 1944.80 | 2121.05 |
| G槽入口酸度 | 0.07 | 0.05 | 0.07 |
| G槽出口酸度 | 0.00 | 0.00 | 0.00 |
| G槽阳极效率 | 95.20 | 94.83 | 95.82 |
| G槽吨碱电耗 | 2076.56 | 1935.54 | 2122.08 |
| H槽入口酸度 | 0.07 | 0.06 | 0.07 |
| H槽出口酸度 | 0.00 | 0.00 | 0.00 |
| H槽阳极效率 | 94.89 | 94.84 | 95.44 |
| H槽吨碱电耗 | 2068.82 | 1935.19 | 2101.75 |

"""
                }
            })
        return {
            'file': formResult["file"]
        }


@tool(version="*")
async def parse_opt_file_upload(context: Context, params: any):
    file = params['file']
    json_obj = {
        "data": ''
    }
    result = await context.get_interaction("open_opt_param_page")
    if result is None:
        pre_info = "现将已确认的关键信息输入至TPT大模型中，通过查询历史数据库、联网搜索等方式，推理此问题的优化目标、优化变量及约束条件等核心要素。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })

        # 写在此为后续提供数据给页面展示
        file_data = file.split(',')[1]  # 分离出base64编码部分
        decoded_data = base64.b64decode(file_data)
        try:
            df = pd.read_csv(StringIO(decoded_data.decode('utf-8')))
        except UnicodeDecodeError:
            df = pd.read_csv(StringIO(decoded_data.decode('gbk')))
        specific_rows = df.iloc[:1, 1:].to_dict("records")
        result = []
        for item in specific_rows:
            for key, value in item.items():
                result.append({
                    "tag_name": key,
                    "description": value
                })

        llm_param = {
            "tag_descriptions": result
        }

        response = requests.post(
            url=context.config["llm_agent_opt_generate_vals_url"],
            data=json.dumps(llm_param, indent=2, ensure_ascii=False), headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })
        # 不规则的模版解析出来的json结构存在问题 调用出错
        json_obj = json.loads(response.text)
        json_obj["data"]["offLineData"] = file

        await context.add_view({
            "format": "markdown",
            "content":
                """
                #### 根据用户提问，结合输入数据中的位号描述、历史数据，识别出氯碱装置吨碱电耗最低的优化方案：
                **1.优化目标**：最小化电耗（KWh/t），当前稳态检测通过；
                **2.优化变量**：8个电解槽的交流电流（8-17KA可调），需同步控制槽电压稳定性（±0.05V）；
                **3.约束条件**：电解总管碱浓度需保持32%以上；
                **4.模型配置**：采用30秒采样频率的回归模型。
                """
        })
        address = "/tpt-auto-model/optForm"
        context.require_interaction({
            "id": "open_opt_param_page",
            "title": "优化参数信息",
            "type": "open_page",
            "open_page": address,
            "page_type": "execute",
            # 增加页面传参，保持回填数据一致
            "page_data": json_obj

        })
    else:
        # 由前端页面带入，否则需要解析file并调用LLM接口重新生成
        json_obj["data"] = result
        json_obj["data"]["offLineData"] = file

    return json_obj


@tool(version="*")
async def exec_opt_file_upload(context: Context, params: any):
    await context.add_view({
        "format": "markdown",
        "content":
            """
            根据提供的优化目标、优化变量及约束条件相关关键工艺参数，基于上传的历史数据，首先对TPT模型进行微调训练
            """
    })

    ## 暂停30s
    time.sleep(30)

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": 'TPT大模型微调',
            "description": "微调过程",
            "details": """
对TPT模型微调训练的方案如下：<br/>
#### 模型架构设计
 - 输入层：'FV_2A221A（进电解槽纯水流量调节）测量值',
 'A线.进精盐水罐PH检测',
 'FV_2A265A（进电解槽淡盐水流量调节）测量值',
 '盐酸浓度',
 'FV_2A211A（进电解槽A盐酸流量调节）测量值',
 'FV_2A231A（进电解槽A盐水流量调节）测量值',
 'A线.电槽A交流电流显示',
 'A线.电槽A单槽槽温显示',
 'FV_2A211B（进电解槽B盐酸流量调节）测量值',
 'FV_2A231B（进电解槽B盐水流量调节）测量值',
 'A线.电槽B交流电流显示',
 'A线.电槽B单槽槽温显示',
 'FV_2A211C（进电解槽C盐酸流量调节）测量值',
 'FV_2A231C（进电解槽C盐水流量调节）测量值',
 'A线.电槽C交流电流显示',
 'A线.电槽C单槽槽温显示',
 'FV_2A211D（进电解槽D盐酸流量调节）测量值',
 'FV_2A231D（进电解槽D盐水流量调节）测量值',
 'A线.电槽D交流电流显示',
 'A线.电槽D单槽槽温显示',
 'FV_2A211E（进电解槽E盐酸流量调节）测量值',
 'FV_2A231E（A线电解槽E盐水调节）测量值',
 'A线.电槽E交流电流显示',
 'A线.电槽E单槽槽温显示',
 'FV_2A211F（进电解槽F盐酸流量调节）测量值',
 'FV_2A231F（进电解槽F盐水流量调节）测量值',
 'A线.电槽F交流电流显示',
 'A线.电槽F单槽槽温显示',
 'FV_2A211G（进电解槽G盐酸流量调节）测量值',
 'FV_2A231G（进电解槽G盐水流量调节）测量值',
 'A线.电槽G交流电流显示',
 'A线.电槽G单槽槽温显示',
 'FV_2A211H（进电解槽H盐酸流量调节）测量值',
 'FV_2A231H（进电解槽H盐水流量调节）测量值',
 'A线.电槽H交流电流显示',
   'A线.电槽H单槽槽温显示'
 - 输出层：A线.电槽A单槽电压显示、A线.电槽B单槽电压显示、A线.电槽C单槽电压显示、A线.电槽D单槽电压显示、A线.电槽E单槽电压显示、A线.电槽F单槽电压显示、A线.电槽G单槽电压显示、A线.电槽H单槽电压显示、A线.三期电解碱液循环罐进口浓度
 - 训练数据：2025/4/1 1:34:00至2025/4/2 12:34:00 （离线数据的前70%）
 - 测试数据：2025/4/2 12:34:00至2025/4/2 21:34:00（离线数据的后30%）
#### TPT大模型微调过程
**模型微调开始**：
>    - Epoch [10/200], Train_Loss: 19.8764, Test_Loss: 25.1233
>    - Epoch [20/200], Train_Loss: 11.2345, Test_Loss: 15.2215
>    - Epoch [30/200], Train_Loss: 6.7890, Test_Loss: 8.2976
>    - Epoch [40/200], Train_Loss: 5.7590, Test_Loss: 8.1427
>    - Epoch [50/200], Train_Loss: 5.2215, Test_Loss: 8.10134
>    - Epoch [60/200], Train_Loss: 3.3890, Test_Loss: 4.2763
>    - Epoch [70/200], Train_Loss: 2.7590, Test_Loss: 3.3178
>    - Epoch [80/200], Train_Loss: 1.1180, Test_Loss: 2.6712
>    - Epoch [90/200], Train_Loss: 1.0120, Test_Loss: 2.0923
>    - Epoch [100/200], Train_Loss: 0.7890, Test_Loss: 1.8723
>    - Epoch [110/200], Train_Loss: 0.7239, Test_Loss: 1.7659
>    - Epoch [120/200], Train_Loss: 0.6890, Test_Loss: 1.3052
>    - Epoch [130/200], Train_Loss: 0.6125, Test_Loss: 0.9245
>    - Epoch [140/200], Train_Loss: 0.5823, Test_Loss: 0.8237
>    - Epoch [150/200], Train_Loss: 0.3643, Test_Loss: 0.8121
>    - Epoch [160/200], Train_Loss: 0.2694, Test_Loss: 0.5643
>    - Epoch [170/200], Train_Loss: 0.2502, Test_Loss: 0.5132
>    - Epoch [180/200], Train_Loss: 0.2390, Test_Loss: 0.4267
>    - Epoch [190/200], Train_Loss: 0.2341, Test_Loss: 0.4113
>    - Epoch [200/200], Train_Loss: 0.1987, Test_Loss: 0.3891
>
**模型微调结束**。<br/>
生成模型model_2025_06_06.mdl（模型名称），当前模型已保存至”C:/usrs/model“路径，用于优化计算。<br/>
#### 模型性能评估
模型训练指标如下表：
| A槽电压_MSE | B槽电压_MSE | C槽电压_MSE | D槽电压_MSE | E槽电压_MSE | F槽电压_MSE | G槽电压_MSE | H槽电压_MSE |
|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
| 1.072018245 | 7.253306573 | 0.9560474   | 4977.2451   | 0.719479471 | 8.072056452 | 1.263075028 | 0.898875812 |

以A电解槽为例，以下为模型预测效果展示：<br/>
![1](http://obp-dev.supcon5t.com:31501/operate-optimization-web/H2SPredict.png)<br/>

"""
        }
    })

    await context.add_view({
        "format": "markdown",
        "content":
            """
            TPT大模型微调结束，现在调用微调后的模型进行优化计算。我将以电解_吨碱电耗最小为优化目标，以电解总管碱浓度需保持32%以上等为优化约束，对A线.电槽A交流电流显示、A线.电槽B交流电流显示、A线.电槽C交流电流显示、A线.电槽D交流电流显示、A线.电槽E交流电流显示、A线.电槽F交流电流显示、A线.电槽G交流电流显示、A线.电槽H交流电流显示进行优化求解，得到以下结果：
            """
    })

    ## 暂停5s
    time.sleep(5)
    opt_param_data = params["data"]
    response = requests.post(
        url=context.config["opt_alg_exec_runtime_url"],
        data=json.dumps(opt_param_data, indent=2, ensure_ascii=False),
        headers={
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
    json_obj = json.loads(convert_runtime_json(response.text))
    markdownRet = ''
    markdownRet += f"| {json_obj['data']['output']['optObject']['variable']} | {json_obj['data']['output']['optObject']['optimizationValue']} {json_obj['data']['output']['optObject']['unit']}| {json_obj['data']['output']['optObject']['currentValue']}  {json_obj['data']['output']['optObject']['unit']}| {round(float(json_obj['data']['output']['optObject']['optimizationValue']) - float(json_obj['data']['output']['optObject']['currentValue']), 2)}  {json_obj['data']['output']['optObject']['unit']} |\n"
    for item in json_obj['data']['output']['optResult']:
        markdownRet += f"| {item['describe']} | {item['optimizationValue']} {item['unit']}| {item['currentValue']}  {item['unit']}| {round(float(item['optimizationValue']) - float(item['currentValue']), 2)}  {item['unit']} |\n"

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '模型优化计算',
            "description": "优化计算结果",
            "details": f""" 
**优化计算结果如下表：**
| 名称                         | 优化前         | 优化后         | 变化幅度       |
|------------------------------|----------------|----------------|----------------|
{markdownRet}
"""
        }
    })

    return json_obj


### 结合TPT模型微调及开放问题场景
# 优化问题定义与标准化
@tool(version="*")
async def opt_question_definition_standard(context: Context, params: any):
    formResult = await context.get_interaction("opt_question_define_form")
    if formResult is None:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "需要确认您的详细需求，请补充信息：",
                "description": "",
                "details": ""
            }
        })
        await context.add_view({
            "format": "markdown",
            "content": "基于TPT的深度分析与智能识别，我已获取了问题的关键信息。为进一步提升最终效果，请对以下关键信息进行确认，以便我们更高效地推进后续工作。"
        })

        optObject_var = params["optObject"]["description"]
        optVariables_str = "、".join(params["optVariables"]) if params["optVariables"] else ""
        constraint_str = "、".join(params["constraint"]) if params["constraint"] else ""

        context.require_interaction({
            "id": "opt_question_define_form",
            "title": "关键信息补充与确认",
            "type": "form",
            "form": {
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        # "industry": {
                        #     "title": "所属行业",
                        #     "type": "string"
                        # },
                        # "device_type": {
                        #     "title": "装置类型",
                        #     "type": "string"
                        # },
                        "optObject_var": {
                            "title": "您希望重点优化的目标变量",
                            "type": "string"
                        },
                        "optVariables_str": {
                            "title": f"您希望可以优化的操作变量(非必填)",
                            "type": "string"
                        },
                        "constraint_str": {
                            "title": f"您重点考虑的约束变量(非必填)",
                            "type": "string"
                        },
                        # "specified_algorithms": {
                        #     "title": "默认使用TPT时序大模型进行微调训练，您是否需要使用传统的机器学习方法：",
                        #     "type": "integer",
                        #     "enum": [0, 1],
                        #     "enumNames": ["TPT时序大模型", "传统机器学习模型"],
                        #     "widget": "radio",
                        # },
                        # "train_device_type": {
                        #     "title": "默认使用GPU进行微调训练，您是否需要使用CPU：",
                        #     "type": "string",
                        #     "enum": ["gpu", "cpu"],
                        #     "enumNames": ["GPU", "CPU"],
                        #     "widget": "radio",
                        # },
                        # "train_device_type":{
                        #     "title": "默认使用GPU基于TPT时序大模型进行微调训练，您是否需要使用CPU：",
                        #     "type": "string",
                        #     "enum": ["gpu", "cpu"],
                        #     "enumNames": ["GPU", "CPU"],
                        #     "widget": "radio",
                        # },
                        "file": {
                            "title": "请上传问题中提及的工艺相关资料文件（包括但不限于工艺规程、操作手册、工艺卡片等），以便让TPT自动理解，生成针对性的优化方案(非必填)",
                            "type": "string",
                            "format": "file-object",
                            "widget": "tptfile"
                        }

                    },
                    "order": ["industry", "device_type", "optObject_var", "optVariables_str", "constraint_str",
                              "specified_algorithms", "train_device_type", "file"],
                    "required": ["industry", "device_type", "optObject_var"]
                },
                "default": {
                    "industry": params["domain_knowledge"]["industry"],
                    "device_type": params["domain_knowledge"]["device_type"],
                    "optObject_var": optObject_var,
                    "optVariables_str": optVariables_str,
                    "constraint_str": constraint_str,
                    "specified_algorithms": 0,
                    "train_device_type": "gpu"
                }
            }
        })
    else:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "请稍作等待，后续需要您上传数据",
                "description": "",
                "details": ""
            }
        })
        output_result = params
        output_result["domain_knowledge"] = {
            "industry": formResult["industry"],
            "device_type": formResult["device_type"]
        }
        output_result["optObject"]["description"] = formResult["optObject_var"]
        output_result["optVariables"] = formResult["optVariables_str"].split("、") if formResult[
            "optVariables_str"] else []
        output_result["constraint"] = formResult["constraint_str"].split("、") if formResult["constraint_str"] else []
        output_result["specified_algorithms"] = formResult["specified_algorithms"]
        output_result["train_device_type"] = formResult["train_device_type"]
        if 'file' not in formResult:
            output_result["upload_file"] = {
                "bucket": "recommend",
                "object": "",
                "name": ""
            }
        else:
            output_result["upload_file"] = json.loads(formResult["file"])

        prompt_str = f"""
# 角色
你是一个专业的问题分析师，能够对输入的json数据进行精准解读与分析。你要根据json结构中的不同数据组合情况，按照对应的填充规则进行替换和总结分析，并确保总结内容总字数少于200字。

## 技能
### 技能 1: 分析json数据
1. 接收用户输入的json数据，数据结构如下：
{output_result}

2. 根据json数据中的不同情况进行分析总结：
    - 如果“optVariables”与“constraint”都有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望通过优化包括[5]的变量参数，使得目标[3]达到[4]，明确关键的约束条件为[6]，建模算法选择[7]。
    - 如果“optVariables”与“constraint”都没有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望使得目标[3]达到[4]，建模算法选择[7]。未指定明确的优化变量及约束条件，未填写内容后续我将根据您上传的数据自行判断。
    - 如果“optVariables”有内容填写，“constraint”没有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望通过优化包括[5]的变量参数，使得目标[3]达到[4]，建模算法选择[7]。未指定明确的约束条件，未填写内容后续我将根据您上传的数据自行判断。
    - 如果“optVariables”没有内容填写，“constraint”有内容填写，参考以下示例，总结分析：已明确您的意图，识别到这是一个[0]任务，您所属行业为[1]，装置类型是[2]，希望使得目标[3]达到[4]，明确关键的约束条件为[6]，建模算法选择[7]。未指定明确的优化变量，未填写内容后续我将根据您上传的数据自行判断。
3. 填充规则:(最后回答时候，用对应的中文表示)
    - `[0]`: 对应 `task_type`，其中，"0"表示RTO，"1"表示预警，"2"表示时间序列预测，"3"表示回归；返回数字表示。(最后回答时候，用对应的中文表示)
    - `[1]`: 对应 `domain_knowledge.industry`。(最后回答时候，用对应的中文表示)
    - `[2]`: 对应 `domain_knowledge.device_type`。(最后回答时候，用对应的中文表示)
    - `[3]`: 对应 `optObject.variable`。(最后回答时候，用对应的中文表示)
    - `[4]`: 对应 `optObject.symbol`。最后回答时，min表示最小，max表示最大。(最后回答时候，用对应的中文表示)
    - `[5]`: 对应 `optVariables`的内容，如果多个用顿号连接。(最后回答时候，用对应的中文表示)
    - `[6]`: 对应 `constraint` 的内容，如果多个用顿号连接。(最后回答时候，用对应的中文表示)
    - `[7]`: 对应 `specified_algorithms`，0是TPT时序大模型，1是传统机器学习模型。(最后回答时候，用对应的中文表示)

## 限制:
- 只围绕输入的json数据进行分析解读，拒绝回答与数据结构分析无关的话题。
- 总结分析内容必须按照给定的示例格式进行组织，不能偏离框架要求。
- 总结分析部分不能超过200字。
- 回答内容不要产生多余字眼，[]对应的内容替换完整，不要出现中括号。

/no_think
"""
        #调用LLM润色
        prompt_str_obj = {
            "prompt_str" : prompt_str
        }
        llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
        if llm_resp_obj["success"] == 1:
            llm_resp = llm_resp_obj["llm_resp"]
        else:
            llm_resp = f"""
已明确您的意图，识别到这是一个RTO，您所属行业为{output_result["domain_knowledge"]["industry"]}，装置类型是{output_result["domain_knowledge"]["device_type"]}，
希望基于{"TPT时序大模型" if output_result["specified_algorithms"] == 0 else "传统机器学习模型"}算法针对{output_result["task_objective_description"]}执行优化算法，不清楚关键影响因素，未填写内容后续我将根据您上传的数据自动判断。  

"""  # 用LLM润色返回的问题
        output_result["task_objective_description"] = llm_resp
        await context.add_view({
            "format": "markdown",
            "content": llm_resp
        })

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '数据源匹配',
                "content": "识别到数据未接入到实时数据系统中，且未找到已经训练好的模型，需要您上传离线数据文件以进行模型调整。",
                "description": "",
                "details": ""
            }
        })

        return output_result

# 优化历史数据选择
@tool(version="*")
async def opt_offline_file_upload(context: Context, params: any):
    formResult = await context.get_interaction("opt_offline_file_upload_form")
    if formResult is None:
        url = context.config["llm_agent_recommend_params"]
        agent_params = {
            "question": params["task_objective_description"],
            "task_type": params["task_type"],
            "file": params["upload_file"]
        }
        # 发送 POST 请求
        response = requests.post(url, json=agent_params)
        # 检查响应状态码
        if response.status_code == 200:
            # 输出响应内容（JSON 格式）
            output = response.json()
            await context.add_view({
                "format": "card",
                "content": {
                    "type": 'markdown',
                    "title": '上传数据推荐',
                    "description": "上传数据位号推荐",
                    "details": output["text"]
                }
            })
            # 将推荐参数预存在cache中
            await context.set_cache("recommended_tags", output["parameters"])
        else:
            return f"调用参数预设推荐请求失败，状态码: {response.status_code}"
        data = output["parameters"]
        rows = (len(data) + 1) // 2
        # 构建表头
        markdown = "| 序号 | 名称  | 序号 | 名称  |\n"
        markdown += "|------|-------|------|-------|\n"
        # 构建每一行
        for i in range(rows):
            index1 = i + 1
            name1 = data[i] if i < len(data) else ""
            index2 = i + rows + 1
            name2 = data[i + rows] if (i + rows) < len(data) else ""
            markdown += f"| {index1}    | {name1}   | {index2}    | {name2}   |\n"
        recommended_tags_str = f"""
已分析找到影响问题的关键因素，推荐您上传包含以下参数的数据文件：
{markdown}
"""
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": recommended_tags_str,
                "description": "",
                "details": ""
            }
        })
        context.require_interaction({
            "id": "opt_offline_file_upload_form",
            "title": "请上传位号历史数据文件",
            "type": "form",
            "form": {
                "form_type": "file",
                "schema": {
                    "type": "object",
                    "description": "",
                    "properties": {
                        "file": {
                            "title": "位号历史数据文件(.csv)，仅支持utf-8编码的.csv格式文件",
                            "type": "string",
                            "format": "file-object",
                            "widget": "tptfile",
                            # "x-validator": "csv_judge_opt"
                        }
                    },
                    "required": ["file"]
                },
                "default": {}
            }
        })
    else:{}
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "请稍作等待，后续需要您进行相关信息确认",
            "description": "",
            "details": ""
        }
    })
    file = json.loads(formResult["file"])
    # todo 调用数据解析脚本进行解析，得到报告内容，调用算法管理发布的算法
    try:
        alg_params = {
            "input_file": file,
            "scenario_id": params["task_type"]
        }
        response = requests.post(
            url=context.config["ts_data_eval_alg_exec_runtime_url"],
            data=json.dumps(alg_params, indent=2, ensure_ascii=False).encode('utf-8'),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })
        if response.status_code == 200:
            json_obj = json.loads(response.text)
            read_data = json.loads(json_obj["data"])
            try:
                frequency = int(pd.to_timedelta(read_data['time_statistics']['dominant_frequency']).total_seconds())
            except Exception as e:
                frequency = 5
        else:
            return f"请求失败，状态码: {response.status_code}"
    except Exception as e:
        return "数据质量分析出现异常：" + str(e)

    prompt_str = f"""
# 角色
你是一位专业的数据质量评估师，擅长对各类数据进行深入分析和评估。你能够根据数据的各项指标，精准判断数据质量状况，给出总体评估和结论。

## 技能
### 技能 1: 数据质量评估分析
1. 接收输入的类似以下格式的json数据：
{read_data}
2. 对整体数据进行全面的数据质量评估分析。

### 技能 2: 输出总结
1. 根据数据质量评估分析，仅输出一段总结，清晰描述数据质量整体状况（好、中、差）。
2. 最终明确告诉用户，这个数据是否可以进行后续建模计算等，如果整体数据缺失不大且重复率低，则可以认为数据可用。
3. 注意只输出综合评价及总结，不输出其余内容。输出的形式好看点
4. 注意格式，不要增加特殊符号，就是正常文字即可

## 限制:
- 仅围绕数据质量评估相关内容进行分析、处理和总结，拒绝回答无关话题。
- 总结性文字需逻辑清晰、简洁明了，全面反映数据质量状况和优化情况。 
/no_think
"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp = ""

    markdown_info_1 = ""
    markdown_info_2 = ""

    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_1 += f"| {read_data['en_columns'][i]} | {k} | {v['missing_percentage']} | {v['missing_count']} | {v['duplicate_ratio']} | {v['duplicate_count']} |\n"
        i += 1
    i = 0
    for k, v in read_data["feature_statistics"].items():
        markdown_info_2 += f"| {read_data['en_columns'][i]} | {k} | {v['min']} | {v['max']} | {v['mean']} | {v['std']} | {v['divergence']} | {v['q1']} | {v['q2']} | {v['q3']} | {v['iqr']} | {v['autocorrelation_lag1']} | {v['autocorrelation_lag5']} | {v['partial_autocorrelation_lag1']} | {v['partial_autocorrelation_lag5']} | \n"
        i += 1
    markdownRet = f"""
{llm_resp}  

"""
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '数据集解析',
            "description": "数据集解析信息展示",
            "details": markdownRet
        }
    })

    # object_name = "rto/" + context.session_id + "_数据集解析报告.md"
    # bucket_name = "reports"
    # file_info = await context.add_file(f"{bucket_name}/{object_name}", markdownRet)
    # await context.add_view({
    #         "format": "card",
    #         "content": {
    #             "type": 'summary_file',
    #             "title": '数据集解析报告',
    #             "content": "数据集解析报告已成功生成，请点击 数据集解析报告 查看",
    #             "details": file_info,
    #             "description": "数据集解析报告.md"
    #         }
    #     })

    output_result = params
    output_result["file"] = file
    output_result["recommended_tags"] = await context.get_cache("recommended_tags")
    output_result["file_variables"] = [{"tag": tag, "description": desc} for tag, desc in
                                       zip(read_data["en_columns"], read_data["zn_columns"])]

    await context.set_cache("opt_offline_file_upload_result", output_result)

    return output_result


# 优化方案生成及参数预设
@tool(version="*")
async def opt_parse_offline_file_upload(context: Context, params: any):
    params = await context.get_cache("opt_offline_file_upload_result")

    result = await context.get_interaction("open_opt_param_page")
    if result is None:
        pre_info = "现将已确认的关键信息输入至TPT大模型中，通过查询历史数据库、联网搜索等方式，推理此问题的优化目标、优化变量及约束条件等核心要素。"
        await context.add_view({
            "format": "markdown",
            "content": pre_info
        })

        # 写调用参数预设推荐
        try:
            agnent_params = {
                "task_type": params["task_type"],
                "user_requirement": params["task_objective_description"],
                "file_variables": params["file_variables"],
                "recommended_tags": params["recommended_tags"],
                "optObject": params["optObject"],
                "constraint": params["constraint"],
                "optVariables": params["optVariables"],
                "file": params["upload_file"]
            }
            try:
                response = requests.post(
                    url=context.config["llm_agent_auto_model_rec"],
                    data=json.dumps(agnent_params, indent=2, ensure_ascii=False),
                    headers={
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                    })
                response.raise_for_status()
            except requests.exceptions.HTTPError as http_err:
                return {
                    'success': False,
                    'error_type': 'HTTPError',
                    'message': str(http_err),
                    'status_code': response.status_code,
                    'response_text': response.text
                }

            except requests.exceptions.ConnectionError as conn_err:
                return {
                    'success': False,
                    'error_type': 'ConnectionError',
                    'message': str(conn_err)
                }

            except requests.exceptions.Timeout as timeout_err:
                return {
                    'success': False,
                    'error_type': 'Timeout',
                    'message': str(timeout_err)
                }

            except requests.exceptions.RequestException as req_err:
                return {
                    'success': False,
                    'error_type': 'RequestException',
                    'message': str(req_err)
                }

            except Exception as err:
                return {
                    'success': False,
                    'error_type': 'UnknownError',
                    'message': str(err)
                }
            # 不规则的模版解析出来的json结构存在问题 调用出错
            json_obj = json.loads(response.text)

            params["output_variables"] = json_obj["output_variables"]
            params["input_variables"] = json_obj["input_variables"]
            params["optObject"] = json_obj["optObject"]
            params["optVariables"] = json_obj["optVariables"]
            params["constraint"] = json_obj["constraint"]
            params["ssdVariables"] = json_obj["ssdVariables"]

        except Exception as e:
            return "调用LLM参数推荐出现异常：" + str(e)

        await context.add_view({
            "format": "markdown",
            "content": """
结合关键需求及上传数据中的位号信息，推理得到的优化参数信息如下界面所示，请确认：
"""
        })
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "已根据您的补充信息与上传数据，生成TPT训练与计算的必须信息，请您确认",
                "description": "",
                "details": ""
            }
        })
        address = "/tpt-auto-model/optForm"
        context.require_interaction({
            "id": "open_opt_param_page",
            "title": "优化参数信息",
            "type": "open_page",
            "open_page": address,
            "page_type": "execute",
            # 增加页面传参，保持回填数据一致
            "page_data": params
        })
    else:
        {}

    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "请稍作等待，后续需要您进行相关信息确认",
            "description": "",
            "details": ""
        }
    })
    output_vars = [item["tag"] for item in result["output_variables"]]
    if (result["optObject"]["tag"] not in output_vars):
        result["output_variables"] += {"tag": result["optObject"]["tag"],
                                       "description": result["optObject"]["description"]}

    for item in result["constraint"]:
        if (item["tag"] not in output_vars):
            result["output_variables"] += [{"tag": item["tag"], "description": item["description"]}]

    input_vars = [item["tag"] for item in result["input_variables"]]
    for item in result["optVariables"]:
        if (item["tag"] not in input_vars):
            result["input_variables"] += [{"tag": item["tag"], "description": item["description"]}]

    # 目标变量和输入变量先用文件的
    json_obj = {
        "task_type": params["task_type"],
        "specified_algorithms": params["specified_algorithms"],
        "train_device_type": params["train_device_type"],
        "file": params["file"],
        "optObject": result["optObject"],
        "optVariables": result["optVariables"],
        "constraint": result["constraint"],
        "output_variables": result["output_variables"],
        "input_variables": result["input_variables"],
        "ssdVariables": result["ssdVariables"],
        "recommended_tags": params["recommended_tags"]
    }

    await context.set_cache("opt_parse_offline_file_upload_result", json_obj)

    return json_obj


# 优化模型微调训练
@tool(version="*")
async def opt_molel_train_offline_file_upload(context: Context, params: any):
    params = await context.get_cache("opt_parse_offline_file_upload_result")

    file = params["file"]
    alg_name = "tpt"
    if params["specified_algorithms"] == 1:
        alg_name = "AutoML"
    params["alg_name"] = alg_name
    origrin_params_input_vars = params["input_variables"]
    origrin_params_output_vars = params["output_variables"]
    input_vars = [item["tag"] for item in params["input_variables"]]
    target_vars = [item["tag"] for item in params["output_variables"]]

    result = await context.get_interaction("open_opt_ssd_param_page")
    if result is None:
        await context.add_view({
            "format": "markdown",
            "content": f"""
开始对数据进行自动预处理，为模型微调提供干净数据，为优化求解奠定良好基础：
"""
        })
        try:
            alg_params = {
                "input_file": file,
                "scenario_id": 1,  # task_type,
                "features": input_vars,
                "targets": target_vars
            }
            response = requests.post(
                url=context.config["ts_data_process_alg_exec_runtime_url"],
                data=json.dumps(alg_params, indent=2, ensure_ascii=False),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            json_obj = json.loads(response.text)
            # 数据处理后产生的新的数据文件
            file = json.loads(json_obj["data"])
        except Exception as e:
            return "数据预处理出现异常：" + str(e) + str(alg_params)

        # todo 数据预处理完，对预处理的数据继续进行数据分析，此时 "scenario_id" 默认为9
        try:
            alg_params = {
                "input_file": file,
                "scenario_id": 9
            }

            response = requests.post(
                url=context.config["ts_data_eval_alg_exec_runtime_url"],
                data=json.dumps(alg_params, indent=2, ensure_ascii=False).encode('utf-8'),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            json_obj = json.loads(response.text)
            read_data = json.loads(json_obj["data"])
            try:
                frequency = int(pd.to_timedelta(read_data['time_statistics']['dominant_frequency']).total_seconds())
            except Exception as e:
                frequency = 5

            markdown_info_1 = ""
            markdown_info_2 = ""
            i = 0
            for k, v in read_data["feature_statistics"].items():
                markdown_info_1 += f"| {read_data['en_columns'][i]} | {k} | {v['missing_percentage']} | {v['missing_count']} | {v['duplicate_ratio']} | {v['duplicate_count']} |\n"
                i += 1
            i = 0
            for k, v in read_data["feature_statistics"].items():
                markdown_info_2 += f"| {read_data['en_columns'][i]} | {k} | {v['min']} | {v['max']} | {v['mean']} | {v['std']} | {v['divergence']} | {v['q1']} | {v['q2']} | {v['q3']} | {v['iqr']} | {v['autocorrelation_lag1']} | {v['autocorrelation_lag5']} | {v['partial_autocorrelation_lag1']} | {v['partial_autocorrelation_lag5']} | \n"
                i += 1
            markdownRet = f"""
# 数据质量评估报告

## 一、数据概况

### 1. 基本信息

在开始评估之前，我们首先梳理数据集的基本情况如下：

- **文件名称**：{file["name"]}  
- **数据量**：{read_data['row_count']} 行，{len(read_data["feature_statistics"])} 列  
- **时间范围**：{read_data['time_statistics']['min_time']}———{read_data['time_statistics']['max_time']}
- **数据采集频次**：每 {f"{frequency}s"} 采集一次  
- **主要字段说明**：{list(read_data["feature_statistics"].keys())}

### 2. 完整性和唯一性
从完整性和唯一性两个维度分析数据，完整性表示数据是否存在缺失，唯一性表示数据是否存在重复记录，具体情况如下表：
| 位号 | 位号描述 | 缺失值比例 | 缺失值数量 | 重复值比例 | 重复值数量 |
| ------- |-------- | --- | --- | --- | --- |
{markdown_info_1}
### 3. 统计分析
对数据进行统计分析，快速了解数据分布情况，为后续数据清洗和模型训练，重点展示数据的最小值、最大值、分位数等关键统计指标。
计算方法：
标准差：衡量数据离散程度的统计量，表示数据集合中每个数据点与平均值的偏离程度。值越大，数据分布越发散，值越小，数据越集中。
散度：衡量数据分布或者离散程度的相对统计量，消除不同数据集量纲影响，离散系数大，说明数据的离散程度大，携带的信息越多。
第一四分位数(Q1)：25%分位数，将数据分为前25%
第二四分位数(Q2)：50%分位数，即中位数，数据的中间值
第三四分位数(Q3)：75%分位数，将数据分为前75%
自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，反映了同一时间序列在不同时间点的相关程度。
偏自相关系数：衡量时间序列数据中当前观测值与特定滞后阶数之前观测值之间的相关性，同时消除了其他滞后阶数的影响。
| 位号 | 位号描述 | 最小值 | 最大值 | 均值 | 标准差 | 散度 | 第一四分位数 | 中位数 | 第三四分位数 | 四分位距 | 自相关性（滞后1阶）| 自相关性（滞后5阶） | 偏自相关（滞后1阶）| 偏自相关（滞后5阶）|
| ------- |-------- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | 
{markdown_info_2}  
"""
        except Exception as e:
            return "数据预处理后数据分析出现异常：" + str(e) + str(alg_params)


        prompt_str = f"""
# 角色
你是一位专业的数据质量评估师，擅长对各类数据进行深入分析和评估，能够根据数据的各项指标，精准判断数据质量状况，并给出针对性的总结和建议。
## 技能
### 技能 1: 数据质量评估分析
1. 接收输入的类似以下格式的json数据：
{read_data}
2. 针对每一个字段名称，依据标准差、散度（变异系数）、缺失占比、重复值比例、自相关性、偏自相关等重点指标，进行数据质量评估分析。
### 技能 2: 输出总结文字
根据数据质量评估分析结果，输出一段总结，清晰描述数据质量整体状况（好、中、差）。例如：整体来看，数据在缺失情况方面表现良好，但重复值问题较为突出，尤其是 “混合气流量指示控制” 字段。部分字段的自相关性和偏自相关较高，有利于时间序列分析，但也需注意潜在风险。综合考虑，数据质量处于中等水平，在使用这些数据进行分析前，建议对重复值进行处理，以提高数据质量和分析结果的准确性。
## 限制:
- 仅围绕数据质量评估相关内容进行分析和总结，拒绝回答无关话题。
- 总结需逻辑清晰、简洁明了，全面反映数据质量状况。 
/no_think
"""
        #调用LLM润色
        prompt_str_obj = {
            "prompt_str" :prompt_str
        }
        llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
        if llm_resp_obj["success"] == 1:
            llm_resp = llm_resp_obj["llm_resp"]
        else:
            llm_resp = ""

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '数据预处理',
                "description": "数据预处理质量评估",
                "details": markdownRet + llm_resp
            }
        })

#         ssd_variables = {
#             "input_file": file,
#             "ssdVariables": params["ssdVariables"]
#         }
#
#         # 稳态检测确认
#         await context.add_view({
#             "format": "markdown",
#             "content": f"""
# 接着，TPT大模型将自动对数据进行稳态检测，基于历史数据库及上传的工艺资料，推荐用于该装置稳态检测的位号如以下界面所示，请您确认：
# """
#         })
#         await context.add_view({
#             "format": "tip",
#             "content": {
#                 "type": 'default',
#                 "title": '',
#                 "content": "已根据您的补充信息与上传数据，生成TPT稳态检测的必须信息，请您确认",
#                 "description": "",
#                 "details": ""
#             }
#         })
#         address = "/tpt-auto-model/detectionConfig"
#         context.require_interaction({
#             "id": "open_opt_ssd_param_page",
#             "title": "稳态检测参数信息",
#             "type": "open_page",
#             "open_page": address,
#             "page_type": "execute",
#             # 增加页面传参，保持回填数据一致
#             "page_data": ssd_variables
#         })
    else:
        {}
    # 处理稳态检测的算法

    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "TPT计算所需要的信息和数据已补齐，下一步将调用TPT进行训练和计算，预计需要7分钟，您可以关闭此会话离开，如有结果将及时通知您！",
            "description": "",
            "details": ""
        }
    })

    # 模型训练
    await context.add_view({
        "format": "markdown",
        "content": f"""
接下来，我将自动选择并构建合适的模型，开始进行模型微调‌：
"""
    })
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": 'TPT模型微调',
            "description": "微调过程",
            "details": """
模型微调日志：   
"""
        }
    })

    output = {}
    # 用数据处理后的file执行
    params["file"] = file
    csv_path = "s3://" + file["bucket"] + "/" + file["object"]
    params["csv_path"] = csv_path
    params["input_variables"] = input_vars
    params["output_variables"] = target_vars

    try:
        alg_result = await context.call_tool("execute_regression_alg", context=context, params=params,
                                             module_name="automl")
        if (alg_result["executeStatus"] == 1):
            try:
                output = alg_result["result"][0]
                output_datas = [{
                    "format": "text",
                    "content": f"""
使用{output["method"]}算法，已经训练好模型文件{output["model_file"]}，模型评估结果如下所示：  
| 平均绝对误差MAE | 均方误差MSE | 均方根误差RMSE | 拟合优度R2 | 平均绝对百分比误差MAPE |
|:-----:|:-----:|:-----:|:-----:|:-----:|
| {output['eval']['MAE']} | {output['eval']['MSE']}  | {output['eval']['RMSE']} | {output['eval']['R2']} | {output['eval']['MAPE']} |    

"""
                }]
                if alg_name == "tpt" and output["loss"] is not None:
                    loss_data = output["loss"]
                    # 生成x轴标签（）
                    xAxisData = []
                    for i in range(len(loss_data)):
                        xAxisData.append(f'{i + 1}')
                    echart_options = {
                        "title": {
                            "text": "训练损失 (Loss) 曲线",
                        },
                        "legend": {
                            "data": ["Loss"],
                            "top": 10,
                            "right": 10
                        },
                        "tooltip": {
                            "trigger": "axis",
                            "formatter": """function(params) {return 'Loss: ' + params[0].value.toFixed(3) + '<br/>' +'Epoch: ' + (params[0].dataIndex + 1);}"""
                        },
                        "grid": {
                            "left": "3%",
                            "right": "10%",
                            "bottom": "3%",
                            "containLabel": True
                        },
                        "xAxis": {
                            "name": "",
                            "type": "category",
                            "boundaryGap": False,
                            "data": xAxisData,
                            "axisLabel": {
                                "interval": len(loss_data) // 10,
                                "rotate": 0
                            }
                        },
                        "yAxis": {
                            "type": "value",
                            "scale": True,
                            "name": "Loss值",
                            "splitLine": {
                                "show": True
                            },
                            "min": "function (value) {return (parseFloat(value.min)-Math.abs(parseFloat(value.min)) * 0.05).toFixed(2);}",
                            "max": "function (value) {return (parseFloat(value.max)+Math.abs(parseFloat(value.max)) * 0.05).toFixed(2);}"
                        },
                        "series": [
                            {
                                "name": "Loss",
                                "type": "line",
                                "smooth": True,
                                "showSymbol": False,
                                "lineStyle": {
                                    "type": "solid",
                                    "width": 1
                                },
                                "markPoint": {
                                    "data": [
                                        {"type": "min", "name": "最小值", "symbolSize": 60},
                                        {"type": "max", "name": "最大值"}
                                    ],
                                    "label": {
                                        "formatter": """function(params) {return params.value.toFixed(3);}"""
                                    }
                                },
                                "markLine": {
                                    "data": [
                                        {"type": "average", "name": "平均值"}
                                    ],
                                    "label": {
                                        "formatter": """function(params) {return "平均: " + params.value.toFixed(3);}"""
                                    }
                                },
                                "data": loss_data
                            }
                        ]
                    }
                    charts_content = {
                        "format": "echarts",
                        "content": [
                            {
                                "chartTheme": 'light',
                                "chartConfig": echart_options,
                            },
                            {
                                "chartTheme": 'dark',
                                "chartConfig": echart_options,
                            }]  # 注意保持数组格式
                    }
                    output_datas.append(charts_content)
            except Exception as e:
                return f"读取模型微调结果失败，返回信息如: {str(e)}"
        else:
            return f"模型微调失败，返回信息如: {alg_result['message']}"
    except Exception as e:
        return f"执行模型微调出现异常，返回信息如: {str(e)}"

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": 'TPT模型微调',
            "description": "微调结果",
            "details": output_datas
        }
    })

    train_obj = {
        "csv_path": csv_path,
        "model_file": output["model_file"],
        "specified_algorithms": params["alg_name"],
        "optObject": params["optObject"],
        "optVariables": params["optVariables"],
        "constraint": params["constraint"],
        "output_variables": origrin_params_output_vars,
        "input_variables": origrin_params_input_vars,
        "recommended_tags": params["recommended_tags"]
    }

    await context.set_cache("opt_molel_train_offline_file_upload_result", train_obj)

    return train_obj


# 优化算法执行
@tool(version="*")
async def opt_model_exec_offline_file_upload(context: Context, params: any):
    params = await context.get_cache("opt_molel_train_offline_file_upload_result")

    alg_name = params["specified_algorithms"]
    csv_path = params["csv_path"]
    model_file = params["model_file"]
    input_variables = [item["tag"] for item in params["input_variables"]]
    target_variables = [item["tag"] for item in params["output_variables"]]
    optObject = params["optObject"]
    opt_vars = params["optVariables"]
    cons_vars = params["constraint"]

    optObject["expression"] = optObject["tag"]
    optObject["tags"] = [optObject["tag"]]
    optObject["steady"] = 1
    optObject["variable"] = optObject["description"]

    optVariables = [
        {"step": item["step"], "describe": item["description"], "unit": item["unit"], "upper": item["upper"],
         "lower": item["lower"], "variable": item["tag"]}
        for item in opt_vars
    ]
    constraint = [
        {"describe": item["description"], "unit": item["unit"], "upper": item["upper"], "lower": item["lower"],
         "variable": item["tag"], "tags": [item["tag"]], "expression": item["tag"]}
        for item in cons_vars
    ]

    inference_alg_params = {
        "inference": {
            "test_data": csv_path,
            "model_path": model_file,
            "input_variables": input_variables,
            "targets": target_variables
        },
        "optObject": optObject,
        "optVariables": optVariables,
        "constraint": constraint,
        "appAlgFileName": alg_name,
        "id": "10000",
        "isTest": "0"
    }
    try:
        result_preinfo = ""
        opt_alg_exec_runtime_url = context.config["opt_alg_exec_runtime_url"]
        try:
            response = requests.post(
                url=opt_alg_exec_runtime_url,
                data=json.dumps(inference_alg_params, indent=2, ensure_ascii=False),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                })
            response.raise_for_status()
        except requests.exceptions.HTTPError as http_err:
            return {
                'success': False,
                'error_type': 'HTTPError',
                'message': str(http_err),
                'status_code': response.status_code,
                'response_text': response.text
            }

        except requests.exceptions.ConnectionError as conn_err:
            return {
                'success': False,
                'error_type': 'ConnectionError',
                'message': str(conn_err)
            }

        except requests.exceptions.Timeout as timeout_err:
            return {
                'success': False,
                'error_type': 'Timeout',
                'message': str(timeout_err)
            }

        except requests.exceptions.RequestException as req_err:
            return {
                'success': False,
                'error_type': 'RequestException',
                'message': str(req_err)
            }

        except Exception as err:
            return {
                'success': False,
                'error_type': 'UnknownError',
                'message': str(err)
            }

        if response.status_code == 200:
            json_obj = json.loads(convert_runtime_json(response.text))
            json_obj = json_obj["data"]["output"]
            if json_obj["message"] != "":
                result_preinfo = json_obj["message"]
            else:
                markdownRet = ""
                markdownRet_optimize = f"| {json_obj['optObject']['expression']} | {json_obj['optObject']['variable']} | {json_obj['optObject']['currentValue']} {json_obj['optObject']['unit']}| {json_obj['optObject']['optimizationValue']}  {json_obj['optObject']['unit']}| {round(float(json_obj['optObject']['optimizationValue']) - float(json_obj['optObject']['currentValue']), 2)}  {json_obj['optObject']['unit']} |\n"
                for item in json_obj['optResult']:
                    markdownRet += f"| {item['tagname']} | {item['describe']} | {item['currentValue']} {item['unit']}| {item['optimizationValue']}  {item['unit']}| {round(float(item['optimizationValue']) - float(item['currentValue']), 2)}  {item['unit']} |\n"
                result_preinfo = f""" 
**经过TPT优化计算，在当前工况条件下运行操作具备优化空间，预计优化目标可达到如下效果：**
| 优化目标位号             |  位号描述            | 当前值        | 预计优化值        | 预计优化幅度       |
|--------------------|----------------------|----------------|----------------|----------------|
{markdownRet_optimize}
**工艺参数优化建议如下表：**
| 优化参数位号             |  位号描述            | 当前设定值        | 推荐设定值        | 变化幅度       |
|--------------------|----------------------|----------------|----------------|----------------|
{markdownRet}

"""
        else:
            return "调用优化推理出错" + response.status_code
    except Exception as e:
        return "执行优化推理算法出现异常:算法入参如下：" + str(json_obj)

    await context.add_view({
        "format": "card",
        "content": {
            "type": 'markdown',
            "title": '模型优化计算',
            "description": "优化计算结果",
            "details": result_preinfo
        }
    })

    llm_agl_param = json_obj
    llm_agl_param["model_file"] = model_file
    llm_agl_param["constraint"] = constraint
    llm_agl_param["input_variables"] = params["input_variables"]
    llm_agl_param["recommended_tags"] = params["recommended_tags"]

    prompt_str = f"""
你是一个解决方案报告生成工作师，主要进行RTO优化结果的整理及总结。
优化结果内容如下所示：optObject表示优化目标，optResult表示优化参数优化结果
{llm_agl_param}

根据上述内容，以optObject中“variable”为关键搜索参数，生成如下报告：

氯碱装置电耗优化解决方案报告（根据用户输入的optObject中“variable”作为关键词生成报告名，字体为报告中最大的）

1.氯碱工艺背景
根据自身对相应工艺的了解，简单叙述，尽可能贴近报告名称的内容。
2.氯碱电耗影响因素分析
根据自身了解，科普可能存在的影响因素，最终引导至recommended_tags中的位号
3.氯碱电耗优化模型搭建
为解决氯碱装置电耗优化的问题，通过查找历史数据库案例及阅读文献，建立了以“input_variables”中所有“description”为输入，“constraint”中的“description”和“optObject”中的“variable”为输出的TPT模型，并进行微调训练，最终模型为 “model_file”。要求所有值都用输入的中文对应值取代。
4.氯碱电耗优化建议
以“optObject”中的“variable”为优化目标，“constraint”中的“description”为约束条件，“optResult”中的各“describe”为优化参数。进行优化计算，得到以下结论。要求所有值都用输入的中文对应值取代。

优化结果（以表格形式生成）
|优化目标描述 |当前值|优化值|优化幅度|单位
optObject.variable|optObject.currentValue|optObject.optimizationValue|optObject.optimizationValue-optObject.currentValue|optResult.unit


关键参数控制（以表格形式生成）
|参数描述 |当前值|优化值|变化范围|单位
optResult.describe|optResult.currentValue|optResult.optimizationValue|优化值减去当前值|optResult.unit

5.经济效益计算
根据当前优化结果，针对优化目标大致估算能够为企业带来多少经济效益。如降低电耗，假设电费按0.7元/kWh，工厂按照15万吨/年生产，以当前的电耗优化来计算。输出计算过程及结果，并生成一句总结文字。

后续建议事例：
1.验证优化方案的可行性，重点关注电流调整对槽电压的实时影响
2.建立电流分布动态监控机制，确保各槽体负荷均衡
3.开展在线优化测试，通过实际运行数据验证理论优化效果
4.持续监测电解总管碱浓度稳定性，避免因调整导致产品品质波动

"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp = ""

    # await context.add_view({
    #     "format": "card",
    #     "content": {
    #         "type": 'markdown',
    #         "title": '优化总结',
    #         "description": "解决方案报告",
    #         "details": llm_resp
    #     }
    # })

    report_file_path = "reports/操作优化解决方案.md"
    file_info = await context.add_file(report_file_path, llm_resp)
    await context.add_view({
            "format": "card",
            "content": {
                "type": 'summary_file',
                "title": '操作优化解决方案',
                "content": "操作优化解决方案已成功生成，请点击 操作优化解决方案 查看",
                "details": file_info,
                "description": "操作优化解决方案.md"
            }
        })
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "所有任务执行完毕，请确认优化结果",
            "description": "",
            "details": ""
        }
    })

    return json_obj

