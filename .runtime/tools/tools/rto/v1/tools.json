{"$schema": "../../../schema/tools.schema.json", "tools": [{"alias": ["装置稳态检测参数信息获取"], "catalog": "optimization", "name": "query_steady_params", "description": "查询稳态配置信息", "params": {"nodeName": {"type": "string", "description": "工厂子节点名称"}, "required": ["nodeName"]}, "result": {"type": "object", "description": "稳态装置配置信息", "properties": {"content": {"type": "object", "description": "返回数据体", "properties": {"nodeName": {"type": "string", "description": "工厂子节点名称"}, "optVarSsdConfigs": {"type": "array", "description": "变量列表"}, "algorithmInput": {"type": "object", "description": "算法入参", "properties": {"algorithm": {"type": "object", "description": "算法信息", "properties": {"algorithmId": {"type": "number", "description": "算法id"}, "input": {"type": "array", "description": "算法输入参数列表"}}, "required": []}}, "required": []}, "isExist": {"type": "boolean", "description": "配置是否存在，为true时表示查询到配置并返回配置信息，为false时则表示未查询到配置。"}, "failedReason": {"type": "string", "description": "查询失败原因"}}, "required": ["nodeName", "isExist"]}}, "required": ["content"]}}, {"alias": ["装置稳态检测执行"], "catalog": "optimization", "name": "exec_steady", "description": "同步执行稳态算法并获取结果", "params": {"nodeName": {"type": "string", "description": "工厂子节点名称"}, "required": ["nodeName"]}, "result": {"type": "object", "description": "稳态算法执行结果", "properties": {"content": {"type": "object", "description": "返回数据体", "properties": {"curTime": {"type": "number", "description": "执行结束时间"}, "id": {"type": "string", "description": "执行id"}, "logInfo": {"type": "string", "description": "执行日志"}, "errorInfo": {"type": "string", "description": "执行错误日志"}, "implementation": {"type": "number", "description": "执行成功/失败，1表示成功，2表示失败"}, "algExecResult": {"type": "object", "description": "稳态执行结果，执行失败情况可能为空", "properties": {"result": {"type": "object", "description": "算法结果", "properties": {}, "required": []}, "ssdresult": {"type": "object", "description": "稳态算法结果", "properties": {"overallSteadyState": {"type": "number", "description": "装置是否稳态，1表示稳态，0表示非稳态"}, "tag": {"type": "array", "description": "位号的稳态结果信息"}}, "required": ["overallSteadyState"]}}, "required": []}}, "required": ["curTime", "id", "logInfo", "implementation"]}}, "required": ["content"]}}, {"alias": ["模组GUI配置"], "catalog": "optimization", "name": "steady_config_page", "description": "打开稳态装置配置页面", "params": {"type": "object", "description": "无参数", "properties": {}, "required": [], "additionalProperties": false}, "result": {}}, {"alias": ["优化参数信息获取"], "catalog": "optimization", "name": "query_opt_params", "description": "查询优化参数配置，不存在则跳转GUI配置页面", "params": {"nodeName": {"type": "string", "description": "工厂子节点名称"}, "required": ["nodeName"]}, "result": {"type": "object", "description": "优化配置", "properties": {"content": {"type": "object", "description": "返回数据体", "properties": {"nodeName": {"type": "string", "description": "工厂子节点名称"}, "optWorkConfigs": {"type": "array", "description": "工况配置列表"}, "optWorkConstraintConfigs": {"type": "array", "description": "工况约束条件配置列表"}, "optVarConfigs": {"type": "array", "description": "工况优化变量配置列表"}, "isExist": {"type": "boolean", "description": "配置是否存在，为true时表示存在并返回配置数据，未false时表示不存在，需要进行参数的配置。"}, "failedReason": {"type": "string", "description": "查询失败原因"}}, "required": ["nodeName", "isExist"]}}, "required": ["content"]}}, {"alias": ["装置优化执行"], "catalog": "optimization", "name": "exec_opt", "description": "同步执行稳态算法并获取结果", "params": {"nodeName": {"type": "string", "description": "工厂子节点名称"}, "required": ["nodeName"]}, "result": {"type": "object", "description": "优化算法执行结果", "properties": {"content": {"type": "object", "description": "返回数据体", "properties": {"curTime": {"type": "number", "description": "执行结束时间"}, "id": {"type": "string", "description": "执行id"}, "logInfo": {"type": "string", "description": "执行日志"}, "errorInfo": {"type": "string", "description": "执行错误日志"}, "implementation": {"type": "number", "description": "执行成功/失败,1表示成功,2表示失败"}, "algExecResult": {"type": "object", "description": "算法结果", "properties": {"output": {"type": "object", "description": "算法执行输出结果", "properties": {"optResult": {"type": "array", "description": "优化参数结果"}, "simResult": {"type": "array", "description": "模拟结果"}, "optObject": {"type": "object", "description": "优化目标结果"}, "message": {"type": "string", "description": "算法返回的消息"}}, "required": []}}, "required": ["output"]}}, "required": ["curTime", "id", "logInfo", "implementation"]}}, "required": ["content"]}}, {"alias": ["优化执行参数GUI配置"], "catalog": "optimization", "name": "opt_config_page", "description": "打开优化装置配置页面", "params": {"type": "object", "description": "无参数", "properties": {}, "required": [], "additionalProperties": false}, "result": {}}, {"skip_summary": true, "alias": ["选择离线数据文件", "上传离线数据文件"], "name": "opt_file_upload", "catalog": "optimization", "description": "上传功能需要的位号的历史数据文件，csv格式", "params": {"type": "object", "description": "", "properties": {}, "required": []}, "result": {"type": "object", "description": "上传成功后返回的文件数据", "required": ["file"], "properties": {"file": {"type": "string", "description": "用户上传csv文件后，返回的文件数据base64"}}}}, {"skip_summary": true, "alias": ["离线优化参数识别"], "name": "parse_opt_file_upload", "catalog": "optimization", "description": "解析上传功能需要的位号的历史数据文件，通过大模型识别优化目标、约束变量、优化变量等信息", "params": {"file": {"type": "string", "description": "上传的离线文件数据"}, "required": ["file"]}, "result": {"type": "object", "description": "大模型识别优化目标、约束变量、优化变量等信息", "required": ["data"], "properties": {"data": {"type": "object", "description": "大模型识别优化目标、约束变量、优化变量等信息结果数据", "required": ["inference", "optObject", "constraint", "optVariables", "offLineData"], "properties": {"inference": {"type": "object", "description": "", "required": [], "properties": {"dependentVariableContent": {"type": "object", "description": "", "properties": {}, "required": []}, "deviceTypeName": {"type": "string", "description": ""}, "fileName": {"type": "string", "description": ""}, "frequency": {"type": "string", "description": ""}, "independentVariableContent": {"type": "object", "description": "", "properties": {}, "required": []}, "modelType": {"type": "string", "description": ""}, "typeName": {"type": "string", "description": ""}}}, "appAlgFileName": {"type": "string", "description": ""}, "optObject": {"type": "object", "description": "", "required": ["symbol", "variable"], "properties": {"expression": {"type": "string", "description": "优化目标表达式"}, "steady": {"type": "string", "description": "稳态检测结果，1表示稳态，0表示非稳态"}, "symbol": {"type": "string", "description": "优化方向，min为最小，max为最大"}, "tags": {"type": "array", "description": ""}, "unit": {"type": "string", "description": "优化目标的单位"}, "variable": {"type": "string", "description": "优化目标变量"}}}, "constraint": {"type": "array", "description": "约束变量", "items": {"type": "object", "required": ["variable", "describe", "lower", "upper"], "properties": {"variable": {"type": "string", "description": "约束变量名"}, "lower": {"type": "string", "description": "约束变量的下限，约束变量的优化值需要大于等于该值"}, "upper": {"type": "string", "description": "约束变量的上限，约束变量的优化值需要小于等于该值"}, "unit": {"type": "string", "description": "约束变量单位"}, "describe": {"type": "string", "description": "约束变量描述"}, "expression": {"type": "string", "description": "约束变量计算表达式"}, "tags": {"type": "array", "description": "表达式包含的位号组"}}}}, "optVariables": {"type": "array", "description": "优化变量", "items": {"type": "object", "required": ["variable", "describe", "upper", "lower", "step"], "properties": {"lower": {"type": "string", "description": "优化变量的下限，优化变量的优化值需要大于等于该值"}, "upper": {"type": "string", "description": "优化变量的上限，优化变量的优化值需要小于等于该值"}, "step": {"type": "string", "description": "优化变量执行步长"}, "unit": {"type": "string", "description": "优化变量单位"}, "describe": {"type": "string", "description": "优化变量描述"}, "variable": {"type": "string", "description": "优化变量名称"}}}}, "offLineData": {"type": "string", "description": ""}, "id": {"type": "string", "description": ""}, "isTest": {"type": "string", "description": ""}}}, "message": {"type": "string", "description": "执行消息说明"}}}}, {"skip_summary": true, "alias": ["离线优化执行"], "name": "exec_opt_file_upload", "catalog": "optimization", "description": "大模型识别优化目标、约束变量、优化变量等信息，调用TPT模型", "params": {"type": "object", "description": "", "required": ["data"], "properties": {"data": {"type": "object", "description": "大模型识别优化目标、约束变量、优化变量等信息结果数据", "required": ["inference", "optObject", "constraint", "optVariables", "offLineData"], "properties": {"inference": {"type": "object", "description": "", "required": [], "properties": {"dependentVariableContent": {"type": "object", "description": "", "properties": {}, "required": []}, "deviceTypeName": {"type": "string", "description": ""}, "fileName": {"type": "string", "description": ""}, "frequency": {"type": "string", "description": ""}, "independentVariableContent": {"type": "object", "description": "", "properties": {}, "required": []}, "modelType": {"type": "string", "description": "模型类型"}, "typeName": {"type": "string", "description": "模型类型名称"}}}, "appAlgFileName": {"type": "string", "description": "优化推理算法名称"}, "optObject": {"type": "object", "description": "", "required": ["symbol", "variable"], "properties": {"expression": {"type": "string", "description": "优化目标表达式"}, "steady": {"type": "string", "description": "稳态检测结果，1表示稳态，0表示非稳态"}, "symbol": {"type": "string", "description": "优化方向，min为最小，max为最大"}, "tags": {"type": "array", "description": ""}, "unit": {"type": "string", "description": "优化目标的单位"}, "variable": {"type": "string", "description": "优化目标变量"}}}, "constraint": {"type": "array", "description": "约束变量", "items": {"type": "object", "required": ["variable", "describe", "lower", "upper"], "properties": {"variable": {"type": "string", "description": "约束变量名"}, "lower": {"type": "string", "description": "约束变量的下限，约束变量的优化值需要大于等于该值"}, "upper": {"type": "string", "description": "约束变量的上限，约束变量的优化值需要小于等于该值"}, "unit": {"type": "string", "description": "约束变量单位"}, "describe": {"type": "string", "description": "约束变量描述"}, "expression": {"type": "string", "description": "约束变量计算表达式"}, "tags": {"type": "array", "description": "表达式包含的位号组"}}}}, "optVariables": {"type": "array", "description": "优化变量", "items": {"type": "object", "required": ["variable", "describe", "upper", "lower", "step"], "properties": {"lower": {"type": "string", "description": "优化变量的下限，优化变量的优化值需要大于等于该值"}, "upper": {"type": "string", "description": "优化变量的上限，优化变量的优化值需要小于等于该值"}, "step": {"type": "string", "description": "优化变量执行步长"}, "unit": {"type": "string", "description": "优化变量单位"}, "describe": {"type": "string", "description": "优化变量描述"}, "variable": {"type": "string", "description": "优化变量名称"}}}}, "offLineData": {"type": "string", "description": ""}, "id": {"type": "string", "description": ""}, "isTest": {"type": "string", "description": ""}}}}}, "result": {"type": "object", "description": "大模型识别优化目标、约束变量、优化变量等信息", "required": ["data"], "properties": {"success": {"type": "number", "description": ""}, "data": {"type": "object", "description": "", "required": ["output"], "properties": {"output": {"type": "object", "description": "优化结果信息，包括优化目标值，优化变量优化值等", "required": ["optObject", "optResult"], "properties": {"optObject": {"type": "object", "description": "优化目标变量信息", "required": ["variable", "currentValue", "optimizationValue"], "properties": {"variable": {"type": "string", "description": "优化目标名称"}, "expression": {"type": "string", "description": "优化目标计算表达式"}, "optimizationValue": {"type": "number", "description": "优化目标优化后的值"}, "unit": {"type": "string", "description": "优化目标单位"}, "currentValue": {"type": "number", "description": "优化目标当前值"}}}, "optResult": {"type": "array", "description": "优化变量优化后的结果信息", "items": {"type": "object", "required": ["tagname", "describe", "currentValue", "optimizationValue"], "properties": {"tagname": {"type": "string", "description": "优化变量位号名"}, "optimizationValue": {"type": "number", "description": "优化变量优化后的值"}, "unit": {"type": "string", "description": "优化变量单位"}, "describe": {"type": "string", "description": "优化变量描述"}, "currentValue": {"type": "number", "description": "优化变量当前值"}}}}, "simResult": {"type": "array", "description": ""}, "message": {"type": "string", "description": "执行消息说明"}}}}}}}}, {"skip_summary": true, "alias": ["优化问题定义与标准化"], "name": "opt_question_definition_standard", "catalog": "optimization", "description": "跟据用户提问的问题，识别问题的场景:优化RTO/预警/预测/回归...任务，并从中提取关键信息", "params": {"type": "object", "description": "", "required": ["task_type", "domain_knowledge", "task_objective_description", "optObject", "optVariables", "constraint"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "根据用户问题判断任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "optObject": {"type": "object", "description": "表示需要优化的变量名称，有且仅有一个元素，如“降低氯碱装置电耗”", "properties": {"description": {"type": "string", "description": "变量名称或描述"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}}}, "result": {"type": "object", "description": "返回用户确认补全后的信息", "required": ["task_type", "domain_knowledge", "specified_algorithms", "train_device_type", "task_objective_description", "optObject", "optVariables", "constraint", "upload_file"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "根据用户问题判断任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型,默认为0"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"description": {"type": "string", "description": "变量名称或描述"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "upload_file": {"type": "object", "description": "用户上传文件存储信息,如果未传object为空字符串", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}}}}, {"skip_summary": true, "alias": ["优化历史数据选择"], "name": "opt_offline_file_upload", "catalog": "optimization", "description": "上传需要的位号的历史数据文件，csv格式", "params": {"type": "object", "description": "补全后的用户输入问题及模型识别数据", "required": ["task_type", "domain_knowledge", "specified_algorithms", "train_device_type", "task_objective_description", "optObject", "optVariables", "constraint", "upload_file"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "根据用户问题判断任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型,默认为0"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"description": {"type": "string", "description": "变量名称或描述"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "upload_file": {"type": "object", "description": "用户上传文件存储信息,如果未传object为空字符串", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}}}, "result": {"type": "object", "description": "上传成功后返回的文件数据,大模型推荐的变量信息", "required": ["task_type", "domain_knowledge", "specified_algorithms", "train_device_type", "task_objective_description", "optObject", "optVariables", "constraint", "file", "file_variables", "recommended_tags", "upload_file"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "根据用户问题判断任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型,默认为0"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"description": {"type": "string", "description": "变量名称或描述"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "file": {"type": "object", "description": "用户上传文件的数据文件.csv存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "file_variables": {"type": "array", "description": "数据集中所有变量的列表", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}, "upload_file": {"type": "object", "description": "用户上传文件存储信息,如果未传object为空字符串", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}}}}, {"skip_summary": true, "alias": ["优化方案生成及参数预设"], "name": "opt_parse_offline_file_upload", "catalog": "optimization", "description": "解析数据集文件，通过大模型识别优化目标、操作变量、约束变量等信息", "params": {"type": "object", "description": "上传成功后返回的文件数据", "required": ["task_type", "domain_knowledge", "specified_algorithms", "train_device_type", "task_objective_description", "optObject", "optVariables", "constraint", "file", "file_variables", "recommended_tags", "upload_file"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "根据用户问题判断任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "domain_knowledge": {"type": "object", "description": "领域知识", "required": ["industry", "device_type"], "properties": {"industry": {"type": "string", "description": "所属行业"}, "device_type": {"type": "string", "description": "装置类型"}}}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型,默认为0"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "task_objective_description": {"type": "string", "description": "任务目标描述"}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"description": {"type": "string", "description": "变量名称或描述"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "string", "description": "变量描述"}}, "file": {"type": "object", "description": "用户上传文件的数据文件.csv存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "file_variables": {"type": "array", "description": "数据集中所有变量的列表", "items": {"type": "object", "required": [], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}, "upload_file": {"type": "object", "description": "用户上传文件存储信息,如果未传object为空字符串", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}}}, "result": {"type": "object", "description": "通过大模型解析数据集数据，识别TPT模型微调需要的参数信息并确认", "required": ["task_type", "specified_algorithms", "train_device_type", "file", "optObject", "optVariables", "constraint", "output_variables", "input_variables", "ssdVariables", "recommended_tags"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "根据用户问题判断任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型,默认为0"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "file": {"type": "object", "description": "用户上传文件的数据文件.csv存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量名称或描述"}, "unit": {"type": "string", "description": "变量的单位"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper", "step"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}, "step": {"type": "number", "description": "操作变量的优化步长"}}}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}}}}, "output_variables": {"type": "array", "description": "数据集中所有输出变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "input_variables": {"type": "array", "description": "数据集中所有输入变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "ssdVariables": {"type": "array", "description": "数据集中所有稳态检测变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}}}}, {"skip_summary": true, "alias": ["优化模型微调训练"], "name": "opt_molel_train_offline_file_upload", "catalog": "optimization", "description": "优化模型微调训练", "params": {"type": "object", "description": "", "required": ["task_type", "specified_algorithms", "train_device_type", "file", "optObject", "optVariables", "constraint", "output_variables", "input_variables", "ssdVariables", "recommended_tags"], "properties": {"task_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "根据用户问题判断任务类型，其中0表示RTO，1表示预警，2表示时间序列预测，3表示回归。如果用户需求中没有明确指出，请根据上下文和目标变量进行合理推断。➢回归任务关键词：“模拟“，“估计XX变量是多少”，“XX与YY的关系”，“影响XX的因素”，不包含时间相关词汇”等；预测任务关键词：“未来怎么样”，”未来变化趋势“，“预测未来趋势”，“趋势怎么样”，“根据历史数据预测”，具有明确的时间/预测/趋势相关词汇等；RTO任务关键词：“优化XX的效益”（如生产效率、能耗、利润），“最大化XX的产出”（如产量、回收率），“最小化XX的成本”（如能耗、原料消耗），”降低电耗/能耗“，“调整XX的参数”（如温度、压力、流量），“提升XX的能效”（如RTO焚烧炉、化工过程），“降低XX的排放”（如VOCs、CO₂）预警任务关键词：“监测XX的异常信号”（如“监测传染病的症候群数据”），“识别XX的风险阈值”（如“识别泥石流的水位临界值”），“触发XX的预警级别”（如“触发台风红色预警”），“评估XX的潜在影响”（如“评估地震的次生灾害风险”），“响应XX的应急措施”（如“响应企业信用风险预警”）"}, "specified_algorithms": {"type": "integer", "enum": [0, 1], "description": "指定模型训练方法模型训练方法，0是TPT时序大模型,1是传统机器学习模型,默认为0"}, "train_device_type": {"type": "string", "description": "训练微调使用的设备类型，CPU或GPU"}, "file": {"type": "object", "description": "用户上传文件的数据文件.csv存储信息", "required": ["bucket", "object", "name"], "properties": {"bucket": {"type": "string", "description": "文件系统中的桶名"}, "object": {"type": "string", "description": "文件路径"}, "name": {"type": "string", "description": "文件名称"}}}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量名称或描述"}, "unit": {"type": "string", "description": "变量的单位"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper", "step"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}, "step": {"type": "number", "description": "操作变量的优化步长"}}}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}}}}, "output_variables": {"type": "array", "description": "数据集中所有输出变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "input_variables": {"type": "array", "description": "数据集中所有输入变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "ssdVariables": {"type": "array", "description": "数据集中所有稳态检测变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}}}, "result": {"type": "object", "description": "通过大模型解析数据集数据，识别TPT模型微调需要的参数信息并确认", "required": ["csv_path", "model_file", "specified_algorithms", "optObject", "optVariables", "constraint", "output_variables", "input_variables", "recommended_tags"], "properties": {"csv_path": {"type": "string", "description": "数据处理后的数据文件路径s3://开头"}, "model_file": {"type": "string", "description": "模型训练生成的模型文件路径s3://开头"}, "specified_algorithms": {"type": "string", "description": "指定模型训练方法模型训练方法，tpt是TPT时序大模型,AutoML是传统机器学习模型"}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量名称或描述"}, "unit": {"type": "string", "description": "变量的单位"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper", "step"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}, "step": {"type": "number", "description": "操作变量的优化步长"}}}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}}}}, "output_variables": {"type": "array", "description": "数据集中所有输出变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "input_variables": {"type": "array", "description": "数据集中所有输入变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}}}}, {"skip_summary": true, "alias": ["优化算法执行"], "name": "opt_model_exec_offline_file_upload", "catalog": "optimization", "description": "优化算法执行", "params": {"type": "object", "description": "通过大模型解析数据集数据，识别TPT模型微调需要的参数信息并确认", "required": ["csv_path", "model_file", "specified_algorithms", "optObject", "optVariables", "constraint", "output_variables", "input_variables", "recommended_tags"], "properties": {"csv_path": {"type": "string", "description": "数据处理后的数据文件路径s3://开头"}, "model_file": {"type": "string", "description": "模型训练生成的模型文件路径s3://开头"}, "specified_algorithms": {"type": "string", "description": "指定模型训练方法模型训练方法，tpt是TPT时序大模型,AutoML是传统机器学习模型"}, "optObject": {"type": "object", "description": "表示需要优化的目标变量名称，至少包含一个元素，如“氯碱装置电耗”", "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量名称或描述"}, "unit": {"type": "string", "description": "变量的单位"}, "symbol": {"type": "string", "description": "优化方向，最小化/降低等用min，最大化/升高/提升等用max"}}}, "optVariables": {"type": "array", "description": "从用户需求中明确提及的用于目标优化的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper", "step"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}, "step": {"type": "number", "description": "操作变量的优化步长"}}}}, "constraint": {"type": "array", "description": "从用户需求中明确提及的用于目标约束的参数变量，如果未明确指定，则为空数组", "items": {"type": "object", "required": ["tag", "description", "lower", "upper"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}, "lower": {"type": "number", "description": "变量值下限"}, "unit": {"type": "string", "description": "变量的单位"}, "upper": {"type": "number", "description": "变量值上限"}}}}, "output_variables": {"type": "array", "description": "数据集中所有输出变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "input_variables": {"type": "array", "description": "数据集中所有输入变量的列表", "items": {"type": "object", "required": ["tag", "description"], "properties": {"tag": {"type": "string", "description": "变量名称"}, "description": {"type": "string", "description": "变量描述"}}}}, "recommended_tags": {"type": "array", "description": "模型推荐参数列表", "items": {"type": "string"}}}}, "result": {"type": "object", "description": "大模型识别优化目标、约束变量、优化变量等信息", "required": ["optObject", "optResult", "message"], "properties": {"optObject": {"type": "object", "description": "优化目标变量信息", "required": [], "properties": {"variable": {"type": "string", "description": "优化目标名称"}, "expression": {"type": "string", "description": "优化目标计算表达式"}, "optimizationValue": {"type": "number", "description": "优化目标优化后的值"}, "unit": {"type": "string", "description": "优化目标单位"}, "currentValue": {"type": "number", "description": "优化目标当前值"}}}, "optResult": {"type": "array", "description": "优化变量优化后的结果信息", "items": {"type": "object", "required": [], "properties": {"tagname": {"type": "string", "description": "优化变量位号名"}, "optimizationValue": {"type": "number", "description": "优化变量优化后的值"}, "unit": {"type": "string", "description": "优化变量单位"}, "describe": {"type": "string", "description": "优化变量描述"}, "currentValue": {"type": "number", "description": "优化变量当前值"}}}}, "message": {"type": "string", "description": "执行消息说明"}}}}]}