stages:
  - deploy

# 提交时，部署 dev 环境
deploy_dev_tools:
  stage: deploy
  tags:
    - windows
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
  parallel:
    matrix:
      - SERVER:
          #- http://***********:15333
          - http://seak8sn1.supcon5t.com:32565
  variables:
    MODULES: modules.json
  script:
    - | # 热重载脚本
      # 初始化错误标志
      $hasErrors = $false

      function Highlight-PyFiles {
        param ([string]$text)
        $text -replace '(\w+\.py)', "`e[31;1m`$1`e[0m"  # 红色 + 粗体
      }

    # 第一步：将 tools 文件夹打包为 tools.zip
    - echo "打包 tools 文件夹为 tools.zip"
    - Compress-Archive -Path ./tools/* -DestinationPath ./tools.zip -Force

    # 第二步：上传 tools.zip 到接口1，只打印错误信息
    - echo "上传 tools.zip 到 $SERVER/api/tool/script 接口"
    - |
      $uploadUrl = "$SERVER/api/tool/script"
      $headers = @{ }
      $form = @{ file = Get-Item ./tools.zip }
      try {
        $response = Invoke-RestMethod -Uri $uploadUrl -Method Post -Headers $headers -Form $form
      } catch {
        Write-Host "❌ 上传错误：$_"
      }

    # 第三步：上传 $MODULES 到接口2，只打印模块信息
    - echo "上传 $MODULES 到 $SERVER/api/tool/modules 接口"
    - |
      try {
        $modulesUrl = "$SERVER/api/tool/modules"
        $headers = @{ 'Content-Type' = 'application/json' }
        $jsonContent = Get-Content ./$MODULES -Raw | ConvertFrom-Json

        $modulesBody = $jsonContent.modules | ConvertTo-Json -Depth 100
        $response = Invoke-RestMethod -Uri $modulesUrl -Method Post -Headers $headers -Body $modulesBody
        if ($response) {
          Write-Host "✅ Loaded modules:"
          foreach ($module in $response.modules) {
              Write-Output "  - Module: $($module.name)"

              # 遍历当前 module 的所有 tools
              foreach ($tool in $module.tools) {
                  Write-Output "     - $($tool.name): $($tool.alias -join ', ')"
              }
          }

          # 直接访问 script_errors 字段
          $moduleErrors = $response.module_errors

          # 检查是否有错误
          if ($null -eq $moduleErrors -or $moduleErrors.Count -eq 0) {
              Write-Host "✅ No module errors found"
          } else {
              Write-Host "❌ Found $($moduleErrors.Count) module errors:"
              # 遍历数组并打印每个错误
              $moduleErrors | ForEach-Object { Write-Host "  - $_" }
              $hasErrors = $true
          }

          # 直接访问 script_errors 字段
          $scriptErrors = $response.script_errors

          # 检查是否有错误
          if ($null -eq $scriptErrors -or $scriptErrors.Count -eq 0) {
              Write-Host "✅ No script errors found"
          } else {
              Write-Host "❌ Found $($scriptErrors.Count) script errors:"
              # 遍历数组并打印每个错误
              foreach ($error in $scriptErrors) {
                $errorWithNewlines = $error -replace "\\n", "`n"
                Write-Host "  - $(Highlight-PyFiles $errorWithNewlines)"
              }
              $hasErrors = $true
          }
        }
      } catch {
        Write-Host "❌ 上传错误：$_"
      }

      # 有错误时，让 CI/CD 作业失败
      if ($hasErrors) {
        exit 1
      }

# 打 test-tag 时，部署 test 环境
deploy_test_tools:
  extends: deploy_dev_tools     # 继承 deploy_dev_tools 的配置
  rules:
    - if: '$CI_COMMIT_TAG =~ /^test-\d{6}-\d+$/'  # 精确匹配 test 格式
      when: always
  parallel:
    matrix:
      - SERVER:
          - https://obp-test.supcon5t.com/tpt-work
  variables:
    MODULES: modules-test.json

# 打 saas-tag 时，部署 saas 环境
deploy_saas_tools:
  extends: deploy_dev_tools     # 继承 deploy_dev_tools 的配置
  rules:
    - if: '$CI_COMMIT_TAG =~ /^saas-\d{6}-\d+$/'  # 精确匹配 saas 格式
      when: always
  parallel:
    matrix:
      - SERVER:
          - http://***********:8090/tpt-work
  variables:
    MODULES: modules-saas.json
