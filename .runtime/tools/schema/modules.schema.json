{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Module Configuration Schema", "type": "object", "properties": {"modules": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Module name"}, "base": {"type": "string", "description": "Folder of the module"}, "alias": {"type": "array", "items": {"type": "string"}, "description": "Alternative names for the module"}, "description": {"type": "string", "description": "Description of the module's purpose"}, "version": {"type": "string", "description": "Semantic version of the module"}, "config": {"type": "object", "properties": {"$log_level": {"type": "string", "enum": ["debug", "info", "warn", "error", "off"], "default": "info", "description": "Optional log level setting"}}, "additionalProperties": true, "required": [], "description": "Dynamic configuration properties"}}, "required": ["name", "base", "alias", "version", "config"], "additionalProperties": false}}}, "required": ["modules"]}