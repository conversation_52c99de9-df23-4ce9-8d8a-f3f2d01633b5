{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {"tool": {"type": "object", "properties": {"name": {"type": "string"}, "alias": {"type": "array", "items": {"type": "string"}}, "catalog": {"type": "string", "enum": ["simulation", "control", "optimization", "prediction", "evaluation", "statistics", "redesign"]}, "description": {"type": "string"}, "params": {"$ref": "http://json-schema.org/draft-07/schema#"}, "result": {"$ref": "http://json-schema.org/draft-07/schema#"}}, "required": ["name", "alias", "catalog", "description", "params", "result"]}}, "properties": {"tools": {"type": "array", "items": {"$ref": "#/definitions/tool"}}}, "required": ["tools"]}