<context>
{%- for message in context %}
  <{{ message.role }}>
    {{ message.content }}
  </{{ message.role }}>
{%- endfor%}
</context>
<history>
{%- for step in history %}
  <step id="{{ step.node.id }}">
    <tool name="{{ step.tool.name }}" alias="{{ step.tool.alias | join: ',' }}" description="{{ step.tool.description }}" module="{{ step.tool.module_name }}">
      <params_schema>
        {{ step.tool.params_schema | json }}
      </params_schema>
      <result_schema>
        {{ step.tool.result_schema | json }}
      </result_schema>
    </tool>
    <params>
      {{ step.params | json }}
    </params>
    <result>
      {{ step.result | json }}
    </result>
    <summary>
      {{ step.summary }}
    </summary>
    <error>
      {{ node.error }}
    </error>
  </step>
{%- endfor%}
</history>
<input_schema>
  {{ step.input_schema | json }}
</input_schema>
<output_schema>
  {{ step.output_schema | json }}
</output_schema>
<last_error>
  {{ last_error }}
</last_error>
<workflow>
  {%- for node in workflow.nodes %}
  <node id="{{ node.id }}" module="{{ node.module }}" tool="{{ node.tool }}"/>
  {%- endfor %}
  {%- for edge in workflow.edges %}
  <edge source_node="{{ edge.source_node }}" source_port="{{ edge.source_port }}" target_node="{{ edge.target_node }}" target_port="{{ edge.target_port }}"/>
  {%- endfor %}
</workflow>