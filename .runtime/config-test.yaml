server:
  # 监听地址
  listen: "0.0.0.0:15336"
  # API基础URL
  chat_api: "http://workflow-search.indu:8800/llm/workflow"
  arguments_api: "http://workflow-search.indu:8800/llm/parameter"
  search_source_api: "http://workflow-search.indu:8800/llm/parameter/rag"
  # explore_api: "ws://127.0.0.1:8000/ws"
  explore_api: "ws://tpt-agent-beta.indu:9389/ws/generate-report"
  # AI API基础URL
  # ai_api_base: "http://sccn2.supcon5t.com:38003/v1"
  # ai_api_version: "v1"
  # ai_api_base: "http://nlb-2weu6cb4a97uoz9cqu39kvmj.nlb.cn-beijing.volces.com:32004/v1"
  # ai_api_model: "qwen3"
  # ai_api_version: "v2.1"
  ai_api_base: "https://ark.cn-beijing.volces.com/api/v3"
  ai_api_model: "deepseek-v3-250324"
  ai_api_key: "6db74026-28dd-4995-bb7c-1f4ba73d39b6"
  ai_api_version: "v2.1"
  # ai_api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  # ai_api_key: "sk-46ce39ac487f4bf9a1cf8ade5a856f52"
  # ai_api_model: "qwen-plus-latest"
  # ai_api_version: "v2.0"
  # 数据库路径
  db_path: "postgres://postgres:<EMAIL>:5432/agent_runner?currentSchema=public"
  minio_endpoint: "http://minio.devops:9000"
  minio_access_key: "admin"
  minio_secret_key: "Supcon1304"
  redis_url: "redis://redis-master.devops:6379"
  integral_api: "http://saas-manage.gpt:8080/api/points-transaction/changePoints"
  calculate_integral: false
  mongodb_url: "mongodb://root:<EMAIL>:27017"
  gateway_public_key: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk2+p3Sxfs77X1rlE4Fh4
    +fqRu2azENIHLlqPs5lliYRxaQz2U/JEqDvIHa3S5pAEP+SnT7iSUX5J2Rh0PHKy
    ZGHiMH+LdDcwVXM9rL3QAvR5voqbpEQPiWaqh7gXXRmAw0n0TJasb5mHIiyK9L0H
    X4VRX1zjmJ3d8Aq3YmcHWL8hPoGA+CUjt8ioeaxEkk/xRZkHAaZUk0m/T3FdGn4E
    GVdlc1GsaGNrZUyytiGqAN1ZO/iqcWw8xV99hh6+s3dd30W4MaMjWSoOT6e6xJvo
    DWUxaLG2dlFX7G73MJLCeL+X9cSAzKqa0lrX2DZwPBr2KYc/QXQtAnWW8ksIodYF
    XwIDAQAB
    -----END PUBLIC KEY-----