from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
import uvicorn
import logging
import time
app = FastAPI()

html = """
<!DOCTYPE html>
<html>
    <head>
        <title>WebSocket Demo</title>
    </head>
    <body>
        <h1>WebSocket FastAPI Demo</h1>
        <textarea id="log" cols="100" rows="20" readonly></textarea><br>
        <input type="text" id="messageInput" autocomplete="off"/>
        <button onclick="sendMessage()">发送</button>
        <script>
            var ws = new WebSocket("ws://localhost:8000/ws");
            ws.onmessage = function(event) {
                document.getElementById('log').value += '\\n收到: ' + event.data;
            };
            ws.onopen = function(event) {
                document.getElementById('log').value += '\\n已连接到服务器';
            };
            ws.onclose = function(event) {
                document.getElementById('log').value += '\\n连接已关闭';
            };
            function sendMessage() {
                var input = document.getElementById("messageInput");
                ws.send(input.value);
                document.getElementById('log').value += '\\n发送: ' + input.value;
                input.value = '';
            }
        </script>
    </body>
</html>
"""

@app.get("/")
async def get():
    return HTMLResponse(html)

# /ws/<id:str>
@app.websocket("/ws/{id}")
async def websocket_endpoint(websocket: WebSocket, id: str):
    await websocket.accept()

    # await websocket.send_json({"type": "text", "text": f"消息 Start"})

    message_count = 2
    try:
        while message_count > 0:
            message_count -= 1

            data = await websocket.receive_text()
            print(f"收到消息: {data} from {id}")

            await websocket.send_json({"type": "todolist"})

            await websocket.send_json({"type": "stage", "content": {"title": f"Stage"}})

            await websocket.send_json({
                "type": "workflow",
                "content": {
                    "think": "think",
                    "json": "[{\"id\": \"1\", \"tool\": \"demo\", \"ability\": \"view\"}]",
                    "branch": "null",
                    "name": "name"
                }
            })
        #     count = 10
        #     while count > 0:
        #         await websocket.send_json({"type": "text", "text": f"消息 {count}"})
        #         count -= 1
            
        #     await websocket.send_json({
        #         "content": {
        #             "type": "form",
        #             "form": {
        #                 "title": "",
        #                 "schema": {
        #                     "type": "object",
        #                     "properties": {
        #                         # "ethaneToEthyleneProcessPackage": {
        #                         #     "type": "array",
        #                         #     "title": "兰州榆林石化乙烷制乙烯的工艺包是？",
        #                         #     "items": {
        #                         #         "type": "string",
        #                         #         "enum": ["蒸汽裂解工艺中的Lummus的 SCORE技术", "蒸汽裂解工艺中的中石化的 CBL®（国产化技术）", "乙烷催化脱氢工艺中的UOP Oleflex™", "乙烷催化脱氢工艺中的Linde乙烷脱氢（EDH）技术", "氧化脱氢工艺"]
        #                         #     }
        #                         # },
        #                         "ethyleneYield": {
        #                             "type": "string",
        #                             "title": "当前乙烯收率是多少？"
        #                         },
        #                         # "mainLimitingFactor": {
        #                         #     "type": "array",
        #                         #     "title": "主要限制因素：影响当前收率的最关键环节？",
        #                         #     "items": {
        #                         #         "type": "string",
        #                         #         "enum": ["原料乙烷纯度不足（含硫/氮等杂质）", "催化剂活性下降（床层压降/中毒）", "反应温度/压力控制波动", "裂解炉运行周期短（结焦严重）", "分离系统效率不足（冷凝/压缩瓶颈）"]
        #                         #     }
        #                         # },
        #                         # "priorityConsiderationForImprovement": {
        #                         #     "type": "array",
        #                         #     "title": "您对提升方案的优先考虑因素是？",
        #                         #     "items": {
        #                         #         "type": "string",
        #                         #         "enum": ["提高乙烷转化率（可能增加副产物）", "降低能耗成本（如减少蒸汽消耗）", "改善产品纯度（减少丙烯/丁烯等副产物）", "延长装置运行周期（减少停车检修）"]
        #                         #     }
        #                         # }
        #                         },
        #                     # "required": ["ethaneToEthyleneProcessPackage", "ethyleneYield", "mainLimitingFactor", "priorityConsiderationForImprovement"]
        #                 },
        #                 "default": {},
        #                 "result": ""
        #             }
        #         },
        #         "type": "interaction"
        #     }
        # )
    except WebSocketDisconnect:
        print("客户端断开连接")

    await websocket.close()

    print(f"WebSocket 连接 {id} 已关闭")


if __name__ == "__main__":
    # log 添加具体时间
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(levelname)s - %(message)s"
            }
        },
        "handlers": {
            "default": {
                "class": "logging.StreamHandler",
                "formatter": "default"
            }
        },
        "root": {
            "handlers": ["default"],
            "level": "DEBUG"
        }
    }
    uvicorn.run("fastapi_websocket_demo:app", host="0.0.0.0", port=8000, reload=True, log_level="error", log_config=log_config) 