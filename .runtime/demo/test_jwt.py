text_base64 = "eyJ1c2VySW5mbyI6IntcInVzZXJcIjp7XCJpZFwiOjEsXCJ1c2VybmFtZVwiOlwiYWRtaW5cIixcIm5pY2tOYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcInR5cGVcIjowLFwiY2xpZW50SWRcIjpcImFkbWluLWFwcFwifSxcInBhcmVudE9yZ1wiOntcImlkXCI6MSxcImNvZGVcIjpcIm9icERlZmF1bHRPcmdcIixcIm5hbWVcIjpcIum7mOiupOe7hOe7h1wifX0iLCJ1c2VyX25hbWUiOiJhZG1pbiIsIm5pY2tOYW1lIjoi6LaF57qn566h55CG5ZGYIiwic2NvcGUiOlsiYWxsIl0sImlkIjoxLCJleHAiOjE3NTEwMTc1NjEsInR5cGUiOjAsImp0aSI6IjIwYWEwMDc3LTgzZTctNDg4My05NDBkLWJkZWZiNDhmMzhiMSIsImNsaWVudF9pZCI6ImFkbWluLWFwcCJ9"

cipher_base64 = "SmBSCiMFeOaDqlfjntVSKmoKB_H-B2prPgoixWRxplDZ84lKZI6EqNJ8m_tkw_yH2nbsItDMEtmeLdjhdbkodc-qtLAP7yGj95Jx68uSUiLvNPakEfbtnVd-9jUSyjomWPqv4TjmmAX3Apq7Qg1I5fsCGYblC14IplNGYTtCiiiG-B67yCnSFVniesu7vYoGRbmmlCtQtwenMACMh9ZkvuGTmf4dMrNXwXk6vG2v-isLpN4IAhagcLm8_qx2cAGVbRHqg-GASeZHdlJR0SYMOLHPXwz1XFeKT5EgzrWCRkDjTbZCMFkiiUNIwcRykSk2ijtbAdgkN7Hn5ntWLWDwhQ"

# RSA
pubkey = """
-----B<PERSON>IN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk2+p3Sxfs77X1rlE4Fh4
+fqRu2azENIHLlqPs5lliYRxaQz2U/JEqDvIHa3S5pAEP+SnT7iSUX5J2Rh0PHKy
ZGHiMH+LdDcwVXM9rL3QAvR5voqbpEQPiWaqh7gXXRmAw0n0TJasb5mHIiyK9L0H
X4VRX1zjmJ3d8Aq3YmcHWL8hPoGA+CUjt8ioeaxEkk/xRZkHAaZUk0m/T3FdGn4E
GVdlc1GsaGNrZUyytiGqAN1ZO/iqcWw8xV99hh6+s3dd30W4MaMjWSoOT6e6xJvo
DWUxaLG2dlFX7G73MJLCeL+X9cSAzKqa0lrX2DZwPBr2KYc/QXQtAnWW8ksIodYF
XwIDAQAB
-----END PUBLIC KEY-----
"""

from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives import serialization
import base64
import binascii

def attempt_decrypt(key, cipher):
    print("正在尝试使用提供的密钥进行解密...")
    try:
        public_key = serialization.load_pem_public_key(
            key.encode('utf-8')
        )

        # The cipher text is URL-safe base64 encoded.
        # It needs to be decoded first.
        try:
            cipher_bytes = base64.urlsafe_b64decode(cipher)
        except binascii.Error:
            padding_needed = '=' * (4 - len(cipher) % 4)
            cipher_bytes = base64.urlsafe_b64decode(cipher + padding_needed)

        # This is the part that will fail.
        # A public key object does not have a 'decrypt' method.
        # This method exists on private key objects.
        # We can demonstrate by checking for the attribute.
        if not hasattr(public_key, 'decrypt'):
            print("\n错误：您提供的密钥是一个公钥，它没有 'decrypt' (解密) 方法。")
            print("RSA算法使用公钥进行加密，但需要使用对应的私钥才能解密。")
            print("如果您有私钥 (通常以 '-----BEGIN RSA PRIVATE KEY-----' 开头)，请使用它来进行解密。")
            print("---")
            print("Error: The key you provided is a public key, which does not have a 'decrypt' method.")
            print("The RSA algorithm uses a public key for encryption, but requires the corresponding private key for decryption.")
            print("If you have the private key (usually starting with '-----BEGIN RSA PRIVATE KEY-----'), please use it for decryption.")
            return

        # This code will not be reached with a public key
        plaintext = public_key.decrypt(
            cipher_bytes,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )

        print("解密成功！")
        print("结果:", plaintext)

    except Exception as e:
        print("解密过程中发生错误。")
        print(f"错误详情: {e}")
        print("\n这通常是因为解密需要与加密时使用的公钥相配对的私钥。")

attempt_decrypt(pubkey, cipher_base64)

print("-" * 20)

import jwt

def verify_jwt(token_payload, signature, key):
    print("正在尝试验证JWT...")
    # A standard JWT header for RS256
    header = '{"alg":"RS256","typ":"JWT"}'
    # It must be base64url encoded
    header_b64 = base64.urlsafe_b64encode(header.encode('utf-8')).rstrip(b'=').decode('utf-8')

    # Construct the full token string
    full_jwt = f"{header_b64}.{token_payload}.{signature}"

    print("构建的完整JWT (for demonstration):")
    # print(full_jwt) # This is too long, let's print parts
    print(f"  Header: {header}")
    print(f"  Payload (first 30 chars): {token_payload[:30]}...")
    print(f"  Signature (first 30 chars): {signature[:30]}...")


    try:
        # The PyJWT library needs the public key object, not the PEM string
        public_key = serialization.load_pem_public_key(key.encode('utf-8'))

        # Attempt to decode and verify the JWT
        # Add options to ignore expiration for this test
        options = {"verify_exp": False}

        decoded_payload = jwt.decode(
            full_jwt,
            public_key,
            algorithms=["RS256"],  # Specify the algorithm used for signing
            options=options
        )

        print("\n✅ JWT 验证成功! (已忽略过期时间)")
        print("解码后的 Payload:")
        import json
        print(json.dumps(decoded_payload, indent=2, ensure_ascii=False))

    except jwt.InvalidSignatureError:
        print("\n❌ JWT 验证失败：签名无效。")
        print("这很可能是因为:")
        print("  1. 我们猜测的 Header ('alg': 'RS256') 不正确。")
        print("  2. `cipher_base64` 并不是这个 Payload 的有效签名。")
        print("  3. 提供的公钥与签发该 JWT 的私钥不匹配。")
    except jwt.ExpiredSignatureError:
        print("\n❌ JWT 验证失败：令牌已过期。")
    except Exception as e:
        print(f"\n❌ JWT 验证时发生其他错误: {e}")


verify_jwt(text_base64, cipher_base64, pubkey)
