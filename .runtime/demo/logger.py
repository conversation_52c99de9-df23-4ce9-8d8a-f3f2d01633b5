import logging
import time
import sys

class MicrosecondFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        ct = self.converter(record.created)
        t = time.strftime("%Y-%m-%dT%H:%M:%S", ct)
        s = "%s.%03dZ" % (t, record.msecs)
        return s

logger_formatter = MicrosecondFormatter(fmt="%(asctime)s %(levelname)s %(message)s")
logger_formatter.converter = time.gmtime

logger = logging.getLogger()
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logger_formatter)
file_handler = logging.FileHandler("output.log")
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(logger_formatter)
logger.addHandler(console_handler)
logger.addHandler(file_handler)
