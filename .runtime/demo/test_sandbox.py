from RestrictedPython import compile_restricted
from RestrictedPython import safe_builtins
from RestrictedPython.PrintCollector import PrintCollector
import json

safe_globals = {
    '__builtins__': safe_builtins,
    '_print_': PrintCollector,
    '_getattr_': getattr,
    'json': json,
}

json.load()

code = """
def example():
  print("This is a safe function.")
  print(json.dumps({"message": "This is a safe function."}))
  return printed
"""

bc = compile_restricted(code, '<string>', 'exec')

loc = {}

exec(bc, safe_globals, loc)

result = loc['example']()

print("Captured output:", result)