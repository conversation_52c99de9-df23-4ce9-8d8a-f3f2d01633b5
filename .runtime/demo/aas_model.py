import os
import json
import inspect

from logger import logger
from dotenv import load_dotenv
from os import getenv
from enum import StrEnum
from pydantic import BaseModel
from functools import wraps 

load_dotenv()

from agents import Agent, Runner, AsyncOpenAI, OpenAIChatCompletionsModel, function_tool, ModelSettings, ModelTracing, RunHooks
from aiohttp import ClientSession

AAS_BASE_URL = getenv("AAS_BASE_URL")
AAS_TOKEN = getenv("AAS_TOKEN")
OPENAI_MODEL = getenv("OPENAI_MODEL")

agent_model = OpenAIChatCompletionsModel(
    model=OPENAI_MODEL,
    openai_client=AsyncOpenAI()
)

agent_model_settings = ModelSettings(
    max_tokens=8*1024 
)

summarize_model = OpenAIChatCompletionsModel(
    model="qwen-turbo-latest",
    openai_client=AsyncOpenAI()
)

summarize_model_settings = ModelSettings(
    max_tokens=8*1024 
)

def summarize(hint: str = ""):
    def decorator(func):
        @wraps(func)  # preserve function metadata for function_tool
        async def wrapper(*args, **kwargs):
            hint2 = kwargs.pop("hint2", "")
            result = await func(*args, **kwargs)
            logger.debug(f"正在总结{func.__name__}, 提示1: {hint}, 提示2: {hint2}")
            # 如果是 str 类型，不序列化
            try:
                if isinstance(result, str):
                    serialized_result = result
                else:
                    serialized_result = json.dumps(result, ensure_ascii=False)
            except Exception:
                serialized_result = str(result)
            
            combined_hint = hint
            if hint2:
                combined_hint = f"{hint}; {hint2}"
                
            async def collect_streaming_response(stream_iterator):
                text = ""
                async for event in stream_iterator:
                    if hasattr(event, "delta") and event.type == "response.output_text.delta":
                        text += event.delta
                return text
                
            stream_iterator = summarize_model.stream_response(
                system_instructions=f"你是专业的数据处理师，现在需要你提取我输入的信息，尽可能完整。只回复数据，不要回复无关内容。提示：{combined_hint}",
                input=serialized_result,
                model_settings=summarize_model_settings,
                tools=[],
                output_schema=None,
                handoffs=[],
                tracing=ModelTracing.DISABLED,
            )
            
            result_text = await collect_streaming_response(stream_iterator)
            return result_text
        
        sig = inspect.signature(func)
        parameters = list(sig.parameters.values())
        parameters.append(
            inspect.Parameter(
                "hint2",
                inspect.Parameter.KEYWORD_ONLY,
                default="",
                annotation=str
            )
        )
        wrapper.__doc__ += "Args: hint2 (str): 用于总结数据的提示信息，重点说明需要提取哪些数据，哪些字段\n"
        wrapper.__signature__ = sig.replace(parameters=parameters)
        return wrapper
    return decorator

async def aas_api(path: str, parameter: str = "", method: str = "GET", body: dict = None):
    """调用AAS的API
        Args:
            path (str): API路径
            parameter (str): 参数
            method (str): 请求方法
            body (dict): 请求体
        Returns:
            dict: API响应的JSON数据
    """
    try:
        async with ClientSession() as session:
            logger.debug(f"正在处理：{path}")
            async with session.request(
                        method=method,
                        url=AAS_BASE_URL, 
                        params={
                            "parameter": parameter, 
                            "path": path
                        },
                        json=body,
                        headers={
                            "Authorization": f"Bearer {AAS_TOKEN}",
                        },
                    ) as response:
                return await response.json()
    except Exception as e:
        logger.debug(f"API调用失败: {path}")
        logger.debug(f"参数: parameter={parameter}, method={method}, body={json.dumps(body, ensure_ascii=False) if body else None}")
        logger.debug(f"错误: {str(e)}")
        raise  # 重新抛出异常

@function_tool
@summarize("节点树，每个节点的ID和名称等信息")
async def aas_get_node_tree():
    """获取AAS中的节点树
    包含节点数、节点ID、节点名称
    """
    return await aas_api("/api/tagmanager/getnodetree")

class TagInfo(BaseModel):
    """位号信息
    """

    name: str
    """位号名称
    """
    parentID: str
    """节点ID

    需要从节点树中根据名称查找，通常是nodeId字段，uuid格式
    """
    type: int = 0
    """位号类型
    需要从接口中获取枚举
    枚举值一般不发生变化，按照顺序填入，不确定时填入0
    """
    description: str | None = None
    unit: str | None = None
    """单位
    默认为空
    """
    operateRange: str | None = None
    """操作范围
    默认为空
    """
    preCategory: str | None = None
    """域
    默认为空
    """
    posCategory: str | None = None
    """拓展模块
    默认为空
    """
    tagPriority: str | None = None
    """位号优先级
    默认为空
    """
    alarmClassification: str | None = None
    """报警分类的名称
    一般是文字，需要从接口中获取枚举，枚举值一般不发生变化，以逗号分割可以选择多个
    """


@function_tool
async def aas_add_tag(tags: list[TagInfo]):
    """添加位号

    Args:
        tags (list[TagInfo]): 位号信息列表
    """


    for tag in tags:
        logger.debug(f"添加位号: {tag.model_dump()}")
        response = await aas_api("/api/tagmanager/addtag", body=tag.model_dump(), method="POST")
        if response["code"] != 0:
            logger.debug(f"添加位号失败: {tag.name}，错误信息: {response["msg"]}")
        else:
            logger.debug(f"添加位号成功: {tag.name}")
    
    return {"result": "success"}

@function_tool
async def aas_get_alarm_classification():
    """获取报警分类
    """
    return await aas_api("/api/DBSetting/alarmclassification")

@function_tool
async def aas_get_tag_type():
    """获取位号类型
    """
    return await aas_api("/api/tagmanager/tagtype")

@function_tool
async def aas_open_page(path: list[str]):
    """为用户打开页面，可以传入多个让用户选择

    Args:
        path (list[str]): The path of the page to open. 
            "http://10.30.76.88:8081/aas-web/#/system-management/tag-management" 节点树页 包含节点树以及位号管理，可以查看位号、添加位号、删除位号、导入位号、导出位号
            "http://10.30.76.88:8081/aas-web/#/systeminfo/platformAuthInfo" 授权模块 平台授权信息管理页面
            "http://10.30.76.88:8081/aas-web/#/homePage/dashboard" 报警统计信息 展示全局报警信息，展板，图标，统计信息，大屏展示 如果用户希望查看统计信息，优先展示这个界面
            "http://10.30.76.88:8081/aas-web/#/homePage/dashboard/Editor" Dashboard配置 配置Dashboard展示页面
            "http://10.30.76.88:8081/aas-web/#/realtime-monitor/multistate-alarm"  多工况报警 监控不同工况下的报警状态
            "http://10.30.76.88:8081/aas-web/#/realtime-monitor/shelving-alarm" 搁置报警 暂时屏蔽或搁置的报警项管理
            "http://10.30.76.88:8081/aas-web/#/system-management/tag-management" 主报警数据库 包含系统中所有报警的主数据库
            "http://10.30.76.88:8081/aas-web/#/system-management/tag-grouping" 自定义报警分组 创建和管理自定义的报警分类和分组
            "http://10.30.76.88:8081/aas-web/#/configuration-audit/audit-result" 审计报告 系统配置变更的审计记录和报告
            "http://10.30.76.88:8081/aas-web/#/realtime-monitor/realtime-alarm" 实时报警，显示当前系统中的实时报警信息，列表，一般用于调试或者追溯
    """
    logger.debug(f"打开页面: {path}")
    return {
        "result": "success",
        "url": path
    }

@function_tool
async def read_directory():
    """读取目录

    本地工具，文件系统，中获取所有文件名

    Args:
        directory (str): 目录路径
    """
    directory_path = os.path.join(os.getcwd(), "root")
    files = os.listdir(directory_path)
    return [file for file in files if os.path.isfile(os.path.join(directory_path, file))]

# read file
@function_tool
@summarize("输入为文件原始内容")
async def read_file(file_name: str):
    """读取文件

    本地工具，文件系统
    读取文件内容

    Args:
        file_name (str): 文件名
    """
    directory_path = os.path.join(os.getcwd(), "root")
    with open(os.path.join(directory_path, file_name), "r", encoding='utf-8') as file:
        return file.read()

agent = Agent(
    name="Assistant", 
    instructions="你是一个资深的DCS工程师。可以处理关于AAS系统的工作，也可以为用户直接打开页面（优先）。在处理完成用户的命令后，用文字总结你的操作。如果有可以展示的内容，需要打开页面展示给用户。", 
    model=agent_model,
    model_settings=agent_model_settings,
    tools=[
        aas_open_page,
        aas_get_node_tree, 
        aas_add_tag,
        aas_get_alarm_classification,
        aas_get_tag_type,
        read_directory,
        read_file,
    ]
)

# override RunHooks
class MyRunHooks(RunHooks):
    async def on_agent_start(self, context, agent):
        logger.info(f"开始处理: {agent.name}, {agent.instructions}")
    
    async def on_agent_end(self, context, agent, output):
        logger.info(f"处理结束: {agent.name}")
        logger.info(f"最终结果: {output}")

    async def on_tool_start(self, context, agent, tool):
        logger.info(f"开始处理工具: {tool.name}")

    async def on_tool_end(self, context, agent, tool, result):
        logger.info(f"工具处理结束: {tool.name}")
        logger.info(f"工具处理结果: {result}")

# result = Runner.run_sync(agent, "根据`位号表_测试单元.csv`中的数据，添加位号到测试单元。", hooks=MyRunHooks())
result = Runner.run_sync(agent, "查看报警统计", hooks=MyRunHooks())
logger.debug(f"最终结果: {result.final_output}")
