import json
import uuid
import requests
import sseclient
import urllib3

# 禁用SSL警告
urllib3.disable_warnings()

class ConversationClient:
    def __init__(self, base_url="http://127.0.0.1:15336"):
        self.base_url = base_url
        
    def create_conversation(self):
        """创建新对话,返回对话ID"""
        response = requests.post(f"{self.base_url}/api/conversation")
        response.raise_for_status()
        return response.json()["id"]

    def stream_conversation(self, conversation_id, message):
        """发送消息并获取流式响应"""
        url = f"{self.base_url}/api/conversation/{conversation_id}/stream"
        data = {
            "parent_id": None,
            "message": {
                "content": [
                    {
                        "type": "text",
                        "text": message
                    }
                ]
            }
        }
        
        response = requests.post(url, json=data, stream=True)
        response.raise_for_status()
        client = sseclient.SSEClient(response)
        
        current_message = None
        
        for event in client.events():
            if event.data == "[DONE]":
                break
                
            event_data = json.loads(event.data)
            event_type = event_data["type"]
            
            if event_type == "new_message":
                current_message = event_data["message"]
                print(f"[{current_message['author']['role']}]: ", end="")
                # 处理初始内容
                for content in current_message.get("content", []):
                    if content["type"] == "text":
                        print(content["text"])
                    elif content["type"] == "interaction":
                        # 立即返回交互信息并携带完整消息
                        return current_message["id"], content, current_message
                
            elif event_type == "message_content_delta":
                # 合并增量内容
                delta_content = event_data.get("content", "")
                # 只处理字符串类型的delta内容
                if isinstance(delta_content, str):
                    print(delta_content, end="")
                    if current_message:
                        # 初始化content结构
                        if "content" not in current_message:
                            current_message["content"] = []
                        if not current_message["content"] or current_message["content"][0]["type"] != "text":
                            current_message["content"].insert(0, {"type": "text", "text": ""})
                        current_message["content"][0]["text"] += delta_content
                
            elif event_type == "conversation_status_changed":
                status = event_data.get("status", "unknown")
                print(f"\n[Status]: {status}\n")
                
        print()  # 换行
        return current_message
        
    def handle_interaction(self, conversation_id, message_id, content_index=0):
        """处理交互,默认选择第一个选项"""
        url = f"{self.base_url}/api/conversation/{conversation_id}/interaction"
        data = {
            "message_id": message_id,
            "content_index": content_index,
            "result": 0  # 选择第一个选项
        }
        response = requests.post(url, json=data)
        response.raise_for_status()

def main(message):
    client = ConversationClient()
    
    # 创建对话
    conversation_id = client.create_conversation()
    print(f"Created conversation: {conversation_id}")
    
    result = client.stream_conversation(conversation_id, message)
    if result:  # 返回格式为(message_id, content, full_message)
        message_id, content, full_message = result
        if content["type"] == "interaction":
            print(f"\n完整消息内容：{json.dumps(full_message, ensure_ascii=False)}")
            print("处理交互中...")
            client.handle_interaction(conversation_id, message_id)

main("帮我整定下FIC102")
