from datetime import timedel<PERSON>


def convert(input):
    # 自动生成的转换逻辑注释说明
    output = []
    
    for item in input:
        new_item = {
            "loopName": item["id2"]["loopName"],  # 直接映射loopName
            "groupId": item["id1"]["groupList"]["id"],  # 从groupList中提取groupId
            "startTime": "",  # 默认值，需要外部提供
            "endTime": ""  # 默认值，需要外部提供
        }
        output.append(new_item)
        timedelta
    
    return output if isinstance(input, list) else output[0]


convert({
  "id1": {
    "groupList": {
      "groupName": "乙烯装置一部",
      "groupPath": "装置/乙烯装置一部",
      "id": 5329847454482912
    },
    "loopList": {
      "groupName": "乙烯装置一部",
      "groupPath": "装置/乙烯装置一部",
      "id": 5329828900487648,
      "loopName": "222FIC102"
    }
  },
  "id2": {
    "description": "",
    "loopName": "222FIC102",
    "realtimeStableStatus": "平稳",
    "rejectCondition": "",
    "runningState": "正常运行",
    "selfControlStatus": "手动"
  }
})