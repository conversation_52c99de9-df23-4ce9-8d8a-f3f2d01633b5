import json
import requests
import sseclient
import urllib3
import datetime
import os
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(message)s')
logger = logging.getLogger(__name__)

# 禁用SSL警告
urllib3.disable_warnings()

def set_by_path(obj, path, value):
    """
    根据 path 设置 obj 中的值，path 支持数字（list下标）和字符串（dict key）
    如果路径上缺少节点，则自动创建
    """
    if not path:
        return

    # 遍历路径上除最后一个节点外的所有节点
    current = obj
    for i, key in enumerate(path[:-1]):
        next_key = path[i + 1] if i + 1 < len(path) else None
        next_is_int = isinstance(next_key, int) or (isinstance(next_key, str) and next_key.isdigit())
        
        # 当前节点是列表
        if isinstance(current, list):
            idx = int(key) if isinstance(key, str) and key.isdigit() else key
            if not isinstance(idx, int):
                logger.warning(f"列表索引必须是整数，收到: {idx}")
                return
            
            # 确保列表长度足够
            while len(current) <= idx:
                current.append([] if next_is_int else {})
            
            if i == len(path) - 2:  # 倒数第二个节点
                current = current[idx]
            else:
                # 如果当前节点不是容器类型，转换为合适的容器
                if not isinstance(current[idx], (dict, list)):
                    current[idx] = [] if next_is_int else {}
                current = current[idx]
        
        # 当前节点是字典
        elif isinstance(current, dict):
            key_str = str(key)
            
            # 如果key不存在或者不是容器类型，创建合适的容器
            if key_str not in current or not isinstance(current[key_str], (dict, list)):
                current[key_str] = [] if next_is_int else {}
            
            current = current[key_str]
        
        else:
            logger.warning(f"无法在类型 {type(current)} 上设置路径")
            return
    
    # 处理最后一个节点
    last_key = path[-1]
    
    if isinstance(current, list):
        idx = int(last_key) if isinstance(last_key, str) and last_key.isdigit() else last_key
        if not isinstance(idx, int):
            logger.warning(f"列表索引必须是整数，收到: {idx}")
            return
        
        # 确保列表长度足够
        while len(current) <= idx:
            current.append(None)
        
        current[idx] = value
    
    elif isinstance(current, dict):
        current[str(last_key)] = value
    
    else:
        logger.warning(f"无法在类型 {type(current)} 上设置路径")


def append_to_path(obj, path, value):
    """
    在 path 指定的位置追加 value
    如果 path 指向的是字符串，则直接拼接
    如果 path 指向的是列表，则直接追加
    其他情况转换为合适的类型后追加
    """
    if not path:
        return
    
    # 先确保路径存在
    current = obj
    for i, key in enumerate(path[:-1]):
        next_key = path[i + 1] if i + 1 < len(path) else None
        next_is_int = isinstance(next_key, int) or (isinstance(next_key, str) and next_key.isdigit())
        
        if isinstance(current, list):
            idx = int(key) if isinstance(key, str) and key.isdigit() else key
            if not isinstance(idx, int):
                logger.warning(f"列表索引必须是整数，收到: {idx}")
                return
            
            # 确保列表长度足够
            while len(current) <= idx:
                current.append([] if next_is_int else {})
            
            if not isinstance(current[idx], (dict, list)):
                current[idx] = [] if next_is_int else {}
            
            current = current[idx]
        
        elif isinstance(current, dict):
            key_str = str(key)
            
            if key_str not in current:
                current[key_str] = [] if next_is_int else {}
            
            if not isinstance(current[key_str], (dict, list)):
                current[key_str] = [] if next_is_int else {}
            
            current = current[key_str]
        
        else:
            logger.warning(f"无法在类型 {type(current)} 上追加值")
            return
    
    # 处理最后一个节点
    last_key = path[-1]
    
    if isinstance(current, list):
        idx = int(last_key) if isinstance(last_key, str) and last_key.isdigit() else last_key
        if not isinstance(idx, int):
            logger.warning(f"列表索引必须是整数，收到: {idx}")
            return
        
        # 确保列表长度足够
        while len(current) <= idx:
            current.append("")
        
        # 如果目标位置是字符串，直接拼接
        if isinstance(current[idx], str):
            current[idx] += value
        # 如果目标位置是列表，直接追加
        elif isinstance(current[idx], list):
            current[idx].append(value)
        # 其他情况，转换为字符串后拼接
        else:
            current[idx] = str(current[idx]) + value if current[idx] is not None else value
    
    elif isinstance(current, dict):
        key_str = str(last_key)
        
        # 如果key存在
        if key_str in current:
            # 如果目标位置是字符串，直接拼接
            if isinstance(current[key_str], str):
                current[key_str] += value
            # 如果目标位置是列表，直接追加
            elif isinstance(current[key_str], list):
                current[key_str].append(value)
            # 其他情况，转换为字符串后拼接
            else:
                current[key_str] = str(current[key_str]) + value if current[key_str] is not None else value
        else:
            # 不存在则创建字符串
            current[key_str] = value
    
    else:
        logger.warning(f"无法在类型 {type(current)} 上追加值")


class ConversationClient:
    def __init__(self, base_url="http://127.0.0.1:15660"):
        self.base_url = base_url
        
    def create_conversation(self):
        """创建新对话,返回对话ID"""
        try:
            response = requests.post(f"{self.base_url}/api/conversation")
            response.raise_for_status()
            return response.json()["id"]
        except Exception as e:
            logger.error(f"创建对话失败: {e}")
            raise

    def stream_conversation(self, conversation_id, message):
        """发送消息并获取流式响应"""
        url = f"{self.base_url}/api/conversation/{conversation_id}/stream"
        data = {
            "parent_id": None,
            "message": {
                "content": [
                    {
                        "type": "text",
                        "text": message
                    }
                ]
            }
        }
        
        logger.info(f"发送请求到: {url}")
        logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        try:
            response = requests.post(url, json=data, stream=True)
            response.raise_for_status()
            client = sseclient.SSEClient(response)
            
            # 用于存储消息的数组，保持顺序
            messages = []
            # 消息ID到索引的映射
            message_index_map = {}
            # 当前消息ID
            current_message_id = None
            # 对话状态
            conversation_status = None
            
            event_count = 0
            
            for event in client.events():
                try:
                    event_count += 1
                    data = json.loads(event.data)
                    event_type = data.get("type")
                    
                    logger.debug(f"接收到SSE事件 #{event_count}: {data}")
                    print(f"接收到SSE事件 #{event_count}: {data}", flush=True)
                    
                    # 处理新消息事件
                    if event_type == "new_message":
                        payload = data.get("parent_id", None)
                        msg = data.get("message", {})
                        msg_id = msg.get("id")
                        
                        if msg_id:
                            # 创建新消息结构
                            new_message = {
                                "id": msg_id,
                                "parent_id": payload,
                                "author": msg.get("author", {}),
                                "create_time": msg.get("create_time"),
                                "status": msg.get("status"),
                                "content": msg.get("content", []),
                                "metadata": msg.get("metadata", {})
                            }
                            # 添加到数组
                            messages.append(new_message)
                            # 记录索引位置
                            message_index_map[msg_id] = len(messages) - 1
                            current_message_id = msg_id
                            logger.info(f"新消息: {msg_id}")
                    
                    # 处理消息内容增量更新
                    elif event_type == "message_content_delta":
                        message_id = data.get("message_id")
                        operation = data.get("operation")
                        path = data.get("path", [])
                        content = data.get("content")
                        
                        logger.debug(f"内容更新: message_id={message_id}, operation={operation}, path={path}")
                        
                        # 找到对应消息的索引
                        msg_index = message_index_map.get(message_id)
                        
                        # 如果消息不存在，创建并添加
                        if msg_index is None:
                            new_message = {
                                "id": message_id,
                                "content": []
                            }
                            messages.append(new_message)
                            msg_index = len(messages) - 1
                            message_index_map[message_id] = msg_index
                        
                        # 根据操作类型处理内容
                        if operation == "append":
                            # 对于append操作，确保值是字符串
                            if not isinstance(content, str) and isinstance(content, dict) and content.get("type") == "text":
                                content = content.get("text", "")
                            append_to_path(messages[msg_index]["content"], path, content)
                        elif operation == "update":
                            set_by_path(messages[msg_index]["content"], path, content)
                    
                    # 处理消息详细信息增量更新
                    elif event_type == "message_details_delta":
                        message_id = data.get("message_id")
                        operation = data.get("operation")
                        path = data.get("path", [])
                        content = data.get("content")
                        
                        logger.debug(f"详细信息更新: message_id={message_id}, operation={operation}, path={path}")
                        
                        # 找到对应消息的索引
                        msg_index = message_index_map.get(message_id)
                        
                        # 如果消息不存在，创建并添加
                        if msg_index is None:
                            new_message = {
                                "id": message_id,
                                "content": []
                            }
                            messages.append(new_message)
                            msg_index = len(messages) - 1
                            message_index_map[message_id] = msg_index
                        
                        # 根据操作类型处理内容
                        if operation == "append":
                            # 对于append操作，确保值是字符串
                            if not isinstance(content, str) and isinstance(content, dict) and content.get("type") == "text":
                                content = content.get("text", "")
                            append_to_path(messages[msg_index]["content"], path, content)
                        elif operation == "update":
                            set_by_path(messages[msg_index]["content"], path, content)
                    
                    # 处理消息状态变更
                    elif event_type == "message_status_changed":
                        message_id = data.get("message_id")
                        status = data.get("status")
                        author = data.get("author")
                        
                        logger.info(f"消息状态变更: message_id={message_id}, status={status}")
                        
                        # 找到对应消息的索引
                        msg_index = message_index_map.get(message_id)
                        
                        # 如果消息不存在，创建并添加
                        if msg_index is None:
                            new_message = {
                                "id": message_id,
                                "content": []
                            }
                            messages.append(new_message)
                            msg_index = len(messages) - 1
                            message_index_map[message_id] = msg_index
                        
                        # 更新状态
                        messages[msg_index]["status"] = status
                        
                        # 如果提供了作者信息，更新作者
                        if author:
                            messages[msg_index]["author"] = author
                    
                    # 处理对话状态变更
                    elif event_type == "conversation_status_changed":
                        status = data.get("status")
                        metadata = data.get("metadata")
                        
                        logger.info(f"对话状态变更: status={status}")
                        conversation_status = status
                
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                except Exception as e:
                    logger.error(f"处理事件异常: {e}")
                    logger.error(f"事件数据: {event.data}")
            
            logger.info(f"共处理 {event_count} 个SSE事件")
            
            # 返回结构化数据
            return {
                "conversation_id": conversation_id,
                "current_message_id": current_message_id,
                "status": conversation_status,
                "messages": messages
            }
        
        except Exception as e:
            logger.error(f"流式对话请求失败: {e}")
            raise


def main():
    final_data = {}
    log_filename = ""
    
    try:
        # client = ConversationClient(base_url="http://127.0.0.1:15660")
        client = ConversationClient(base_url="http://127.0.0.1:15336")
        
        # 创建新对话
        conversation_id = client.create_conversation()
        logger.info(f"创建新对话: {conversation_id}")
        
        # 发送消息并获取流式响应
        message = "整定FIC102"
        # message = "测试demo"
        logger.info(f"发送消息: {message}")
        
        final_data = client.stream_conversation(conversation_id, message)
        
    except Exception as e:
        logger.error(f"处理过程出错: {e}")
        print(f"处理过程出错: {e}")
        final_data = {"error": str(e)}
    
    finally:
        # 无论是否出错，都保存文件
        # 生成日志文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        log_filename = f"merge_{timestamp}.log"
        
        try:
            # 保存结构化数据到日志文件
            with open(log_filename, "w", encoding="utf-8") as f:
                json.dump(final_data, f, ensure_ascii=False, indent=2)
            
            file_size = os.path.getsize(log_filename)
            logger.info(f"结构化数据已保存到: {log_filename}")
            logger.info(f"文件大小: {file_size} 字节")
            
            print(f"处理完成，结果已保存到: {log_filename}")
            print(f"文件大小: {file_size} 字节")
        except Exception as write_error:
            logger.error(f"保存文件出错: {write_error}")
            print(f"保存文件出错: {write_error}")


if __name__ == "__main__":
    main()
