from dotenv import load_dotenv

load_dotenv()

import os
import anyio
import json
from logger import logger
from mcp.client.session import ClientSession
from mcp.client.sse import sse_client
from openai import AsyncOpenAI
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

# 添加处理 Pydantic 模型序列化的辅助函数
def serialize_pydantic_content(content_list):
    """将 Pydantic 模型列表转换为可 JSON 序列化的字典列表"""
    if not content_list:
        return []
    
    result = []
    for item in content_list:
        if hasattr(item, "text"):
            result.append(item.text)
        elif hasattr(item, "model_dump"):
            result.append(item.model_dump())
        elif hasattr(item, "dict"):
            result.append(item.dict())
        else:
            result.append(dict(item))
    
    return result

async def main(input: str):
    async with sse_client("http://127.0.0.1:15336/api/mcp") as (read_stream, write_stream):
        async with ClientSession(read_stream, write_stream) as session:
            logger.info(f"{Fore.CYAN}正在初始化会话{Style.RESET_ALL}")
            await session.initialize()
            logger.info(f"{Fore.GREEN}初始化完成{Style.RESET_ALL}")
            tools = await session.list_tools()
            logger.info(f"{Fore.YELLOW}可用工具: %s{Style.RESET_ALL}", tools)

            # map tools to openai tools
            openai_tools = []
            for tool in tools.tools:
                # 将 MCP 工具转换为 OpenAI 工具格式
                openai_tool = {
                    "type": "function",
                    "function": {
                        "name": tool.name.replace("/", "____"),
                        "description": tool.description if tool.description else f"Tool: {tool.name}",
                        "parameters": tool.inputSchema
                    }
                }
                openai_tools.append(openai_tool)
            
            # 创建消息历史记录
            messages = [
                {"role": "system", "content": "你是一个只会使用工具的机器人，哔哔哔。当前用户是 ronbb，位置在杭州。"},
                {"role": "user", "content": input},
            ]
            
            async with AsyncOpenAI() as openai_client:
                # 处理对话循环
                while True:
                    logger.info(f"{Fore.BLUE}正在创建补全，消息内容: %s{Style.RESET_ALL}", messages)
                    completion = await openai_client.chat.completions.create(
                        model=os.getenv("OPENAI_MODEL"),
                        messages=messages,
                        tools=openai_tools,
                    )
                    
                    # 获取返回内容
                    response_message = completion.choices[0].message
                    logger.info(f"{Fore.GREEN}收到响应: %s{Style.RESET_ALL}", response_message)
                    
                    # 添加到消息历史中
                    messages.append(response_message)
                    
                    # 检查是否有工具调用
                    if response_message.tool_calls:
                        # 处理所有的工具调用
                        for tool_call in response_message.tool_calls:
                            function_name = tool_call.function.name
                            function_args = json.loads(tool_call.function.arguments)
                            
                            logger.info(f"{Fore.MAGENTA}工具调用: {function_name}, 参数: {function_args}{Style.RESET_ALL}")
                            
                            # 将 OpenAI 工具名称转换回 MCP 工具名称
                            mcp_tool_name = function_name.replace("____", "/")
                            
                            # 调用 MCP 工具
                            try:
                                tool_result = await session.call_tool(mcp_tool_name, function_args)
                                logger.info(f"{Fore.GREEN}工具返回结果: {tool_result}{Style.RESET_ALL}")

                                if tool_result.isError:
                                    raise Exception(f"工具调用失败: {mcp_tool_name} {function_args} {tool_result}")
                                
                                # 将工具结果添加到消息历史 - 使用辅助函数处理 Pydantic 模型
                                serialized_content = serialize_pydantic_content(tool_result.content)
                                messages.append({
                                    "tool_call_id": tool_call.id,
                                    "role": "tool",
                                    "name": function_name,
                                    "content": json.dumps(serialized_content),
                                })
                            except Exception as e:
                                logger.error(f"{Fore.RED}工具调用失败: {e}{Style.RESET_ALL}")
                                messages.append({
                                    "tool_call_id": tool_call.id,
                                    "role": "tool",
                                    "name": function_name,
                                    "content": json.dumps({"error": str(e)}),
                                })
                    else:
                        # 如果没有工具调用，显示返回内容并结束会话
                        print(f"\n\n\n{input}\n")
                        print(f"\n{response_message.content}\n\n\n")
                        break
                
anyio.run(main, "杭州到西安的直线距离有多远？开车需要多久？途径哪些省？")
