http:
  routers:
    root-redirect:
      rule: "Path(`/`)"
      service: noop@internal
      priority: 100
      middlewares:
        - root-redirect

    tpt-app-redirect:
      rule: "Path(`/tpt-app`)"
      service: noop@internal
      priority: 200
      middlewares:
        - tpt-app-redirect

    chat-tool-agent:
      rule: "PathPrefix(`/tpt-app/chat-tool-agent/`)"
      service: chat-tool-service
      priority: 300
      entryPoints:
        - web
      middlewares:
        - strip-prefix-chat-tool

    chat-tool-work:
      rule: "PathPrefix(`/tpt-app/chat-tool-work/`)"
      service: chat-tool-service
      priority: 300
      entryPoints:
        - web
      middlewares:
        - strip-prefix-chat-tool

    chat-tool-work-pid:
      rule: "PathPrefix(`/xpt-tpt-pid/chat-tool-work/`)"
      service: chat-tool-service
      priority: 300
      entryPoints:
        - web
      middlewares:
        - strip-prefix-chat-tool

    chat-tool-socket-work:
      rule: "PathPrefix(`/tpt-app/chat-tool-socket-work/`)"
      service: chat-tool-service
      priority: 300
      entryPoints:
        - web
      middlewares:
        - strip-prefix-chat-tool

    tpt-app:
      rule: "PathPrefix(`/tpt-app/`)"
      service: tpt-app-service
      priority: 180
      entryPoints:
        - web

    tpt-app2:
      rule: "PathPrefix(`/`)"
      service: tpt-app-service2
      priority: 150
      entryPoints:
        - web2

    api2:
      rule: "PathPrefix(`/api/`)"
      service: chat-tool-service
      priority: 300
      entryPoints:
        - web2

    default:
      rule: "PathPrefix(`/`)"
      service: tpt-app-service
      priority: 50
      entryPoints:
        - web

  middlewares:
    root-redirect:
      redirectRegex:
        regex: "^http://([^/]+)/$"
        replacement: "http://${1}/tpt-app/"
        permanent: true

    tpt-app-redirect:
      redirectRegex:
        regex: "^http://([^/]+)/tpt-app$"
        replacement: "http://${1}/tpt-app/"
        permanent: true

    strip-prefix-chat-tool:
      stripPrefix:
        prefixes:
          - "/tpt-app/chat-tool-work"
          - "/tpt-app/chat-tool-agent"
          - "/tpt-app/chat-tool-socket-work"
          - "/xpt-tpt-pid/chat-tool-work"

  services:
    chat-tool-service:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: "http://localhost:15336"

    tpt-app-service:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: "https://obp-dev.supcon5t.com"

    tpt-app-service2:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: "https://obp-dev.supcon5t.com"
