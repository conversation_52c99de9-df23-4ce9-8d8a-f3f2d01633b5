###>ref and orig from https://gitlab.supcon5t.com/5tpd/private/cicd/glab-ci-template/-/blob/master/indu/s1-rust-cargo-buld-dockbuldpush-demo.yml
---
stages:
  - ".pre"
  - s1-*********************bin
  - ********************buldpush
  - ********************buldpush-tag
  - s3-Agent-Run-k8s-deploy
  - s3-Agent-Run-k8s-deploy-tag
  - ".post"

###>global var define here
variables:
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_FORCE_HTTPS: "true"
  GIT_SUBMODULE_DEPTH: 1
  TARGET_PATH: "${CI_PROJECT_DIR}"
  IMGNAME: "harbor.supcon5t.com/indu/agent-runner-tools-server:dev-x64-${CI_COMMIT_SHORT_SHA}"
  TAGIMGNAME: "harbor.supcon5t.com/indu/agent-runner-tools-server:x64-${CI_COMMIT_TAG}"
  PYPI_MIRROR: "https://proxynexus.supcon5t.com/repository/pypi/simple"

###>follow action set only affect on gitlab CI branch + Tag do dual env
##
s1-*********************bin-job:
  stage: s1-*********************bin
  rules:
    - if: "$CI_COMMIT_BRANCH"
    - if: "$CI_COMMIT_TAG"
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
  image:
    name: harbor.supcon5t.com/indu/rust-buld:1.88-py313-pandoc-an8-x64
  tags:
    - docker-lb
  script:
    - echo "set-build-img-env-use-indu-ofl-env"
    - export CARGO_HOME=/usr/local/bin && curl https://mirror.supcon5t.com/crates.io-index/cargo-client-indu-ofl-set.bash |bash && env |grep -i cargo
    - echo "s1-*********************bin"
    #  - cargo clean && cargo check
    - cargo clean && cargo check && cargo build --release --target x86_64-unknown-linux-gnu
    - pip3.13 install -i $PYPI_MIRROR -r ${TARGET_PATH}/.runtime/tools/requirements.txt
    - mkdir -p ${TARGET_PATH}/.dep/usr/local/bin
    - mkdir -p ${TARGET_PATH}/.dep/usr/local/lib
    - cp -r /usr/local/bin/pandoc* ${TARGET_PATH}/.dep/usr/local/bin/ || true
    - cp -r /usr/local/lib/python* ${TARGET_PATH}/.dep/usr/local/lib/ || true
    - cp -r /usr/local/lib/libpython* ${TARGET_PATH}/.dep/usr/local/lib/ || true
    - cp -r /usr/local/bin/py* ${TARGET_PATH}/.dep/usr/local/bin/ || true
    - cp -r /usr/local/bin/pip* ${TARGET_PATH}/.dep/usr/local/bin/ || true
    - cp -r /usr/local/bin/idle* ${TARGET_PATH}/.dep/usr/local/bin/ || true
  artifacts:
    paths:
      - "${TARGET_PATH}/.dep"
      - "${TARGET_PATH}/.runtime"
      - "${TARGET_PATH}/target/x86_64-unknown-linux-gnu/release/tools-server"
    expire_in: 3 days

###>follow action set only affect on gitlab CI branch with Static IMG Tag
********************buldpush-job:
  stage: ********************buldpush
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
  tags:
    - shell-lb
  dependencies:
    - s1-*********************bin-job
  script:
    - echo "Agent-Run-docker-buldpush"
    - pwd && ls -al ${TARGET_PATH}/ && ls -al ./cicd/ && echo ${IMGNAME}
    - docker login -u induadmin -p Harbor12345 harbor.supcon5t.com
    - docker buildx build ./ -f ./cicd/Dockerfile.x64 --load -t ${IMGNAME} --platform=linux/amd64 --compress --no-cache=true --progress plain
    - docker push ${IMGNAME}

###>follow action set only affect on gitlab TAG do for SelfDefine IMG Tag
********************buldpush-tag-job:
  stage: ********************buldpush-tag
  rules:
    - if: "$CI_COMMIT_TAG"
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
  tags:
    - shell-lb
  dependencies:
    - s1-*********************bin-job
  script:
    - echo "Agent-Run-docker-buldpush-with-tag"
    - pwd && ls -al ${TARGET_PATH}/ && ls -al ./cicd/ && echo ${TAGIMGNAME}
    - docker login -u induadmin -p Harbor12345 harbor.supcon5t.com
    - docker buildx build ./ -f ./cicd/Dockerfile.x64 --load -t ${TAGIMGNAME} --platform=linux/amd64 --compress --no-cache=true --progress plain
    - docker push ${TAGIMGNAME}

##>follow action set only affect on gitlab CI Branch for most Dev Env
s3-Agent-Run-k8s-deploy-job:
  stage: s3-Agent-Run-k8s-deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
  tags:
    - kubectl-lb

  dependencies:
    - ********************buldpush-job

  #subStage var define here
  variables:
    MY_NS: indu-dev
    NACOS_ADDRESS: "nacos-cs.devops-dev:8848"

  script:
    - echo "Agent-Run-k8s-deployTo-DEV-env"
    - sed -i "s/NACOS_ADDRESS/${NACOS_ADDRESS}/g" ./cicd/k8s-deploy.yml
    - sed -i "s/K8S_NAMESPACE/${MY_NS}/g" ./cicd/k8s-deploy.yml
    - sed -i "s/MY_NS/${MY_NS}/g" ./cicd/k8s-deploy.yml
    - sed -i "s|IMGNAME|${IMGNAME}|g" ./cicd/k8s-deploy.yml
    - sed -i "s|CI_PIPELINE_ID|${CI_PIPELINE_ID}|g" ./cicd/k8s-deploy.yml
    - cat ./cicd/k8s-deploy.yml
    - export KUBECONFIG=~/.kube/seak8sv2025.conf && kubectl apply -f ./cicd/k8s-deploy.yml

#>follow action set only affect on gitlab CI Branch for most Test-Prod env
s3-Agent-Run-k8s-deploy-tag-job:
  stage: s3-Agent-Run-k8s-deploy-tag
  rules:
    - if: "$CI_COMMIT_TAG"
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
  tags:
    - kubectl-lb

  dependencies:
    - ********************buldpush-tag-job

  #subStage var define here
  variables:
    MY_NS: indu
    NACOS_ADDRESS: "nacos-cs.devops:8848"

  script:
    - echo "Agent-Run-k8s-deployTo-PROD-env"
    - sed -i "s/NACOS_ADDRESS/${NACOS_ADDRESS}/g" ./cicd/k8s-deploy.yml
    - sed -i "s/K8S_NAMESPACE/${MY_NS}/g" ./cicd/k8s-deploy.yml
    - sed -i "s/MY_NS/${MY_NS}/g" ./cicd/k8s-deploy.yml
    - sed -i "s|IMGNAME|${TAGIMGNAME}|g" ./cicd/k8s-deploy.yml
    - sed -i "s|CI_PIPELINE_ID|${CI_PIPELINE_ID}|g" ./cicd/k8s-deploy.yml
    - cat ./cicd/k8s-deploy.yml
    - export KUBECONFIG=~/.kube/seak8sv2025.conf && kubectl apply -f ./cicd/k8s-deploy.yml
