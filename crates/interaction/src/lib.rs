use std::fmt::Debug;

use serde::{Deserialize, Serialize};

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct Interaction {
    pub id: String,
    pub title: String,
    /// 交互内容
    #[serde(flatten)]
    pub content: InteractionContent,
}

impl std::fmt::Display for Interaction {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.title)
    }
}

impl std::error::Error for Interaction {}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SelectItem {
    pub title: String,
    pub data: Option<serde_json::Value>,
    ///选项描述
    pub description: Option<String>,
}

/// 交互内容
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(tag = "type")]
pub enum InteractionContent {
    /// 选择类型交互
    #[serde(rename = "select")]
    Select {
        mode: Option<String>, // single, multiple
        /// 选择项
        select: Vec<SelectItem>,
        /// 交互结果
        #[serde(skip_serializing_if = "Option::is_none")]
        result: Option<Vec<SelectItem>>,
    },
    /// 确认类型交互
    #[serde(rename = "confirm")]
    Confirm {
        /// 确认内容
        confirm: String,
        /// 交互结果
        #[serde(skip_serializing_if = "Option::is_none")]
        result: Option<bool>,
    },
    /// 运行前确认
    #[serde(rename = "execute_confirm")]
    ExecuteConfirm {
        /// 确认内容
        execute_confirm: ExecuteConfirmContent,
        /// 交互结果
        #[serde(skip_serializing_if = "Option::is_none")]
        result: Option<serde_json::Value>,
    },
    /// 打开页面类型交互
    #[serde(rename = "open_page")]
    OpenPage {
        /// 打开页面URL
        open_page: String,
        /// 打开页面数据
        #[serde(skip_serializing_if = "Option::is_none")]
        page_data: Option<serde_json::Value>,
        /// 交互结果
        #[serde(skip_serializing_if = "Option::is_none")]
        result: Option<serde_json::Value>,
        ///当前页面类型
        page_type: Option<String>,
    },
    /// 创建动态表单
    #[serde(rename = "form")]
    Form {
        form: Form,
        /// 表单结果
        #[serde(skip_serializing_if = "Option::is_none")]
        result: Option<serde_json::Value>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ExecuteConfirmContent {
    pub workflow_id: uuid::Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Default)]
pub struct Form {
    /// 表单标题
    pub title: Option<String>,
    /// 表单类型
    pub form_type: Option<String>,
    /// 表单格式
    pub schema: serde_json::Value,
    /// 表单默认值
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default: Option<serde_json::Value>,
    /// UI
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ui: Option<serde_json::Value>,
}

impl InteractionContent {
    // get result
    pub fn get_result(&self) -> Option<serde_json::Value> {
        match self {
            InteractionContent::Select { result, .. } => {
                result.as_ref().and_then(|v| serde_json::to_value(v).ok())
            }
            InteractionContent::Confirm { result, .. } => {
                result.as_ref().and_then(|v| serde_json::to_value(v).ok())
            }
            InteractionContent::ExecuteConfirm { result, .. } => {
                result.as_ref().and_then(|v| serde_json::to_value(v).ok())
            }
            InteractionContent::OpenPage { result, .. } => {
                result.as_ref().and_then(|v| serde_json::to_value(v).ok())
            }
            InteractionContent::Form { result, .. } => {
                result.as_ref().and_then(|v| serde_json::to_value(v).ok())
            }
        }
    }

    // set result
    pub fn set_result(&mut self, new_result: serde_json::Value) -> error::Result<()> {
        match self {
            InteractionContent::Select { result, .. } => {
                *result = serde_json::from_value(new_result)?
            }
            InteractionContent::Confirm { result, .. } => {
                *result = serde_json::from_value(new_result)?
            }
            InteractionContent::ExecuteConfirm { result, .. } => {
                *result = serde_json::from_value(new_result)?
            }
            InteractionContent::OpenPage { result, .. } => {
                *result = serde_json::from_value(new_result)?
            }
            InteractionContent::Form { result, .. } => {
                *result = serde_json::from_value(new_result)?
            }
        }

        Ok(())
    }
}

#[derive(Serialize, Deserialize, Clone, Hash)]
#[serde(tag = "type")]
pub enum InteractionResult {
    #[serde(rename = "select")]
    Select { select: Vec<usize> },
    #[serde(rename = "confirm")]
    Confirm { confirm: bool },
    #[serde(rename = "execute_confirm")]
    ExecuteConfirm { execute_confirm: serde_json::Value },
    #[serde(rename = "open_page")]
    OpenPage { open_page: serde_json::Value },
    #[serde(rename = "form")]
    Form { form: serde_json::Value },
}

impl Debug for InteractionResult {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            InteractionResult::Select { .. } => write!(f, "Select"),
            InteractionResult::Confirm { .. } => write!(f, "Confirm"),
            InteractionResult::ExecuteConfirm { .. } => write!(f, "ExecuteConfirm"),
            InteractionResult::OpenPage { .. } => write!(f, "OpenPage"),
            InteractionResult::Form { .. } => write!(f, "Form"),
        }
    }
}

impl InteractionResult {
    // 设置结果
    pub fn apply(self, content: &mut InteractionContent) {
        match (content, self) {
            (
                InteractionContent::Select { result, select, .. },
                InteractionResult::Select { select: indexes },
            ) => {
                *result = Some(
                    indexes
                        .into_iter()
                        .flat_map(|i| select.get(i).cloned())
                        .collect(),
                )
            }
            (
                InteractionContent::Confirm { result, .. },
                InteractionResult::Confirm { confirm },
            ) => *result = Some(confirm),
            (
                InteractionContent::ExecuteConfirm { result, .. },
                InteractionResult::ExecuteConfirm { execute_confirm },
            ) => *result = Some(execute_confirm),
            (
                InteractionContent::OpenPage {
                    result, page_type, ..
                },
                InteractionResult::OpenPage { open_page },
            ) => {
                *result = Some(open_page);
                if page_type.is_none() {
                    *page_type = Some("view".to_string())
                }
            }
            (InteractionContent::Form { result, .. }, InteractionResult::Form { form }) => {
                *result = Some(form)
            }
            _ => {}
        }
    }
}
