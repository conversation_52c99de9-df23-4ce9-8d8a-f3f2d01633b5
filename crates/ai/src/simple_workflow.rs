use std::sync::Arc;

use serde::{Deserialize, Serialize};
use workflow::{<PERSON>, EdgeConfig, Node, Workflow};

#[derive(Deserialize, Serialize)]
pub struct Step {
    pub id: String,
    pub tool: String,
    pub ability: String,
    pub dep: Option<Vec<String>>,
}

#[derive(Deserialize, Serialize)]
pub struct Branch {
    pub from: String,
    pub to: String,
    pub expression: Option<String>,
}

#[derive(Deserialize)]
pub struct SimpleWorkflow {
    pub steps: Vec<Step>,
    pub branches: Option<Vec<Branch>>,
}

impl SimpleWorkflow {
    pub fn parse(id: uuid::Uuid, mut simple_workflow: SimpleWorkflow) -> error::Result<Workflow> {
        // start_id, is_start_step
        let origin_start_id = Some(
            simple_workflow
                .steps
                .iter()
                .find_map(|step| {
                    if step.tool == "general_tool" && step.ability == "start" {
                        Some((step.id.clone(), true))
                    } else if step.dep.as_ref().is_none_or(|deps| deps.is_empty()) {
                        Some((step.id.clone(), false))
                    } else {
                        None
                    }
                })
                .ok_or_else(|| {
                    error::Error::Workflow(Box::new(error::WorkflowError::NoStartNode))
                })?,
        );

        let mut edges = {
            let mut edges = simple_workflow
                .steps
                .iter()
                .filter_map(|step| {
                    step.dep.as_ref().map(|deps| {
                        deps.iter().map(|dep| Edge {
                            source_node: dep.clone(),
                            target_node: step.id.clone(),
                            ..Default::default()
                        })
                    })
                })
                .flatten()
                .collect::<Vec<_>>();

            if let Some(branches) = simple_workflow.branches.take() {
                for branch in branches {
                    if let Some(edge) = edges
                        .iter_mut()
                        .find(|e| e.source_node == branch.from && e.target_node == branch.to)
                    {
                        edge.config = Some(EdgeConfig {
                            expression: branch
                                .expression
                                .as_ref()
                                .map(|s| expression::parse_expr(s))
                                .transpose()?,
                        });
                    } else {
                        // 最后将 branches 中的 edge 添加到 edges 中
                        edges.push(Edge {
                            source_node: branch.from.clone(),
                            target_node: branch.to.clone(),
                            config: Some(EdgeConfig {
                                expression: branch
                                    .expression
                                    .as_ref()
                                    .map(|s| expression::parse_expr(s))
                                    .transpose()?,
                            }),
                            ..Default::default()
                        });
                    }
                }
            }

            edges.into_iter().map(Arc::new).collect::<Vec<_>>()
        };

        let mut nodes = simple_workflow
            .steps
            .into_iter()
            .map(|step| {
                Arc::new(Node {
                    id: step.id,
                    module: if step.tool == "general_tool" {
                        "builtin".to_string()
                    } else {
                        step.tool
                    },
                    tool: step.ability,
                    ..Default::default()
                })
            })
            .collect::<Vec<_>>();

        // 如果 start node 后续只有一个 edge，则将 start node 的 id 设置为 edge 的 target_node
        let start_id = if let Some((start_id, is_start_step)) = origin_start_id {
            if is_start_step {
                let next_edges = edges
                    .iter()
                    .filter(|e| e.source_node == start_id)
                    .collect::<Vec<_>>();
                if next_edges.len() == 1 {
                    let new_start_id = next_edges[0].target_node.clone();
                    // 同时移除这个 start node 和 edge
                    edges.retain(|e| e.source_node != start_id);
                    nodes.retain(|n| n.id != start_id);
                    Some(new_start_id)
                } else {
                    Some(start_id)
                }
            } else {
                Some(start_id)
            }
        } else {
            None
        };

        // 移除所有 builtin 的 end node，以及连接到他们自身的 edge
        let end_nodes = nodes
            .iter()
            .filter(|n| n.module == "builtin" && n.tool == "end")
            .map(|n| n.id.clone())
            .collect::<Vec<_>>();
        nodes.retain(|n| !end_nodes.contains(&n.id));
        edges.retain(|e| !end_nodes.contains(&e.target_node));

        Ok(Workflow {
            id,
            start_id,
            edges,
            nodes,
            ..Default::default()
        })
    }

    pub fn parse_string(id: uuid::Uuid, json: String, branch: String) -> error::Result<Workflow> {
        let json = serde_json::from_str(&json)?;
        let branch = serde_json::from_str(&branch)?;

        Self::parse(
            id,
            SimpleWorkflow {
                steps: json,
                branches: branch,
            },
        )
    }

    pub fn build_string(flow: &Workflow) -> error::Result<(String, String)> {
        // 反向生成 json / branch 字符串
        // 先转换为 LLMWorkflow 的 step 和 Branch
        let mut json = Vec::new();
        let mut branch = Vec::new();

        for node in &flow.nodes {
            let step = Step {
                id: node.id.clone(),
                tool: node.module.clone(),
                ability: node.tool.clone(),
                dep: {
                    let mut deps = Vec::new();
                    for edge in &flow.edges {
                        if edge.target_node == node.id {
                            deps.push(edge.source_node.clone());
                        }
                    }
                    if deps.is_empty() { None } else { Some(deps) }
                },
            };

            json.push(step);

            // 如果这个步没有后续的 edge，就添加一个 end 节点
            if flow.edges.iter().all(|e| e.source_node != node.id) {
                json.push(Step {
                    id: "end".to_string(),
                    tool: "general_tool".to_string(),
                    ability: "end".to_string(),
                    dep: Some(vec![node.id.clone()]),
                });
            }
        }

        for edge in &flow.edges {
            if edge.config.is_some() {
                branch.push(Branch {
                    from: edge.source_node.clone(),
                    to: edge.target_node.clone(),
                    expression: edge
                        .config
                        .as_ref()
                        .and_then(|c| c.expression.as_ref().map(|e| e.to_string())),
                });
            }
        }

        Ok((
            serde_json::to_string(&json)?,
            serde_json::to_string(&branch)?,
        ))
    }
}
