use std::{
    pin::Pin,
    sync::Arc,
    task::{Context, Poll},
};

use bytes::Bytes;
use futures::{Sink, SinkExt as _, Stream, StreamExt as _};
use interaction::InteractionContent;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Debug, Clone)]
#[serde(tag = "type")]
pub enum ExploreRequest {
    #[serde(rename = "research_topic")]
    ResearchTopic { content: String },
    #[serde(rename = "workflow_result")]
    WorkflowResult { content: ExploreWorkflowResult },
    #[serde(rename = "interaction_result")]
    InteractionResult { content: serde_json::Value },
    #[serde(rename = "ping")]
    Ping,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ExploreWorkflowResult {
    pub final_summary: String,
    pub steps: Vec<ExploreWorkflowStepResult>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ExploreWorkflowStepResult {
    pub id: String,
    pub name: String,
    pub module: String,
    pub params: Option<Arc<serde_json::Value>>,
    pub result: Option<Arc<serde_json::Value>>,
    pub views: Vec<Arc<serde_json::Value>>,
    pub summary: Option<Arc<String>>,
    pub error: Option<String>,
}

#[derive(Deserialize, Debug, Clone)]
#[serde(tag = "type")]
pub enum ExploreEvent {
    #[serde(rename = "stage")]
    Stage { content: ContentStage },
    #[serde(rename = "task")]
    Task { content: ContentTask },
    #[serde(rename = "task_append")]
    TaskAppend { content: ContentTaskDelta },
    #[serde(rename = "text")]
    Text { text: String },
    #[serde(rename = "workflow")]
    Workflow { content: WorkflowContent },
    #[serde(rename = "interaction")]
    Interaction { content: Box<InteractionContent> },
    #[serde(rename = "todolist")]
    Todolist,
    #[serde(rename = "done")]
    Done,
    #[serde(untagged)]
    Other(serde_json::Value),
}

#[derive(Deserialize, Debug, Clone)]
pub struct WorkflowContent {
    pub json: String,
    pub branch: String,
    pub think: String,
    pub name: String,
    pub query: Option<String>,
}

pub type ContentTask = serde_json::Value;

pub type ContentTaskDelta = serde_json::Value;

#[derive(Deserialize, Debug, Clone)]
pub struct ContentStage {
    pub title: String,
    pub description: Option<String>,
}

pub trait ExploreStreamSink:
    Stream<Item = error::Result<ExploreEvent>> + Sink<ExploreRequest> + std::fmt::Debug
{
}

pub type ExploreStream = Pin<Box<dyn ExploreStreamSink<Error = error::Error> + Send>>;

pub struct ExploreController(pub reqwest_websocket::WebSocket);

impl Stream for ExploreController {
    type Item = error::Result<ExploreEvent>;

    fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        loop {
            return match self.0.poll_next_unpin(cx) {
                Poll::Ready(Some(Ok(event))) => {
                    // 只处理 Text 其他都抛弃
                    match event {
                        reqwest_websocket::Message::Text(text) => {
                            let event = match serde_json::from_str(&text) {
                                Ok(event) => event,
                                Err(err) => return Poll::Ready(Some(Err(err.into()))),
                            };

                            Poll::Ready(Some(Ok(event)))
                        }
                        reqwest_websocket::Message::Ping(_) => {
                            tracing::debug!("ping");
                            continue;
                        }
                        reqwest_websocket::Message::Pong(_) => {
                            tracing::debug!("pong");
                            continue;
                        }
                        reqwest_websocket::Message::Close { code, reason } => {
                            tracing::debug!("websocket close: {:?}, {}", code, reason);
                            return Poll::Ready(None);
                        }
                        reqwest_websocket::Message::Binary(_) => {
                            tracing::debug!("binary message, ignore");
                            continue;
                        }
                    }
                }
                Poll::Ready(Some(Err(e))) => Poll::Ready(Some(Err(e.into()))),
                Poll::Ready(None) => Poll::Ready(None),
                Poll::Pending => Poll::Pending,
            };
        }
    }
}

impl Sink<ExploreRequest> for ExploreController {
    type Error = error::Error;

    fn poll_ready(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        match self.0.poll_ready_unpin(cx) {
            Poll::Ready(Ok(())) => Poll::Ready(Ok(())),
            Poll::Ready(Err(e)) => Poll::Ready(Err(e.into())),
            Poll::Pending => Poll::Pending,
        }
    }

    fn start_send(mut self: Pin<&mut Self>, item: ExploreRequest) -> Result<(), Self::Error> {
        let message = match item {
            ExploreRequest::Ping => reqwest_websocket::Message::Ping(Bytes::new()),
            _ => reqwest_websocket::Message::Text(serde_json::to_string(&item)?),
        };

        match self.0.start_send_unpin(message) {
            Ok(()) => Ok(()),
            Err(e) => Err(e.into()),
        }
    }

    fn poll_flush(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        match self.0.poll_flush_unpin(cx) {
            Poll::Ready(Ok(())) => Poll::Ready(Ok(())),
            Poll::Ready(Err(e)) => Poll::Ready(Err(e.into())),
            Poll::Pending => Poll::Pending,
        }
    }

    fn poll_close(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        match self.0.poll_close_unpin(cx) {
            Poll::Ready(Ok(())) => Poll::Ready(Ok(())),
            Poll::Ready(Err(e)) => Poll::Ready(Err(e.into())),
            Poll::Pending => Poll::Pending,
        }
    }
}

impl std::fmt::Debug for ExploreController {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Controller")
    }
}

impl ExploreStreamSink for ExploreController {}
