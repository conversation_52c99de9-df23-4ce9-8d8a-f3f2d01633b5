use std::error::Error as _;

pub type Result<T> = core::result::Result<T, Error>;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error(transparent)]
    Mongo(Box<MongoError>),
    #[error(transparent)]
    Transaction(anyhow::Error),
    #[error(transparent)]
    Io(#[from] std::io::Error),
    #[error(transparent)]
    HttpClient(#[from] reqwest::Error),
    #[error("websocket error: {:?}", .0.source())]
    WebsocketClient(#[from] Box<reqwest_websocket::Error>),
    #[error(transparent)]
    EventSourceClient(#[from] Box<reqwest_eventsource::Error>),
    #[error(transparent)]
    SerdeJson(#[from] serde_json::Error),
    #[error(transparent)]
    Redis(#[from] redis::RedisError),
    #[error(transparent)]
    Minio(#[from] Box<minio::s3::error::Error>),
    #[error(transparent)]
    Regex(#[from] regex::Error),
    #[error(transparent)]
    Python(#[from] pyo3::PyErr),
    #[error(transparent)]
    CString(#[from] std::ffi::NulError),
    #[error(transparent)]
    RuntimeJoin(#[from] tokio::task::JoinError),
    #[error(transparent)]
    Mcp(#[from] Box<rmcp::RmcpError>),
    #[error(transparent)]
    JsonSchemaValidation(Box<jsonschema::ValidationError<'static>>),
    #[error(transparent)]
    OpenAI(#[from] async_openai::error::OpenAIError),
    #[error(transparent)]
    ConfigLoader(#[from] config::ConfigError),
    #[error(transparent)]
    Nacos(Box<nacos_sdk::api::error::Error>),
    #[error(transparent)]
    BroadcastStreamRecv(Box<tokio_stream::wrappers::errors::BroadcastStreamRecvError>),

    /* anyhow */
    // #[error(transparent)]
    // Other(#[from] error::Error),

    /* public */
    #[error(transparent)]
    Conversation(#[from] ConversationError),
    #[error(transparent)]
    Message(#[from] MessageError),
    #[error(transparent)]
    Expression(#[from] Box<ExpressionError>),
    #[error(transparent)]
    Integral(#[from] IntegralError),
    #[error(transparent)]
    Script(#[from] ScriptError),
    #[error(transparent)]
    Task(#[from] TaskError),
    #[error("interaction required: {0}")]
    Interaction(serde_json::Value),
    #[error(transparent)]
    Tool(#[from] ToolError),
    #[error(transparent)]
    Workflow(#[from] Box<WorkflowError>),
    #[error(transparent)]
    Config(#[from] ConfigError),
    #[error(transparent)]
    Agent(#[from] AgentError),
}

#[derive(thiserror::Error, Debug)]
pub enum ConversationError {
    #[error("failed to find conversation: {0}")]
    NotFound(uuid::Uuid),
    #[error("failed to load conversation: {0}")]
    LoadConversation(#[source] Box<Error>),
}

#[derive(thiserror::Error, Debug)]
pub enum MessageError {
    #[error("failed to find message: {0}")]
    NotFound(uuid::Uuid),
    #[error("current message not found")]
    CurrentNotFound,
    #[error("metadata must be an object")]
    InvalidMetadata,
    #[error("last content not found")]
    LastContentNotFound,
    #[error("last user input not found")]
    LastUserInputNotFound,
}

#[derive(thiserror::Error, Debug)]
pub enum ExpressionError {
    #[error("failed to parse expression: {0}")]
    Parse(String),
    #[error("use unknown operator: {0}")]
    UnknownOperator(String),
    #[error("expect number but got: {0}")]
    ExpectNumber(String),
    #[error("expect literal but got: {0}")]
    ExpectLiteral(String),
    #[error("cannot compare {0} with {1}")]
    MismatchedCompareFormat(serde_json::Value, serde_json::Value),
}

#[derive(thiserror::Error, Debug)]
pub enum TaskError {
    #[error("task not found: {0}")]
    NotFound(uuid::Uuid),
}

#[derive(thiserror::Error, Debug)]
pub enum ScriptError {
    #[error("python error: {0}")]
    Python(#[from] pyo3::PyErr),
    #[error("task join error: {0}")]
    TaskJoin(#[from] tokio::task::JoinError),
    #[error("nul error: {0}")]
    Nul(#[from] std::ffi::NulError),
    #[error("pythonize error: {0}")]
    Pythonize(#[from] pythonize::PythonizeError),
    #[error("invalid bucket: {0}")]
    InvalidBucket(String),
    #[error("invalid scope: {0}")]
    InvalidScope(String),
}

#[derive(thiserror::Error, Debug)]
pub enum IntegralError {
    #[error("failed to deduct: {0}")]
    Deduct(#[source] Box<Error>),
    #[error("user not found")]
    UserNotFound,
    #[error("event not found")]
    EventNotFound,
    #[error("repeated gift")]
    RepeatedGift,
    #[error("not enough points")]
    NotEnoughPoints,
    #[error("integral mode not enabled")]
    NotEnabled,
    #[error("server error")]
    ServerError,
}

#[derive(thiserror::Error, Debug)]
pub enum ToolError {
    #[error("unknown tool: {0}")]
    UnknownTool(String),
}

#[derive(thiserror::Error, Debug)]
pub enum ConfigError {
    #[error("unknown format: {0}")]
    UnknownFormat(String),
    #[error("config lock timeout")]
    Timeout,
    #[error("failed to load script module: {0}")]
    LoadScriptModule(#[source] Box<Error>),
}

fn value_path(path: &[serde_json::Value]) -> String {
    path.iter()
        .map(|v| v.to_string())
        .collect::<Vec<_>>()
        .join(".")
}

#[derive(thiserror::Error, Debug)]
pub enum WorkflowError {
    #[error("node not found: {0}")]
    NodeNotFound(String),
    #[error("input is not an object: {0}")]
    InputNotObject(serde_json::Value),
    #[error("index is not a unsigned integer: {} in {}", .0, value_path(.1))]
    IndexNotUnsignedInteger(serde_json::Value, Vec<serde_json::Value>),
    #[error("key is not a string or number: {} in {}", .0, value_path(.1))]
    KeyNotStringOrNumber(serde_json::Value, Vec<serde_json::Value>),
    #[error("key path {} not found", value_path(.0))]
    KeyPathNotFound(Vec<serde_json::Value>),
    #[error("conflict key path: {} in {}, parent type: {}", .0, value_path(.1), .2)]
    ConflictKeyPath(serde_json::Value, Vec<serde_json::Value>, String),
    #[error("no start node found")]
    NoStartNode,
}

#[derive(thiserror::Error, Debug)]
pub enum AgentError {
    #[error("name can not be empty")]
    NameCanNotBeEmpty,
    #[error("id can not be empty")]
    IdCanNotBeEmpty,
    #[error("background agent error: {0}")]
    BackgroundAgentError(String),
}

#[derive(thiserror::Error, Debug)]
pub enum MongoError {
    #[error("filter can not be empty")]
    FilterCanNotBeEmpty,
    #[error(transparent)]
    SeverError(mongodb::error::Error),
    #[error("record not found :{0}")]
    RecordNotFound(uuid::Uuid),
    #[error("record not found")]
    RecordNotFound_,
    #[error("bson deserialize error: {0}")]
    BsonDeError(bson::de::Error),
    #[error("bson serialize error: {0}")]
    BsonSerError(bson::ser::Error),
}

impl From<Error> for pyo3::PyErr {
    fn from(value: Error) -> Self {
        match value {
            Error::Python(e) => e,
            _ => pyo3::PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(value.to_string()),
        }
    }
}

impl<'a> From<jsonschema::ValidationError<'a>> for Error {
    fn from(value: jsonschema::ValidationError<'a>) -> Self {
        Self::JsonSchemaValidation(Box::new(value.to_owned()))
    }
}

macro_rules! box_error {
    ($name:ident, $e:ty) => {
        impl From<$e> for Error {
            fn from(value: $e) -> Self {
                Self::$name(Box::new(value))
            }
        }
    };
}

macro_rules! into_error {
    ($name:ident, $e:ty) => {
        impl From<$e> for Error {
            fn from(value: $e) -> Self {
                Self::$name(value.into())
            }
        }
    };
}

macro_rules! into_box_error {
    ($name:ident, $e:ty) => {
        impl From<$e> for Error {
            fn from(value: $e) -> Self {
                Self::$name(Box::new(value.into()))
            }
        }
    };
}

box_error!(Minio, minio::s3::error::Error);
box_error!(Mcp, rmcp::RmcpError);
box_error!(WebsocketClient, reqwest_websocket::Error);
box_error!(EventSourceClient, reqwest_eventsource::Error);
box_error!(Nacos, nacos_sdk::api::error::Error);
box_error!(
    BroadcastStreamRecv,
    tokio_stream::wrappers::errors::BroadcastStreamRecvError
);
box_error!(Mongo, MongoError);
box_error!(Expression, ExpressionError);
box_error!(Workflow, WorkflowError);

into_error!(Python, pythonize::PythonizeError);

into_box_error!(Mcp, rmcp::service::ServiceError);
into_box_error!(Mcp, rmcp::service::ClientInitializeError);
into_box_error!(Mcp, rmcp::service::ServerInitializeError);
