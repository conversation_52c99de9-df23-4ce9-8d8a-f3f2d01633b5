pub mod builtin;
pub mod global;
pub mod mcp;
pub mod repository;
pub mod script;

use std::sync::Arc;

use builtin::{BUILTIN_MODULE_NAME, BuiltinTool};
use serde::{Deserialize, Serialize};

pub use mcp::McpTool;
pub use script::{ScriptLanguage, ScriptTool};

use crate::mcp::McpClientBuilder;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Module {
    pub name: String,
    pub description: String,
    pub alias: Vec<String>,
    #[serde(flatten)]
    pub source: ModuleSource,
    pub config: Arc<serde_json::Value>,
    pub tools: Vec<Arc<Tool>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "source")]
pub enum ModuleSource {
    #[serde(rename = "builtin")]
    Builtin,
    #[serde(rename = "mcp")]
    Mcp(McpClientBuilder),
    #[serde(rename = "script")]
    Script(ScriptModuleSource),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScriptModuleSource {
    pub base: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum Tool {
    #[serde(rename = "script")]
    Script(Arc<ScriptTool>),
    #[serde(rename = "mcp")]
    Mcp(Arc<McpTool>),
    #[serde(rename = "builtin")]
    Builtin(Arc<BuiltinTool>),
}

impl Default for Tool {
    fn default() -> Self {
        Tool::Builtin(Arc::new(BuiltinTool::default()))
    }
}

impl Tool {
    pub fn name(&self) -> &str {
        match self {
            Tool::Script(tool) => &tool.name,
            Tool::Mcp(tool) => &tool.name,
            Tool::Builtin(tool) => &tool.name,
        }
    }

    pub fn module_name(&self) -> &str {
        match self {
            Tool::Script(tool) => &tool.module_name,
            Tool::Mcp(tool) => &tool.module_name,
            Tool::Builtin(_) => BUILTIN_MODULE_NAME,
        }
    }

    pub fn module_config(&self) -> Arc<serde_json::Value> {
        match self {
            Tool::Script(tool) => tool.module_config.clone(),
            Tool::Mcp(tool) => tool.module_config.clone(),
            Tool::Builtin(_) => Arc::new(serde_json::Value::Null),
        }
    }

    pub fn description(&self) -> &str {
        match self {
            Tool::Script(tool) => &tool.description,
            Tool::Mcp(tool) => &tool.description,
            Tool::Builtin(tool) => &tool.description,
        }
    }

    pub fn verbose_summary(&self) -> bool {
        match self {
            Tool::Script(tool) => tool.features.verbose_summary.unwrap_or_default(),
            Tool::Mcp(_) => false,
            Tool::Builtin(_) => false,
        }
    }

    pub fn ignore_converter_cache(&self) -> bool {
        match self {
            Tool::Script(tool) => tool.features.ignore_converter_cache.unwrap_or_default(),
            Tool::Mcp(_) => false,
            Tool::Builtin(_) => false,
        }
    }

    pub fn skip_summary(&self) -> bool {
        match self {
            Tool::Script(tool) => tool.features.skip_summary.unwrap_or_default(),
            Tool::Mcp(_) => false,
            Tool::Builtin(tool) => tool.features.skip_summary,
        }
    }

    pub fn params_schema(&self) -> Arc<serde_json::Value> {
        match self {
            Tool::Script(tool) => tool.params_schema.clone(),
            Tool::Mcp(tool) => tool.params_schema.clone(),
            Tool::Builtin(tool) => tool.params.clone(),
        }
    }

    pub fn result_schema(&self) -> Arc<serde_json::Value> {
        match self {
            Tool::Script(tool) => tool.result_schema.clone(),
            Tool::Mcp(tool) => tool.result_schema.clone(),
            Tool::Builtin(tool) => tool.result.clone(),
        }
    }
}
