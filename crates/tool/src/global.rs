use std::sync::Arc;

use tokio::sync::RwLock;

use crate::{
    Module, Tool,
    builtin::{BUILTIN_MODULE_NAME, BuiltinToolRepository},
    mcp::McpToolRepository,
    repository::*,
    script::ScriptToolRepository,
};

pub struct GlobalToolRepository {
    pub builtin: BuiltinToolRepository,
    pub script: RwLock<Arc<ScriptToolRepository>>,
    pub mcp: Vec<Arc<McpToolRepository>>,
}

impl GlobalToolRepository {
    pub async fn get_script(&self) -> Arc<ScriptToolRepository> {
        self.script.read().await.clone()
    }

    pub async fn set_script(&self, script: Arc<ScriptToolRepository>) {
        *self.script.write().await = script;
    }

    pub async fn list_repositories(self: Arc<Self>) -> Vec<Arc<dyn ToolRepository + Send + Sync>> {
        let mut repositories: Vec<Arc<dyn ToolRepository + Send + Sync>> =
            vec![self.clone(), self.get_script().await];

        for mcp in self.mcp.iter() {
            repositories.push(mcp.clone());
        }

        repositories
    }

    pub async fn list_module_tools(&self, module_name: &str) -> error::Result<Vec<Arc<Tool>>> {
        for mcp in self.mcp.iter() {
            if mcp.module_name == module_name {
                return mcp.list_tools().await;
            }
        }

        self.get_script().await.list_tools().await
    }
}

#[async_trait::async_trait]
impl ToolRepository for GlobalToolRepository {
    async fn list_tools(&self) -> error::Result<Vec<Arc<Tool>>> {
        let mut tools = match self.builtin.list_tools().await {
            Ok(t) => t,
            Err(e) => {
                tracing::error!("Failed to list builtin tools: {:?}", e);
                vec![]
            }
        };

        match self.get_script().await.list_tools().await {
            Ok(t) => tools.extend(t),
            Err(e) => {
                tracing::error!("Failed to list script tools: {:?}", e);
            }
        };

        for mcp in self.mcp.iter() {
            match mcp.list_tools().await {
                Ok(t) => tools.extend(t),
                Err(e) => {
                    tracing::error!("Failed to list mcp tools: {:?}", e);
                }
            }
        }

        Ok(tools)
    }

    async fn list_modules(&self) -> error::Result<Vec<Arc<Module>>> {
        let mut modules = match self.builtin.list_modules().await {
            Ok(m) => m,
            Err(e) => {
                tracing::error!("Failed to list builtin modules: {:?}", e);
                vec![]
            }
        };

        match self.get_script().await.list_modules().await {
            Ok(m) => modules.extend(m),
            Err(e) => {
                tracing::error!("Failed to list script modules: {:?}", e);
            }
        };

        for mcp in self.mcp.iter() {
            match mcp.list_modules().await {
                Ok(m) => modules.extend(m),
                Err(e) => {
                    tracing::error!("Failed to list mcp modules: {:?}", e);
                }
            }
        }

        Ok(modules)
    }

    async fn call_tool(&self, input: CallToolInput) -> error::Result<CallToolOutput> {
        // 先从 builtin 中匹配 input.module_name
        if input.module_name == BUILTIN_MODULE_NAME {
            return self.builtin.call_tool(input).await;
        }

        // 从 mcp 中匹配 input.module_name
        for mcp in self.mcp.iter() {
            if mcp.module_name == input.module_name {
                return mcp.call_tool(input).await;
            }
        }

        // 如果 mcp 中没有匹配的模块，则从 script 中匹配
        self.get_script().await.call_tool(input).await
    }
}
