use std::sync::Arc;

use reqwest::Url;
use rmcp::{
    ServiceExt as _,
    model::{CallToolRequestParam, ClientCapabilities, ClientInfo, RootsCapabilities},
    transport::{
        StreamableHttpClientTransport, TokioChildProcess,
        streamable_http_client::StreamableHttpClientTransportConfig,
    },
};
use serde::{Deserialize, Serialize};
use tokio::{process::Command, sync::RwLock};

use crate::{Module, ModuleSource, Tool, repository::*};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpTool {
    pub name: String,
    pub module_name: String,
    pub description: String,
    pub module_config: Arc<serde_json::Value>,
    pub params_schema: Arc<serde_json::Value>,
    pub result_schema: Arc<serde_json::Value>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum McpClientBuilder {
    Streamable { url: Url },
    ChildProcess { command: String, args: Vec<String> },
}

pub type McpClient =
    rmcp::service::RunningService<rmcp::RoleClient, rmcp::model::InitializeRequestParam>;

impl McpClientBuilder {
    pub async fn client(&self) -> error::Result<McpClient> {
        let client_info = ClientInfo {
            capabilities: ClientCapabilities {
                roots: Some(RootsCapabilities {
                    list_changed: Some(false),
                }),
                ..Default::default()
            },
            ..Default::default()
        };

        match self {
            Self::Streamable { url } => {
                let transport = StreamableHttpClientTransport::with_client(
                    reqwest::ClientBuilder::new().no_proxy().build()?,
                    StreamableHttpClientTransportConfig::with_uri(url.to_string()),
                );
                let client = client_info.serve(transport).await?;

                Ok(client)
            }
            Self::ChildProcess { command, args } => {
                let mut command = Command::new(command);
                command.args(args);

                let transport = TokioChildProcess::new(command)?;
                let client = client_info.serve(transport).await?;

                Ok(client)
            }
        }
    }
}

pub struct McpToolRepository {
    pub module_name: String,
    builder: McpClientBuilder,
    client: RwLock<Option<Arc<McpClient>>>,
}

impl McpToolRepository {
    pub fn new(module_name: String, builder: McpClientBuilder) -> Self {
        Self {
            module_name,
            builder,
            client: RwLock::new(None),
        }
    }

    pub async fn get_client(&self) -> error::Result<Arc<McpClient>> {
        let client = self.client.read().await;

        if let Some(client) = client.as_ref() {
            Ok(client.clone())
        } else {
            drop(client);

            let mut saved = self.client.write().await;

            if let Some(client) = saved.as_ref() {
                Ok(client.clone())
            } else {
                let client = Arc::new(self.builder.client().await?);
                *saved = Some(client.clone());
                Ok(client)
            }
        }
    }
}

#[async_trait::async_trait]
impl ToolRepository for McpToolRepository {
    async fn list_tools(&self) -> error::Result<Vec<Arc<Tool>>> {
        let client = self
            .get_client()
            .await
            .inspect_err(|e| tracing::error!("Failed to create mcp client: {:?}", e))?;

        let tools = client
            .list_all_tools()
            .await
            .inspect_err(|e| tracing::error!("Failed to list tools: {:?}", e))?;

        Ok(tools
            .into_iter()
            .map(|tool| {
                Arc::new(Tool::Mcp(Arc::new(McpTool {
                    name: tool.name.to_string(),
                    module_name: self.module_name.clone(),
                    description: tool.description.unwrap_or_default().to_string(),
                    module_config: Arc::new(serde_json::Map::new().into()),
                    params_schema: Arc::new(tool.input_schema.as_ref().clone().into()),
                    result_schema: Arc::new(serde_json::Map::new().into()),
                })))
            })
            .collect())
    }

    async fn list_modules(&self) -> error::Result<Vec<Arc<Module>>> {
        let tools = self.list_tools().await?;

        Ok(vec![Arc::new(Module {
            name: self.module_name.clone(),
            description: "".to_string(),
            alias: vec![],
            config: Arc::new(serde_json::Map::new().into()),
            source: ModuleSource::Mcp(self.builder.clone()),
            tools,
        })])
    }

    async fn call_tool(&self, input: CallToolInput) -> error::Result<CallToolOutput> {
        let client = self
            .get_client()
            .await
            .inspect_err(|e| tracing::error!("Failed to create mcp client: {:?}", e))?;

        let result = client
            .call_tool(CallToolRequestParam {
                name: input.tool_name.into(),
                arguments: match input.payload {
                    serde_json::Value::Object(params) => Some(params),
                    _ => None,
                },
            })
            .await
            .inspect_err(|e| tracing::error!("Failed to call tool: {:?}", e))?;

        Ok(CallToolOutput {
            payload: serde_json::to_value(
                result
                    .content
                    .into_iter()
                    .map(|v| v.raw)
                    .collect::<Vec<_>>(),
            )
            .unwrap_or_default(),
        })
    }
}
