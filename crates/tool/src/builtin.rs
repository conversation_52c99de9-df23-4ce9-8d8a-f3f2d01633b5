use std::sync::Arc;

use serde::{Deserialize, Serialize};

use crate::{<PERSON><PERSON><PERSON>, ModuleSource, Tool, repository::*};

pub const BUILTIN_MODULE_NAME: &str = "builtin";

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct BuiltinTool {
    pub name: String,
    pub description: String,
    pub params: Arc<serde_json::Value>,
    pub result: Arc<serde_json::Value>,
    pub features: BuiltinToolFeatures,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct BuiltinToolFeatures {
    pub skip_summary: bool,
}

pub struct BuiltinToolRepository {
    modules: Vec<Arc<Module>>,
    tools: Vec<Arc<Tool>>,
}

#[async_trait::async_trait]
impl ToolRepository for BuiltinToolRepository {
    async fn list_modules(&self) -> error::Result<Vec<Arc<Module>>> {
        Ok(self.modules.clone())
    }

    async fn list_tools(&self) -> error::Result<Vec<Arc<Tool>>> {
        Ok(self.tools.clone())
    }

    async fn call_tool(&self, input: CallToolInput) -> error::Result<CallToolOutput> {
        match input.tool_name.as_str() {
            "start" | "end" | "select" => Ok(CallToolOutput {
                payload: input.payload,
            }),
            _ => Err(error::ToolError::UnknownTool(input.tool_name).into()),
        }
    }
}

impl Default for BuiltinToolRepository {
    fn default() -> Self {
        Self::new()
    }
}

impl BuiltinToolRepository {
    pub fn new() -> Self {
        let start = Arc::new(Tool::Builtin(Arc::new(BuiltinTool {
            name: "start".to_string(),
            description: "开始".to_string(),
            params: Arc::new(serde_json::Value::Null),
            result: Arc::new(serde_json::Value::Null),
            features: BuiltinToolFeatures { skip_summary: true },
        })));

        let end = Arc::new(Tool::Builtin(Arc::new(BuiltinTool {
            name: "end".to_string(),
            description: "结束".to_string(),
            params: Arc::new(serde_json::Value::Null),
            result: Arc::new(serde_json::Value::Null),
            features: BuiltinToolFeatures { skip_summary: true },
        })));

        let select = Arc::new(Tool::Builtin(Arc::new(BuiltinTool {
            name: "select".to_string(),
            description: "选择".to_string(),
            params: Arc::new(serde_json::Value::Null),
            result: Arc::new(serde_json::Value::Null),
            features: BuiltinToolFeatures { skip_summary: true },
        })));

        let module = Arc::new(Module {
            name: BUILTIN_MODULE_NAME.to_string(),
            alias: vec![],
            description: "内置工具".to_string(),
            source: ModuleSource::Builtin,
            config: Arc::new(serde_json::Value::Null),
            tools: vec![start, end, select],
        });

        Self {
            tools: module.tools.clone(),
            modules: vec![module],
        }
    }
}
