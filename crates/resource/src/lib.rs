use std::pin::Pin;

use futures::Stream;
use minio::s3::builders::DeleteObject;
use minio::s3::{
    builders::{BucketExists, CreateBucket, DeleteObjects, ObjectToDelete},
    types::S3Api as _,
};
use redis::AsyncTypedCommands as _;
use serde::{Deserialize, Serialize, de::DeserializeOwned};

pub const RUNTIME_BUCKET_NAME: &str = "agent-runner.runtime";
pub const PLAYBACK_BUCKET_NAME: &str = "agent-runner.playback";
pub const USER_BUCKET_NAME: &str = "agent-runner.user";
pub const SESSION_BUCKET_NAME: &str = "agent-runner.session";
pub const GLOBAL_BUCKET_NAME: &str = "agent-runner.global";
pub const BUCKETS: [&str; 5] = [
    RUNTIME_BUCKET_NAME,
    PLAYBACK_BUCKET_NAME,
    USER_BUCKET_NAME,
    SESSION_BUCKET_NAME,
    GLOBAL_BUCKET_NAME,
];

pub struct ResourceManager {
    pub redis: Redis,
    pub minio: minio::s3::Client,
}

#[derive(Clone)]
pub enum Redis {
    Standalone(redis::aio::ConnectionManager),
    Cluster(redis::cluster_async::ClusterConnection),
}

impl Redis {
    pub async fn build(url: String, is_cluster: bool) -> error::Result<Redis> {
        Ok(if is_cluster {
            let client = redis::cluster::ClusterClient::new(vec![url])?;
            Self::Cluster(client.get_async_connection().await?)
        } else {
            let client = redis::Client::open(url)?;
            Self::Standalone(client.get_connection_manager().await?)
        })
    }
}

// Implement missing trait methods for redis::aio::ConnectionLike
impl redis::aio::ConnectionLike for Redis {
    fn req_packed_command<'a>(
        &'a mut self,
        cmd: &'a redis::Cmd,
    ) -> Pin<Box<dyn futures::Future<Output = redis::RedisResult<redis::Value>> + Send + 'a>> {
        match self {
            Redis::Standalone(conn) => conn.req_packed_command(cmd),
            Redis::Cluster(conn) => conn.req_packed_command(cmd),
        }
    }

    fn req_packed_commands<'a>(
        &'a mut self,
        cmd: &'a redis::Pipeline,
        offset: usize,
        count: usize,
    ) -> Pin<Box<dyn futures::Future<Output = redis::RedisResult<Vec<redis::Value>>> + Send + 'a>>
    {
        match self {
            Redis::Standalone(conn) => conn.req_packed_commands(cmd, offset, count),
            Redis::Cluster(conn) => conn.req_packed_commands(cmd, offset, count),
        }
    }

    fn get_db(&self) -> i64 {
        match self {
            Redis::Standalone(conn) => conn.get_db(),
            Redis::Cluster(conn) => conn.get_db(),
        }
    }
}

impl ResourceManager {
    pub fn new(redis: Redis, minio: minio::s3::Client) -> Self {
        ResourceManager { redis, minio }
    }

    pub async fn get_value<T: DeserializeOwned>(&self, key: &str) -> error::Result<Option<T>> {
        let result = self.redis.clone().get(key).await?;
        Ok(result.and_then(|v| serde_json::from_str(&v).ok()))
    }

    pub async fn set_value<T: Serialize>(&self, key: String, value: &T) -> error::Result<()> {
        self.redis
            .clone()
            .set(&key, serde_json::to_string(value)?)
            .await?;

        let _ = self.redis.clone().expire(key, 60 * 60 * 24 * 7).await?;

        Ok(())
    }

    pub async fn push_list<T: Serialize>(&self, key: String, value: &T) -> error::Result<()> {
        self.redis
            .clone()
            .rpush(&key, serde_json::to_string(value)?)
            .await?;

        let _ = self.redis.clone().expire(key, 60 * 60 * 24 * 7).await?;

        Ok(())
    }

    pub async fn get_list<T: DeserializeOwned>(&self, key: &str) -> error::Result<Vec<T>> {
        let result = self.redis.clone().lrange(key, 0, -1).await?;
        Ok(result
            .into_iter()
            .filter_map(|v| serde_json::from_str(&v).ok())
            .collect())
    }

    pub async fn remove_value(&self, key: &str) -> error::Result<()> {
        self.redis.clone().del(key).await?;
        Ok(())
    }

    pub async fn write_file(
        &self,
        bucket: &str,
        object: &str,
        data: bytes::Bytes,
    ) -> error::Result<FileInfo> {
        let response = self
            .minio
            .put_object(bucket, object, data.into())
            .send()
            .await?;
        Ok(FileInfo {
            bucket: response.bucket,
            object: response.object,
            region: response.region,
            etag: response.etag,
            version_id: response.version_id,
        })
    }

    pub async fn read_file(
        &self,
        bucket: &str,
        object: &str,
        version_id: Option<String>,
    ) -> error::Result<bytes::Bytes> {
        let response = self
            .minio
            .get_object(bucket, object)
            .version_id(version_id)
            .send()
            .await?;

        Ok(response.content.to_segmented_bytes().await?.to_bytes())
    }

    pub async fn read_file_stream(
        &self,
        bucket: &str,
        object: &str,
        version_id: Option<String>,
    ) -> error::Result<(
        Pin<Box<dyn Stream<Item = std::io::Result<bytes::Bytes>> + Send>>,
        axum::http::HeaderMap,
    )> {
        let response = self
            .minio
            .get_object(bucket, object)
            .version_id(version_id)
            .send()
            .await?;

        let mut headers = response.headers;

        headers.remove("server");

        let filename = response.object.rsplit('/').next().unwrap_or("");
        let encoded = url::form_urlencoded::byte_serialize(filename.as_bytes()).collect::<String>();
        let content_disposition = format!("inline; filename*=UTF-8''{encoded}");
        headers.append("Content-Disposition", content_disposition.parse().unwrap());

        Ok((response.content.to_stream().await?.0, headers))
    }

    pub async fn create_bucket_if_not_exists(&self, bucket_name: String) -> error::Result<()> {
        let response = BucketExists::new(self.minio.clone(), bucket_name.clone())
            .send()
            .await?;
        if !response.exists {
            let _ = CreateBucket::new(self.minio.clone(), bucket_name)
                .send()
                .await?;
        }
        Ok(())
    }

    pub async fn delete_file(
        &self,
        bucket_name: String,
        object_to_delete: (String, Option<&str>),
    ) -> error::Result<()> {
        let object = ObjectToDelete::from(object_to_delete);
        DeleteObject::new(self.minio.clone(), bucket_name, object)
            .send()
            .await?;
        Ok(())
    }

    pub async fn delete_files(
        &self,
        bucket_name: String,
        objects_to_delete: Vec<(String, Option<String>)>,
    ) -> error::Result<()> {
        let objects = objects_to_delete
            .into_iter()
            .map(|(object, version_id)| {
                ObjectToDelete::from((object.clone(), version_id.as_deref()))
            })
            .collect::<Vec<_>>();
        if !objects.is_empty() {
            DeleteObjects::new(self.minio.clone(), bucket_name, objects)
                .send()
                .await?;
        }
        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    pub bucket: String,
    pub object: String,
    pub region: String,
    pub etag: String,
    pub version_id: Option<String>,
}

impl ResourceManager {
    pub async fn get_interaction<T: DeserializeOwned>(
        &self,
        session_id: &str,
        executation_id: &str,
        interaction_id: &str,
    ) -> error::Result<Option<T>> {
        self.get_value(
            format!("interaction:{session_id}:{executation_id}:{interaction_id}").as_str(),
        )
        .await
    }

    pub async fn set_interaction<T: Serialize>(
        &self,
        session_id: &str,
        executation_id: &str,
        interaction_id: &str,
        interaction: T,
    ) -> error::Result<()> {
        self.set_value(
            format!("interaction:{session_id}:{executation_id}:{interaction_id}"),
            &interaction,
        )
        .await
    }

    pub async fn add_view<T: Serialize>(&self, session_id: &str, view: T) -> error::Result<()> {
        self.push_list(format!("view:{session_id}"), &view).await
    }

    pub async fn get_views<T: DeserializeOwned + Default>(
        &self,
        session_id: &str,
    ) -> error::Result<Vec<T>> {
        self.get_list(format!("view:{session_id}").as_str()).await
    }

    pub async fn set_cache<T: Serialize>(
        &self,
        session_id: &str,
        key: &str,
        data: T,
    ) -> error::Result<()> {
        self.set_value(format!("cache:{session_id}:{key}"), &data)
            .await
    }

    pub async fn get_cache<T: DeserializeOwned>(
        &self,
        session_id: &str,
        key: &str,
    ) -> error::Result<Option<T>> {
        self.get_value(format!("cache:{session_id}:{key}").as_str())
            .await
    }

    pub async fn write_user_file(
        &self,
        tenant_id: &str,
        user_id: &str,
        path: &str,
        data: bytes::Bytes,
    ) -> error::Result<FileInfo> {
        self.write_file(
            USER_BUCKET_NAME,
            &format!("{tenant_id}/{user_id}/{path}"),
            data,
        )
        .await
    }

    pub async fn read_user_file(
        &self,
        tenant_id: &str,
        user_id: &str,
        path: &str,
        version_id: Option<String>,
    ) -> error::Result<bytes::Bytes> {
        self.read_file(
            USER_BUCKET_NAME,
            &format!("{tenant_id}/{user_id}/{path}"),
            version_id,
        )
        .await
    }

    pub async fn write_session_file(
        &self,
        tenant_id: &str,
        session_id: &str,
        path: &str,
        data: bytes::Bytes,
    ) -> error::Result<FileInfo> {
        self.write_file(
            SESSION_BUCKET_NAME,
            &format!("{tenant_id}/{session_id}/{path}"),
            data,
        )
        .await
    }

    pub async fn read_session_file(
        &self,
        tenant_id: &str,
        session_id: &str,
        path: &str,
        version_id: Option<String>,
    ) -> error::Result<bytes::Bytes> {
        self.read_file(
            SESSION_BUCKET_NAME,
            &format!("{tenant_id}/{session_id}/{path}"),
            version_id,
        )
        .await
    }

    // global
    pub async fn write_global_file(
        &self,
        tenant_id: &str,
        path: &str,
        data: bytes::Bytes,
    ) -> error::Result<FileInfo> {
        self.write_file(GLOBAL_BUCKET_NAME, &format!("{tenant_id}/{path}"), data)
            .await
    }

    pub async fn read_global_file(
        &self,
        tenant_id: &str,
        path: &str,
        version_id: Option<String>,
    ) -> error::Result<bytes::Bytes> {
        self.read_file(
            GLOBAL_BUCKET_NAME,
            &format!("{tenant_id}/{path}"),
            version_id,
        )
        .await
    }

    pub async fn update_task_state<T: Serialize>(
        &self,
        task_id: &str,
        state: T,
    ) -> error::Result<()> {
        self.set_value(format!("task:{task_id}"), &state).await
    }

    pub async fn get_task_state<T: DeserializeOwned>(
        &self,
        task_id: &str,
    ) -> error::Result<Option<T>> {
        self.get_value(format!("task:{task_id}").as_str()).await
    }

    pub async fn update_task_history<T: Serialize>(
        &self,
        task_id: &str,
        history: T,
    ) -> error::Result<()> {
        self.set_value(format!("task:{task_id}:history"), &history)
            .await
    }

    pub async fn get_task_history<T: DeserializeOwned>(
        &self,
        task_id: &str,
    ) -> error::Result<Option<T>> {
        self.get_value(format!("task:{task_id}:history").as_str())
            .await
    }
}
