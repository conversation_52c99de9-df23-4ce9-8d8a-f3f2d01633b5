[package]
name = "resource"
version = "0.1.0"
edition = "2024"
publish = false

[lib]
name = "resource"
path = "src/lib.rs"

[dependencies]
axum = { workspace = true }
bytes = { workspace = true }
error = { workspace = true }
futures = { workspace = true }
minio = { workspace = true }
redis = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
url = { workspace = true }
