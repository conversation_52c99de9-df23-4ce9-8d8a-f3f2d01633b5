use std::sync::Arc;

use ai::{Chat<PERSON><PERSON><PERSON>, ChatTextMetadata, SimpleWorkflow};
use entity::DocumentRepository;
use futures::StreamExt as _;
use interaction::InteractionResult;
use ractor::*;
use workflow::Workflow;

use crate::{
    Chat<PERSON><PERSON><PERSON><PERSON><PERSON>, ModelRunner, WorkflowActor, WorkflowActorEvent, WorkflowActorNotification,
    WorkflowActorOptions, WorkflowRunner,
};

pub struct ChatWorkflowActor {
    pub session_id: uuid::Uuid,
    pub tenant_id: String,
    pub user_id: Option<String>,
    pub output_port: Arc<OutputPort<ChatWorkflowActorNotification>>,
    pub model_runner: Arc<dyn ModelRunner + Send + Sync>,
    pub workflow_runner: Arc<dyn WorkflowRunner + Send + Sync>,
    pub repository: Arc<DocumentRepository>,
}

pub struct ChatWorkflowActorState {
    pub context: Vec<ai::Message>,
    pub workflow_actor: Option<ActorRef<WorkflowActorEvent>>,
}

#[derive(Debug)]
pub enum ChatWorkflowActorEvent {
    Input(Vec<ai::Message>),
    Interact(InteractionResult, RpcReplyPort<Result<(), error::Error>>),
    /* internal */
    Workflow {
        workflow: Arc<Workflow>,
        options: WorkflowActorOptions,
    },
    WorkflowNotification(WorkflowActorNotification),
}

#[derive(Debug, Clone)]
pub enum ChatWorkflowActorNotification {
    Receive(ChatDeltaBlock),
    Workflow(WorkflowActorNotification),
    Error(String),
    Start(Option<serde_json::Value>),
    Metadata(serde_json::Value),
    Done,
}

impl ChatWorkflowActor {
    async fn user_input(
        &self,
        myself: ActorRef<ChatWorkflowActorEvent>,
        state: &mut ChatWorkflowActorState,
        messages: Vec<ai::Message>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始处理用户输入，消息数量: {}", messages.len());
        state.context = messages;

        self.output_port
            .send(ChatWorkflowActorNotification::Start(None));

        let mut stream = match self
            .model_runner
            .chat_workflow(state.context.as_slice())
            .await
        {
            Ok(stream) => {
                tracing::debug!("成功创建聊天工作流流");
                stream.into_block_stream()
            }
            Err(e) => {
                tracing::error!("创建聊天工作流流失败: {}", e);
                self.output_port
                    .send(ChatWorkflowActorNotification::Error(e.to_string()));
                return Ok(());
            }
        };

        while let Some(block) = stream.next().await {
            let block = block.inspect_err(|e| {
                tracing::error!("处理聊天块失败: {:?}", e);
            })?;

            match &block {
                ChatDeltaBlock::Block(metadata) => {
                    if let ChatDelta::Metadata(metadata) = metadata.as_ref() {
                        let metadata =
                            serde_json::from_str::<ChatTextMetadata>(metadata).unwrap_or_default();

                        self.output_port
                            .send(ChatWorkflowActorNotification::Metadata(serde_json::json!({
                                "chat_type": metadata.r#type,
                                "workflow": metadata.workflow,
                            })));
                    }
                }
                ChatDeltaBlock::Done(deltas) => {
                    tracing::debug!("收到完成块，deltas 数量: {}", deltas.len());
                    let json = deltas.iter().rev().find_map(|delta| {
                        if let ChatDelta::Json(json) = delta.as_ref() {
                            Some(json.clone())
                        } else {
                            None
                        }
                    });

                    if let Some(json) = json {
                        tracing::debug!("找到 JSON delta");
                        let branch = deltas.iter().rev().find_map(|delta| {
                            if let ChatDelta::Branch(branch) = delta.as_ref() {
                                Some(branch.clone())
                            } else {
                                None
                            }
                        });

                        let workflow = SimpleWorkflow::parse_string(
                            uuid::Uuid::now_v7(),
                            json,
                            branch.unwrap_or_default(),
                        );

                        let require_confirm = deltas
                            .iter()
                            .rev()
                            .find_map(|delta| {
                                if let ChatDelta::RequireConfirm(require_confirm) = delta.as_ref() {
                                    Some(require_confirm == "true")
                                } else {
                                    None
                                }
                            })
                            .unwrap_or_default();

                        match workflow {
                            Ok(workflow) => {
                                tracing::debug!("成功解析工作流");

                                let arc = Arc::new(workflow);
                                self.output_port
                                    .send(ChatWorkflowActorNotification::Workflow(
                                        WorkflowActorNotification::Created {
                                            workflow: arc.clone(),
                                            name: None,
                                        },
                                    ));
                                myself.send_message(ChatWorkflowActorEvent::Workflow {
                                    workflow: arc,
                                    options: WorkflowActorOptions {
                                        confirm_start: require_confirm,
                                        ..Default::default()
                                    },
                                })?;
                            }
                            Err(e) => {
                                tracing::error!("解析工作流失败: {}", e);
                                self.output_port
                                    .send(ChatWorkflowActorNotification::Error(e.to_string()));
                            }
                        }
                    } else {
                        tracing::debug!("未找到 JSON delta，发送完成通知");
                        self.output_port.send(ChatWorkflowActorNotification::Done);
                    }
                }
                _ => {}
            }

            self.output_port
                .send(ChatWorkflowActorNotification::Receive(block));
        }

        Ok(())
    }

    async fn start_workflow(
        &self,
        myself: ActorRef<ChatWorkflowActorEvent>,
        state: &mut ChatWorkflowActorState,
        workflow: Arc<Workflow>,
        options: WorkflowActorOptions,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始启动工作流");
        if state.workflow_actor.is_some() {
            tracing::error!("工作流已经启动");
            self.output_port.send(ChatWorkflowActorNotification::Error(
                "工作流已启动".to_string(),
            ));

            return Ok(());
        }

        let output_port = Arc::new(OutputPort::default());

        output_port.subscribe(myself.clone(), |message| {
            Some(ChatWorkflowActorEvent::WorkflowNotification(message))
        });

        tracing::debug!("创建工作流 Actor");
        let (workflow_actor, _) = myself
            .spawn_linked(
                None,
                WorkflowActor {
                    session_id: self.session_id,
                    tenant_id: self.tenant_id.clone(),
                    user_id: self.user_id.clone(),
                    context: state.context.clone(),
                    output_port,
                    workflow,
                    runner: self.workflow_runner.clone(),
                    options,
                },
                (),
            )
            .await?;

        tracing::debug!("发送启动消息到工作流 Actor");
        workflow_actor.send_message(WorkflowActorEvent::Start)?;

        state.workflow_actor = Some(workflow_actor);
        tracing::debug!("工作流 Actor 启动完成");

        Ok(())
    }

    async fn handle_error(
        &self,
        myself: ActorRef<ChatWorkflowActorEvent>,
        state: &mut ChatWorkflowActorState,
        error: ActorProcessingErr,
    ) -> Result<(), ActorProcessingErr> {
        self.output_port
            .send(ChatWorkflowActorNotification::Error(format!(
                "chat workflow actor terminated or failed: {error:?}"
            )));

        self.output_port.send(ChatWorkflowActorNotification::Done);

        tracing::error!("chat workflow actor terminated or failed: {:?}", error);

        if let Some(workflow_actor) = &mut state.workflow_actor {
            workflow_actor.unlink(myself.get_cell());
            workflow_actor.stop(None);
        }

        Ok(())
    }
}

#[async_trait::async_trait]
impl Actor for ChatWorkflowActor {
    type Msg = ChatWorkflowActorEvent;
    type State = ChatWorkflowActorState;
    type Arguments = ();

    async fn pre_start(
        &self,
        _myself: ActorRef<Self::Msg>,
        _args: Self::Arguments,
    ) -> Result<Self::State, ActorProcessingErr> {
        Ok(ChatWorkflowActorState {
            workflow_actor: None,
            context: vec![],
        })
    }

    async fn handle(
        &self,
        myself: ActorRef<Self::Msg>,
        message: Self::Msg,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        let actor = myself.clone();

        let result = match message {
            ChatWorkflowActorEvent::Input(messages) => {
                self.user_input(myself, state, messages).await
            }
            ChatWorkflowActorEvent::Interact(result, reply) => {
                if let Some(workflow_actor) = state.workflow_actor.as_ref() {
                    workflow_actor.send_message(WorkflowActorEvent::Interact(result, reply))?;
                }

                Ok(())
            }
            ChatWorkflowActorEvent::Workflow { workflow, options } => {
                self.start_workflow(myself, state, workflow, options).await
            }
            ChatWorkflowActorEvent::WorkflowNotification(notification) => {
                let is_done = matches!(notification, WorkflowActorNotification::Done { .. });

                self.output_port
                    .send(ChatWorkflowActorNotification::Workflow(notification));

                if is_done {
                    self.output_port.send(ChatWorkflowActorNotification::Done);
                }

                Ok(())
            }
        };

        match result {
            Ok(()) => Ok(()),
            Err(e) => self.handle_error(actor, state, e).await,
        }
    }

    async fn handle_supervisor_evt(
        &self,
        myself: ActorRef<Self::Msg>,
        message: SupervisionEvent,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        match message {
            SupervisionEvent::ActorTerminated(_, _, _) | SupervisionEvent::ActorFailed(_, _) => {
                tracing::error!("actor terminated or failed: {:?}", message);
                return self
                    .handle_error(
                        myself,
                        state,
                        anyhow::anyhow!("Actor 异常终止: {:?}", message).into(),
                    )
                    .await;
            }
            _ => {}
        }

        Ok(())
    }
}
