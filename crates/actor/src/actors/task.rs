use std::sync::Arc;

use ractor::*;

use crate::{
    Task, TaskKind, TaskRunner, TaskState, WorkflowActor, WorkflowActorEvent,
    WorkflowActorNotification, WorkflowHistory,
};

pub struct TaskActor {
    pub task: Arc<Task>,
    pub runner: Arc<dyn TaskRunner + Send + Sync + 'static>,
}

impl TaskActor {}

pub enum TaskActorEvent {
    Start,
    Update(TaskState, Option<WorkflowHistory>),
}

pub enum TaskActorState {
    Workflow(ActorRef<WorkflowActorEvent>),
}

impl TaskActor {
    async fn update(
        &self,
        myself: ActorRef<TaskActorEvent>,
        state: TaskState,
        history: Option<WorkflowHistory>,
    ) -> Result<(), ActorProcessingErr> {
        self.runner.update_task_state(self.task.id, state).await?;

        if let Some(history) = history {
            self.runner
                .update_task_history(self.task.id, history)
                .await?;
        }

        myself.stop(None);

        Ok(())
    }
}

#[async_trait::async_trait]
impl Actor for TaskActor {
    type Msg = TaskActorEvent;
    type State = TaskActorState;
    type Arguments = ();

    async fn pre_start(
        &self,
        myself: ActorRef<Self::Msg>,
        _args: Self::Arguments,
    ) -> Result<Self::State, ActorProcessingErr> {
        match &self.task.kind {
            TaskKind::Workflow(task) => {
                let output_port = OutputPort::default();

                output_port.subscribe(myself.clone(), move |message: WorkflowActorNotification| {
                    match message {
                        WorkflowActorNotification::Done { history } => {
                            let last = match history
                                .nodes
                                .iter()
                                .rev()
                                .find(|state| !state.node.id.is_empty())
                            {
                                Some(node) => node,
                                None => {
                                    return Some(TaskActorEvent::Update(
                                        TaskState::error("工作流未执行".to_string()),
                                        None,
                                    ));
                                }
                            };

                            if let Some(error) = last.error.as_ref() {
                                Some(TaskActorEvent::Update(
                                    TaskState::error(error.clone()),
                                    Some(history.clone()),
                                ))
                            } else {
                                Some(TaskActorEvent::Update(
                                    TaskState::done(last.clone()),
                                    Some(history.clone()),
                                ))
                            }
                        }
                        _ => None,
                    }
                });

                let (workflow, _) = myself
                    .spawn_linked(
                        None,
                        WorkflowActor {
                            context: vec![],
                            options: task.options.clone(),
                            output_port: Arc::new(output_port),
                            workflow: task.workflow.clone(),
                            runner: self.runner.workflow_runner(),
                            session_id: self.task.id,
                            tenant_id: self.task.tenant_id.clone(),
                            user_id: None,
                        },
                        (),
                    )
                    .await?;

                Ok(TaskActorState::Workflow(workflow))
            }
        }
    }

    async fn handle(
        &self,
        myself: ActorRef<Self::Msg>,
        message: Self::Msg,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        #[allow(clippy::single_match)]
        match (message, state) {
            (TaskActorEvent::Start, TaskActorState::Workflow(workflow)) => {
                workflow.send_message(WorkflowActorEvent::Start)?;
            }
            (TaskActorEvent::Update(state, history), TaskActorState::Workflow(..)) => {
                self.update(myself, state, history).await?;
            }
        }

        Ok(())
    }

    async fn handle_supervisor_evt(
        &self,
        myself: ActorRef<Self::Msg>,
        message: SupervisionEvent,
        _state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        match message {
            SupervisionEvent::ActorTerminated(_, _, _) | SupervisionEvent::ActorFailed(_, _) => {
                tracing::error!("actor terminated or failed: {:?}", message);

                self.update(
                    myself.clone(),
                    TaskState::error(format!("Actor 异常终止: {message:?}")),
                    None,
                )
                .await?;

                myself.drain()?;
            }
            _ => {}
        }

        Ok(())
    }
}
