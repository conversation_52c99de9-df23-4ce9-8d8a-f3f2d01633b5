use std::sync::Arc;

use ai::{
    ExploreEvent, ExploreRequest, ExploreStream, ExploreWorkflowResult, ExploreWorkflowStepResult,
    SimpleWorkflow,
};
use futures::{SinkExt as _, StreamExt as _, stream::SplitSink};
use interaction::{Interaction, InteractionResult};
use ractor::*;
use workflow::Workflow;

use crate::{
    ModelRunner, WorkflowActor, WorkflowActorEvent, WorkflowActorNotification,
    WorkflowActorOptions, WorkflowHistory, WorkflowRunner,
};

pub struct ExploreActor {
    pub session_id: uuid::Uuid,
    pub tenant_id: String,
    pub user_id: Option<String>,
    pub output_port: Arc<OutputPort<ExploreActorNotification>>,
    pub model_runner: Arc<dyn ModelRunner + Send + Sync>,
    pub workflow_runner: Arc<dyn WorkflowRunner + Send + Sync>,
}

#[derive(Default)]
pub struct ExploreActorState {
    handle: Option<tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON><Result<(), ActorProcessingErr>>>,
    sink: Option<SplitSink<ExploreStream, ExploreRequest>>,
    workflow_actor: Option<ActorRef<WorkflowActorEvent>>,
    pending_interaction: Option<Interaction>,
    topic: Option<String>,
}

#[derive(Debug)]
pub enum ExploreActorEvent {
    Start {
        topic: String,
    },
    Request(ExploreRequest),
    Interact(InteractionResult, RpcReplyPort<Result<(), error::Error>>),
    /* internal */
    Workflow {
        workflow: Arc<Workflow>,
        context: Option<Vec<ai::Message>>,
    },
    WorkflowNotification(WorkflowActorNotification),
    RequireInteraction(Box<Interaction>),
}

#[derive(Debug, Clone)]
pub enum ExploreActorNotification {
    Receive(Vec<ExploreEvent>),
    Workflow(WorkflowActorNotification),
    Error(String),
    Start(Option<serde_json::Value>),
    Done,
}

impl ExploreActor {
    async fn start(
        &self,
        myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        topic: String,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始探索主题: {}", topic);
        state.topic = Some(topic.clone());

        self.output_port
            .send(ExploreActorNotification::Start(Some(serde_json::json!({
                "use_deep_explore": true,
                "chat_type": "deep_explore"
            }))));

        let output_port = self.output_port.clone();

        let (sink, stream) = match self.model_runner.chat_explore(topic, self.session_id).await {
            Ok(stream) => {
                tracing::debug!("成功创建探索流");
                stream.split()
            }
            Err(e) => {
                tracing::error!("创建探索流失败: {}", e);
                self.output_port
                    .send(ExploreActorNotification::Error(e.to_string()));
                return Err(e.into());
            }
        };

        state.handle = Some(tokio::spawn(async move {
            let stream = tokio_stream::StreamExt::chunks_timeout(
                stream,
                1024 * 4,
                std::time::Duration::from_millis(100),
            );

            tokio::pin!(stream);

            while let Some(results) = stream.next().await {
                let mut output = Vec::with_capacity(results.len());

                for result in results {
                    match result {
                        Ok(response) => {
                            tracing::debug!("收到探索响应: {:?}", response);
                            match &response {
                                ExploreEvent::Workflow { content } => {
                                    tracing::debug!("处理工作流内容: {}", content.name);
                                    let result = SimpleWorkflow::parse_string(
                                        uuid::Uuid::now_v7(),
                                        content.json.clone(),
                                        content.branch.clone(),
                                    );

                                    match result {
                                        Ok(workflow) => {
                                            tracing::debug!("成功解析工作流");
                                            let arc = Arc::new(workflow);
                                            myself.send_message(ExploreActorEvent::Workflow {
                                                workflow: arc.clone(),
                                                context: content
                                                    .query
                                                    .clone()
                                                    .map(|q| vec![ai::Message::user(q)]),
                                            })?;
                                            output_port.send(ExploreActorNotification::Workflow(
                                                WorkflowActorNotification::Created {
                                                    workflow: arc.clone(),
                                                    name: content.query.clone(),
                                                },
                                            ));
                                        }
                                        Err(e) => {
                                            tracing::error!("解析工作流失败: {}", e);
                                            output_port.send(ExploreActorNotification::Error(
                                                e.to_string(),
                                            ));
                                            break;
                                        }
                                    }
                                }
                                ExploreEvent::Interaction { content } => {
                                    tracing::debug!("处理交互内容: {:?}", content);
                                    myself.send_message(ExploreActorEvent::RequireInteraction(
                                        Box::new(Interaction {
                                            id: uuid::Uuid::now_v7().to_string(),
                                            title: "".to_string(),
                                            content: *content.clone(),
                                        }),
                                    ))?;
                                }
                                _ => {}
                            }

                            output.push(response);
                        }
                        Err(e) => {
                            tracing::error!("处理探索响应失败: {}", e);
                            output_port.send(ExploreActorNotification::Error(e.to_string()));
                            break;
                        }
                    }
                }

                if !output.is_empty() {
                    output_port.send(ExploreActorNotification::Receive(output));
                }
            }

            tracing::debug!("探索流处理完成");
            output_port.send(ExploreActorNotification::Done);

            Ok(())
        }));

        state.sink = Some(sink);
        tracing::debug!("探索流设置完成");

        Ok(())
    }

    async fn request(
        &self,
        _myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        request: ExploreRequest,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("处理探索请求: {:?}", request);
        if let Some(sink) = &mut state.sink {
            match sink.send(request).await {
                Ok(()) => {
                    tracing::debug!("成功发送探索请求");
                    Ok(())
                }
                Err(e) => {
                    tracing::error!("发送探索请求失败: {}", e);
                    self.output_port
                        .send(ExploreActorNotification::Error(e.to_string()));

                    Err(e.into())
                }
            }
        } else {
            tracing::error!("探索流未建立");
            self.output_port
                .send(ExploreActorNotification::Error("连接未建立".to_string()));
            Ok(())
        }
    }

    async fn start_workflow(
        &self,
        myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        workflow: Arc<Workflow>,
        context: Option<Vec<ai::Message>>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始启动探索工作流");
        if state.workflow_actor.is_some() {
            tracing::error!("工作流已经启动");
            self.output_port
                .send(ExploreActorNotification::Error("工作流已启动".to_string()));

            return Ok(());
        }

        let output_port = Arc::new(OutputPort::default());

        output_port.subscribe(myself.clone(), |message| {
            Some(ExploreActorEvent::WorkflowNotification(message))
        });

        tracing::debug!("创建探索工作流 Actor");
        let (workflow_actor, _) = myself
            .spawn_linked(
                None,
                WorkflowActor {
                    session_id: self.session_id,
                    tenant_id: self.tenant_id.clone(),
                    user_id: self.user_id.clone(),
                    context: context.unwrap_or_else(|| {
                        vec![ai::Message::user(state.topic.clone().unwrap_or_default())]
                    }),
                    output_port,
                    workflow,
                    runner: self.workflow_runner.clone(),
                    options: WorkflowActorOptions::default(),
                },
                (),
            )
            .await?;

        tracing::debug!("发送启动消息到探索工作流 Actor");
        workflow_actor.send_message(WorkflowActorEvent::Start)?;

        state.workflow_actor = Some(workflow_actor);
        tracing::debug!("探索工作流 Actor 启动完成");

        Ok(())
    }

    async fn handle_workflow(
        &self,
        myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        notification: WorkflowActorNotification,
    ) -> Result<(), ActorProcessingErr> {
        #[allow(clippy::single_match)]
        match &notification {
            WorkflowActorNotification::Done { history } => {
                self.handle_workflow_done(myself, state, history.clone())
                    .await?;
            }
            _ => {}
        }

        self.output_port
            .send(ExploreActorNotification::Workflow(notification));

        Ok(())
    }

    async fn handle_workflow_done(
        &self,
        myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        history: WorkflowHistory,
    ) -> Result<(), ActorProcessingErr> {
        if let Some(sink) = &mut state.sink {
            sink.send(ExploreRequest::WorkflowResult {
                content: ExploreWorkflowResult {
                    final_summary: history
                        .final_summary
                        .as_ref()
                        .map(|s| s.as_ref().to_string())
                        .unwrap_or_default(),
                    steps: history
                        .nodes
                        .into_iter()
                        .map(|state| {
                            let node = state.node.clone();

                            ExploreWorkflowStepResult {
                                id: node.id.clone(),
                                name: node.tool.clone(),
                                module: node.module.clone(),
                                params: state.params.clone(),
                                result: state.result.clone(),
                                views: state.views.clone(),
                                summary: state.summary.clone(),
                                error: state.error.clone(),
                            }
                        })
                        .collect(),
                },
            })
            .await?;
        }

        if let Some(actor) = state.workflow_actor.take() {
            //
            tracing::debug!("移除探索工作流 Actor");
            actor.unlink(myself.get_cell());
            actor.stop(None);
        }

        Ok(())
    }

    async fn handle_interaction(
        &self,
        _myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        result: InteractionResult,
        reply: RpcReplyPort<Result<(), error::Error>>,
    ) -> Result<(), ActorProcessingErr> {
        if let Some(mut interaction) = state.pending_interaction.take() {
            if let Some(sink) = &mut state.sink {
                sink.send(ExploreRequest::InteractionResult {
                    content: serde_json::to_value(&result).unwrap_or_default(),
                })
                .await?;
            }

            reply.send(Ok(()))?;

            result.apply(&mut interaction.content);

            self.output_port.send(ExploreActorNotification::Workflow(
                WorkflowActorNotification::InteractDone {
                    interaction: Arc::new(interaction),
                },
            ));

            return Ok(());
        }

        if let Some(workflow_actor) = state.workflow_actor.as_ref() {
            workflow_actor.send_message(WorkflowActorEvent::Interact(result, reply))?;
        }

        Ok(())
    }

    async fn handle_require_interaction(
        &self,
        _myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        interaction: Interaction,
    ) -> Result<(), ActorProcessingErr> {
        state.pending_interaction = Some(interaction.clone());

        self.output_port.send(ExploreActorNotification::Workflow(
            WorkflowActorNotification::Interact {
                interaction: Arc::new(interaction),
            },
        ));

        Ok(())
    }

    async fn handle_error(
        &self,
        myself: ActorRef<ExploreActorEvent>,
        state: &mut ExploreActorState,
        error: ActorProcessingErr,
    ) -> Result<(), ActorProcessingErr> {
        self.output_port
            .send(ExploreActorNotification::Error(format!(
                "explore actor terminated or failed: {error:?}"
            )));

        self.output_port.send(ExploreActorNotification::Done);

        tracing::error!("explore actor terminated or failed: {:?}", error);

        if let Some(sink) = &mut state.sink {
            let _ = sink.close().await;
        }

        if let Some(handle) = &mut state.handle {
            handle.abort();
        }

        if let Some(workflow_actor) = state.workflow_actor.take() {
            workflow_actor.unlink(myself.get_cell());
            workflow_actor.stop(None);
        }

        Ok(())
    }
}

#[async_trait::async_trait]
impl Actor for ExploreActor {
    type Msg = ExploreActorEvent;
    type State = ExploreActorState;
    type Arguments = ();

    async fn pre_start(
        &self,
        _myself: ActorRef<Self::Msg>,
        _args: Self::Arguments,
    ) -> Result<Self::State, ActorProcessingErr> {
        Ok(ExploreActorState::default())
    }

    async fn handle(
        &self,
        myself: ActorRef<Self::Msg>,
        message: Self::Msg,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        let actor = myself.clone();

        let result = match message {
            ExploreActorEvent::Start { topic } => self.start(myself, state, topic).await,
            ExploreActorEvent::Request(request) => self.request(myself, state, request).await,
            ExploreActorEvent::Interact(result, reply) => {
                self.handle_interaction(myself, state, result, reply).await
            }
            ExploreActorEvent::RequireInteraction(content) => {
                self.handle_require_interaction(myself, state, *content)
                    .await
            }
            ExploreActorEvent::Workflow { workflow, context } => {
                self.start_workflow(myself, state, workflow, context).await
            }
            ExploreActorEvent::WorkflowNotification(notification) => {
                self.handle_workflow(myself, state, notification).await
            }
        };

        match result {
            Ok(()) => Ok(()),
            Err(e) => self.handle_error(actor, state, e).await,
        }
    }

    async fn post_stop(
        &self,
        _myself: ActorRef<Self::Msg>,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        if let Some(sink) = &mut state.sink {
            let _ = sink.close().await;
        }

        Ok(())
    }

    async fn handle_supervisor_evt(
        &self,
        myself: ActorRef<Self::Msg>,
        message: SupervisionEvent,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        match message {
            SupervisionEvent::ActorTerminated(_, _, _) | SupervisionEvent::ActorFailed(_, _) => {
                tracing::error!("actor terminated or failed: {:?}", message);
                return self
                    .handle_error(
                        myself,
                        state,
                        anyhow::anyhow!("Actor 异常终止: {:?}", message).into(),
                    )
                    .await;
            }
            _ => {}
        }

        Ok(())
    }
}
