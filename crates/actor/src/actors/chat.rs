use std::sync::Arc;

use futures::StreamExt as _;
use ractor::*;

use crate::{Chat<PERSON>el<PERSON><PERSON><PERSON>, ModelRunner};

pub struct ChatActor {
    runner: Arc<dyn ModelRunner + Send + Sync>,
    output_port: Arc<OutputPort<ChatActorNotification>>,
}

#[derive(Debug)]
pub enum ChatActorEvent {
    Input { messages: Vec<ai::Message> },
}

#[derive(Debug, Clone)]
pub enum ChatActorNotification {
    Receive(ChatDeltaBlock),
    Error(String),
}

impl ChatActor {
    async fn user_input(
        &self,
        _myself: ActorRef<ChatActorEvent>,
        _state: &mut (),
        messages: Vec<ai::Message>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始处理用户输入，消息数量: {}", messages.len());
        let mut stream = match self.runner.chat_workflow(messages.as_slice()).await {
            Ok(stream) => {
                tracing::debug!("成功创建聊天流");
                stream.into_block_stream()
            }
            Err(e) => {
                tracing::error!("创建聊天流失败: {}", e);
                self.output_port
                    .send(ChatActorNotification::Error(e.to_string()));
                return Ok(());
            }
        };

        while let Some(Ok(block)) = stream.next().await {
            tracing::debug!("收到新的聊天块");
            self.output_port.send(ChatActorNotification::Receive(block));
        }

        tracing::debug!("聊天流处理完成");
        Ok(())
    }
}

#[async_trait::async_trait]
impl Actor for ChatActor {
    type Msg = ChatActorEvent;
    type State = ();
    type Arguments = ();

    async fn pre_start(
        &self,
        _myself: ActorRef<Self::Msg>,
        _args: Self::Arguments,
    ) -> Result<Self::State, ActorProcessingErr> {
        Ok(())
    }

    async fn handle(
        &self,
        myself: ActorRef<Self::Msg>,
        message: Self::Msg,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        match message {
            ChatActorEvent::Input { messages } => self.user_input(myself, state, messages).await,
        }
    }
}
