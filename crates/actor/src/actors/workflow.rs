use std::sync::Arc;

use content::{ContentCard, ContentView, ContentViewOrDelta};
use futures::FutureExt as _;
use interaction::{
    ExecuteConfirmContent, Form, Interaction, InteractionContent, InteractionResult,
};
use jsonschema::{Keyword, ValidationError, paths::LazyLocation};
use ractor::*;
use serde::{Deserialize, Serialize};
use tokio_stream::StreamExt as _;
use tool::{
    Tool,
    repository::{CallToolInput, CallValidatorInput},
};
use workflow::{
    Node, ScriptConverter, Workflow, WorkflowNodeState, WorkflowState, WorkflowStateExecuting,
    WorkflowStateInteracting,
};

use crate::WorkflowRunner;

pub struct WorkflowActor {
    pub session_id: uuid::Uuid,
    pub tenant_id: String,
    pub user_id: Option<String>,
    pub context: Vec<ai::Message>,
    pub output_port: Arc<OutputPort<WorkflowActorNotification>>,
    pub workflow: Arc<Workflow>,
    pub runner: Arc<dyn WorkflowRunner + Send + Sync>,
    pub options: WorkflowActorOptions,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct WorkflowActorOptions {
    pub input_first_params: bool,
    pub confirm_start: bool,
    pub skip_extract_params: bool,
    pub skip_converter: bool,
    pub skip_summary: bool,
    pub skip_final_summary: bool,
    pub prevent_interaction: bool,
}

pub type WorkflowActorState = WorkflowState;

#[derive(Debug)]
pub enum WorkflowActorEvent {
    Start,
    /* Internal */
    MoveNext,
    CreateParams,
    ExtractParams,
    CreateConverter,
    Execute,
    Interact(InteractionResult, RpcReplyPort<Result<(), error::Error>>),
    Summary,
    Finish,
}

#[derive(Clone, Debug)]
pub enum WorkflowActorNotification {
    Created {
        workflow: Arc<Workflow>,
        name: Option<String>,
    },
    Move {
        node: Arc<Node>,
        workflow_id: uuid::Uuid,
    },
    Execute {
        current_node: Arc<Node>,
    },
    Interact {
        interaction: Arc<Interaction>,
    },
    InteractDone {
        interaction: Arc<Interaction>,
    },
    Views {
        views: Vec<Arc<ContentViewOrDelta>>,
    },
    Converter {
        converter: Arc<String>,
    },
    Params {
        params: Arc<serde_json::Value>,
    },
    Result {
        result: Arc<serde_json::Value>,
    },
    Summary {
        summary: Arc<String>,
    },
    FinalSummary {
        final_summary: Arc<String>,
    },
    Error {
        error: Arc<ContentCard>,
    },
    Warn {
        warn: Arc<ContentCard>,
    },
    Done {
        history: WorkflowHistory,
    },
}

#[derive(Clone, Default, Debug, Serialize, Deserialize)]
pub struct WorkflowHistory {
    pub nodes: Vec<Arc<WorkflowNodeState>>,
    pub final_summary: Option<Arc<String>>,
}

const SHOULD_INPUT_FIRST_PARAMS: &str = "$should_input_first_params$";

const SHOULD_CONFIRM_START: &str = "$should_confirm_start$";

impl WorkflowActor {
    async fn check_retry_count(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<bool, ActorProcessingErr> {
        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting { retry_count, .. } = executing.as_mut();

            tracing::debug!("当前重试次数: {}", *retry_count);

            if *retry_count > self.runner.get_max_retry_count() {
                tracing::error!("超过最大重试次数");

                self.output_port.send(WorkflowActorNotification::Error {
                    error: Arc::new(ContentCard {
                        title: "超过最大重试次数".to_string(),
                        ..Default::default()
                    }),
                });

                myself.send_message(WorkflowActorEvent::Finish)?;

                return Ok(false);
            }

            *retry_count += 1;
        }

        Ok(true)
    }

    async fn create_input_params_interaction(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始创建输入参数交互");

        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting {
                current:
                    WorkflowNodeState {
                        params, tool, node, ..
                    },
                ..
            } = executing.as_mut();

            let schema = tool.params_schema();
            let schema = if schema.is_null() {
                let schema = node.config.as_ref().and_then(|c| c.input_schema.as_ref());
                match schema {
                    Some(schema) => schema,
                    None => {
                        tracing::error!("未找到输入参数模式，跳过交互");
                        myself.send_message(WorkflowActorEvent::Execute)?;
                        return Ok(());
                    }
                }
            } else {
                &schema
            };

            let default = if let Some(params) = params {
                Some(params.clone())
            } else {
                node.config.as_ref().and_then(|c| c.default_value.clone())
            };

            let interaction = Interaction {
                id: SHOULD_INPUT_FIRST_PARAMS.to_string(),
                title: "输入参数".to_string(),
                content: InteractionContent::Form {
                    form: Form {
                        schema: schema.as_ref().clone(),
                        default: default.as_ref().map(|p| p.as_ref().clone()),
                        ..Default::default()
                    },
                    result: None,
                },
            };

            self.output_port.send(WorkflowActorNotification::Interact {
                interaction: Arc::new(interaction.clone()),
            });

            *state = WorkflowState::Interacting(Box::new(WorkflowStateInteracting {
                interaction,
                executing: Box::new(*executing.clone()),
            }));
        }

        Ok(())
    }

    async fn validate_params(
        &self,
        _myself: ActorRef<WorkflowActorEvent>,
        params: &serde_json::Value,
        tool: &Arc<Tool>,
        node: &Arc<Node>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始验证输入参数");

        let schema = match WorkflowNodeState::create_params_schema(tool, node) {
            Some(schema) => schema,
            None => {
                tracing::error!("未找到输入参数模式，跳过参数验证");
                return Ok(());
            }
        };

        match jsonschema::validator_for(schema.as_ref()) {
            Ok(validator) => match validator.validate(params) {
                Ok(_) => {}
                Err(e) => {
                    tracing::error!("输入参数验证失败: {:?}", e);

                    self.output_port.send(WorkflowActorNotification::Warn {
                        warn: Arc::new(ContentCard {
                            title: "自动重试中".to_string(),
                            description: Some("输入数据格式不符合要求，尝试重新生成".to_string()),
                            details: Some(format!("```plaintext\n{e:?}\n```").into()),
                            ..Default::default()
                        }),
                    });

                    return Err(anyhow::anyhow!("输入参数验证失败: {:?}", e).into());
                }
            },
            Err(e) => {
                tracing::error!("创建输入参数验证器失败: {:?}", e);

                self.output_port.send(WorkflowActorNotification::Warn {
                    warn: Arc::new(ContentCard {
                        title: "创建输入参数验证器失败".to_string(),
                        details: Some(format!("```plaintext\n{e:?}\n```").into()),
                        ..Default::default()
                    }),
                });

                return Err(anyhow::anyhow!("创建输入参数验证器失败: {:?}", e).into());
            }
        }

        Ok(())
    }

    async fn validate_result(
        &self,
        _myself: ActorRef<WorkflowActorEvent>,
        result: &serde_json::Value,
        tool: &Arc<Tool>,
        node: &Arc<Node>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始验证输出参数");

        let schema = match WorkflowNodeState::create_result_schema(tool, node) {
            Some(schema) => schema,
            None => {
                tracing::error!("未找到输出参数模式，跳过参数验证");
                return Ok(());
            }
        };

        match jsonschema::validator_for(schema.as_ref()) {
            Ok(validator) => match validator.validate(result) {
                Ok(_) => {}
                Err(e) => {
                    tracing::error!("输出参数验证失败: {:?}", e);

                    self.output_port.send(WorkflowActorNotification::Warn {
                        warn: Arc::new(ContentCard {
                            title: "任务已中止".to_string(),
                            description: Some(
                                "输出数据格式不符合要求，导致后续步骤无法进行".to_string(),
                            ),
                            details: Some(format!("```plaintext\n{e:?}\n```").into()),
                            ..Default::default()
                        }),
                    });

                    return Err(anyhow::anyhow!("输出参数验证失败: {:?}", e).into());
                }
            },
            Err(e) => {
                tracing::error!("创建输出参数验证器失败: {:?}", e);

                self.output_port.send(WorkflowActorNotification::Warn {
                    warn: Arc::new(ContentCard {
                        title: "创建输出参数验证器失败".to_string(),
                        details: Some(format!("```plaintext\n{e:?}\n```").into()),
                        ..Default::default()
                    }),
                });

                return Err(anyhow::anyhow!("创建结果验证器失败: {:?}", e).into());
            }
        };

        Ok(())
    }
}

impl WorkflowActor {
    async fn move_next(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始移动到下一个节点");
        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting {
                current, history, ..
            } = executing.as_mut();

            let current = Arc::new(core::mem::take(current));

            history.push(current.clone());

            let result = match &current.result {
                Some(result) => result,
                None => {
                    tracing::error!("未找到结果");
                    myself.send_message(WorkflowActorEvent::Finish)?;
                    return Ok(());
                }
            };

            let next_node = self.workflow.eval_next_node(&current.node, result);

            if let Some(next_node) = next_node {
                tracing::debug!("找到下一个节点: id={}", next_node.id);

                let tool = match self
                    .runner
                    .get_tool(&next_node.module, &next_node.tool)
                    .await
                {
                    Some(tool) => tool,
                    None => {
                        tracing::error!("获取工具失败: {}/{}", next_node.module, next_node.tool);

                        myself.send_message(WorkflowActorEvent::Finish)?;

                        self.output_port.send(WorkflowActorNotification::Error {
                            error: Arc::new(ContentCard {
                                title: "获取工具失败".to_string(),
                                description: Some(format!(
                                    "{}/{}",
                                    next_node.module, next_node.tool
                                )),
                                ..Default::default()
                            }),
                        });

                        return Ok(());
                    }
                };

                *state = WorkflowActorState::Executing(Box::new(WorkflowStateExecuting {
                    history: history.clone(),
                    current: WorkflowNodeState {
                        node: next_node.clone(),
                        tool,
                        ..Default::default()
                    },
                    ..Default::default()
                }));

                self.output_port.send(WorkflowActorNotification::Move {
                    node: next_node,
                    workflow_id: self.workflow.id,
                });

                myself.send_message(WorkflowActorEvent::CreateParams)?;
            } else {
                tracing::debug!("没有找到下一个节点");

                myself.send_message(WorkflowActorEvent::Finish)?;
            }
        } else {
            tracing::error!("当前状态不是执行状态");
        }

        Ok(())
    }

    async fn create_params(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始创建参数");

        if !self.check_retry_count(myself.clone(), state).await? {
            return Ok(());
        }

        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting {
                current:
                    WorkflowNodeState {
                        params,
                        tool,
                        node,
                        error,
                        ..
                    },
                history,
                ..
            } = executing.as_mut();

            if node
                .config
                .as_ref()
                .is_none_or(|c| c.properties.as_ref().is_none_or(|p| p.is_empty()))
            {
                let params_schema = WorkflowNodeState::create_params_schema(tool, node);
                // 如果 params_schema 为空，或者类型 Object 但是 properties 为空，则直接返回 {}
                if params_schema.as_ref().is_none_or(|schema| {
                    schema.as_object().is_some_and(|schema_obj| {
                        schema_obj.get("type").is_some_and(|schema_type| {
                            schema_type == "object"
                                && schema_obj.get("properties").is_none_or(|properties| {
                                    properties.as_object().is_none_or(|p| p.is_empty())
                                })
                        })
                    })
                }) {
                    let debug_msg = if params_schema.is_none() {
                        "未找到参数模式，设置默认空对象参数"
                    } else {
                        "参数模式为空对象，设置默认空对象参数"
                    };
                    tracing::debug!("{}", debug_msg);
                    *params = Some(Arc::new(serde_json::json!({})));
                    myself.send_message(WorkflowActorEvent::Execute)?;
                    return Ok(());
                }
            }

            if params.is_some() {
                tracing::debug!("参数已存在，跳过创建参数");
                myself.send_message(WorkflowActorEvent::CreateConverter)?;
                return Ok(());
            }

            let input = serde_json::Map::from_iter(history.iter().map(|h| {
                (
                    h.node.id.clone(),
                    h.result
                        .as_ref()
                        .map(|r| r.as_ref().clone())
                        .unwrap_or_default(),
                )
            }))
            .into();

            let new_params = match node.create_params(&input) {
                Ok(params) => params,
                Err(e) => {
                    tracing::error!("创建参数失败: {:?}", e);
                    self.output_port.send(WorkflowActorNotification::Warn {
                        warn: Arc::new(ContentCard {
                            title: "创建参数失败".to_string(),
                            details: Some(format!("{e:#?}").into()),
                            ..Default::default()
                        }),
                    });
                    None
                }
            };

            if let Some(new_params) = new_params {
                let new_params = Arc::new(new_params);

                *params = Some(new_params.clone());

                self.output_port.send(WorkflowActorNotification::Params {
                    params: new_params.clone(),
                });

                if let Err(e) = self
                    .validate_params(myself.clone(), &new_params, tool, node)
                    .await
                {
                    myself.send_message(WorkflowActorEvent::ExtractParams)?;

                    *error = Some(format!("参数验证失败: {e:#?}"));

                    return Ok(());
                }

                *error = None;

                if history.is_empty() && self.options.input_first_params {
                    self.create_input_params_interaction(myself, state).await?;
                } else {
                    myself.send_message(WorkflowActorEvent::Execute)?;
                }
            } else if let Some(default_value) =
                node.config.as_ref().and_then(|c| c.default_value.as_ref())
            {
                *params = Some(default_value.clone());
                myself.send_message(WorkflowActorEvent::Execute)?;
            } else {
                myself.send_message(WorkflowActorEvent::ExtractParams)?;
            }
        }

        Ok(())
    }

    async fn extract_params(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始提取参数");

        if !self.check_retry_count(myself.clone(), state).await? {
            return Ok(());
        }

        if self.options.skip_extract_params {
            tracing::debug!("跳过提取参数");
            myself.send_message(WorkflowActorEvent::CreateConverter)?;
            return Ok(());
        }

        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting {
                current:
                    WorkflowNodeState {
                        params,
                        tool,
                        node,
                        error,
                        ..
                    },
                history,
                ..
            } = executing.as_mut();

            if !history.is_empty() || params.is_some() {
                myself.send_message(WorkflowActorEvent::CreateConverter)?;
                return Ok(());
            }

            if self.options.input_first_params {
                self.create_input_params_interaction(myself.clone(), state)
                    .await?;
            } else {
                self.output_port.send(WorkflowActorNotification::Views {
                    views: vec![Arc::new(
                        ContentView::Other(serde_json::json!({
                            "format": "tip",
                            "content": {
                                "type": "default",
                                "title": "",
                                "content": "后续可能需要您进行交互操作，请稍作等待",
                                "description": "",
                                "details": ""
                            }
                        }))
                        .into(),
                    )],
                });

                self.output_port.send(WorkflowActorNotification::Views {
                    views: vec![Arc::new(
                        ContentView::Card(ContentCard {
                            r#type: "think".to_string(),
                            title: "思考过程".to_string(),
                            description: Some("正在提取输入参数".into()),
                            ..Default::default()
                        })
                        .into(),
                    )],
                });

                let schema = match WorkflowNodeState::create_params_schema(tool, node) {
                    Some(schema) => schema,
                    None => {
                        tracing::error!("未找到输入参数模式，跳过参数验证");
                        myself.send_message(WorkflowActorEvent::Execute)?;
                        return Ok(());
                    }
                };

                let new_params = match self
                    .runner
                    .extract_params(
                        self.context.as_slice(),
                        self.workflow.clone(),
                        schema,
                        self.tenant_id.clone(),
                    )
                    .await
                {
                    Ok(params) => {
                        tracing::debug!("成功提取参数");
                        Arc::new(params)
                    }
                    Err(e) => {
                        tracing::error!("提取参数失败: {:?}", e);
                        myself.send_message(WorkflowActorEvent::CreateConverter)?;

                        return Ok(());
                    }
                };

                *params = Some(new_params.clone());

                self.output_port.send(WorkflowActorNotification::Params {
                    params: new_params.clone(),
                });

                match self
                    .validate_params(myself.clone(), new_params.as_ref(), tool, node)
                    .await
                {
                    Ok(_) => {
                        *error = None;
                        myself.send_message(WorkflowActorEvent::Execute)?;
                    }
                    Err(e) => {
                        *error = Some(e.to_string());
                        myself.send_message(WorkflowActorEvent::CreateConverter)?;
                    }
                }
            }
        } else {
            tracing::error!("当前状态不是执行状态");
        }

        Ok(())
    }

    async fn create_converter(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始创建转换器");

        if !self.check_retry_count(myself.clone(), state).await? {
            return Ok(());
        }

        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting {
                current, history, ..
            } = executing.as_mut();

            let old_state = Arc::new(current.clone());

            let WorkflowNodeState {
                converter,
                params,
                tool,
                node,
                error,
                ..
            } = current;

            let (converter, can_retry) = if let Some(input_converter) = node
                .config
                .as_ref()
                .and_then(|c| c.input_converter.as_ref())
            {
                tracing::debug!("使用内置转换器");

                let c = match input_converter.as_ref() {
                    ScriptConverter::Python { code } => code.clone(),
                };

                *converter = Some(c.clone());

                (c, false)
            } else {
                if self.options.skip_converter {
                    tracing::debug!("跳过创建转换器");
                    myself.send_message(WorkflowActorEvent::Execute)?;
                    return Ok(());
                }

                let c = match self
                    .runner
                    .generate_converter(
                        self.context.as_slice(),
                        self.workflow.clone(),
                        history.clone(),
                        old_state,
                        self.tenant_id.clone(),
                    )
                    .await
                {
                    Ok(c) => {
                        tracing::debug!("成功生成转换函数");
                        let c = Arc::new(c.clone());
                        *converter = Some(c.clone());

                        self.output_port.send(WorkflowActorNotification::Converter {
                            converter: c.clone(),
                        });

                        c
                    }
                    Err(e) => {
                        *error = Some(format!("生成转换函数失败: \n{e:#?}"));

                        tracing::error!("生成转换函数失败: {:#?}", e);

                        self.output_port.send(WorkflowActorNotification::Error {
                            error: Arc::new(ContentCard {
                                title: "生成转换函数失败".to_string(),
                                details: Some(format!("{e:#?}").into()),
                                ..Default::default()
                            }),
                        });

                        myself.send_message(WorkflowActorEvent::CreateConverter)?;

                        return Ok(());
                    }
                };

                (c, true)
            };

            let retry_event = if can_retry {
                WorkflowActorEvent::CreateConverter
            } else {
                WorkflowActorEvent::Finish
            };

            match self
                .runner
                .call_converter(&converter, history.clone())
                .await
            {
                Ok(result) => {
                    tracing::debug!("成功调用转换器");

                    *params = Some(Arc::new(result.clone()));

                    self.output_port.send(WorkflowActorNotification::Params {
                        params: Arc::new(result.clone()),
                    });

                    if let Err(e) = self
                        .validate_params(myself.clone(), &result, tool, node)
                        .await
                    {
                        *error = Some(format!("参数验证失败: \n{e:#?}"));

                        myself.send_message(retry_event)?;

                        return Ok(());
                    }
                }
                Err(e) => {
                    tracing::error!("调用转换器失败: {:?}", e);

                    *error = Some(format!("调用转换器失败: \n{e:#?}"));

                    self.output_port.send(WorkflowActorNotification::Warn {
                        warn: Arc::new(ContentCard {
                            title: "调用转换函数失败".to_string(),
                            details: Some(format!("{e:#?}").into()),
                            ..Default::default()
                        }),
                    });

                    myself.send_message(retry_event)?;

                    return Ok(());
                }
            };

            *error = None;
            myself.send_message(WorkflowActorEvent::Execute)?;
        } else {
            tracing::error!("当前状态不是执行状态");
        }

        Ok(())
    }

    async fn start(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始启动工作流");

        if self.options.confirm_start {
            match state {
                WorkflowActorState::Interacting(_) => {
                    tracing::debug!("交互不匹配或者没有结果，不继续执行");
                    return Ok(());
                }
                WorkflowActorState::Executing(_) => {
                    tracing::debug!("当前状态是执行状态，开始执行");
                }
                WorkflowActorState::Ready => {
                    self.output_port.send(WorkflowActorNotification::Interact {
                        interaction: Arc::new(Interaction {
                            id: SHOULD_CONFIRM_START.to_string(),
                            title: "开始工作流".to_string(),
                            content: InteractionContent::ExecuteConfirm {
                                execute_confirm: ExecuteConfirmContent {
                                    workflow_id: self.workflow.id,
                                },
                                result: None,
                            },
                        }),
                    });

                    *state = WorkflowActorState::Interacting(Box::new(WorkflowStateInteracting {
                        executing: Box::new(WorkflowStateExecuting::default()),
                        interaction: Interaction {
                            id: "start".to_string(),
                            title: "开始工作流".to_string(),
                            content: InteractionContent::ExecuteConfirm {
                                execute_confirm: ExecuteConfirmContent {
                                    workflow_id: self.workflow.id,
                                },
                                result: None,
                            },
                        },
                    }));

                    return Ok(());
                }
                _ => {
                    tracing::debug!("当前状态不是交互状态或准备状态");
                    return Ok(());
                }
            }
        }

        let first_node = match self.workflow.first_node() {
            Some(node) => node,
            None => {
                tracing::error!("未找到第一个节点");
                self.output_port.send(WorkflowActorNotification::Error {
                    error: Arc::new(ContentCard {
                        title: "未找到第一个节点".to_string(),
                        ..Default::default()
                    }),
                });
                self.output_port.send(WorkflowActorNotification::Done {
                    history: WorkflowHistory::default(),
                });
                *state = WorkflowActorState::Done;
                return Ok(());
            }
        };

        let tool = match self
            .runner
            .get_tool(&first_node.module, &first_node.tool)
            .await
        {
            Some(tool) => tool,
            None => {
                tracing::error!("获取工具失败: {}/{}", first_node.module, first_node.tool);

                myself.send_message(WorkflowActorEvent::Finish)?;

                self.output_port.send(WorkflowActorNotification::Error {
                    error: Arc::new(ContentCard {
                        title: "获取工具失败".to_string(),
                        description: Some(format!("{}/{}", first_node.module, first_node.tool)),
                        ..Default::default()
                    }),
                });

                return Ok(());
            }
        };

        *state = WorkflowActorState::Executing(Box::new(WorkflowStateExecuting {
            current: WorkflowNodeState {
                node: first_node.clone(),
                tool,
                ..Default::default()
            },
            ..Default::default()
        }));

        self.output_port.send(WorkflowActorNotification::Move {
            node: first_node,
            workflow_id: self.workflow.id,
        });

        myself.send_message(WorkflowActorEvent::CreateParams)?;

        tracing::debug!("工作流启动完成");

        Ok(())
    }

    async fn execute(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始执行节点");
        if let WorkflowActorState::Executing(executing_state) = state {
            let WorkflowStateExecuting {
                history,
                current:
                    WorkflowNodeState {
                        node: current_node,
                        params,
                        result,
                        tool,
                        error,
                        views,
                        ..
                    },
                ..
            } = executing_state.as_mut();

            self.output_port.send(WorkflowActorNotification::Execute {
                current_node: current_node.clone(),
            });

            let params = match params {
                Some(params) => params,
                None => {
                    tracing::error!("未找到参数");

                    myself.send_message(WorkflowActorEvent::Finish)?;

                    self.output_port.send(WorkflowActorNotification::Error {
                        error: Arc::new(ContentCard {
                            title: "没有参数生成".to_string(),
                            ..Default::default()
                        }),
                    });

                    return Ok(());
                }
            };

            let (temp_view_sender, temp_view_receiver) =
                tokio::sync::mpsc::channel::<serde_json::Value>(1);

            let output_port = self.output_port.clone();

            let temp_views = Arc::new(std::sync::Mutex::new(Vec::new()));

            let temp_views_clone = temp_views.clone();

            let view_handle = tokio::spawn(async move {
                let stream = tokio_stream::wrappers::ReceiverStream::new(temp_view_receiver)
                    .take_while(|v| *v != serde_json::Value::Null)
                    .filter_map(|v| serde_json::from_value(v).ok().map(Arc::new))
                    .chunks_timeout(1024, std::time::Duration::from_secs(1));

                tokio::pin!(stream);

                while let Some(views) = stream.next().await {
                    output_port.send(WorkflowActorNotification::Views {
                        views: views.clone(),
                    });

                    temp_views_clone.lock().unwrap().extend(views);
                }
            });

            match self
                .runner
                .call_tool(CallToolInput {
                    module_name: tool.module_name().to_string(),
                    tool_name: tool.name().to_string(),
                    session_id: self.session_id.to_string(),
                    user_id: self.user_id.clone().unwrap_or_default(),
                    tenant_id: self.tenant_id.clone(),
                    payload: params.as_ref().clone(),
                    temp_view_sender: Some(temp_view_sender.clone()),
                    ..Default::default()
                })
                .then(|r| async move {
                    let _ = temp_view_sender.send(serde_json::Value::Null).await;
                    drop(temp_view_sender);
                    let _ = view_handle.await;

                    views.extend(
                        temp_views
                            .lock()
                            .unwrap()
                            .iter()
                            .map(|v| Arc::new(serde_json::to_value(v).unwrap_or_default())),
                    );

                    r
                })
                .await
            {
                Ok(r) => {
                    tracing::debug!("工具调用成功");
                    let r = Arc::new(r.clone());

                    *error = None;
                    *result = Some(r.clone());

                    self.output_port
                        .send(WorkflowActorNotification::Result { result: r.clone() });

                    match self
                        .validate_result(myself.clone(), &r, tool, current_node)
                        .await
                    {
                        Ok(_) => {
                            tracing::debug!("输出参数验证通过");
                        }
                        Err(e) => {
                            *error = Some(e.to_string());
                            myself.send_message(WorkflowActorEvent::Finish)?;
                            return Ok(());
                        }
                    }

                    myself.send_message(WorkflowActorEvent::Summary)?;
                }
                Err(e) => {
                    if let error::Error::Interaction(interaction) = e {
                        let mut interaction: Interaction = serde_json::from_value(interaction)?;

                        let prepared_result = self
                            .workflow
                            .runtime
                            .as_ref()
                            .and_then(|r| r.interactions.get(&interaction.id));

                        if let Some(prepared_result) = prepared_result {
                            match interaction.content.set_result(prepared_result.clone()) {
                                Ok(_) => {
                                    match self
                                        .runner
                                        .set_interaction(
                                            &self.session_id.to_string(),
                                            interaction.id.as_str(),
                                            interaction.clone(),
                                        )
                                        .await
                                    {
                                        Ok(_) => {
                                            self.output_port.send(
                                                WorkflowActorNotification::Interact {
                                                    interaction: Arc::new(interaction.clone()),
                                                },
                                            );

                                            myself.send_message(WorkflowActorEvent::Execute)?;
                                        }
                                        Err(e) => {
                                            tracing::error!("设置交互失败: {:?}", e);

                                            self.output_port.send(
                                                WorkflowActorNotification::Error {
                                                    error: Arc::new(ContentCard {
                                                        title: "设置交互失败".to_string(),
                                                        details: Some(format!("{e:#?}").into()),
                                                        ..Default::default()
                                                    }),
                                                },
                                            );

                                            myself.send_message(WorkflowActorEvent::Finish)?;
                                        }
                                    }

                                    return Ok(());
                                }
                                Err(e) => tracing::error!("使用预设交互失败: {e}"),
                            }
                        }

                        if self.options.prevent_interaction {
                            tracing::debug!("需要交互，但是禁止交互");
                            *error =
                                Some(format!("需要交互，但是禁止交互: [{}]", interaction.title));
                            myself.send_message(WorkflowActorEvent::Finish)?;
                            return Ok(());
                        }

                        tracing::debug!("需要交互: {:?}", interaction);

                        self.output_port.send(WorkflowActorNotification::Interact {
                            interaction: Arc::new(interaction.clone()),
                        });

                        *state =
                            WorkflowActorState::Interacting(Box::new(WorkflowStateInteracting {
                                executing: Box::new(*executing_state.clone()),
                                interaction: interaction.clone(),
                            }));

                        return Ok(());
                    }

                    tracing::error!("调用工具失败: {:#?}", e);

                    self.output_port.send(WorkflowActorNotification::Error {
                        error: Arc::new(ContentCard {
                            title: "调用工具失败".to_string(),
                            details: Some(format!("```plaintext\n{e:#?}\n```").into()),
                            ..Default::default()
                        }),
                    });

                    *error = Some(format!("执行错误: \n{e:#?}"));

                    if history.is_empty() {
                        myself.send_message(WorkflowActorEvent::ExtractParams)?;
                    } else {
                        myself.send_message(WorkflowActorEvent::CreateConverter)?;
                    }
                }
            }
        } else {
            tracing::error!("当前状态不是执行状态");
        }

        Ok(())
    }

    async fn validate_value(
        &self,
        module_name: &str,
        schema: &serde_json::Value,
        value: &serde_json::Value,
    ) -> Result<(), error::Error> {
        let will_validate_fields = Arc::new(std::sync::Mutex::new(Vec::new()));

        #[derive(Clone)]
        struct ValidateField {
            payload: Option<Arc<serde_json::Value>>,
            validator: Arc<String>,
            data: Arc<serde_json::Value>,
        }

        struct NoopValidator {
            fields: Arc<std::sync::Mutex<Vec<ValidateField>>>,
            validator: Arc<String>,
            payload: Option<Arc<serde_json::Value>>,
        }

        impl Keyword for NoopValidator {
            fn validate<'i>(
                &self,
                instance: &'i serde_json::Value,
                _location: &LazyLocation,
            ) -> Result<(), ValidationError<'i>> {
                self.fields.lock().unwrap().push(ValidateField {
                    payload: self.payload.clone(),
                    validator: self.validator.clone(),
                    data: Arc::new(instance.clone()),
                });
                Ok(())
            }

            fn is_valid(&self, instance: &serde_json::Value) -> bool {
                self.fields.lock().unwrap().push(ValidateField {
                    payload: self.payload.clone(),
                    validator: self.validator.clone(),
                    data: Arc::new(instance.clone()),
                });

                true
            }
        }

        let will_validate_fields_clone = will_validate_fields.clone();

        let validator = jsonschema::options()
            .with_keyword("x-validator", move |parent, value, _| {
                Ok(Box::new(NoopValidator {
                    fields: will_validate_fields_clone.clone(),
                    validator: Arc::new(value.as_str().unwrap_or_default().to_string()),
                    payload: parent
                        .get("x-validator-payload")
                        .map(|v| Arc::new(v.clone())),
                }))
            })
            .build(schema)?;

        validator.validate(value)?;

        // 在 schema 中，递归查找所有带有 x-validator 的字段，获取 x-validator 的值，以及该字段的值

        let fields = will_validate_fields.lock().unwrap().clone();

        for ValidateField {
            validator,
            data,
            payload,
        } in fields.into_iter()
        {
            tracing::debug!("验证器: {:?}, 值: {:?}", validator, data);

            self.runner
                .call_validator(CallValidatorInput {
                    module_name: module_name.to_string(),
                    validator_name: validator.to_string(),
                    session_id: self.session_id.to_string(),
                    value: data.as_ref().clone(),
                    payload: payload.as_deref().cloned(),
                    ..Default::default()
                })
                .await?;
        }

        Ok(())
    }

    async fn interact(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
        result: InteractionResult,
        reply: RpcReplyPort<Result<(), error::Error>>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始处理交互结果");
        if let WorkflowActorState::Interacting(interacting) = state {
            let WorkflowStateInteracting {
                executing,
                interaction,
            } = interacting.as_mut();

            result.apply(&mut interaction.content);

            if let InteractionContent::Form {
                form,
                result: Some(result),
            } = &interaction.content
            {
                if let Err(e) = self
                    .validate_value(executing.current.tool.module_name(), &form.schema, result)
                    .await
                {
                    tracing::error!("交互表单验证失败: {:?}", e);
                    let _ = reply.send(Err(e));
                    return Ok(());
                }
            }

            let _ = reply.send(Ok(()));

            if interaction.id.as_str() == SHOULD_INPUT_FIRST_PARAMS {
                if let Some(result) = interaction.content.get_result() {
                    executing.current.params = Some(Arc::new(result));

                    self.output_port
                        .send(WorkflowActorNotification::InteractDone {
                            interaction: Arc::new(interaction.clone()),
                        });

                    *state = WorkflowActorState::Executing(Box::new(*executing.clone()));

                    myself.send_message(WorkflowActorEvent::Execute)?;
                }

                return Ok(());
            }

            match self
                .runner
                .set_interaction(
                    &self.session_id.to_string(),
                    interaction.id.as_str(),
                    interaction.clone(),
                )
                .await
            {
                Ok(_) => {
                    tracing::debug!("成功设置交互结果");
                    self.output_port
                        .send(WorkflowActorNotification::InteractDone {
                            interaction: Arc::new(interaction.clone()),
                        });

                    if matches!(
                        interaction.content,
                        InteractionContent::ExecuteConfirm { .. }
                    ) {
                        *state = WorkflowActorState::Executing(Box::new(*executing.clone()));

                        myself.send_message(WorkflowActorEvent::Start)?;
                    } else {
                        *state = WorkflowActorState::Executing(Box::new(*executing.clone()));

                        myself.send_message(WorkflowActorEvent::Execute)?;
                    }
                }
                Err(e) => {
                    tracing::error!("设置交互失败: {:?}", e);

                    self.output_port.send(WorkflowActorNotification::Error {
                        error: Arc::new(ContentCard {
                            title: "设置交互失败".to_string(),
                            details: Some(format!("{e:#?}").into()),
                            ..Default::default()
                        }),
                    });

                    myself.send_message(WorkflowActorEvent::Finish)?;
                }
            }
        } else {
            tracing::error!("当前状态不是交互状态");
        }
        Ok(())
    }

    async fn summary(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("开始生成总结");
        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting {
                history, current, ..
            } = executing.as_mut();

            if !current.tool.skip_summary() && !self.options.skip_summary {
                let summary = self
                    .runner
                    .generate_summary(
                        self.context.as_slice(),
                        self.workflow.clone(),
                        history.clone(),
                        Arc::new(current.clone()),
                    )
                    .await?;

                tracing::debug!("成功生成总结");

                let summary = Arc::new(summary);

                current.summary = Some(summary.clone());

                self.output_port
                    .send(WorkflowActorNotification::Summary { summary });
            } else {
                tracing::debug!("跳过总结生成");
            }

            myself.send_message(WorkflowActorEvent::MoveNext)?;
        } else {
            tracing::error!("当前状态不是执行状态");
        }

        Ok(())
    }

    async fn final_summary(
        &self,
        _myself: ActorRef<WorkflowActorEvent>,
        state: &mut WorkflowActorState,
    ) -> Result<(), ActorProcessingErr> {
        if let WorkflowActorState::Executing(executing) = state {
            let WorkflowStateExecuting {
                history, current, ..
            } = executing.as_mut();

            history.push(Arc::new(current.clone()));

            if !self.options.skip_final_summary {
                tracing::debug!("开始生成最终总结");
                let final_summary = self
                    .runner
                    .generate_final_summary(
                        self.context.as_slice(),
                        self.workflow.clone(),
                        history.clone(),
                    )
                    .await?;

                let final_summary = Arc::new(final_summary);

                tracing::debug!("成功生成最终总结");
                self.output_port
                    .send(WorkflowActorNotification::FinalSummary {
                        final_summary: final_summary.clone(),
                    });

                self.output_port.send(WorkflowActorNotification::Done {
                    history: WorkflowHistory {
                        nodes: history.clone(),
                        final_summary: Some(final_summary),
                    },
                });

                return Ok(());
            }

            tracing::debug!("跳过生成最终总结");

            self.output_port.send(WorkflowActorNotification::Done {
                history: WorkflowHistory {
                    nodes: history.clone(),
                    final_summary: None,
                },
            });
        } else {
            tracing::error!("当前状态不是执行状态");
        }

        Ok(())
    }

    async fn handle_error(
        &self,
        myself: ActorRef<WorkflowActorEvent>,
        _state: &mut WorkflowActorState,
        error: ActorProcessingErr,
    ) -> Result<(), ActorProcessingErr> {
        self.output_port.send(WorkflowActorNotification::Error {
            error: Arc::new(ContentCard {
                title: "工作流终止或失败".to_string(),
                details: Some(format!("{error:#?}").into()),
                ..Default::default()
            }),
        });

        tracing::error!("workflow actor terminated or failed: {:?}", error);

        myself.stop(None);

        Ok(())
    }
}

#[async_trait::async_trait]
impl Actor for WorkflowActor {
    type Msg = WorkflowActorEvent;
    type State = WorkflowActorState;
    type Arguments = ();

    async fn pre_start(
        &self,
        _myself: ActorRef<Self::Msg>,
        _args: Self::Arguments,
    ) -> Result<Self::State, ActorProcessingErr> {
        Ok(WorkflowActorState::Ready)
    }

    async fn handle(
        &self,
        myself: ActorRef<Self::Msg>,
        message: Self::Msg,
        mut state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!(
            "WorkflowActor handle message: {:?}, state: {:?}",
            message,
            state
        );

        let actor = myself.clone();

        let result = match (&mut state, message) {
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::MoveNext) => {
                self.move_next(myself, state).await
            }
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::ExtractParams) => {
                self.extract_params(myself, state).await
            }
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::CreateParams) => {
                self.create_params(myself, state).await
            }
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::CreateConverter) => {
                self.create_converter(myself, state).await
            }
            (WorkflowActorState::Ready, WorkflowActorEvent::Start) => {
                self.start(myself, state).await
            }
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::Start) => {
                self.start(myself, state).await
            }
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::Execute) => {
                self.execute(myself, state).await
            }
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::Summary) => {
                self.summary(myself, state).await
            }
            (WorkflowActorState::Executing { .. }, WorkflowActorEvent::Finish) => {
                self.final_summary(myself, state).await
            }
            (
                WorkflowActorState::Interacting { .. },
                WorkflowActorEvent::Interact(result, reply),
            ) => self.interact(myself, state, result, reply).await,
            (other_state, other_message) => {
                tracing::warn!(
                    "unhandled message: {:?}, state: {:?}",
                    other_message,
                    other_state
                );

                Ok(())
            }
        };

        match result {
            Ok(()) => Ok(()),
            Err(e) => self.handle_error(actor, state, e).await,
        }
    }
}
