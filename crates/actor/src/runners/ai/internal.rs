use std::{borrow::Cow, sync::Arc};

use ai::{
    ChatDel<PERSON>, ChatRequest, ChatStreamResponse, Content, ExploreController, ExploreRequest,
    ExploreStream, Message, ParseState, Role, parse_stream, parse_stream_with_state,
};
use futures::{SinkExt as _, StreamExt as _};
use reqwest_eventsource::{Event, RequestBuilderExt};
use reqwest_websocket::RequestBuilderExt as _;
use serde::Deserialize;
use workflow::Workflow;

use crate::{ChatDeltaStream, ModelRunner};

pub struct InternalModel {
    pub chat_api: String,
    pub explore_api: String,
    pub arguments_api: String,
    pub search_source_api: String,
    pub ai_api_base: String,
    pub ai_api_key: Option<String>,
    pub ai_api_model: Option<String>,
    pub ai_api_version: Option<String>,
}

impl InternalModel {
    fn convert_messages(&self, messages: &[Message]) -> Vec<Message> {
        messages
            .iter()
            .map(|message| match &message.role {
                Role::System | Role::User | Role::Assistant => message.clone(),
                Role::Tool => Message::assistant_content(message.content.clone()),
            })
            .collect()
    }
}

#[async_trait::async_trait]
impl ModelRunner for InternalModel {
    async fn chat_workflow(&self, messages: &[Message]) -> error::Result<ChatDeltaStream> {
        let mut stream = reqwest::ClientBuilder::new()
            .no_proxy()
            .build()?
            .post(&self.chat_api)
            .json(&serde_json::json!({
                "messages": self.convert_messages(messages),
            }))
            .eventsource()
            .unwrap();

        stream.set_retry_policy(Box::new(reqwest_eventsource::retry::Never));

        let stream = parse_stream(
            stream
                .take_while(|event| {
                    if let Ok(Event::Message(message)) = event {
                        if message.data == "[DONE]" {
                            return std::future::ready(false);
                        }
                    }
                    std::future::ready(true)
                })
                .filter_map(|event| async move {
                    match event {
                        Ok(Event::Message(message)) => Some(Ok(message.data)),
                        Err(e) => Some(Err(e.into())),
                        _ => None,
                    }
                }),
        );

        Ok(ChatDeltaStream::new(stream))
    }

    async fn chat_explore(
        &self,
        topic: String,
        session_id: uuid::Uuid,
    ) -> error::Result<ExploreStream> {
        let ws = reqwest::ClientBuilder::new()
            .no_proxy()
            .build()?
            .get(format!("{}/{}", self.explore_api, session_id))
            .upgrade()
            .send()
            .await?
            .into_websocket()
            .await?;

        let mut stream = ExploreController(ws);

        stream
            .send(ExploreRequest::ResearchTopic { content: topic })
            .await?;

        Ok(Box::pin(stream))
    }

    async fn chat_stream(&self, messages: &[Message]) -> error::Result<ChatDeltaStream> {
        let version = self.ai_api_version.clone().unwrap_or_default();

        let client = async_openai::Client::with_config(
            async_openai::config::OpenAIConfig::new()
                .with_api_base(self.ai_api_base.clone())
                .with_api_key(self.ai_api_key.clone().unwrap_or_default()),
        )
        .with_http_client(reqwest::ClientBuilder::new().no_proxy().build()?);

        match version.as_str() {
            "v2.1" => {
                // add /no_think to first message
                let mut messages = messages.to_vec();
                if let Some(message) = messages.first_mut() {
                    let Content::Text(content) = &mut message.content;
                    content.insert_str(0, "/no_think");
                }

                let stream = client
                    .chat()
                    .create_stream_byot::<_, serde_json::Value>(serde_json::json! ({
                        "messages": messages,
                        "model": self.ai_api_model.clone().unwrap_or_default(),
                        "stream": true,
                        "top_p": 0.8,
                        "top_k": 20,
                        "temperature": 0.7
                    }))
                    .await?;

                Ok(ChatDeltaStream::new(parse_stream(stream.filter_map(
                    |event| async move {
                        if let Ok(event) = event {
                            let delta = event
                                .as_object()
                                .and_then(|delta| delta.get("choices"))
                                .and_then(|choices| choices.as_array())
                                .and_then(|choices| choices.first())
                                .and_then(|choice| choice.as_object())
                                .and_then(|choice| choice.get("delta"))
                                .and_then(|delta| delta.as_object())
                                .and_then(|delta| delta.get("content"))
                                .and_then(|content| content.as_str())
                                .map(|content| Ok(content.to_string()));

                            return delta;
                        }

                        None
                    },
                ))))
            }
            "v2" => {
                let stream = client
                    .chat()
                    .create_stream_byot::<_, serde_json::Value>(serde_json::json! ({
                        "messages": messages,
                        "model": self.ai_api_model.clone().unwrap_or_default(),
                        "stream": true,
                        "temperature": 0.6,
                        "top_p": 0.95,
                        "top_k": 20,
                    }))
                    .await?;

                let stream = stream.take_while(|event| {
                    if let Ok(event) = event {
                        if event
                            .as_object()
                            .and_then(|o| o.get("choices"))
                            .and_then(|c| c.as_array())
                            .is_none_or(|c| c.is_empty())
                        {
                            return std::future::ready(false);
                        }
                    }
                    std::future::ready(true)
                });

                let (think_sender, think_receiver) = tokio::sync::mpsc::channel(1024);
                let (text_sender, text_receiver) = tokio::sync::mpsc::channel(1024);

                tokio::spawn(async move {
                    let mut stream = stream;
                    while let Some(Ok(delta)) = stream.next().await {
                        let delta = delta
                            .as_object()
                            .and_then(|delta| delta.get("choices"))
                            .and_then(|choices| choices.as_array())
                            .and_then(|choices| choices.first())
                            .and_then(|choice| choice.as_object())
                            .and_then(|choice| choice.get("delta"))
                            .and_then(|delta| delta.as_object());

                        if let Some(delta) = delta {
                            if let Some(reasoning_content) = delta.get("reasoning_content") {
                                if let Some(reasoning_content) = reasoning_content.as_str() {
                                    let _ = think_sender
                                        .send(Ok(ChatDelta::Think(reasoning_content.to_string())))
                                        .await;
                                }
                            }

                            if let Some(content) = delta.get("content") {
                                if let Some(content) = content.as_str() {
                                    let _ = text_sender.send(Ok(content.to_string())).await;
                                }
                            }
                        } else {
                            return;
                        }
                    }
                });

                let think_stream = tokio_stream::wrappers::ReceiverStream::new(think_receiver);
                let text_stream =
                    parse_stream(tokio_stream::wrappers::ReceiverStream::new(text_receiver));

                {
                    use tokio_stream::StreamExt as _;

                    Ok(ChatDeltaStream::new(think_stream.merge(text_stream)))
                }
            }
            _ => {
                let stream = client
                    .chat()
                    .create_stream_byot::<_, ChatStreamResponse>(ChatRequest {
                        messages: Cow::Borrowed(messages),
                        model: self.ai_api_model.clone().unwrap_or_default(),
                        stream: Some(true),
                    })
                    .await?;

                let initial_state = ParseState::TagContent {
                    tag: ChatDelta::THINK,
                    delta_builder: ChatDelta::get_builder(ChatDelta::THINK),
                };

                let stream = parse_stream_with_state(
                    stream
                        .take_while(|event| {
                            if let Ok(event) = event {
                                if event.choices.is_empty() {
                                    return std::future::ready(false);
                                }
                            }
                            std::future::ready(true)
                        })
                        .filter_map(|event| async move {
                            match event {
                                Ok(event) => event
                                    .choices
                                    .first()
                                    .and_then(|c| c.delta.content.clone())
                                    .map(Ok),
                                Err(e) => Some(Err(e.into())),
                            }
                        }),
                    initial_state,
                );

                Ok(ChatDeltaStream::new(stream))
            }
        }
    }

    async fn extract_params(
        &self,
        messages: &[Message],
        schema: Arc<serde_json::Value>,
        workflow: Arc<Workflow>,
        tenant_id: String,
    ) -> error::Result<serde_json::Value> {
        #[derive(Deserialize, Debug)]
        struct Response {
            #[serde(rename = "isSuccess")]
            is_success: bool,
            param: Option<serde_json::Value>,
        }

        let workflow_steps: Vec<serde_json::Value> = workflow.nodes.iter().map(|node| {
            let deps: Vec<String> = workflow.edges.iter()
                .filter(|edge| edge.target_node == node.id)
                .map(|edge| edge.source_node.clone())
                .collect();

            serde_json::json!({
                "id": node.id,
                "tool": node.module,
                "ability": node.tool,
                "dep": if deps.is_empty() { serde_json::Value::Null } else { serde_json::json!(deps) }
            })
        }).collect();

        let input = serde_json::json!({
            "messages": self.convert_messages(messages),
            "schema": schema,
            "workflow": workflow_steps,
            "tenant_id": tenant_id,
        });

        let response = reqwest::ClientBuilder::new()
            .no_proxy()
            .build()?
            .post(&self.arguments_api)
            .json(&input)
            .send()
            .await?
            .json::<Response>()
            .await?;

        tracing::debug!(name = "extract_params", input =? input, response = ?response);

        if response.is_success {
            Ok(response.param.unwrap_or(serde_json::Value::Null))
        } else {
            Err(error::Error::OpenAI(
                async_openai::error::OpenAIError::ApiError(async_openai::error::ApiError {
                    message: "Failed to extract params".to_string(),
                    r#type: None,
                    param: None,
                    code: None,
                }),
            ))
        }
    }

    async fn search_source(
        &self,
        messages: &[Message],
        schema: Arc<serde_json::Value>,
        workflow: Arc<Workflow>,
        tenant_id: String,
    ) -> error::Result<String> {
        #[derive(Deserialize, Debug)]
        struct Response {
            #[serde(rename = "isSuccess")]
            is_success: bool,
            param: Option<String>,
        }

        let workflow_steps: Vec<serde_json::Value> = workflow.nodes.iter().map(|node| {
            let deps: Vec<String> = workflow.edges.iter()
                .filter(|edge| edge.target_node == node.id)
                .map(|edge| edge.source_node.clone())
                .collect();

            serde_json::json!({
                "id": node.id,
                "tool": node.module,
                "ability": node.tool,
                "dep": if deps.is_empty() { serde_json::Value::Null } else { serde_json::json!(deps) }
            })
        }).collect();

        let input = serde_json::json!({
            "messages": self.convert_messages(messages),
            "schema": schema,
            "workflow": workflow_steps,
            "tenant_id": tenant_id,
        });

        let mut response = reqwest::ClientBuilder::new()
            .no_proxy()
            .build()?
            .post(&self.search_source_api)
            .json(&input)
            .send()
            .await?
            .json::<Response>()
            .await?;

        tracing::debug!(name = "search_source", input =? input, response = ?response);

        // TODO: remove this
        response.is_success = true;

        if response.is_success {
            Ok(response.param.unwrap_or_default())
        } else {
            Err(error::Error::OpenAI(
                async_openai::error::OpenAIError::ApiError(async_openai::error::ApiError {
                    message: "Failed to search source".to_string(),
                    r#type: None,
                    param: None,
                    code: None,
                }),
            ))
        }
    }
}
