use std::{
    pin::Pin,
    sync::Arc,
    task::{Context, Poll},
};

use ai::Chat<PERSON><PERSON><PERSON>;
use futures::{Stream, StreamExt as _};

use crate::ChatDeltaStream;

pin_project_lite::pin_project! {
    pub struct ChatDeltaBlockStream {
        #[pin]
        stream: ChatDeltaStream,
        deltas: Vec<Arc<ChatDelta>>,
        done: bool,
    }
}

impl ChatDeltaBlockStream {
    pub async fn done(self) -> Vec<Arc<ChatDelta>> {
        let mut stream = self;

        while let Some(Ok(delta)) = stream.next().await {
            if let ChatDeltaBlock::Done(deltas) = delta {
                return deltas;
            }
        }

        vec![]
    }
}

#[derive(Debug, Clone)]
pub enum ChatDeltaBlock {
    Delta(Arc<ChatDelta>),
    Block(Arc<ChatDelta>),
    Done(Vec<Arc<ChatDelta>>),
}

impl ChatDeltaBlock {
    pub fn done(deltas: Vec<Arc<ChatDelta>>) -> Self {
        let mut merged = vec![];
        let mut current = None;

        for delta in deltas {
            match current {
                None => {
                    current = Some(delta.as_ref().clone());
                    continue;
                }
                Some(mut old) => match old.append_or_new(delta.as_ref().clone()) {
                    None => {
                        current = Some(old);
                    }
                    Some(new) => {
                        merged.push(old);
                        current = Some(new);
                    }
                },
            }
        }

        if let Some(current) = current {
            merged.push(current);
        }

        Self::Done(merged.into_iter().map(Arc::new).collect())
    }
}

impl Stream for ChatDeltaBlockStream {
    type Item = error::Result<ChatDeltaBlock>;

    fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let mut this = self.as_mut().project();

        if *this.done {
            return Poll::Ready(None);
        }

        match this.stream.as_mut().poll_next(cx) {
            Poll::Ready(Some(Ok(delta))) => {
                let delta = Arc::new(delta);

                let deltas = this.deltas;

                let has_new_block = match deltas.iter().last() {
                    Some(last) => last.split().0 != delta.split().0,
                    None => true,
                };

                deltas.push(delta.clone());

                if has_new_block {
                    return Poll::Ready(Some(Ok(ChatDeltaBlock::Block(delta.clone()))));
                }

                Poll::Ready(Some(Ok(ChatDeltaBlock::Delta(delta))))
            }
            Poll::Ready(Some(Err(e))) => Poll::Ready(Some(Err(e))),
            Poll::Ready(None) => {
                *this.done = true;
                Poll::Ready(Some(Ok(ChatDeltaBlock::done(core::mem::take(this.deltas)))))
            }
            Poll::Pending => Poll::Pending,
        }
    }
}

impl ChatDeltaStream {
    pub fn into_block_stream(self) -> ChatDeltaBlockStream {
        ChatDeltaBlockStream {
            stream: self,
            deltas: vec![],
            done: false,
        }
    }
}
