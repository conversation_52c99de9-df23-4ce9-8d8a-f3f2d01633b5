use std::{
    pin::Pin,
    sync::Arc,
    task::{Context, Poll},
};

use ai::{Chat<PERSON><PERSON><PERSON>, ExploreStream, Message};
use futures::Stream;
use workflow::Workflow;

mod ext;
mod internal;

pub use ext::*;
pub use internal::*;

pub struct ChatDeltaStream {
    inner: Pin<Box<dyn Stream<Item = error::Result<ChatDelta>> + Send>>,
}

impl ChatDeltaStream {
    pub fn new(inner: impl Stream<Item = error::Result<ChatDelta>> + Send + 'static) -> Self {
        Self {
            inner: Box::pin(inner),
        }
    }
}

impl Stream for ChatDeltaStream {
    type Item = error::Result<ChatDelta>;

    fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        self.inner.as_mut().poll_next(cx)
    }
}

#[mockall::automock]
#[async_trait::async_trait]
pub trait ModelRunner {
    async fn chat_workflow(&self, messages: &[Message]) -> error::Result<ChatDeltaStream>;

    async fn chat_explore(
        &self,
        topic: String,
        session_id: uuid::Uuid,
    ) -> error::Result<ExploreStream>;

    async fn chat_stream(&self, messages: &[Message]) -> error::Result<ChatDeltaStream>;

    async fn extract_params(
        &self,
        messages: &[Message],
        schema: Arc<serde_json::Value>,
        workflow: Arc<Workflow>,
        tenant_id: String,
    ) -> error::Result<serde_json::Value>;

    async fn search_source(
        &self,
        messages: &[Message],
        schema: Arc<serde_json::Value>,
        workflow: Arc<Workflow>,
        tenant_id: String,
    ) -> error::Result<String>;
}
