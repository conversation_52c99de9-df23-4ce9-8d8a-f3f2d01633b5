use std::sync::Arc;

use crate::{TaskState, WorkflowHistory, WorkflowRunner};

#[async_trait::async_trait]
pub trait TaskRunner {
    fn workflow_runner(&self) -> Arc<dyn WorkflowRunner + Send + Sync + 'static>;

    async fn update_task_state(&self, id: uuid::Uuid, state: TaskState) -> error::Result<()>;

    async fn get_task_state(&self, id: uuid::Uuid) -> error::Result<TaskState>;

    async fn get_task_history(&self, id: uuid::Uuid) -> error::Result<WorkflowHistory>;

    async fn update_task_history(
        &self,
        id: uuid::Uuid,
        history: WorkflowHistory,
    ) -> error::Result<()>;
}
