use std::sync::Arc;

use ai::Message;
use interaction::Interaction;
use tool::{
    Tool,
    repository::{CallToolInput, CallValidatorInput},
};
use workflow::{Workflow, WorkflowNodeState};

#[mockall::automock]
#[async_trait::async_trait]
pub trait WorkflowRunner {
    fn get_max_retry_count(&self) -> usize;

    async fn extract_params(
        &self,
        context: &[Message],
        workflow: Arc<Workflow>,
        params_schema: Arc<serde_json::Value>,
        tenant_id: String,
    ) -> error::Result<serde_json::Value>;

    async fn generate_converter(
        &self,
        context: &[Message],
        workflow: Arc<Workflow>,
        history: Vec<Arc<WorkflowNodeState>>,
        current: Arc<WorkflowNodeState>,
        tenant_id: String,
    ) -> error::Result<String>;

    async fn call_converter(
        &self,
        converter: &str,
        history: Vec<Arc<WorkflowNodeState>>,
    ) -> error::Result<serde_json::Value>;

    async fn get_tool(&self, module: &str, tool: &str) -> Option<Arc<Tool>>;

    async fn call_tool(&self, input: CallToolInput) -> error::Result<serde_json::Value>;

    async fn call_validator(&self, input: CallValidatorInput) -> error::Result<serde_json::Value>;

    async fn generate_summary(
        &self,
        context: &[ai::Message],
        workflow: Arc<Workflow>,
        history: Vec<Arc<WorkflowNodeState>>,
        current: Arc<WorkflowNodeState>,
    ) -> error::Result<String>;

    async fn generate_final_summary(
        &self,
        context: &[ai::Message],
        workflow: Arc<Workflow>,
        history: Vec<Arc<WorkflowNodeState>>,
    ) -> error::Result<String>;

    async fn set_interaction(
        &self,
        session_id: &str,
        interaction_id: &str,
        interaction: Interaction,
    ) -> error::Result<()>;
}
