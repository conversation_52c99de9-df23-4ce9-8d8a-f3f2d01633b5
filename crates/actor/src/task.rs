use std::sync::Arc;

use serde::{Deserialize, Serialize};
use workflow::{Workflow, WorkflowNodeState};

use crate::WorkflowActorOptions;

pub struct Task {
    pub id: uuid::Uuid,
    pub tenant_id: String,
    pub user_id: String,
    pub created_at: time::OffsetDateTime,
    pub kind: TaskKind,
}

pub enum TaskKind {
    Workflow(WorkflowTask),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum TaskState {
    Running,
    #[serde(rename = "done")]
    Done {
        #[serde(with = "time::serde::rfc3339")]
        datetime: time::OffsetDateTime,
        output: Arc<WorkflowNodeState>,
    },
    #[serde(rename = "error")]
    Error {
        #[serde(with = "time::serde::rfc3339")]
        datetime: time::OffsetDateTime,
        error: String,
    },
}

impl TaskState {
    pub fn running() -> Self {
        Self::Running
    }

    pub fn done(output: Arc<WorkflowNodeState>) -> Self {
        Self::Done {
            datetime: time::OffsetDateTime::now_utc(),
            output,
        }
    }

    pub fn error(error: String) -> Self {
        Self::Error {
            datetime: time::OffsetDateTime::now_utc(),
            error,
        }
    }
}

pub struct WorkflowTask {
    pub workflow: Arc<Workflow>,
    pub options: WorkflowActorOptions,
}
