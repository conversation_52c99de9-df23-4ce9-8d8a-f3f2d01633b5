[package]
name = "actor"
version = "0.1.0"
edition = "2024"
publish = false

[lib]
name = "actor"
path = "src/lib.rs"

[dependencies]
ai = { workspace = true }
anyhow = { workspace = true }
async-openai = { workspace = true }
async-trait = { workspace = true }
content = { workspace = true }
entity = { workspace = true }
error = { workspace = true }
futures = { workspace = true }
interaction = { workspace = true }
jsonschema = { workspace = true }
mockall = { workspace = true }
pin-project-lite = { workspace = true }
ractor = { workspace = true }
reqwest = { workspace = true }
reqwest-eventsource = { workspace = true }
reqwest-websocket = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
time = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tool = { workspace = true }
tracing = { workspace = true }
uuid = { workspace = true }
workflow = { workspace = true }
