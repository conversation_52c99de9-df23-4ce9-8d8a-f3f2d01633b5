[package]
name = "workflow"
version = "0.1.0"
edition = "2024"
publish = false

[lib]
name = "workflow"
path = "src/lib.rs"

[dependencies]
bson = { workspace = true }
entity = { workspace = true }
error = { workspace = true }
expression = { workspace = true }
interaction = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tool = { workspace = true }
tracing = { workspace = true }

uuid = { workspace = true }
