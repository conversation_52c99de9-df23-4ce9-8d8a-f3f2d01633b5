use std::sync::Arc;

use interaction::Interaction;
use serde::{Deserialize, Serialize};
use tool::Tool;

use crate::Node;

#[derive(Serialize, Deserialize, Debug, <PERSON>lone, Default)]
#[serde(tag = "type")]
pub enum WorkflowState {
    #[serde(rename = "ready")]
    #[default]
    Ready,
    #[serde(rename = "executing")]
    Executing(Box<WorkflowStateExecuting>),
    #[serde(rename = "interacting")]
    Interacting(Box<WorkflowStateInteracting>),
    #[serde(rename = "done")]
    Done,
}

#[derive(Serialize, Deserialize, Debug, <PERSON>lone, De<PERSON>ult)]
pub struct WorkflowStateExecuting {
    pub current: WorkflowNodeState,
    pub history: Vec<Arc<WorkflowNodeState>>,
    pub retry_count: usize,
}

#[derive(Serialize, Deserialize, Clone, Default)]
pub struct WorkflowNodeState {
    pub node: Arc<Node>,
    pub tool: Arc<Tool>,
    pub converter: Option<Arc<String>>,
    pub views: Vec<Arc<serde_json::Value>>,
    pub params: Option<Arc<serde_json::Value>>,
    pub result: Option<Arc<serde_json::Value>>,
    pub summary: Option<Arc<String>>,
    pub error: Option<String>,
}

impl std::fmt::Debug for WorkflowNodeState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("WorkflowNodeState")
            .field("node", &self.node.id)
            .field("tool", &self.tool.name())
            .field("summary", &self.summary)
            .field("error", &self.error)
            .finish()
    }
}

impl WorkflowNodeState {
    pub fn create_params_schema(
        tool: &Arc<Tool>,
        node: &Arc<Node>,
    ) -> Option<Arc<serde_json::Value>> {
        let schema = tool.params_schema();

        if schema.is_null() {
            let schema = node.config.as_ref().and_then(|c| c.input_schema.as_ref());

            match schema {
                Some(schema) => {
                    if schema.is_null() {
                        None
                    } else {
                        Some(schema.clone())
                    }
                }
                None => {
                    tracing::error!("未找到输入参数模式，跳过参数验证");
                    None
                }
            }
        } else {
            Some(schema.clone())
        }
    }

    pub fn create_result_schema(
        tool: &Arc<Tool>,
        node: &Arc<Node>,
    ) -> Option<Arc<serde_json::Value>> {
        let schema = tool.result_schema();

        if schema.is_null() {
            let schema = node.config.as_ref().and_then(|c| c.output_schema.as_ref());

            match schema {
                Some(schema) => {
                    if schema.is_null() {
                        None
                    } else {
                        Some(schema.clone())
                    }
                }
                None => {
                    tracing::error!("未找到输出参数模式，跳过参数验证");
                    None
                }
            }
        } else {
            Some(schema.clone())
        }
    }

    pub fn get_params_schema(&self) -> Option<Arc<serde_json::Value>> {
        Self::create_params_schema(&self.tool, &self.node)
    }

    pub fn get_result_schema(&self) -> Option<Arc<serde_json::Value>> {
        Self::create_result_schema(&self.tool, &self.node)
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WorkflowStateInteracting {
    pub interaction: Interaction,
    pub executing: Box<WorkflowStateExecuting>,
}
