use std::sync::Arc;

use expression::Expr;
use serde::{Deserialize, Serialize};
use serde_json::map::Entry;

#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>h, PartialEq)]
pub struct Node {
    pub id: String,
    pub module: String,
    pub tool: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ports: Option<Vec<NodePort>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<serde_json::Value>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<NodeConfig>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub diagram: Option<serde_json::Value>,
}

fn get_type(value: &serde_json::Value) -> &str {
    match value {
        serde_json::Value::Object(_) => "object",
        serde_json::Value::Array(_) => "array",
        serde_json::Value::String(_) => "string",
        serde_json::Value::Number(_) => "number",
        serde_json::Value::Bool(_) => "boolean",
        serde_json::Value::Null => "null",
    }
}

impl Node {
    pub fn create_params(
        &self,
        input: &serde_json::Value,
    ) -> error::Result<Option<serde_json::Value>> {
        let properties = match self.config.as_ref().and_then(|c| c.properties.as_ref()) {
            Some(properties) => {
                if properties.is_empty() {
                    return Ok(None);
                } else {
                    properties
                }
            }
            None => return Ok(None),
        };

        let input = match input.as_object() {
            Some(input) => input,
            None => return Err(error::WorkflowError::InputNotObject(input.clone()).into()),
        };

        let mut params = serde_json::json!({});

        for property in properties {
            let value = match &property.value {
                NodePropertyValue::Literal { value } => value,
                NodePropertyValue::Mapping { path } => {
                    let mut current = match input.get(&path.node_id) {
                        Some(current) => current,
                        None => {
                            return Err(
                                error::WorkflowError::NodeNotFound(path.node_id.clone()).into()
                            );
                        }
                    };

                    for key in &path.property_path {
                        let next = match key {
                            serde_json::Value::String(s) => current.get(s),
                            serde_json::Value::Number(n) => {
                                let index = match n.as_u64() {
                                    Some(index) => index as usize,
                                    None => {
                                        return Err(error::WorkflowError::IndexNotUnsignedInteger(
                                            n.clone().into(),
                                            path.property_path.clone(),
                                        )
                                        .into());
                                    }
                                };
                                current.get(index)
                            }
                            other => {
                                return Err(error::WorkflowError::KeyNotStringOrNumber(
                                    other.clone(),
                                    path.property_path.clone(),
                                )
                                .into());
                            }
                        };

                        current = match next {
                            Some(next) => next,
                            None => {
                                return Err(error::WorkflowError::KeyPathNotFound(
                                    path.property_path.clone(),
                                )
                                .into());
                            }
                        };
                    }

                    current
                }
            };

            let mut current = &mut params;
            let key_path_len = property.key_path.len();
            for (i, key) in property.key_path.iter().enumerate() {
                let is_last = i == key_path_len - 1;
                match key {
                    serde_json::Value::String(s) => match current {
                        serde_json::Value::Object(o) => match o.entry(s) {
                            Entry::Occupied(e) => current = e.into_mut(),
                            Entry::Vacant(e) => {
                                if !is_last {
                                    if let Some(serde_json::Value::Number(_)) =
                                        property.key_path.get(i + 1)
                                    {
                                        current = e.insert(serde_json::json!([]));
                                    } else {
                                        current = e.insert(serde_json::json!({}));
                                    }
                                } else {
                                    current = e.insert(serde_json::Value::Null);
                                }
                            }
                        },
                        serde_json::Value::Null => {
                            if !is_last {
                                if let Some(serde_json::Value::Number(_)) =
                                    property.key_path.get(i + 1)
                                {
                                    *current = serde_json::json!([]);
                                } else {
                                    *current = serde_json::json!({});
                                }
                            } else {
                                *current = serde_json::json!({});
                            }
                            if let serde_json::Value::Object(o) = current {
                                current = o.entry(s).or_insert(serde_json::Value::Null);
                            }
                        }
                        _ => {
                            return Err(error::WorkflowError::ConflictKeyPath(
                                key.clone(),
                                property.key_path.clone(),
                                get_type(current).to_string(),
                            )
                            .into());
                        }
                    },
                    serde_json::Value::Number(n) => {
                        let index = match n.as_u64() {
                            Some(index) => index as usize,
                            None => {
                                return Err(error::WorkflowError::IndexNotUnsignedInteger(
                                    n.clone().into(),
                                    property.key_path.clone(),
                                )
                                .into());
                            }
                        };

                        let array = match current {
                            serde_json::Value::Array(a) => a,
                            serde_json::Value::Null => {
                                *current = serde_json::json!([]);
                                current.as_array_mut().unwrap()
                            }
                            _ => {
                                return Err(error::WorkflowError::ConflictKeyPath(
                                    key.clone(),
                                    property.key_path.clone(),
                                    get_type(current).to_string(),
                                )
                                .into());
                            }
                        };

                        if index >= array.len() {
                            array.resize(index + 1, serde_json::Value::Null);
                        }

                        current = array.get_mut(index).unwrap();
                    }
                    _ => {
                        return Err(error::WorkflowError::KeyNotStringOrNumber(
                            key.clone(),
                            vec![key.clone()],
                        )
                        .into());
                    }
                }
            }
            *current = value.clone();
        }

        Ok(Some(params))
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, Default, Hash, PartialEq, Eq)]
pub enum NodePortType {
    #[serde(rename = "input")]
    Input,
    #[default]
    #[serde(rename = "output")]
    Output,
}

#[derive(Serialize, Deserialize, Debug, Clone, Default, Hash, PartialEq)]
pub struct NodePort {
    pub id: String,
    pub r#type: NodePortType,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<serde_json::Value>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<NodePortConfig>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub diagram: Option<serde_json::Value>,
}

#[derive(Serialize, Deserialize, Debug, Clone, Default, Hash, PartialEq)]
pub struct NodePortConfig {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub title: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub expression: Option<Expr>,
}

#[derive(Serialize, Deserialize, Debug, Clone, Default, Hash, PartialEq)]
pub struct NodeConfig {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub title: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub properties: Option<Vec<NodeProperty>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub input_schema: Option<Arc<serde_json::Value>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_value: Option<Arc<serde_json::Value>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub output_schema: Option<Arc<serde_json::Value>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub input_converter: Option<Arc<ScriptConverter>>,
}

#[derive(Serialize, Deserialize, Debug, Clone, Hash, PartialEq)]
#[serde(tag = "type")]
pub enum ScriptConverter {
    #[serde(rename = "python")]
    Python { code: Arc<String> },
}

impl Default for ScriptConverter {
    fn default() -> Self {
        ScriptConverter::Python {
            code: Arc::new("".to_string()),
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, Default, Hash, PartialEq)]
pub struct NodePropertyPath {
    pub node_id: String,
    pub property_path: NodePropertyKeyPath,
}

pub type NodePropertyKeyPath = Vec<serde_json::Value>;

#[derive(Serialize, Deserialize, Debug, Clone, Hash, PartialEq)]
#[serde(tag = "type")]
pub enum NodePropertyValue {
    #[serde(rename = "literal")]
    Literal { value: serde_json::Value },
    #[serde(rename = "mapping")]
    Mapping { path: NodePropertyPath },
}

impl Default for NodePropertyValue {
    fn default() -> Self {
        NodePropertyValue::Literal {
            value: serde_json::Value::Null,
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, Default, Hash, PartialEq)]
pub struct NodeProperty {
    pub key_path: NodePropertyKeyPath,
    pub value: NodePropertyValue,
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    fn create_test_node() -> Node {
        Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                title: Some("Test Node".to_string()),
                description: Some("Test Description".to_string()),
                properties: Some(vec![
                    NodeProperty {
                        key_path: vec![json!("simple")],
                        value: NodePropertyValue::Literal {
                            value: json!("value"),
                        },
                    },
                    NodeProperty {
                        key_path: vec![json!("nested"), json!("path")],
                        value: NodePropertyValue::Literal { value: json!(42) },
                    },
                    NodeProperty {
                        key_path: vec![json!("array"), json!(0)],
                        value: NodePropertyValue::Literal {
                            value: json!("first"),
                        },
                    },
                ]),
                ..Default::default()
            }),
            ..Default::default()
        }
    }

    #[test]
    fn test_create_params_with_literal_values() {
        let node = create_test_node();
        let input = json!({});
        let result = node.create_params(&input).unwrap().unwrap();

        assert_eq!(result["simple"], "value");
        assert_eq!(result["nested"]["path"], 42);
        assert_eq!(result["array"][0], "first");
    }

    #[test]
    fn test_create_params_with_mapping() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),

            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("value")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "value": "mapped_value"
            }
        });

        let result = node.create_params(&input).unwrap().unwrap();
        assert_eq!(result["mapped"], "mapped_value");
    }

    #[test]
    fn test_create_params_with_nested_mapping() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("deep"), json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("nested"), json!("value")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "nested": {
                    "value": "deep_value"
                }
            }
        });

        let result = node.create_params(&input).unwrap().unwrap();
        assert_eq!(result["deep"]["mapped"], "deep_value");
    }

    #[test]
    fn test_create_params_with_array_mapping() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("array"), json!(0)],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("items"), json!(0)],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "items": ["first_item"]
            }
        });

        let result = node.create_params(&input).unwrap().unwrap();
        assert_eq!(result["array"][0], "first_item");
    }

    #[test]
    fn test_create_params_with_mapping_in_array() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("array"), json!(0), json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("items"), json!(0), json!("value")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "items": [
                    {
                        "value": "mapped_in_array"
                    }
                ]
            }
        });

        let result = node.create_params(&input).unwrap().unwrap();
        assert_eq!(result["array"][0]["mapped"], "mapped_in_array");
    }

    #[test]
    fn test_create_params_with_mapping_in_nested_object() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("deep"), json!("nested"), json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("nested"), json!("deep"), json!("value")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "nested": {
                    "deep": {
                        "value": "mapped_in_nested"
                    }
                }
            }
        });

        let result = node.create_params(&input).unwrap().unwrap();
        assert_eq!(result["deep"]["nested"]["mapped"], "mapped_in_nested");
    }

    #[test]
    fn test_create_params_with_mapping_in_array_error() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("array"), json!(0), json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("items"), json!(0), json!("value")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "items": []
            }
        });

        let result = node.create_params(&input);
        assert!(result.is_err());
    }

    #[test]
    fn test_create_params_with_mapping_in_nested_object_error() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("deep"), json!("nested"), json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("nested"), json!("deep"), json!("value")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "nested": {}
            }
        });

        let result = node.create_params(&input);
        assert!(result.is_err());
    }

    #[test]
    fn test_create_params_with_invalid_input() {
        let node = create_test_node();
        let input = json!("not_an_object");
        let result = node.create_params(&input);
        assert!(result.is_err());
    }

    #[test]
    fn test_create_params_with_missing_node() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "non_existent".to_string(),
                            property_path: vec![json!("value")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({});
        let result = node.create_params(&input);
        assert!(result.is_err());
    }

    #[test]
    fn test_create_params_with_invalid_path() {
        let node = Node {
            id: "test_node".to_string(),
            module: "test_module".to_string(),
            tool: "test_tool".to_string(),
            config: Some(NodeConfig {
                properties: Some(vec![NodeProperty {
                    key_path: vec![json!("mapped")],
                    value: NodePropertyValue::Mapping {
                        path: NodePropertyPath {
                            node_id: "source".to_string(),
                            property_path: vec![json!("invalid"), json!("path")],
                        },
                    },
                }]),
                ..Default::default()
            }),
            ..Default::default()
        };

        let input = json!({
            "source": {
                "value": "test"
            }
        });

        let result = node.create_params(&input);
        assert!(result.is_err());
    }
}
