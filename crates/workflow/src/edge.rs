use expression::Expr;
use serde::{Deserialize, Serialize};

#[derive(Ser<PERSON><PERSON>, Deserialize, Debug, <PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON><PERSON>ult, Hash)]
pub struct Edge {
    pub source_node: String,
    pub source_port: Option<String>,
    pub target_node: String,
    pub target_port: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub config: Option<EdgeConfig>,
    pub diagram: Option<serde_json::Value>,
}

#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, PartialEq, De<PERSON>ult, Hash)]
pub struct EdgeConfig {
    pub expression: Option<Expr>,
}
