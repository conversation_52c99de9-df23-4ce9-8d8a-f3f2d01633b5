use std::{collections::HashMap, sync::Arc};

use serde::{Deserialize, Serialize};

use crate::{NodePortType, edge::Edge, node::Node};

#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, <PERSON><PERSON>ult, <PERSON>h)]
pub struct Workflow {
    pub id: uuid::Uuid,
    pub start_id: Option<String>,
    pub edges: Vec<Arc<Edge>>,
    pub nodes: Vec<Arc<Node>>,
    pub metadata: Option<serde_json::Value>,
    pub config: Option<WorkflowConfig>,
    pub diagram: Option<serde_json::Value>,
    pub runtime: Option<WorkflowRuntime>,
}

#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, De<PERSON>ult)]
pub struct WorkflowRuntime {
    pub interactions: HashMap<String, serde_json::Value>,
}

impl std::hash::Hash for WorkflowRuntime {
    fn hash<H: std::hash::Hasher>(&self, _state: &mut H) {}
}

#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, De<PERSON>ult, Hash)]
pub struct WorkflowConfig {}

impl Workflow {
    pub fn first_node(&self) -> Option<Arc<Node>> {
        if let Some(start_id) = &self.start_id {
            self.nodes.iter().find(|node| node.id == *start_id).cloned()
        } else {
            None
        }
    }

    pub fn first_node_mut(&mut self) -> Option<&mut Arc<Node>> {
        if let Some(start_id) = &self.start_id {
            self.nodes.iter_mut().find(|node| node.id == *start_id)
        } else {
            None
        }
    }

    pub fn next_edges(&self, source_node_id: &str) -> Vec<Arc<Edge>> {
        self.edges
            .iter()
            .filter(|edge| edge.source_node == source_node_id)
            .cloned()
            .collect()
    }

    pub fn next_edges_with_port(
        &self,
        source_node_id: &str,
        source_port_id: &str,
    ) -> Vec<Arc<Edge>> {
        self.edges
            .iter()
            .filter(|edge| {
                edge.source_node == source_node_id
                    && edge.source_port == Some(source_port_id.to_string())
            })
            .cloned()
            .collect()
    }

    pub fn get_node(&self, id: &str) -> Option<Arc<Node>> {
        self.nodes.iter().find(|node| node.id == id).cloned()
    }

    pub fn eval_next_node(
        &self,
        current_node: &Node,
        results: &serde_json::Value,
    ) -> Option<Arc<Node>> {
        tracing::debug!("查找下一个节点");

        let edges = self.next_edges(&current_node.id);

        tracing::debug!("找到 {} 条边连接到当前节点", edges.len());

        if let Some(ports) = &current_node.ports {
            // 如果 Node 定义了 Ports，则需要根据 Ports 选择
            tracing::debug!("当前节点定义了 {} 个 Ports", ports.len());
            let mut selected_node = None;
            for port in ports.iter() {
                if port.r#type != NodePortType::Output {
                    continue;
                }

                if let Some(config) = &port.config {
                    let edges = if let Some(expression) = &config.expression {
                        if let Ok(true) = expression::eval_expr(expression, results) {
                            self.next_edges_with_port(&current_node.id, &port.id)
                        } else {
                            continue;
                        }
                    } else {
                        self.next_edges_with_port(&current_node.id, &port.id)
                    };

                    for edge in edges.iter() {
                        let target_node = if let Some(expression) =
                            &edge.config.as_ref().and_then(|c| c.expression.as_ref())
                        {
                            if expression::eval_expr(expression, results).unwrap_or_default() {
                                Some(&edge.target_node)
                            } else {
                                None
                            }
                        } else {
                            Some(&edge.target_node)
                        };

                        if let Some(target_node) = target_node {
                            if let Some(n) = self.get_node(target_node) {
                                selected_node = Some(n);
                                break;
                            }
                        }
                    }
                }
            }

            if selected_node.is_some() {
                return selected_node;
            }

            if !ports.is_empty() {
                return None;
            }
        }

        match edges.len() {
            0 => {
                // 没有后续节点，工作流结束
                tracing::debug!("没有后续节点，工作流结束");
                None
            }
            // 1 => {
            //     tracing::debug!("只有一个后续节点: to={}", edges[0].target_node);
            //     self.get_node(&edges[0].target_node) // 只有一个后续节点
            // }
            _ => {
                // 有多个后续节点，需要根据条件选择
                tracing::debug!("有多个后续节点，根据条件选择");
                let mut selected_node = None;
                for (i, edge) in edges.iter().enumerate() {
                    let expression = edge.config.as_ref().and_then(|c| c.expression.clone());

                    tracing::debug!(
                        "检查第 {} 条边: to={}, 是否有表达式: {}",
                        i,
                        edge.target_node,
                        expression.is_some()
                    );
                    if let Some(expression) = &expression {
                        // 评估条件表达式
                        tracing::debug!("评估条件表达式: {:?}", expression);
                        match expression::eval_expr(expression, results) {
                            Ok(true) => {
                                tracing::debug!("表达式评估为真");
                                if let Some(n) = self.get_node(&edge.target_node) {
                                    tracing::debug!("找到匹配的节点: id={}", n.id);
                                    selected_node = Some(n);
                                    break;
                                }
                            }
                            Ok(false) => {
                                tracing::debug!("表达式评估为假，继续检查下一条边");
                                // 条件不满足，继续检查下一个边
                                continue;
                            }
                            Err(e) => {
                                tracing::error!(
                                    "表达式 {} 执行错误: {}",
                                    expression.to_string(),
                                    e
                                );
                                // TODO add error

                                return None;
                            }
                        }
                    } else {
                        tracing::debug!("边没有表达式，直接使用");
                        if let Some(n) = self.get_node(&edge.target_node) {
                            tracing::debug!("找到下一个节点: id={}", n.id);
                            selected_node = Some(n);
                            break;
                        }
                    }
                }
                selected_node
            }
        }
    }
}

// impl TryFrom<entity::workflow::Model> for Workflow {
//     type Error = anyhow::Error;

//     fn try_from(value: entity::workflow::Model) -> Result<Self, Self::Error> {
//         let edges: Vec<Arc<Edge>> = match value.edges {
//             Some(edges) => serde_json::from_value(edges)?,
//             None => vec![],
//         };
//         let nodes: Vec<Arc<Node>> = match value.nodes {
//             Some(nodes) => serde_json::from_value(nodes)?,
//             None => vec![],
//         };
//         let metadata = match value.metadata {
//             Some(metadata) => serde_json::from_value(metadata)?,
//             None => None,
//         };
//         let config = match value.config {
//             Some(config) => serde_json::from_value(config)?,
//             None => None,
//         };
//         let diagram = match value.diagram {
//             Some(diagram) => serde_json::from_value(diagram)?,
//             None => None,
//         };
//         Ok(Self {
//             id: value.id,
//             start_id: value.start_id,
//             edges,
//             nodes,
//             metadata,
//             config,
//             diagram,
//         })
//     }
// }

// impl TryFrom<Workflow> for entity::workflow::Model {
//     type Error = anyhow::Error;

//     fn try_from(value: Workflow) -> Result<Self, Self::Error> {
//         let edges = serde_json::to_value(value.edges)?;
//         let nodes = serde_json::to_value(value.nodes)?;
//         let metadata = value
//             .metadata
//             .map(|metadata| -> Result<serde_json::Value, anyhow::Error> {
//                 Ok(serde_json::to_value(metadata)?)
//             })
//             .transpose()?;
//         let config = value
//             .config
//             .map(|config| -> Result<serde_json::Value, anyhow::Error> {
//                 Ok(serde_json::to_value(config)?)
//             })
//             .transpose()?;
//         let diagram = value
//             .diagram
//             .map(|diagram| -> Result<serde_json::Value, anyhow::Error> {
//                 Ok(serde_json::to_value(diagram)?)
//             })
//             .transpose()?;
//         Ok(Self {
//             id: value.id,
//             start_id: value.start_id,
//             edges: Some(edges),
//             nodes: Some(nodes),
//             metadata,
//             config,
//             diagram,
//             status: entity::workflow::Status::Normal,
//             user_id: "default_user_id".to_string(),
//             tenant_id: "default_tenant_id".to_string(),
//         })
//     }
// }

impl TryFrom<entity::document::Workflow> for Workflow {
    type Error = error::Error;

    fn try_from(value: entity::document::Workflow) -> error::Result<Self> {
        Ok(Self {
            id: value.id,
            start_id: value.start_id,
            edges: bson::from_bson(value.edges).map_err(error::MongoError::BsonDeError)?,
            nodes: bson::from_bson(value.nodes).map_err(error::MongoError::BsonDeError)?,
            metadata: bson::from_bson(value.metadata).map_err(error::MongoError::BsonDeError)?,
            config: bson::from_bson(value.config).map_err(error::MongoError::BsonDeError)?,
            diagram: bson::from_bson(value.diagram).map_err(error::MongoError::BsonDeError)?,
            runtime: bson::from_bson(value.runtime).map_err(error::MongoError::BsonDeError)?,
        })
    }
}

impl TryFrom<Workflow> for entity::document::Workflow {
    type Error = error::Error;

    fn try_from(value: Workflow) -> error::Result<Self> {
        Ok(Self {
            id: value.id,
            start_id: value.start_id,
            edges: bson::to_bson(&value.edges).map_err(error::MongoError::BsonSerError)?,
            nodes: bson::to_bson(&value.nodes).map_err(error::MongoError::BsonSerError)?,
            metadata: bson::to_bson(&value.metadata.clone())
                .map_err(error::MongoError::BsonSerError)?,
            config: bson::to_bson(&value.config).map_err(error::MongoError::BsonSerError)?,
            diagram: bson::to_bson(&value.diagram).map_err(error::MongoError::BsonSerError)?,
            runtime: bson::to_bson(&value.runtime).map_err(error::MongoError::BsonSerError)?,
            status: entity::document::WorkflowStatus::Normal,
            user_id: "".to_string(),
            tenant_id: "".to_string(),
        })
    }
}
