[package]
name = "entity"
version = "0.1.0"
edition = "2024"
publish = false

[lib]
name = "entity"
path = "src/lib.rs"

[dependencies]
async-trait = { workspace = true }
bson = { workspace = true }
error = { workspace = true }
field_name_macro = { workspace = true }
futures = { workspace = true }
mongodb = { workspace = true }
serde = { workspace = true }
time = { workspace = true }
tracing = { workspace = true }
uuid = { workspace = true }