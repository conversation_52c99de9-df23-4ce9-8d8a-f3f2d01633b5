use bson::Bson;
use field_name_macro::DeriveFieldNames;
use field_name_macro::FieldEnum;
use serde::{Deserialize, Serialize};
use time::OffsetDateTime;
use uuid::Uuid;

use crate::Document;
use crate::helpers;

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub struct Conversation {
    #[serde(rename = "_id", with = "helpers::uuid")]
    pub id: Uuid,
    pub title: String,
    #[serde(with = "bson::serde_helpers::time_0_3_offsetdatetime_as_bson_datetime")]
    pub create_time: OffsetDateTime,
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        with = "helpers::time_0_3_offsetdatetime_as_bson_datetime_optional"
    )]
    pub update_time: Option<OffsetDateTime>,
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        with = "helpers::uuid_optional"
    )]
    pub current_message_id: Option<Uuid>,
    pub status: ConversationStatus,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub metadata: Option<Bson>,
    pub user_id: String,
    pub tenant_id: String,
    pub user_name: String,
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub messages: Vec<Message>,
    pub sub_status: ConversationSubStatus,
}

impl Document for Conversation {
    const COLLECTION_NAME: &'static str = "conversation";
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum ConversationStatus {
    #[serde(rename = "ready")]
    Ready,
    #[serde(rename = "chat")]
    Chat,
    #[serde(rename = "execute")]
    Execute,
    #[serde(rename = "interaction")]
    Interaction,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Default)]
pub enum ConversationSubStatus {
    #[serde(rename = "select")]
    Select,
    /// 确认类型交互
    #[serde(rename = "confirm")]
    Confirm,
    /// 运行前确认
    #[serde(rename = "execute_confirm")]
    ExecuteConfirm,
    /// 打开页面类型交互
    #[serde(rename = "open_page")]
    OpenPage,
    /// 创建动态表单
    #[serde(rename = "form")]
    Form,
    #[serde(rename = "")]
    #[default]
    None,
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub struct Message {
    #[serde(rename = "_id", with = "helpers::uuid")]
    pub id: Uuid,
    pub author: Bson,
    #[serde(with = "bson::serde_helpers::time_0_3_offsetdatetime_as_bson_datetime")]
    pub create_time: OffsetDateTime,
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        with = "helpers::time_0_3_offsetdatetime_as_bson_datetime_optional"
    )]
    pub finish_time: Option<OffsetDateTime>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub metadata: Option<Bson>,
    pub status: MessageStatus,
    #[serde(default, with = "helpers::uuid")]
    pub conversation_id: Uuid,
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        with = "helpers::uuid_optional"
    )]
    pub parent_id: Option<Uuid>,
    //Vec<Uuid>
    pub children: Bson,
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub contents: Vec<Content>,
}

impl Document for Message {
    const COLLECTION_NAME: &'static str = "message";
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum MessageStatus {
    #[serde(rename = "chat")]
    Chat,
    #[serde(rename = "done")]
    Done,
    #[serde(rename = "error")]
    Error,
    #[serde(rename = "interaction")]
    Interaction,
    #[serde(rename = "execute")]
    Execute,
    #[serde(rename = "cancel")]
    Cancel,
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub struct Content {
    #[serde(rename = "_id", with = "helpers::uuid")]
    pub id: Uuid,
    #[serde(with = "helpers::uuid")]
    pub message_id: Uuid,
    pub content: Bson,
}

impl Document for Content {
    const COLLECTION_NAME: &'static str = "content";
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub struct Workflow {
    #[serde(rename = "_id", with = "helpers::uuid")]
    pub id: Uuid,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub start_id: Option<String>,
    pub edges: Bson,
    pub nodes: Bson,
    pub metadata: Bson,
    pub config: Bson,
    pub diagram: Bson,
    pub status: WorkflowStatus,
    pub user_id: String,
    pub tenant_id: String,
    pub runtime: Bson,
}

impl Document for Workflow {
    const COLLECTION_NAME: &'static str = "workflow";
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub enum WorkflowStatus {
    #[serde(rename = "deleted")]
    Deleted,
    #[serde(rename = "normal")]
    Normal,
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub struct Agent {
    #[serde(rename = "_id", with = "helpers::uuid")]
    pub id: Uuid,
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        with = "helpers::uuid_optional"
    )]
    pub parent_id: Option<Uuid>,
    pub name: String,
    pub r#type: AgentType,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        with = "helpers::uuid_optional"
    )]
    pub workflow_id: Option<Uuid>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub workflow: Option<Workflow>,
    pub user_id: String,
    pub tenant_id: String,
    #[serde(with = "bson::serde_helpers::time_0_3_offsetdatetime_as_bson_datetime")]
    pub create_time: OffsetDateTime,
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        with = "helpers::time_0_3_offsetdatetime_as_bson_datetime_optional"
    )]
    pub update_time: Option<OffsetDateTime>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub agent_type: Option<String>,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub tag: Option<String>,
}

#[derive(Clone, Debug, Serialize, Deserialize, DeriveFieldNames, FieldEnum)]
pub enum AgentType {
    #[serde(rename = "folder")]
    Folder,
    #[serde(rename = "workflow")]
    Workflow,
}

impl Document for Agent {
    const COLLECTION_NAME: &'static str = "agent";
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub struct Playback {
    #[serde(rename = "_id", with = "helpers::uuid")]
    pub id: Uuid,
    pub title: String,
    pub description: Option<String>,
    #[serde(with = "helpers::uuid")]
    pub conversation_id: Uuid,
    pub r#type: Option<String>,
    pub images: Bson,
    pub user_id: String,
    pub tenant_id: String,
}

impl Document for Playback {
    const COLLECTION_NAME: &'static str = "playback";
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
pub struct BackgroundAgent {
    #[serde(rename = "_id", with = "helpers::uuid")]
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub r#type: Option<String>,
    pub dynamic: Bson,
    pub user_id: String,
    pub tenant_id: String,
    pub order: i64,
    #[serde(with = "helpers::uuid")]
    pub workflow_id: Uuid,
}

impl Document for BackgroundAgent {
    const COLLECTION_NAME: &'static str = "background_agent";
}
