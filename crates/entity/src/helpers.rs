#![allow(dead_code)]
pub use datetime as time_0_3_offsetdatetime_as_bson_datetime_optional;

pub mod datetime {
    use bson::DateTime;
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use std::result::Result;

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<time::OffsetDateTime>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let val =
            Option::deserialize(deserializer)?.map(|datetime: DateTime| datetime.to_time_0_3());
        Ok(val)
    }

    pub fn serialize<S: Serializer>(
        val: &Option<time::OffsetDateTime>,
        serializer: S,
    ) -> Result<S::Ok, S::Error> {
        let datetime = val.map(DateTime::from_time_0_3);
        datetime.serialize(serializer)
    }
}

pub mod uuid {
    use bson;
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use uuid::Uuid;

    pub fn serialize<S>(uuid: &Uuid, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let bson_uuid = bson::Uuid::from(*uuid);
        bson_uuid.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Uuid, D::Error>
    where
        D: Deserializer<'de>,
    {
        let bson_uuid = bson::Uuid::deserialize(deserializer)?;
        Ok(Uuid::from(bson_uuid))
    }
}

pub mod uuid_vec {
    use bson;
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use uuid::Uuid;

    pub fn serialize<S>(uuid: &[Uuid], serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let bson_uuid = uuid
            .iter()
            .map(|uuid| bson::Uuid::from(*uuid))
            .collect::<Vec<_>>();
        bson_uuid.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Vec<Uuid>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let vec_bson_uuid: Vec<bson::Uuid> = Vec::<bson::Uuid>::deserialize(deserializer)?;

        let vec_uuid: Vec<Uuid> = vec_bson_uuid.into_iter().map(Uuid::from).collect();

        Ok(vec_uuid)
    }
}

pub mod uuid_optional {
    use bson;
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use uuid::Uuid;

    pub fn serialize<S>(uuid: &Option<Uuid>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let bson_uuid = uuid.map(bson::Uuid::from);
        bson_uuid.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<Uuid>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let bson_uuid = Option::<bson::Uuid>::deserialize(deserializer)?;
        Ok(bson_uuid.map(Uuid::from))
    }
}
