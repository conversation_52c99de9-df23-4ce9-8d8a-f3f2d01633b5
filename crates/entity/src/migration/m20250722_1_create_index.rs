use bson::doc;
use mongodb::{IndexModel, options::IndexOptions};

use crate::{document::*, migration::MigrationTrait};

const AGENT_NAME_UNIQUE_INDEX: &str = "agent_name_unique_index";
const PLAYBACK_TITLE_UNIQUE_INDEX: &str = "playback_title_unique_index";

// #[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, repository: &crate::DocumentRepository) -> error::Result<()> {
        let collection = repository.collection::<Agent>().await;
        let options = IndexOptions::builder()
            .unique(true)
            .name(Some(AGENT_NAME_UNIQUE_INDEX.to_string()))
            .build();
        let model = IndexModel::builder()
            .keys(doc! {
                AgentField::Name.as_str(): 1,
                AgentField::Type.as_str(): 1,
                AgentField::UserId.as_str(): 1,
                AgentField::TenantId.as_str(): 1,
            })
            .options(options)
            .build();
        let _ = collection
            .create_index(model)
            .await
            .map_err(error::MongoError::SeverError)?;

        let collection = repository.collection::<Playback>().await;
        let options = IndexOptions::builder()
            .unique(true)
            .name(Some(PLAYBACK_TITLE_UNIQUE_INDEX.to_string()))
            .build();
        let model = IndexModel::builder()
            .keys(doc! {
                PlaybackField::Title.as_str(): 1
            })
            .options(options)
            .build();
        let _ = collection
            .create_index(model)
            .await
            .map_err(error::MongoError::SeverError)?;

        Ok(())
    }

    async fn down(&self, repository: &crate::DocumentRepository) -> error::Result<()> {
        let _ = repository
            .collection::<Agent>()
            .await
            .drop_index(AGENT_NAME_UNIQUE_INDEX)
            .await
            .map_err(error::MongoError::SeverError)?;

        let _ = repository
            .collection::<Playback>()
            .await
            .drop_index(PLAYBACK_TITLE_UNIQUE_INDEX)
            .await
            .map_err(error::MongoError::SeverError)?;

        Ok(())
    }
}
