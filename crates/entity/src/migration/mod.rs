use std::path::Path;

use bson::doc;
use field_name_macro::FieldEnum;
use mongodb::{Collection, IndexModel, options::IndexOptions};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{Document, DocumentRepository};

pub mod m20250722_1_create_index;

const MIGRATION_COLLECTION_NAME: &str = "migrations";
const MIGRATION_VERSION_UNIQUE_INDEX: &str = "migration_version_unique_index";

pub trait MigrationName {
    fn name(&self) -> &str;
}

#[async_trait::async_trait]
pub trait MigrationTrait: Send + Sync {
    fn name(&self) -> &str {
        let path = Path::new(file!());
        path.file_stem()
            .map(|o| o.to_str())
            .unwrap_or_default()
            .unwrap_or_default()
    }

    async fn up(&self, repository: &DocumentRepository) -> error::Result<()>;

    async fn down(&self, repository: &DocumentRepository) -> error::Result<()>;
}

trait MigratorTrait {
    fn migration_collection_name() -> &'static str {
        MIGRATION_COLLECTION_NAME
    }

    fn migrations() -> Vec<Box<dyn MigrationTrait>>;
}

#[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
struct MigrationRecord {
    #[serde(rename = "_id", with = "crate::helpers::uuid")]
    pub id: Uuid,
    pub version: String,
    #[serde(with = "bson::serde_helpers::time_0_3_offsetdatetime_as_bson_datetime")]
    pub applied_at: time::OffsetDateTime,
}

impl Document for MigrationRecord {
    const COLLECTION_NAME: &'static str = MIGRATION_COLLECTION_NAME;
}

async fn create_version_index(collection: &Collection<MigrationRecord>) -> error::Result<()> {
    let options = IndexOptions::builder()
        .unique(true)
        .name(Some(MIGRATION_VERSION_UNIQUE_INDEX.to_string()))
        .build();
    let model = IndexModel::builder()
        .keys(doc! {"version": 1})
        .options(options)
        .build();
    let _ = collection
        .create_index(model)
        .await
        .map_err(error::MongoError::SeverError)?;
    Ok(())
}

pub async fn exec_up(repository: &DocumentRepository) -> error::Result<()> {
    let collection = repository.collection::<MigrationRecord>().await;
    create_version_index(&collection).await?;

    for migration in Migrator::migrations() {
        let version = migration.name();
        let count = collection
            .count_documents(doc! {
                "version": version
            })
            .await
            .map_err(error::MongoError::SeverError)?;
        if count > 0 {
            tracing::debug!("migration {} exists, skip", version);
            continue;
        }

        match migration.up(repository).await {
            Ok(_) => {
                match repository
                    .insert_one(MigrationRecord {
                        id: Uuid::now_v7(),
                        version: version.to_string(),
                        applied_at: time::OffsetDateTime::now_utc(),
                    })
                    .await
                {
                    Ok(_) => {
                        tracing::info!("migration {} succeed", version);
                    }
                    Err(e) => {
                        tracing::error!("migration {} failed: {:?}", version, e);
                        migration.down(repository).await?;
                    }
                }
            }
            Err(e) => {
                tracing::error!("migration {} failed: {:?}", version, e);
                migration.down(repository).await?;
            }
        }
    }

    Ok(())
}

pub struct Migrator;

impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            // Box::new(m20250722_1_create_index::Migration),
        ]
    }
}
