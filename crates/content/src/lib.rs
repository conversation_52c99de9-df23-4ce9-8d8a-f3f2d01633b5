use std::borrow::Cow;

use interaction::{ExecuteConfirmContent, Interaction};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ContentItem {
    pub id: Uuid,
    #[serde(flatten)]
    pub content: ContentItemInner,
}

/// 消息内容项
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ContentItemInner {
    /// 普通对话内容
    #[serde(rename = "text")]
    Text {
        /// 文本内容
        text: String,
    },
    /// 错误文本
    #[serde(rename = "error_text")]
    ErrorText { error_text: String },
    /// 工作流部分
    #[serde(rename = "json")]
    Json {
        /// 步骤
        json: String,
    },
    #[serde(rename = "branch")]
    Branch {
        /// 分支
        branch: String,
    },
    /// 交互内容
    #[serde(rename = "interaction")]
    Interaction {
        /// 交互内容
        interaction: Interaction,
    },
    #[serde(rename = "tool")]
    Tool {
        #[serde(skip_serializing_if = "Option::is_none")]
        input: Option<serde_json::Value>,
        #[serde(skip_serializing_if = "Option::is_none")]
        output: Option<serde_json::Value>,
    },
    /// 执行确认
    #[serde(rename = "require_confirm")]
    RequireConfirm {
        // true or false
        require_confirm: String,
    },
    #[serde(rename = "execute_confirm")]
    ExecuteConfirm {
        execute_confirm: ExecuteConfirmContent,
    },
    /// 工作流
    #[serde(rename = "workflow")]
    Workflow {
        /// 工作流
        workflow: uuid::Uuid,
        #[serde(skip_serializing_if = "Option::is_none")]
        json: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        branch: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        name: Option<String>,
    },
    /// 视图
    #[serde(rename = "view")]
    View {
        /// 视图
        view: ContentView,
        #[serde(skip_serializing_if = "Option::is_none")]
        hide_all: Option<bool>,
    },
    #[serde(untagged)]
    Other(serde_json::Value),
}

impl From<ContentItemInner> for ContentItem {
    fn from(value: ContentItemInner) -> Self {
        Self {
            id: Uuid::now_v7(),
            content: value,
        }
    }
}

impl From<ContentItem> for ContentItemInner {
    fn from(value: ContentItem) -> Self {
        value.content
    }
}

impl ContentItem {
    pub fn try_into_document(
        &self,
        message_id: uuid::Uuid,
    ) -> error::Result<entity::document::Content> {
        Ok(entity::document::Content {
            id: self.id,
            message_id,
            content: bson::to_bson(&self.content).map_err(error::MongoError::BsonSerError)?,
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "format", content = "content")]
pub enum ContentView {
    #[serde(rename = "markdown")]
    Markdown(String),
    #[serde(rename = "card")]
    Card(ContentCard),
    #[serde(untagged)]
    Other(serde_json::Value),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ContentViewOrDelta {
    #[serde(rename = "view")]
    View { view: ContentView },
    #[serde(rename = "delta")]
    Delta { delta: ContentCardDelta },
}

impl From<ContentView> for ContentViewOrDelta {
    fn from(value: ContentView) -> Self {
        Self::View { view: value }
    }
}

impl Default for ContentView {
    fn default() -> Self {
        Self::Markdown("".to_string())
    }
}

impl From<ContentCard> for ContentView {
    fn from(value: ContentCard) -> Self {
        Self::Card(value)
    }
}

impl From<ContentView> for String {
    fn from(value: ContentView) -> Self {
        match value {
            ContentView::Markdown(text) => text,
            ContentView::Card(card) => {
                let mut result = String::new();
                result += &card.title;

                if let Some(description) = &card.description {
                    result += "\n";
                    result += description;
                }

                if let Some(content) = &card.content {
                    result += "\n";
                    result += &Into::<String>::into(content.clone());
                }

                match (card.r#type.as_str(), card.details.as_ref()) {
                    ("summary", Some(details)) => {
                        result += "\n";
                        result += &Into::<String>::into(details.clone());
                    }
                    ("error", Some(details)) => {
                        result += "\n";
                        result += &Into::<String>::into(details.clone());
                    }
                    _ => {}
                }

                result
            }
            _ => "".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ContentViewOrText {
    Text(String),
    View(Box<ContentView>),
    Views(Vec<ContentView>),
}

impl Default for ContentViewOrText {
    fn default() -> Self {
        Self::Text("".to_string())
    }
}

impl From<ContentViewOrText> for String {
    fn from(value: ContentViewOrText) -> Self {
        match value {
            ContentViewOrText::Text(text) => text,
            ContentViewOrText::View(view) => Into::<String>::into(*view),
            ContentViewOrText::Views(views) => {
                views.into_iter().map(Into::<String>::into).collect()
            }
        }
    }
}

impl From<String> for ContentViewOrText {
    fn from(value: String) -> Self {
        Self::Text(value)
    }
}

impl From<&str> for ContentViewOrText {
    fn from(value: &str) -> Self {
        Self::Text(value.to_string())
    }
}

impl From<Cow<'_, str>> for ContentViewOrText {
    fn from(value: Cow<'_, str>) -> Self {
        Self::Text(value.into_owned())
    }
}

impl From<Vec<ContentView>> for ContentViewOrText {
    fn from(value: Vec<ContentView>) -> Self {
        Self::Views(value)
    }
}

pub struct ContentAppendResult {
    pub operation: ContentDeltaOperation,
    pub path: Vec<serde_json::Value>,
    pub content: serde_json::Value,
}

impl ContentViewOrText {
    pub fn append(&mut self, delta: Self) -> Vec<ContentAppendResult> {
        let mut results = vec![];

        match (&mut *self, delta) {
            (Self::Text(text), Self::Text(delta)) => {
                *text += &delta;

                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Append,
                    path: vec![],
                    content: delta.into(),
                });
            }
            (Self::Views(views), Self::Views(delta)) => {
                let len = views.len();

                results.extend(
                    delta
                        .iter()
                        .enumerate()
                        .map(|(index, view)| ContentAppendResult {
                            operation: ContentDeltaOperation::Update,
                            path: vec![(len + index).into()],
                            content: serde_json::to_value(view).unwrap_or_default(),
                        }),
                );

                views.extend(delta);
            }
            (Self::Text(text), Self::Views(delta)) => {
                let mut views = vec![ContentView::Markdown(text.clone())];
                views.extend(delta);

                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Update,
                    path: vec![],
                    content: serde_json::json!([]),
                });

                results.extend(
                    views
                        .iter()
                        .enumerate()
                        .map(|(index, view)| ContentAppendResult {
                            operation: ContentDeltaOperation::Update,
                            path: vec![index.into()],
                            content: serde_json::to_value(view).unwrap_or_default(),
                        }),
                );

                *self = Self::Views(views);
            }
            (Self::Views(views), Self::Text(delta)) => {
                let view = ContentView::Markdown(delta);
                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Update,
                    path: vec![(views.len() - 1).into()],
                    content: serde_json::to_value(&view).unwrap_or_default(),
                });
                views.push(view);
            }
            (Self::Text(text), Self::View(delta)) => {
                let views = vec![ContentView::Markdown(text.clone()), *delta];

                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Update,
                    path: vec![],
                    content: serde_json::json!([]),
                });

                results.extend(
                    views
                        .iter()
                        .enumerate()
                        .map(|(index, view)| ContentAppendResult {
                            operation: ContentDeltaOperation::Update,
                            path: vec![index.into()],
                            content: serde_json::to_value(view).unwrap_or_default(),
                        }),
                );

                *self = Self::Views(views);
            }
            (Self::View(view), Self::View(delta)) => {
                let views = vec![core::mem::take(view.as_mut()), *delta];

                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Update,
                    path: vec![],
                    content: serde_json::json!([]),
                });

                results.extend(
                    views
                        .iter()
                        .enumerate()
                        .map(|(index, view)| ContentAppendResult {
                            operation: ContentDeltaOperation::Update,
                            path: vec![index.into()],
                            content: serde_json::to_value(view).unwrap_or_default(),
                        }),
                );

                *self = Self::Views(views);
            }
            (Self::View(view), Self::Views(delta)) => {
                let mut views = vec![core::mem::take(view.as_mut())];
                views.extend(delta);

                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Update,
                    path: vec![],
                    content: serde_json::json!([]),
                });

                results.extend(
                    views
                        .iter()
                        .enumerate()
                        .map(|(index, view)| ContentAppendResult {
                            operation: ContentDeltaOperation::Update,
                            path: vec![index.into()],
                            content: serde_json::to_value(view).unwrap_or_default(),
                        }),
                );

                *self = Self::Views(views);
            }
            (Self::Views(views), Self::View(delta)) => {
                views.push(*delta);
                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Update,
                    path: vec![(views.len() - 1).into()],
                    content: serde_json::to_value(views.last().unwrap()).unwrap_or_default(),
                });
            }
            (Self::View(view), Self::Text(delta)) => {
                let views = vec![core::mem::take(view.as_mut()), ContentView::Markdown(delta)];

                results.push(ContentAppendResult {
                    operation: ContentDeltaOperation::Update,
                    path: vec![],
                    content: serde_json::json!([]),
                });

                results.extend(
                    views
                        .iter()
                        .enumerate()
                        .map(|(index, view)| ContentAppendResult {
                            operation: ContentDeltaOperation::Update,
                            path: vec![index.into()],
                            content: serde_json::to_value(view).unwrap_or_default(),
                        }),
                );
            }
        }

        results
    }

    pub fn append_str(&mut self, delta: &str) -> Vec<ContentAppendResult> {
        self.append(Self::Text(delta.to_string()))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ContentCard {
    pub title: String,
    pub r#type: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub content: Option<ContentViewOrText>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<ContentViewOrText>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub download_url: Option<serde_json::Value>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub link: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub hide_details: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ContentCardDelta {
    pub title: Option<String>,
    pub description: Option<String>,
    pub content: Option<ContentViewOrText>,
    pub details: Option<ContentViewOrText>,
}

impl ContentCardDelta {
    pub fn apply(&self, card: &mut ContentCard) -> Vec<ContentAppendResult> {
        let mut results = vec![];

        if let Some(title) = &self.title {
            card.title += title;

            results.push(ContentAppendResult {
                operation: ContentDeltaOperation::Append,
                path: vec!["title".into()],
                content: serde_json::json!(title),
            });
        }

        if let Some(description) = &self.description {
            match &mut card.description {
                Some(d) => *d += description,
                None => card.description = Some(description.clone()),
            }

            results.push(ContentAppendResult {
                operation: ContentDeltaOperation::Append,
                path: vec!["description".into()],
                content: serde_json::json!(description),
            });
        }

        if let Some(content) = &self.content {
            match &mut card.content {
                Some(c) => {
                    let r = c.append(content.clone());
                    results.extend(r.into_iter().map(|mut r| {
                        r.path.insert(0, "content".into());
                        r
                    }));
                }
                None => {
                    card.content = Some(content.clone());

                    results.push(ContentAppendResult {
                        operation: ContentDeltaOperation::Update,
                        path: vec!["content".into()],
                        content: serde_json::json!(content),
                    });
                }
            }
        }

        if let Some(details) = &self.details {
            match &mut card.details {
                Some(d) => {
                    let r = d.append(details.clone());
                    results.extend(r.into_iter().map(|mut r| {
                        r.path.insert(0, "details".into());
                        r
                    }));
                }
                None => {
                    card.details = Some(details.clone());

                    results.push(ContentAppendResult {
                        operation: ContentDeltaOperation::Update,
                        path: vec!["details".into()],
                        content: serde_json::json!(details),
                    });
                }
            }
        }

        results
    }
}

impl FromIterator<ContentItemInner> for ai::Content {
    fn from_iter<T: IntoIterator<Item = ContentItemInner>>(iter: T) -> Self {
        let mut result = String::new();

        for item in iter {
            match item {
                ContentItemInner::Text { text } => result += &text,
                ContentItemInner::View { view, .. } => result += &Into::<String>::into(view),
                _ => {}
            }

            result += "\n";
        }

        ai::Content::Text(result)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContentDeltaOperation {
    #[serde(rename = "update")]
    Update,
    #[serde(rename = "append")]
    Append,
}
