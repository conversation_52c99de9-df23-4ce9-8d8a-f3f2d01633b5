#[cfg(test)]
mod tests {
    use error::Result;
    use serde_json::json;

    use crate::eval_expr_string;

    fn eval_expression(expression: &str, context: &serde_json::Value) -> Result<bool> {
        println!(
            "调用 eval_expression: '{expression}' 上下文: {context:?}"
        );
        // 简单进行debug打印
        let result = eval_expr_string(expression, context);
        println!("eval_expr_string 返回: {result:?}");
        result
    }

    #[test]
    fn test_single_bool_true() {
        println!("Running test_single_bool_true");
        let ctx = json!({"a": true});
        assert!(eval_expression("${a} == true", &ctx).unwrap());
    }

    #[test]
    fn test_single_bool_false() {
        let ctx = json!({"a": false});
        assert!(!eval_expression("${a} == true", &ctx).unwrap());
        assert!(eval_expression("${a} == false", &ctx).unwrap());
    }

    #[test]
    fn test_and() {
        let ctx = json!({"a": 1, "b": 2});
        assert!(eval_expression("${a} == 1 and ${b} == 2", &ctx).unwrap());
        assert!(!eval_expression("${a} == 1 and ${b} == 3", &ctx).unwrap());
    }

    #[test]
    fn test_or() {
        let ctx = json!({"a": 1, "b": 2});
        assert!(eval_expression("${a} == 1 or ${b} == 3", &ctx).unwrap());
        assert!(!eval_expression("${a} == 0 or ${b} == 3", &ctx).unwrap());
    }

    #[test]
    fn test_and_or_priority() {
        let ctx = json!({"a": 1, "b": 2, "c": 3});
        assert!(eval_expression("${a} == 1 and ${b} == 2 or ${c} == 0", &ctx).unwrap());
        assert!(eval_expression("${a} == 1 and ${b} == 3 or ${c} == 3", &ctx).unwrap());
        assert!(eval_expression("${c} == 3 or ${a} == 1 and ${b} == 3", &ctx).unwrap());
        assert!(eval_expression("${a} == 0 and ${b} == 2 or ${c} == 3", &ctx).unwrap());
        assert!(!eval_expression("${a} == 0 and ${b} == 2 or ${c} == 0", &ctx).unwrap());
    }

    #[test]
    fn test_brackets() {
        let ctx = json!({"a": 1, "b": 2, "c": 3});
        assert!(eval_expression("(${a} == 1 or ${b} == 3) and ${c} == 3", &ctx).unwrap());
        assert!(!eval_expression("(${a} == 0 or ${b} == 3) and ${c} == 3", &ctx).unwrap());
    }

    #[test]
    fn test_nested_brackets() {
        let ctx = json!({"a": 1, "b": 2, "c": 3});
        assert!(eval_expression("(((${a} == 1))) and (${b} == 2)", &ctx).unwrap());
    }

    #[test]
    fn test_string_and_number() {
        let ctx = json!({"s": "hello", "n": 42});
        assert!(eval_expression("${s} == \"hello\" and ${n} == 42", &ctx).unwrap());
        assert!(eval_expression("${s} == \"world\" or ${n} == 42", &ctx).unwrap());
    }

    #[test]
    fn test_array_index() {
        let ctx = json!({"arr": [1, 2, 3]});
        assert!(eval_expression("${arr[0]} == 1 and ${arr[2]} == 3", &ctx).unwrap());
        assert!(eval_expression("${arr[1]} == 3 or ${arr[2]} == 3", &ctx).unwrap());
    }

    #[test]
    fn test_comparison_operators() {
        let ctx = json!({"num": 10, "str": "abc"});
        assert!(eval_expression("${num} != 5", &ctx).unwrap());
        assert!(!eval_expression("${num} != 10", &ctx).unwrap());

        assert!(eval_expression("${num} < 20", &ctx).unwrap());
        assert!(!eval_expression("${num} < 5", &ctx).unwrap());

        assert!(eval_expression("${num} <= 10", &ctx).unwrap());
        assert!(eval_expression("${num} <= 20", &ctx).unwrap());
        assert!(!eval_expression("${num} <= 5", &ctx).unwrap());

        assert!(eval_expression("${num} > 5", &ctx).unwrap());
        assert!(!eval_expression("${num} > 10", &ctx).unwrap());

        assert!(eval_expression("${num} >= 10", &ctx).unwrap());
        assert!(eval_expression("${num} >= 5", &ctx).unwrap());
        assert!(!eval_expression("${num} >= 20", &ctx).unwrap());

        assert!(eval_expression("${str} < \"def\"", &ctx).unwrap());
        assert!(eval_expression("${str} > \"aaa\"", &ctx).unwrap());
        assert!(eval_expression("${str} == \"abc\"", &ctx).unwrap());
    }

    #[test]
    fn test_complex_expressions_with_operators() {
        let ctx = json!({"a": 5, "b": 10, "c": 15});
        assert!(eval_expression("${a} < 10 and ${b} == 10", &ctx).unwrap());
        assert!(eval_expression("${a} >= 5 and ${c} > ${b}", &ctx).unwrap());
        assert!(eval_expression("${a} > 10 or ${b} <= 10", &ctx).unwrap());
        assert!(eval_expression("(${a} < ${b}) and (${b} < ${c})", &ctx).unwrap());
    }

    #[test]
    fn test_nonexistent_key() {
        let ctx = json!({});
        assert!(!eval_expression("${nonexistent} == 1", &ctx).unwrap());
        assert!(eval_expression("${nonexistent} == null", &ctx).unwrap());
    }
}
