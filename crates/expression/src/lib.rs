use serde::{Deserialize, Serialize};

mod exp;

// 不需要显式导入Rule，pest_derive会自动生成
// use self::Rule;

#[derive(Debug, PartialEq, Serialize, Deserialize, Clone, Hash)]
#[serde(tag = "type", content = "content")]
pub enum Expr {
    // 变量（如 `${a}`, `${arr[0]}`）
    #[serde(rename = "variable")]
    Var(Variable),
    // 字面量（true, 42, "hello", null）
    #[serde(rename = "literal")]
    Literal(serde_json::Value),
    // 比较表达式（left OP right）
    #[serde(rename = "compare")]
    Compare {
        left: Box<Expr>,
        op: CompOp,
        right: Box<Expr>,
    },
    // 逻辑表达式（left OP right）
    #[serde(rename = "logical")]
    Logical {
        left: Box<Expr>,
        op: LogicalOp,
        right: Box<Expr>,
    },
}

impl std::fmt::Display for Expr {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match &self {
            Expr::Var(v) => write!(f, "{v:?}"),
            Expr::Literal(l) => write!(f, "{l}"),
            Expr::Compare { left, op, right } => {
                write!(f, "{left} {op:?} {right}")
            }
            Expr::Logical { left, op, right } => {
                write!(f, "{left} {op:?} {right}")
            }
        }
    }
}

// 变量结构（支持多级索引）
#[derive(Debug, PartialEq, Serialize, Deserialize, Clone, Hash)]
#[serde(transparent)]
pub struct Variable(pub Vec<Index>);

impl std::fmt::Display for Variable {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.iter()
            .map(|i| i.to_string())
            .collect::<Vec<String>>()
            .join("")
            .fmt(f)
    }
}

// deref
impl std::ops::Deref for Variable {
    type Target = Vec<Index>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

// 索引类型（数字或字符串）
#[derive(Debug, PartialEq, Serialize, Deserialize, Clone, Hash)]
#[serde(untagged)]
pub enum Index {
    Number(i64),
    String(String),
}

impl std::fmt::Display for Index {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Index::Number(n) => write!(f, "[{n}]"),
            Index::String(s) => write!(f, ".{s}"),
        }
    }
}

// 比较运算符枚举
#[derive(Debug, PartialEq, Clone, Serialize, Deserialize, Hash)]
pub enum CompOp {
    #[serde(rename = "==")]
    Eq, // ==
    #[serde(rename = "!=")]
    Ne, // !=
    #[serde(rename = "<")]
    Lt, // <
    #[serde(rename = ">")]
    Gt, // >
    #[serde(rename = "<=")]
    Le, // <=
    #[serde(rename = ">=")]
    Ge, // >=
}

impl std::fmt::Display for CompOp {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CompOp::Eq => write!(f, "=="),
            CompOp::Ne => write!(f, "!="),
            CompOp::Lt => write!(f, "<"),
            CompOp::Gt => write!(f, ">"),
            CompOp::Le => write!(f, "<="),
            CompOp::Ge => write!(f, ">="),
        }
    }
}

// 逻辑运算符枚举
#[derive(Debug, PartialEq, Clone, Serialize, Deserialize, Hash)]
pub enum LogicalOp {
    #[serde(rename = "and")]
    And,
    #[serde(rename = "or")]
    Or,
}

impl std::fmt::Display for LogicalOp {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LogicalOp::And => write!(f, "and"),
            LogicalOp::Or => write!(f, "or"),
        }
    }
}

/// 将字符串表达式解析为Expr结构体
///
/// 这个函数负责将表达式字符串解析为结构化的Expr枚举，
/// 支持解析括号、逻辑运算符（and、or）、比较运算符以及变量引用和字面量。
///
/// # 参数
///
/// * `expression` - 要解析的表达式字符串
///
/// # 返回值
///
/// 返回Result<Expr>，成功时包含解析后的表达式结构体
///
/// # 错误
///
/// 当表达式格式不正确或无法解析时返回错误
///
/// # 示例
///
/// ```
/// use expression::parse_expr;
/// let expr = parse_expr("${a} == 10 and ${b} > 5").unwrap();
/// ```
pub fn parse_expr(expression: &str) -> error::Result<Expr> {
    let expr = expression.trim();
    // 递归处理外层完整括号
    if expr.starts_with('(') && expr.ends_with(')') {
        let mut depth = 0;
        let mut matched = false;
        for (i, c) in expr.chars().enumerate() {
            match c {
                '(' => depth += 1,
                ')' => depth -= 1,
                _ => {}
            }
            if depth == 0 && i != expr.len() - 1 {
                matched = false;
                break;
            } else if depth == 0 && i == expr.len() - 1 {
                matched = true;
            }
        }
        if matched {
            return parse_expr(&expr[1..expr.len() - 1]);
        }
    }

    // 处理 or（最低优先级）
    let mut depth = 0;
    let mut last = 0;
    let mut or_parts = vec![];
    let chars: Vec<_> = expr.char_indices().collect();
    let mut i = 0;
    while i < chars.len() {
        let (idx, c) = chars[i];
        match c {
            '(' => depth += 1,
            ')' => depth -= 1,
            'o' if depth == 0 && expr[idx..].starts_with("or ") => {
                or_parts.push(expr[last..idx].trim());
                last = idx + 2;
                i += 2;
            }
            _ => {}
        }
        i += 1;
    }
    if !or_parts.is_empty() {
        or_parts.push(expr[last..].trim());
        let mut left = parse_expr(or_parts[0])?;
        for part in &or_parts[1..] {
            let right = parse_expr(part)?;
            left = Expr::Logical {
                left: Box::new(left),
                op: LogicalOp::Or,
                right: Box::new(right),
            };
        }
        return Ok(left);
    }

    // 处理 and（高于A or）
    depth = 0;
    last = 0;
    let mut and_parts = vec![];
    let mut i = 0;
    while i < chars.len() {
        let (idx, c) = chars[i];
        match c {
            '(' => depth += 1,
            ')' => depth -= 1,
            'a' if depth == 0 && expr[idx..].starts_with("and ") => {
                and_parts.push(expr[last..idx].trim());
                last = idx + 3;
                i += 3;
            }
            _ => {}
        }
        i += 1;
    }
    if !and_parts.is_empty() {
        and_parts.push(expr[last..].trim());
        let mut left = parse_expr(and_parts[0])?;
        for part in &and_parts[1..] {
            let right = parse_expr(part)?;
            left = Expr::Logical {
                left: Box::new(left),
                op: LogicalOp::And,
                right: Box::new(right),
            };
        }
        return Ok(left);
    }

    // 处理单个表达式
    let re = regex::Regex::new(r"^\s*(.+?)\s*(==|!=|<=|>=|<|>)\s*(.+)\s*$")?;

    let captures = match re.captures(expr) {
        Some(captures) => captures,
        None => {
            // 如果不是比较表达式，则尝试解析为字面量或变量
            if expr.trim().is_empty() {
                return Ok(Expr::Literal(serde_json::Value::Bool(false)));
            }
            // 解析为变量或字面量
            return parse_value_to_expr(expr);
        }
    };

    if captures.len() != 4 {
        return Err(error::ExpressionError::Parse(expr.to_string()).into());
    }

    let left_part = captures.get(1).unwrap().as_str();
    let operator = captures.get(2).unwrap().as_str();
    let right_part = captures.get(3).unwrap().as_str();

    // 解析左值和右值
    let left_expr = parse_value_to_expr(left_part)?;
    let right_expr = parse_value_to_expr(right_part)?;

    // 根据操作符创建比较表达式
    let op = match operator {
        "==" => CompOp::Eq,
        "!=" => CompOp::Ne,
        "<" => CompOp::Lt,
        ">" => CompOp::Gt,
        "<=" => CompOp::Le,
        ">=" => CompOp::Ge,
        _ => {
            return Err(error::ExpressionError::UnknownOperator(operator.to_string()).into());
        }
    };

    Ok(Expr::Compare {
        left: Box::new(left_expr),
        op,
        right: Box::new(right_expr),
    })
}

// 将值字符串解析为Expr
fn parse_value_to_expr(part: &str) -> error::Result<Expr> {
    // 检查是否是动态变量引用 ${...}
    if part.trim().starts_with("${") && part.trim().ends_with("}") && part.trim().len() > 3 {
        let var_path = &part.trim()[2..part.trim().len() - 1];
        let path_re = regex::Regex::new(r"([^\.\[\]]+)|\[(\d+)\]")?;

        let mut indices = Vec::new();

        for cap in path_re.captures_iter(var_path) {
            if let Some(key_match) = cap.get(1) {
                let key = key_match.as_str();
                indices.push(Index::String(key.to_string()));
            } else if let Some(index_match) = cap.get(2) {
                let index: i64 = index_match
                    .as_str()
                    .parse()
                    .map_err(|_| error::ExpressionError::Parse(index_match.as_str().to_string()))?;
                indices.push(Index::Number(index));
            }
        }

        Ok(Expr::Var(Variable(indices)))
    } else {
        // 解析字面量
        match part.trim() {
            "true" => Ok(Expr::Literal(serde_json::Value::Bool(true))),
            "false" => Ok(Expr::Literal(serde_json::Value::Bool(false))),
            "null" => Ok(Expr::Literal(serde_json::Value::Null)),
            num if num.parse::<i64>().is_ok() => Ok(Expr::Literal(serde_json::Value::Number(
                serde_json::Number::from(num.parse::<i64>().unwrap()),
            ))),
            num if num.parse::<f64>().is_ok() => {
                if let Some(num_value) = serde_json::Number::from_f64(num.parse::<f64>().unwrap()) {
                    Ok(Expr::Literal(serde_json::Value::Number(num_value)))
                } else {
                    Err(error::ExpressionError::ExpectNumber(num.to_string()).into())
                }
            }
            str_value
                if str_value.starts_with('"') && str_value.ends_with('"')
                    || str_value.starts_with('\'') && str_value.ends_with('\'') =>
            {
                Ok(Expr::Literal(serde_json::Value::String(
                    str_value[1..str_value.len() - 1].to_string(),
                )))
            }
            _ => Err(error::ExpressionError::ExpectLiteral(part.to_string()).into()),
        }
    }
}

/// 计算给定Expr的布尔值
///
/// 这个函数负责根据上下文计算Expr结构体的布尔值结果。
///
/// # 参数
///
/// * `expr` - 要求值的表达式结构体
/// * `context` - 求值上下文，包含变量的值
///
/// # 返回值
///
/// 返回Result<bool>，成功时包含表达式的布尔求值结果
///
/// # 错误
///
/// 当表达式无法在给定上下文中正确求值时返回错误
///
/// # 示例
///
/// ```
/// use expression::{parse_expr, eval_expr};
/// use serde_json::json;
///
/// let expr = parse_expr("${a} > 5").unwrap();
/// let ctx = json!({"a": 10});
/// let result = eval_expr(&expr, &ctx).unwrap();
/// assert_eq!(result, true);
/// ```
pub fn eval_expr(expr: &Expr, context: &serde_json::Value) -> error::Result<bool> {
    match expr {
        Expr::Literal(value) => {
            match value {
                serde_json::Value::Bool(b) => Ok(*b),
                serde_json::Value::Null => Ok(false),
                _ => Ok(true), // 非空非布尔值视为true
            }
        }
        Expr::Var(var) => {
            // 根据变量路径获取值
            let value = resolve_variable(var, context);
            match value {
                serde_json::Value::Bool(b) => Ok(b),
                serde_json::Value::Null => Ok(false),
                _ => Ok(true), // 非空非布尔值视为true
            }
        }
        Expr::Compare { left, op, right } => {
            let left_value = eval_value(left, context)?;
            let right_value = eval_value(right, context)?;

            match op {
                CompOp::Eq => Ok(value_eq(&left_value, &right_value)),
                CompOp::Ne => Ok(!value_eq(&left_value, &right_value)),
                CompOp::Lt => compare_values(&left_value, &right_value, |a, b| a < b),
                CompOp::Gt => compare_values(&left_value, &right_value, |a, b| a > b),
                CompOp::Le => compare_values(&left_value, &right_value, |a, b| a <= b),
                CompOp::Ge => compare_values(&left_value, &right_value, |a, b| a >= b),
            }
        }
        Expr::Logical { left, op, right } => match op {
            LogicalOp::And => {
                if !eval_expr(left, context)? {
                    return Ok(false);
                }
                eval_expr(right, context)
            }
            LogicalOp::Or => {
                if eval_expr(left, context)? {
                    return Ok(true);
                }
                eval_expr(right, context)
            }
        },
    }
}

// 根据表达式获取值
fn eval_value(expr: &Expr, context: &serde_json::Value) -> error::Result<serde_json::Value> {
    match expr {
        Expr::Literal(value) => Ok(value.clone()),
        Expr::Var(var) => Ok(resolve_variable(var, context)),
        Expr::Compare { left, .. } => eval_value(left, context),
        Expr::Logical { left, .. } => eval_value(left, context),
    }
}

// 解析变量，根据变量名和索引从上下文中获取值
fn resolve_variable(var: &Variable, context: &serde_json::Value) -> serde_json::Value {
    let mut current_value = context;

    for index in var.iter() {
        match index {
            Index::String(key) => {
                current_value = match current_value.get(key) {
                    Some(value) => value,
                    None => &serde_json::Value::Null,
                };
            }
            Index::Number(idx) => {
                if !current_value.is_array() {
                    return serde_json::Value::Null;
                }
                let idx_usize = if *idx >= 0 {
                    *idx as usize
                } else {
                    return serde_json::Value::Null;
                };
                current_value = match current_value.get(idx_usize) {
                    Some(value) => value,
                    None => &serde_json::Value::Null,
                };
            }
        }
    }

    current_value.clone()
}

// 值比较辅助函数
fn value_eq(left_value: &serde_json::Value, right_value: &serde_json::Value) -> bool {
    match (left_value, right_value) {
        (serde_json::Value::Number(a), serde_json::Value::Number(b)) => {
            if let (Some(a_f64), Some(b_f64)) = (a.as_f64(), b.as_f64()) {
                a_f64 == b_f64
            } else {
                false
            }
        }
        (a, b) => a == b,
    }
}

// 比较两个值
fn compare_values<F>(
    left_value: &serde_json::Value,
    right_value: &serde_json::Value,
    cmp: F,
) -> error::Result<bool>
where
    F: FnOnce(f64, f64) -> bool,
{
    match (left_value, right_value) {
        (serde_json::Value::Number(a), serde_json::Value::Number(b)) => {
            if let (Some(a_f64), Some(b_f64)) = (a.as_f64(), b.as_f64()) {
                Ok(cmp(a_f64, b_f64))
            } else {
                Err(error::ExpressionError::MismatchedCompareFormat(
                    a.clone().into(),
                    b.clone().into(),
                )
                .into())
            }
        }
        (serde_json::Value::String(a), serde_json::Value::String(b)) => {
            Ok(cmp(a.cmp(b) as i8 as f64, 0.0))
        }
        _ => Ok(false),
    }
}

/// 解析并计算表达式字符串的布尔值
///
/// 这个函数结合了parse_expr和eval_expr的功能，
/// 首先将表达式字符串解析为Expr结构体，然后计算其布尔值。
///
/// # 参数
///
/// * `expression` - 要解析和求值的表达式字符串
/// * `context` - 求值上下文，包含变量的值
///
/// # 返回值
///
/// 返回Result<bool>，成功时包含表达式的布尔求值结果
///
/// # 错误
///
/// 当表达式格式不正确或无法在给定上下文中正确求值时返回错误
///
/// # 示例
///
/// ```
/// use expression::eval_expr_string;
/// use serde_json::json;
///
/// let ctx = json!({"a": 10, "b": 5});
/// let result = eval_expr_string("${a} > ${b}", &ctx).unwrap();
/// assert_eq!(result, true);
/// ```
pub fn eval_expr_string(expression: &str, context: &serde_json::Value) -> error::Result<bool> {
    let expr = parse_expr(expression)?;
    eval_expr(&expr, context)
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_simple_variable() {
        let ctx = json!({"a": true});
        assert_eval_eq("${a} == true", &ctx, true);
    }

    #[test]
    fn test_empty() {
        let ctx = json!({});
        assert_eval_eq("", &ctx, false);
        assert_eval_eq(" ", &ctx, false);
    }

    #[test]
    fn test_simple_comparison() {
        let ctx = json!({"num": 10});
        assert_eval_eq("${num} == 10", &ctx, true);
        assert_eval_eq("${num} != 5", &ctx, true);
        assert_eval_eq("${num} > 5", &ctx, true);
        assert_eval_eq("${num} < 5", &ctx, false);
    }

    // #[test]
    // fn test_simple_logic() {
    //     let ctx = json!({"a": true, "b": false});
    //     assert_eval_eq("${a} and ${b}", &ctx, false);
    //     assert_eval_eq("${a} or ${b}", &ctx, true);
    // }

    // #[test]
    // fn test_literals() {
    //     let ctx = json!({});
    //     assert_eval_eq("true", &ctx, true);
    //     assert_eval_eq("false", &ctx, false);
    //     assert_eval_eq("null", &ctx, false);
    //     assert_eval_eq("5", &ctx, true);
    //     assert_eval_eq("\"hello\"", &ctx, true);
    // }

    #[test]
    fn test_literal_comparison() {
        let ctx = json!({});
        assert_eval_eq("5 > 3", &ctx, true);
        assert_eval_eq("5 < 3", &ctx, false);
        assert_eval_eq("5 == 5", &ctx, true);
        assert_eval_eq("\"a\" == \"a\"", &ctx, true);
        assert_eval_eq("\"a\" != \"b\"", &ctx, true);
    }

    fn assert_eval_eq(expr_str: &str, context: &serde_json::Value, expected: bool) {
        match eval_expr_string(expr_str, context) {
            Ok(val) => assert_eq!(val, expected, "Expression: '{expr_str}'"),
            Err(e) => panic!("Expression '{expr_str}' failed: {e:?}"),
        }
    }

    #[test]
    fn test_single_bool_true() {
        let ctx = json!({"a": true});
        assert_eval_eq("${a} == true", &ctx, true);
    }

    #[test]
    fn test_single_bool_false() {
        let ctx = json!({"a": false});
        assert_eval_eq("${a} == true", &ctx, false);
        assert_eval_eq("${a} == false", &ctx, true);
    }

    #[test]
    fn test_and() {
        let ctx = json!({"a": 1, "b": 2});
        assert_eval_eq("${a} == 1 and ${b} == 2", &ctx, true);
        assert_eval_eq("${a} == 1 and ${b} == 3", &ctx, false);
    }

    #[test]
    fn test_or() {
        let ctx = json!({"a": 1, "b": 2});
        assert_eval_eq("${a} == 1 or ${b} == 3", &ctx, true);
        assert_eval_eq("${a} == 0 or ${b} == 3", &ctx, false);
    }

    #[test]
    fn test_and_or_priority() {
        let ctx = json!({"a": 1, "b": 2, "c": 3});
        assert_eval_eq("${a} == 1 and ${b} == 2 or ${c} == 0", &ctx, true);
        assert_eval_eq("${a} == 1 and ${b} == 3 or ${c} == 3", &ctx, true);
        assert_eval_eq("${c} == 3 or ${a} == 1 and ${b} == 3", &ctx, true);
        assert_eval_eq("${a} == 0 and ${b} == 2 or ${c} == 3", &ctx, true);
        assert_eval_eq("${a} == 0 and ${b} == 2 or ${c} == 0", &ctx, false);
    }

    #[test]
    fn test_brackets() {
        let ctx = json!({"a": 1, "b": 2, "c": 3});
        assert_eval_eq("(${a} == 1 or ${b} == 3) and ${c} == 3", &ctx, true);
        assert_eval_eq("(${a} == 0 or ${b} == 3) and ${c} == 3", &ctx, false);
        assert_eval_eq("${a} == 1 and (${b} == 3 or ${c} == 3)", &ctx, true);
    }

    #[test]
    fn test_nested_brackets() {
        let ctx = json!({"a": 1, "b": 2, "c": 3});
        assert_eval_eq("(((${a} == 1))) and (${b} == 2)", &ctx, true);
    }

    #[test]
    fn test_string_and_number_literals() {
        let ctx = json!({"s": "hello", "n": 42});
        assert_eval_eq("${s} == \"hello\" and ${n} == 42", &ctx, true);
        assert_eval_eq("${s} == \"world\" or ${n} == 42", &ctx, true);
        assert_eval_eq("${s} == \"hello\"", &ctx, true);
        assert_eval_eq("${n} == 42", &ctx, true);
        assert_eval_eq("\"string\" == \"string\"", &ctx, true);
        assert_eval_eq("123 == 123", &ctx, true);
        assert_eval_eq("-5 == -5", &ctx, true);
        assert_eval_eq("3.14 == 3.14", &ctx, true);
        assert_eval_eq("true == true", &ctx, true);
        assert_eval_eq("null == null", &ctx, true);
    }

    #[test]
    fn test_array_index() {
        let ctx = json!({
            "arr": [10, 20, 30, {"nested_key": "nested_val"}],
            "obj": {"prop": "val", "nested": {"num": 7}}
        });
        assert_eval_eq("${arr[0]} == 10 and ${arr[2]} == 30", &ctx, true);
        assert_eval_eq("${arr[1]} == 30 or ${arr[2]} == 30", &ctx, true);
        assert_eval_eq("${arr[99]} == null", &ctx, true);
        // assert_eval_eq("${obj[\"prop\"]} == \"val\"", &ctx, true);
        // assert_eval_eq("${obj[\"missing\"]} == null", &ctx, true);
        // assert_eval_eq("${obj[\"nested\"][\"num\"]} == 7", &ctx, true);
        // assert_eval_eq("${obj[\"nested\"][\"num\"]} > 6", &ctx, true);
        // assert_eval_eq("${obj[\"nested\"][\"missing\"]} == null", &ctx, true);
        // assert_eval_eq("${arr[3][\"nested_key\"]} == \"nested_val\"", &ctx, true);
        // assert_eval_eq("${arr[3][\"bad_key\"]} == null", &ctx, true);
        // assert_eval_eq("${arr[0][\"bad_key\"]} == null", &ctx, true); // Indexing non-object with string
    }

    #[test]
    fn test_comparison_operators_numbers() {
        let ctx = json!({"num": 10, "f_num": 10.5, "neg": -5});
        assert_eval_eq("${num} != 5", &ctx, true);
        assert_eval_eq("${num} != 10", &ctx, false);
        assert_eval_eq("${num} < 20", &ctx, true);
        assert_eval_eq("${num} < 5", &ctx, false);
        assert_eval_eq("${num} <= 10", &ctx, true);
        assert_eval_eq("${num} > 5", &ctx, true);
        assert_eval_eq("${num} >= 10", &ctx, true);
        assert_eval_eq("${f_num} > 10", &ctx, true);
        assert_eval_eq("${f_num} < 10.6", &ctx, true);
        assert_eval_eq("${f_num} == 10.5", &ctx, true);
        assert_eval_eq("${neg} < 0", &ctx, true);
        assert_eval_eq("${neg} == -5", &ctx, true);
    }

    // #[test]
    // fn test_non_finite_numbers() {
    //     let ctx_nan = json!({ "val": f64::NAN });
    //     let ctx_inf = json!({ "val": f64::INFINITY });
    //     let ctx_neg_inf = json!({ "val": f64::NEG_INFINITY });

    //     // NaN comparisons (== and != are direct, others error)
    //     assert_eval_eq("${val} == ${val}", &ctx_nan, false);
    //     assert_eval_eq("${val} != ${val}", &ctx_nan, true);

    //     // Infinity comparisons (== and != are direct, others error if both are non-finite and same)
    //     assert_eval_eq("${val} > 100", &ctx_inf, true);
    //     assert_eval_eq("${val} < 100", &ctx_inf, false);
    //     assert_eval_eq("${val} == ${val}", &ctx_inf, true);

    //     assert_eval_eq("${val} < -100", &ctx_neg_inf, true);
    //     assert_eval_eq("${val} > -100", &ctx_neg_inf, false);
    //     assert_eval_eq("${val} == ${val}", &ctx_neg_inf, true);
    // }

    #[test]
    fn test_comparison_operators_strings() {
        let ctx = json!({"str": "abc"});
        assert_eval_eq("${str} != \"def\"", &ctx, true);
        assert_eval_eq("${str} != \"abc\"", &ctx, false);
        assert_eval_eq("${str} < \"def\"", &ctx, true);
        assert_eval_eq("${str} <= \"abc\"", &ctx, true);
        assert_eval_eq("${str} > \"aa\"", &ctx, true);
        assert_eval_eq("${str} >= \"abc\"", &ctx, true);
    }

    #[test]
    fn test_complex_expressions_with_operators() {
        let ctx = json!({"a": 5, "b": 10, "c": 15});
        assert_eval_eq("${a} < 10 and ${b} == 10", &ctx, true);
        assert_eval_eq("${a} >= 5 and ${c} > ${b}", &ctx, true);
        assert_eval_eq("${a} > 10 or ${b} <= 10", &ctx, true);
        assert_eval_eq("(${a} < ${b}) and (${b} < ${c})", &ctx, true);
    }

    #[test]
    fn test_nonexistent_key() {
        let ctx = json!({});
        assert_eval_eq("${nonexistent} == null", &ctx, true);
        assert_eval_eq("${nonexistent.child} == null", &ctx, true);
        assert_eval_eq("${nonexistent[0]} == null", &ctx, true);
        assert_eval_eq("${nonexistent} == true", &ctx, false);
        assert_eval_eq("${nonexistent} != true", &ctx, true);
    }

    #[test]
    fn test_empty_expression() {
        let ctx = json!({});
        assert_eval_eq(" ", &ctx, false);
        assert_eval_eq("", &ctx, false);
    }

    #[test]
    fn test_string_comparisons_with_numbers_should_be_error_or_false() {
        let ctx = json!({"s": "10", "n": 10});
        assert_eval_eq("${s} == ${n}", &ctx, false); // Type mismatch for == is false
    }

    #[test]
    fn test_null_comparisons() {
        let ctx = json!({"n_val": null, "num": 5, "str_val": "text"});
        assert_eval_eq("${n_val} == null", &ctx, true);
        assert_eval_eq("${n_val} != null", &ctx, false);
        assert_eval_eq("${num} != null", &ctx, true);
        assert_eval_eq("${num} == null", &ctx, false);
        assert_eval_eq("${str_val} != null", &ctx, true);

        // assert_eval_eq("${n_val} < ${num}", &ctx, false);
        // assert_eval_eq("${num} > ${n_val}", &ctx, false);
        // assert_eval_eq("${n_val} <= ${num}", &ctx, false);
        // assert_eval_eq("${num} >= ${n_val}", &ctx, false);
        // assert_eval_eq("${n_val} < ${str_val}", &ctx, false);
        // assert_eval_eq("${str_val} > ${n_val}", &ctx, false);
    }

    // #[test]
    // fn test_boolean_comparisons() {
    //     let ctx = json!({"t": true, "f": false, "num": 0, "s": "true"});
    //     assert_eval_eq("${t} == true", &ctx, true);
    //     assert_eval_eq("${f} == false", &ctx, true);
    //     assert_eval_eq("${t} != false", &ctx, true);
    //     assert_eval_eq("${f} != true", &ctx, true);
    //     assert_eval_eq("${t} == ${f}", &ctx, false);
    //     assert_eval_eq("${t} != ${f}", &ctx, true);

    //     assert_eval_eq("${t} > ${f}", &ctx, false);
    //     assert_eval_eq("${t} < ${f}", &ctx, false);
    //     assert_eval_eq("${t} > ${num}", &ctx, false);
    //     assert_eval_eq("${f} < ${num}", &ctx, false);
    //     assert_eval_eq("${t} == ${s}", &ctx, false); // true == "true" is false
    // }

    // #[test]
    // fn test_escaped_strings_in_expression() {
    //     let ctx = json!({"name": "complex \"name\""});
    //     assert_eval_eq("${name} == \"complex \\\"name\\\"\"", &ctx, true);
    //     assert_eval_eq("\"a \\\"b\\\" c\" == \"a \\\"b\\\" c\"", &json!({}), true);
    //     assert_eval_eq("\"test\" == \"test\"", &json!({}), true);
    // }

    #[test]
    fn test_parsing_various_numbers() {
        assert_eval_eq("1 == 1.0", &json!({}), true);
        assert_eval_eq("1.23 == 1.23", &json!({}), true);
        assert_eval_eq("1e3 == 1000", &json!({}), true);
        assert_eval_eq("1.2e-2 == 0.012", &json!({}), true);
        assert_eval_eq("-5 == -5.0", &json!({}), true);
    }

    #[test]
    fn test_dot_notation() {
        let ctx = json!({
            "obj": {
                "prop": "value",
                "nested": {
                    "num": 42,
                    "arr": [1, 2, 3]
                }
            }
        });

        // 点号访问
        assert_eval_eq("${obj.prop} == \"value\"", &ctx, true);
        assert_eval_eq("${obj.nested.num} == 42", &ctx, true);
        assert_eval_eq("${obj.nested.num} > 40", &ctx, true);

        // 点号和方括号混合使用
        assert_eval_eq("${obj.nested.arr[1]} == 2", &ctx, true);
        assert_eval_eq("${obj.nested.arr[0]} < ${obj.nested.arr[2]}", &ctx, true);

        // 访问不存在的属性
        assert_eval_eq("${obj.missing} == null", &ctx, true);
        assert_eval_eq("${obj.nested.missing} == null", &ctx, true);
        assert_eval_eq("${obj.missing.prop} == null", &ctx, true);
    }

    // #[test]
    // fn test_accessor_equivalence() {
    //     let ctx = json!({
    //         "user": {
    //             "name": "张三",
    //             "profile": {
    //                 "age": 30,
    //                 "address": {
    //                     "city": "上海"
    //                 }
    //             },
    //             "roles": ["admin", "user"]
    //         }
    //     });

    //     // 测试点号和方括号等价访问
    //     assert_eval_eq("${user.name} == ${user[\"name\"]}", &ctx, true);
    //     assert_eval_eq(
    //         "${user.profile.age} == ${user[\"profile\"][\"age\"]}",
    //         &ctx,
    //         true,
    //     );
    //     assert_eval_eq(
    //         "${user.profile.address.city} == ${user[\"profile\"][\"address\"][\"city\"]}",
    //         &ctx,
    //         true,
    //     );

    //     // 混合使用
    //     assert_eval_eq("${user.profile[\"age\"]} == 30", &ctx, true);
    //     assert_eval_eq("${user[\"profile\"].age} == 30", &ctx, true);

    //     // 数组访问
    //     assert_eval_eq("${user.roles[0]} == \"admin\"", &ctx, true);
    //     assert_eval_eq("${user.roles[1]} == ${user[\"roles\"][1]}", &ctx, true);

    //     // 不存在的属性
    //     assert_eval_eq("${user.missing} == ${user[\"missing\"]}", &ctx, true);
    //     assert_eval_eq(
    //         "${user.profile.missing} == ${user[\"profile\"][\"missing\"]}",
    //         &ctx,
    //         true,
    //     );
    // }

    #[test]
    fn test_function_interoperability() {
        let ctx = json!({"a": 5, "b": 10});
        let expr_str = "${a} < ${b} and ${b} == 10";

        // 测试 parse_expr
        let parsed_expr = parse_expr(expr_str).unwrap();
        match &parsed_expr {
            Expr::Logical { left, op, right } => {
                assert!(matches!(op, LogicalOp::And));
                match &**left {
                    Expr::Compare { left, op, right } => {
                        assert!(matches!(op, CompOp::Lt));
                        if let Expr::Var(v) = &**left {
                            assert_eq!(v[0], Index::String("a".to_string()));
                        } else {
                            panic!("Expected Var expression for left operand of <");
                        }
                        if let Expr::Var(v) = &**right {
                            assert_eq!(v[0], Index::String("b".to_string()));
                        } else {
                            panic!("Expected Var expression for right operand of <");
                        }
                    }
                    _ => panic!("Expected Compare expression for left of AND"),
                }
                match &**right {
                    Expr::Compare { left, op, right } => {
                        assert!(matches!(op, CompOp::Eq));
                        if let Expr::Var(v) = &**left {
                            assert_eq!(v[0], Index::String("b".to_string()));
                        } else {
                            panic!("Expected Var expression for left operand of ==");
                        }
                        if let Expr::Literal(v) = &**right {
                            assert_eq!(v.as_i64().unwrap(), 10);
                        } else {
                            panic!("Expected Literal expression for right operand of ==");
                        }
                    }
                    _ => panic!("Expected Compare expression for right of AND"),
                }
            }
            _ => panic!("Expected Logical expression"),
        }

        // 测试 eval_expr
        let result = eval_expr(&parsed_expr, &ctx).unwrap();
        assert!(result);

        // 测试 eval_expr_string
        let string_result = eval_expr_string(expr_str, &ctx).unwrap();
        assert!(string_result);

        // 测试三个函数的一致性
        assert_eq!(result, string_result);
    }

    #[test]
    fn test_parse_expr_basic() {
        // 测试基本变量解析
        let expr = parse_expr("${a}").unwrap();
        if let Expr::Var(var) = expr {
            assert_eq!(var[0], Index::String("a".to_string()));
            assert_eq!(var.len(), 1);
        } else {
            panic!("Expected Var expression");
        }

        // 测试字面量解析
        let expr = parse_expr("42").unwrap();
        if let Expr::Literal(value) = expr {
            assert_eq!(value.as_i64().unwrap(), 42);
        } else {
            panic!("Expected Literal expression");
        }

        // 测试字符串字面量
        let expr = parse_expr("\"hello\"").unwrap();
        if let Expr::Literal(value) = expr {
            assert_eq!(value.as_str().unwrap(), "hello");
        } else {
            panic!("Expected Literal expression");
        }
    }

    #[test]
    fn test_parse_expr_comparison() {
        // 测试比较表达式
        let expr = parse_expr("${a} == 10").unwrap();
        if let Expr::Compare { left, op, right } = expr {
            assert!(matches!(op, CompOp::Eq));
            if let Expr::Var(var) = &*left {
                assert_eq!(var[0], Index::String("a".to_string()));
            } else {
                panic!("Expected Var expression for left operand");
            }
            if let Expr::Literal(value) = &*right {
                assert_eq!(value.as_i64().unwrap(), 10);
            } else {
                panic!("Expected Literal expression for right operand");
            }
        } else {
            panic!("Expected Compare expression");
        }
    }

    #[test]
    fn test_parse_expr_logical() {
        // 测试逻辑表达式
        let expr = parse_expr("${a} == 1 and ${b} == 2").unwrap();
        if let Expr::Logical { left, op, .. } = expr {
            assert!(matches!(op, LogicalOp::And));
            if let Expr::Compare { left: left_var, .. } = &*left {
                if let Expr::Var(var) = &**left_var {
                    assert_eq!(var[0], Index::String("a".to_string()));
                } else {
                    panic!("Expected Var expression for left of AND");
                }
            } else {
                panic!("Expected Compare expression for left of AND");
            }
        } else {
            panic!("Expected Logical expression");
        }
    }

    #[test]
    fn test_parse_expr_nested() {
        // 测试嵌套表达式
        let expr = parse_expr("(${a} == 1 or ${b} == 2) and ${c} == 3").unwrap();
        if let Expr::Logical { left, op, .. } = expr {
            assert!(matches!(op, LogicalOp::And));
            if let Expr::Logical {
                left: left2,
                op: op2,
                right: right2,
            } = &*left
            {
                assert!(matches!(op2, LogicalOp::Or));
                if let Expr::Compare { .. } = &**left2 {
                } else {
                    panic!("left2 is not compare");
                }
                if let Expr::Compare { .. } = &**right2 {
                } else {
                    panic!("right2 is not compare");
                }
            } else {
                panic!("left is not logical");
            }
        } else {
            panic!("Expected Logical expression");
        }
    }

    #[test]
    fn test_parse_expr_array_access() {
        // 测试数组访问
        let expr = parse_expr("${arr[0]} == 1").unwrap();
        if let Expr::Compare { left, op, .. } = expr {
            assert!(matches!(op, CompOp::Eq));
            if let Expr::Var(var) = &*left {
                assert_eq!(var[0], Index::String("arr".to_string()));
                assert_eq!(var.len(), 2);
                if let Index::Number(idx) = &var[1] {
                    assert_eq!(*idx, 0);
                } else {
                    panic!("Expected Number index");
                }
            } else {
                panic!("Expected Var expression");
            }
        } else {
            panic!("Expected Compare expression");
        }
    }

    #[test]
    fn test_parse_expr_invalid() {
        // 测试无效表达式
        assert!(parse_expr("${a} == ").is_err());
        assert!(parse_expr("and").is_err());
        assert!(parse_expr("${a} == 1 and").is_err());
        assert!(parse_expr("(${a} == 1").is_err());
    }
}
