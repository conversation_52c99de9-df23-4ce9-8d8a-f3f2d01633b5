// 定义空白符（自动忽略）
WHITESPACE = _{ " " | "\t" | "\n" | "\r" }

// 变量解析（如 `${a}`, `${arr[0]}`, `${obj.prop}`）
variable = ${ "${" ~ ident ~ accessor* ~ "}" }
accessor = _{ bracket_accessor | dot_accessor }
bracket_accessor = { "[" ~ (int | string_literal) ~ "]" }  // 数组或对象的方括号访问 (arr[0] 或 obj["key"])
dot_accessor = { "." ~ ident }  // 对象的点号访问 (obj.key)

// 整数定义
int      = @{ "-"? ~ ASCII_DIGIT+ }  // 允许负数（如 -42）

// 标识符（变量名）
ident    = @{ ASCII_ALPHA ~ (ASCII_ALPHANUMERIC | "_")* }

// 字面量（布尔、数字、字符串、null）
literal = _{ boolean | null | number | string_literal }
boolean  = { "true" | "false" }
null     = { "null" }
number   = @{ "-"? ~ ("0" | ASCII_NONZERO_DIGIT ~ ASCII_DIGIT*) ~ ("." ~ ASCII_DIGIT+)? ~ (^"e" ~ int)? }  // 支持科学计数法
string_literal = ${ "\"" ~ inner_string ~ "\"" }
inner_string = @{ (!"\"" ~ ("\\" ~ "\"" | ANY))* }  // 支持转义引号

// 比较运算符（==, !=, <, >, <=, >=）
comp_op = @{ "==" | "!=" | "<=" | ">=" | "<" | ">" }

// 逻辑运算符（and, or）
logical_op_and = @{ "and" }
logical_op_or = @{ "or" }

// 表达式优先级处理
expr = { logical_expr }
logical_expr = { comparison ~ ((logical_op_and | logical_op_or) ~ comparison)* }
comparison = { primary ~ (comp_op ~ primary)? }
primary = _{ term | parenthesized_expr }
parenthesized_expr = { "(" ~ expr ~ ")" }
term = _{ variable | literal }

// 定义一个完整的表达式规则，包含SOI和EOI
equation = _{ SOI ~ expr ~ EOI }