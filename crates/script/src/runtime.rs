pub fn set_runtime_builder(builder: tokio::runtime::Builder) {
    pyo3_async_runtimes::tokio::init(builder);
}

pub fn get_runtime<'a>() -> &'a tokio::runtime::Runtime {
    pyo3_async_runtimes::tokio::get_runtime::<'a>()
}

pub fn block_on<F, T, Func>(func: Func) -> error::Result<T>
where
    F: Future<Output = error::Result<T>> + Send + 'static,
    Func: FnOnce() -> F + Send + Sync + 'static,
    T: Send + Sync + 'static,
{
    let result = pyo3::Python::with_gil(|py| {
        pyo3_async_runtimes::tokio::run(py, async {
            let result = func()
                .await
                .inspect_err(|e| tracing::debug!("Failed to run main: {:?}", e))
                .map_err(|e| pyo3::exceptions::PyException::new_err(e.to_string()))?;

            pyo3::PyResult::Ok(result)
        })
        .inspect_err(|e| {
            e.print_and_set_sys_last_vars(py);
        })
    })?;

    Ok(result)
}
