//! Lua Script Support Module
//! Implements loading and running of Lua scripts

use std::{collections::HashMap, path::PathBuf, sync::Arc};

use mlua::{LuaSerdeExt as _, UserData};
use serde::{Deserialize, Serialize};

use crate::{config::ScriptFile, pool::Pool};

use super::{
    SourceTool,
    tool::{ScriptLanguage, ScriptTool},
};

macro_rules! lua_error {
    ($result:expr) => {
        $result.map_err(|e| mlua::Error::external(e))?
    };
}

macro_rules! lua_value {
    ($lua:expr, $value:expr) => {
        $lua.to_value_with(
            &$value,
            mlua::SerializeOptions::default()
                .serialize_none_to_null(false)
                .serialize_unit_to_null(false),
        )
    };
}

#[derive(Serialize, Deserialize)]
struct Request {
    method: String,
    url: String,
    headers: Option<serde_json::Value>,
    body: Option<serde_json::Value>,
}

#[tracing::instrument(skip(lua, request))]
async fn request(lua: mlua::Lua, request: mlua::Value) -> mlua::Result<mlua::Value> {
    let Request {
        method,
        url,
        headers,
        body,
    }: Request = lua
        .from_value(request)
        .inspect_err(|e| tracing::debug!("Failed to deserialize request: {:?}", e))?;

    tracing::debug!("Lua HTTP request: {} {}", method, url);

    let headers: Option<error::Result<reqwest::header::HeaderMap>> = headers
        .as_ref()
        .map(|headers| headers.as_object())
        .flatten()
        .map(|object| {
            Ok(reqwest::header::HeaderMap::from_iter(
                object
                    .iter()
                    .map(|(k, v)| {
                        Ok((
                            lua_error!(reqwest::header::HeaderName::from_bytes(k.as_bytes())),
                            lua_error!(reqwest::header::HeaderValue::from_bytes(
                                v.as_str().unwrap_or_default().as_bytes(),
                            )),
                        ))
                    })
                    .collect::<error::Result<Vec<_>>>()?,
            ))
        });

    let client = reqwest::Client::new().request(
        lua_error!(reqwest::Method::from_bytes(method.as_bytes())),
        &url,
    );

    let client = if let Some(headers) = headers {
        client.headers(lua_error!(headers))
    } else {
        client
    };

    let response = lua_error!(
        client
            .json(&body.unwrap_or(serde_json::Value::Null))
            .send()
            .await
            .inspect_err(|e| tracing::debug!("HTTP request failed: {:?}", e))
    );

    let status = response.status().as_u16();
    tracing::debug!("HTTP response status: {}", status);

    let headers: serde_json::Value = serde_json::Value::Object(serde_json::Map::from_iter(
        response.headers().iter().map(|(key, value)| {
            (
                key.as_str().to_string(),
                serde_json::Value::String(String::from_utf8_lossy(value.as_bytes()).into_owned()),
            )
        }),
    ));

    let bytes = lua_error!(
        response
            .bytes()
            .await
            .inspect_err(|e| tracing::debug!("Failed to read response body: {:?}", e))
    );

    let body = match serde_json::from_slice(&bytes) {
        Ok(body) => body,
        Err(_) => serde_json::Value::String(String::from_utf8_lossy(&bytes).to_string()),
    };

    let mut response = HashMap::new();

    response.insert("headers", headers);
    response.insert("body", body);
    response.insert("status", status.into());

    lua_value!(lua, &response)
        .inspect_err(|e| tracing::debug!("Failed to serialize response: {:?}", e))
}

#[derive(Clone)]
struct LuaTool {
    pub(super) source: PathBuf,
    pub(super) name: String,
    pub(super) dependency_version: String,
    pub(super) function: mlua::Function,
}

#[derive(Default)]
struct Repository {
    tools: HashMap<(PathBuf, String), LuaTool>,
}

impl UserData for Repository {}

/// Set up Lua environment and register global functions
#[tracing::instrument(skip(lua))]
fn setup(lua: &mlua::Lua) -> mlua::Result<()> {
    let globals = lua.globals();
    let bridge = lua
        .create_table()
        .inspect_err(|e| tracing::debug!("Failed to create bridge table: {:?}", e))?;
    let http = lua
        .create_table()
        .inspect_err(|e| tracing::debug!("Failed to create http table: {:?}", e))?;
    let repository = lua
        .create_userdata(Repository::default())
        .inspect_err(|e| tracing::debug!("Failed to create repository table: {:?}", e))?;

    http.set(
        "request",
        lua.create_async_function(request)
            .inspect_err(|e| tracing::debug!("Failed to create request function: {:?}", e))?,
    )
    .inspect_err(|e| tracing::debug!("Failed to set request function: {:?}", e))?;

    bridge
        .set("http", http)
        .inspect_err(|e| tracing::debug!("Failed to set http table: {:?}", e))?;

    globals
        .set("bridge", bridge)
        .inspect_err(|e| tracing::debug!("Failed to set bridge: {:?}", e))?;

    globals
        .set("__repository__", repository)
        .inspect_err(|e| tracing::debug!("Failed to set __repository__: {:?}", e))?;

    globals
        .set(
            "tool",
            lua.create_function(
                |lua: &mlua::Lua,
                 (version, function): (String, mlua::Function)|
                 -> mlua::Result<()> {
                    let mut repository: mlua::UserDataRefMut<Repository> =
                        lua.globals().get("__repository__").inspect_err(|e| {
                            tracing::debug!("Failed to get __repository__: {:?}", e)
                        })?;

                    let source: PathBuf = function
                        .info()
                        .source
                        .ok_or_else(|| mlua::Error::external("Function source not found"))?
                        .parse()
                        .map_err(|e| mlua::Error::external(e))?;

                    let name = function
                        .info()
                        .name
                        .ok_or_else(|| mlua::Error::external("Function name not found"))?;

                    repository.tools.insert(
                        (source.clone(), name.clone()),
                        LuaTool {
                            source,
                            name,
                            dependency_version: version,
                            function,
                        },
                    );

                    Ok(())
                },
            )
            .inspect_err(|e| tracing::debug!("Failed to create tool function: {:?}", e))?,
        )
        .inspect_err(|e| tracing::debug!("Failed to set tool function: {:?}", e))?;

    Ok(())
}

pub(super) fn empty() -> Arc<Pool<mlua::Lua>> {
    return Pool::new(1024, move || Ok(mlua::Lua::new()));
}

/// Create Lua runtime pool
#[tracing::instrument(skip(lua_files))]
pub(super) fn create(lua_files: Vec<Arc<ScriptFile>>) -> Arc<Pool<mlua::Lua>> {
    let lua_files = Arc::new(lua_files.into_iter().map(Arc::new).collect::<Vec<_>>());

    return Pool::new(1024, move || {
        tracing::debug!("Creating new Lua instance");
        let lua = mlua::Lua::new();

        setup(&lua).inspect_err(|e| tracing::debug!("Failed to setup Lua environment: {:?}", e))?;

        let lua_files = lua_files.clone();

        for file in lua_files.iter() {
            let file = file.clone();
            tracing::debug!("Loading Lua script: {:?}", file.path);
            lua.load(&file.code)
                .set_name(format!("@{:?}", file.path))
                .exec()
                .inspect_err(|e| {
                    tracing::debug!("Failed to execute Lua script: {:?}, {:?}", file.path, e)
                })?;
        }

        Ok(lua)
    });
}

/// Get registered Lua capabilities
#[tracing::instrument(skip(_lua))]
pub(super) fn get_tools(_lua: &mlua::Lua) -> mlua::Result<Vec<SourceTool>> {
    let repository: mlua::UserDataRef<Repository> = _lua
        .globals()
        .get("__repository__")
        .inspect_err(|e| tracing::debug!("Failed to get __repository__: {:?}", e))?;

    let tools = &repository.tools;

    let tools = tools
        .iter()
        .map(|(_, tool)| SourceTool {
            source: tool.source.clone(),
            tool_name: tool.name.clone(),
            script_language: ScriptLanguage::Lua,
            dependency_version: tool.dependency_version.clone(),
        })
        .collect::<Vec<_>>();

    Ok(tools)
}

/// Get specified capability function
#[tracing::instrument(skip(lua))]
pub(super) fn get_tool_function(
    lua: &mlua::Lua,
    code_source: &PathBuf,
    name: &String,
) -> mlua::Result<mlua::Function> {
    let repository: mlua::UserDataRef<Repository> = lua
        .globals()
        .get("__repository__")
        .inspect_err(|e| tracing::debug!("Failed to get __repository__: {:?}", e))?;

    let tool = repository
        .tools
        .get(&(code_source.clone(), name.clone()))
        .ok_or_else(|| mlua::Error::external(format!("Tool '{}' not found in repository", name)))
        .inspect_err(|e| tracing::debug!("Failed to get module versions {}: {:?}", name, e))?;

    Ok(tool.function.clone())
}

/// Asynchronously execute Lua capability
#[tracing::instrument(skip(pool, tool, params))]
pub(super) async fn run_tool<'a>(
    pool: &'a Arc<Pool<mlua::Lua>>,
    tool: &ScriptTool,
    params: serde_json::Value,
) -> error::Result<serde_json::Value> {
    tracing::debug!("Getting Lua instance");
    let lua = pool
        .acquire()
        .await
        .inspect_err(|e| tracing::debug!("Failed to get Lua instance: {:?}", e))?;

    tracing::debug!(
        "Getting tool function: {}@{}/{}",
        tool.group_name,
        tool.group_version,
        tool.name
    );
    let function = get_tool_function(&lua, &tool.code_source, &tool.name)
        .inspect_err(|e| tracing::debug!("Failed to get tool function: {:?}", e))?;

    tracing::debug!("Serializing params");
    let lua_arg = lua_value!(lua, &params)
        .inspect_err(|e| tracing::debug!("Failed to serialize params: {:?}", e))?;

    tracing::debug!("Calling Lua function");
    let result = function
        .call_async::<mlua::Value>(lua_arg)
        .await
        .inspect_err(|e| tracing::debug!("Failed to call Lua function: {:?}", e))?;

    tracing::debug!("Deserializing result");
    let result = lua
        .from_value(result)
        .inspect_err(|e| tracing::debug!("Failed to deserialize result: {:?}", e))?;

    tracing::debug!("Lua tool execution completed");
    Ok(result)
}
