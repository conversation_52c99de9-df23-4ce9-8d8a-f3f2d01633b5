use std::sync::Arc;

use serde::{Deserialize, Serialize};

use crate::{ScriptTool, config::ScriptModuleDefinition};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ContextId {
    pub tenant_id: String,
    pub user_id: String,
    pub session_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTarget {
    pub context_id: ContextId,
    pub scope: Option<String>,
    pub bucket: Option<String>,
    pub object: String,
    pub name: Option<String>,
    pub version_id: Option<String>,
}

#[mockall::automock]
#[async_trait::async_trait]
pub trait ScriptManager {
    async fn load_modules(&self, modules: Vec<Arc<ScriptModuleDefinition>>) -> Vec<String>;

    async fn list_tools(&self) -> error::Result<Vec<Arc<ScriptTool>>>;

    async fn run_tool(
        &self,
        context_id: ContextId,
        module_name: &str,
        tool_name: &str,
        params: &serde_json::Value,
        temp_view_sender: Option<tokio::sync::mpsc::Sender<serde_json::Value>>,
    ) -> error::Result<serde_json::Value>;

    async fn run_validator(
        &self,
        context_id: ContextId,
        module_name: &str,
        validator_name: &str,
        params: &serde_json::Value,
        payload: &Option<serde_json::Value>,
    ) -> error::Result<serde_json::Value>;

    async fn run_code_function(
        &self,
        code: &str,
        function_name: &str,
        params: &serde_json::Value,
    ) -> error::Result<serde_json::Value>;
}

pub struct ScriptLoadResult {}
