//! Script Engine Module
//! Manages loading, running, and resource management for Python and Lua scripts

use std::path::PathBuf;

pub use runner::ScriptRunner;
pub use tool::{ScriptLanguage, ScriptTool};

pub mod config;
pub mod manager;
pub mod python;
pub mod runner;
pub mod runtime;
pub mod tool;
pub mod utils;

/// Source Capability Structure
/// Represents original capability information loaded from script files
#[derive(Debug)]
pub struct SourceTool {
    pub source: PathBuf,
    pub tool_name: String,
    pub script_language: ScriptLanguage,
    pub dependency_version: String,
}

/// Initialize script engine environment
#[tracing::instrument]
pub fn on_initialize() {
    python::on_initialize();
}

/// Clean up script engine resources
#[tracing::instrument]
pub fn on_finalize() {
    python::on_finalize();
}

/// Clean up thread-related script resources
#[tracing::instrument]
pub fn on_thread_stop() {
    python::on_thread_stop();
}
