//! 能力（Tool）定义模块
//! 包含系统中能力的核心数据结构及基本操作

use std::{path::PathBuf, sync::Arc};

use semver::{Version, VersionReq};
use serde::{Deserialize, Serialize};

use crate::config::ScriptToolFeatures;

/// 能力类型枚举，支持Lua和Python两种脚本实现
#[derive(Serialize, Deserialize, Debug, Clone)]
pub enum ScriptLanguage {
    #[serde(rename = "lua")]
    Lua,
    #[serde(rename = "python")]
    Python,
}

/// 脚本能力结构体，系统中能力的核心表示
/// 包含能力的元数据、版本信息、代码来源及参数定义
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ScriptTool {
    /// 能力类型（Lua或Python）
    pub language: ScriptLanguage,
    /// 能力名称
    pub name: String,
    /// 能力别名
    pub alias: Vec<String>,
    /// 能力目录
    pub catalog: Option<String>,
    /// 能力描述
    pub description: String,
    /// 能力功能
    pub features: ScriptToolFeatures,
    /// 所属能力组名称
    pub module_name: String,
    /// 能力组版本
    pub module_version: Version,
    /// 能力组别名
    pub module_alias: Vec<String>,
    /// 能力依赖的版本范围，采用语义化版本要求格式
    pub dependency_version: VersionReq,
    /// 能力的代码文件路径
    pub code_source: PathBuf,
    /// 能力的配置信息
    pub module_config: Arc<serde_json::Value>,
    /// 能力的输入参数定义
    pub params_schema: Arc<serde_json::Value>,
    /// 能力的返回结果定义
    pub result_schema: Arc<serde_json::Value>,
}

#[cfg(test)]
impl ScriptTool {
    pub fn new_python() -> Self {
        ScriptTool {
            language: ScriptLanguage::Python,
            name: String::new(),
            alias: Vec::new(),
            catalog: None,
            features: ScriptToolFeatures::default(),
            description: String::new(),
            module_name: String::new(),
            module_version: Version::parse("0.0.0").unwrap(),
            module_alias: Vec::new(),
            dependency_version: VersionReq::default(),
            code_source: PathBuf::new(),
            module_config: Arc::new(serde_json::Map::new().into()),
            params_schema: Arc::new(serde_json::Map::new().into()),
            result_schema: Arc::new(serde_json::Map::new().into()),
        }
    }
}
