//! 旧版配置管理模块
//! 保留原有的配置加载逻辑，以便兼容现有系统

use std::{path::PathBuf, sync::Arc};

use semver::Version;
use serde::{Deserialize, Serialize};

/// 单个能力配置，定义其名称、描述和参数结构
#[derive(Serialize, Deserialize, Clone, Default, Debug)]
pub struct ScriptToolDefinition {
    pub name: String,
    pub alias: Option<Vec<String>>,
    pub verbose: Option<bool>,
    pub ignore_converter_cache: Option<bool>,
    pub skip_summary: Option<bool>,
    pub catalog: Option<String>,
    pub description: String,
    pub params: Arc<serde_json::Value>,
    pub result: Arc<serde_json::Value>,
    // any other fields
    #[serde(flatten)]
    pub extra: Option<serde_json::Value>,
}

#[derive(Serialize, Deserialize, Clone, Default, Debug)]
pub struct ScriptToolFeatures {
    pub verbose_summary: Option<bool>,
    pub ignore_converter_cache: Option<bool>,
    pub skip_summary: Option<bool>,
}

/// 脚本代码文件
#[derive(Serialize, Deserialize, Default, Debug, <PERSON>lone)]
pub struct ScriptFile {
    pub code: Vec<u8>,
    pub path: PathBuf,
}

/// 脚本模块
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ScriptModuleDefinition {
    pub name: String,
    pub base: String,
    pub alias: Option<Vec<String>>,
    pub description: String,
    pub version: Version,
    pub config: Arc<serde_json::Value>,
    pub files: Vec<Arc<ScriptFile>>,
    pub tools_definition: Vec<Arc<ScriptToolDefinition>>,
}

impl Default for ScriptModuleDefinition {
    fn default() -> Self {
        Self {
            name: "".to_string(),
            base: "".to_string(),
            alias: None,
            description: "".to_string(),
            version: Version::new(0, 0, 0),
            config: Arc::new(serde_json::Map::new().into()),
            files: vec![],
            tools_definition: vec![],
        }
    }
}
