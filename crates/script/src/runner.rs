use interaction::Interaction;
use serde::{Deserialize, Serialize};

use crate::manager::FileTarget;

#[mockall::automock]
#[async_trait::async_trait]
pub trait ScriptRunner {
    async fn get_interaction(&self, session_id: &str, id: &str) -> Option<Interaction>;

    async fn set_interaction(&self, session_id: &str, id: &str, interaction: Interaction);

    async fn add_view(&self, session_id: &str, view: serde_json::Value);

    async fn get_views(&self, session_id: &str) -> error::Result<Vec<serde_json::Value>>;

    async fn set_cache(&self, session_id: &str, key: &str, data: serde_json::Value);

    async fn get_cache(&self, session_id: &str, key: &str) -> Option<serde_json::Value>;

    async fn add_file(&self, target: FileTarget, data: bytes::Bytes) -> error::Result<FileInfo>;

    async fn get_file(&self, target: FileTarget) -> error::Result<bytes::Bytes>;
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileInfo {
    pub bucket: String,
    pub object: String,
    pub version_id: Option<String>,
}
