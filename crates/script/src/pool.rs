//! Resource Pool Management Module
//! Provides resource pool implementation, supporting high-concurrency resource acquisition and automatic cleanup

use std::collections::VecDeque;
use std::sync::Arc;
use std::sync::Mutex;
use tokio::sync::{OwnedSemaphorePermit, Semaphore};

/// Resource Pool
///
/// Maintains a concurrency-safe queue of reusable resources
pub struct Pool<T> {
    resources: Mutex<VecDeque<Box<T>>>,
    semaphore: Arc<Semaphore>,
    creator: Box<dyn Fn() -> error::Result<T> + Send + Sync + 'static>,
}

/// Resource Guard
///
/// Ensures resources are automatically returned to the pool after use
pub struct PooledResource<T> {
    resource: Option<Box<T>>,
    pool: Arc<Pool<T>>,
    _permit: OwnedSemaphorePermit,
}

impl<T> std::ops::Deref for PooledResource<T> {
    type Target = T;
    fn deref(&self) -> &Self::Target {
        self.resource.as_ref().unwrap()
    }
}

impl<T> Drop for PooledResource<T> {
    fn drop(&mut self) {
        let resource = self.resource.take().unwrap();
        let mut resources = self.pool.resources.lock().unwrap();
        resources.push_back(resource);
    }
}

impl<T: Send + 'static> Pool<T> {
    /// Create a new resource pool
    ///
    /// # Arguments
    /// * `max_size` - Maximum capacity of the pool
    /// * `creator` - Factory function for creating new resources
    #[tracing::instrument(skip(creator))]
    pub fn new<F>(max_size: usize, creator: F) -> Arc<Self>
    where
        F: Fn() -> error::Result<T> + Send + Sync + 'static,
    {
        Arc::new(Pool {
            resources: Mutex::new(VecDeque::with_capacity(max_size)),
            semaphore: Arc::new(Semaphore::new(max_size)),
            creator: Box::new(creator),
        })
    }

    /// Acquire a resource from the pool
    ///
    /// When no resources are available in the pool, it will create a new resource or wait for other resources to be released
    #[tracing::instrument(skip(self))]
    pub async fn acquire(self: &Arc<Self>) -> error::Result<PooledResource<T>> {
        let permit = self
            .semaphore
            .clone()
            .acquire_owned()
            .await
            .inspect_err(|e| tracing::debug!("Failed to acquire semaphore: {:?}", e))?;

        let resource =
            {
                let mut resources = self.resources.lock().unwrap();
                match resources.pop_front() {
                    Some(resource) => {
                        tracing::debug!("Acquiring existing resource from pool");
                        resource
                    }
                    None => {
                        tracing::debug!("Creating new resource");
                        Box::new((self.creator)().inspect_err(|e| {
                            tracing::debug!("Failed to create resource: {:?}", e)
                        })?)
                    }
                }
            };

        Ok(PooledResource {
            resource: Some(resource),
            pool: Arc::clone(self),
            _permit: permit,
        })
    }
}
