use std::{ffi::CString, sync::Arc};

use interaction::Interaction;
use pyo3::{exceptions::*, ffi, prelude::*, types::PyTuple};
use pythonize::{depythonize, pythonize};
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;

use crate::{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rip<PERSON>Tool,
    config::{ScriptModuleDefinition, ScriptToolFeatures},
    manager::*,
    python::runner::__runner__,
};

use super::with_python;

pub struct PythonScriptManager {
    runner: Arc<dyn ScriptRunner + Send + Sync>,
}

impl PythonScriptManager {
    pub fn new(runner: Arc<dyn ScriptRunner + Send + Sync>) -> Self {
        Self { runner }
    }
}

#[async_trait::async_trait]
impl ScriptManager for PythonScriptManager {
    #[tracing::instrument(skip(self, modules))]
    async fn load_modules(&self, modules: Vec<Arc<ScriptModuleDefinition>>) -> Vec<String> {
        with_python(|py| {
            let mut errors: Vec<String> = vec![];

            tracing::debug!("Loading Python modules, count: {}", modules.len());

            // for initialize __main__
            match py.run(ffi::c_str!(""), None, None) {
                Ok(_) => {}
                Err(e) => {
                    tracing::error!("failed to initialize __main__: {:?}", e);
                    errors.push(format!("failed to initialize __main__: {e:?}"));
                    return errors;
                }
            }

            let repository = match __runner__::Repository::get() {
                Ok(repository) => repository,
                Err(e) => {
                    tracing::error!("failed to get repository: {:?}", e);
                    errors.push(format!("failed to get repository: {e:?}"));
                    return errors;
                }
            };

            for module in modules.iter() {
                tracing::debug!("Loading Python module: {}", module.name);

                {
                    repository
                        .borrow()
                        .inner()
                        .write()
                        .unwrap()
                        .current_loading_module = Some(module.clone());
                }

                for file in module.files.iter() {
                    tracing::debug!("Loading Python file: {}", file.path.display());
                    let code = CString::new(file.code.as_slice()).unwrap();
                    let path = CString::new(file.path.to_string_lossy().as_ref()).unwrap();
                    let module_name = CString::new(
                        format!(
                            "{}.{}",
                            module.name,
                            file.path.file_stem().unwrap_or_default().to_string_lossy()
                        )
                        .as_str(),
                    )
                    .unwrap();

                    let _ = PyModule::from_code(py, &code, &path, &module_name).map_err(|e| {
                        tracing::error!(
                            "Failed to load Python file: {:?}, {}",
                            e,
                            &file.path.display()
                        );

                        errors.push(format!(
                            "failed to load Python file: {:?}, {}",
                            e,
                            &file.path.display()
                        ));

                        e
                    });
                }
            }

            repository
                .borrow()
                .inner()
                .write()
                .unwrap()
                .current_loading_module = None;

            tracing::debug!("Python files loading completed");

            errors
        })
    }

    #[tracing::instrument(skip(self))]
    async fn list_tools(&self) -> error::Result<Vec<Arc<ScriptTool>>> {
        with_python(|py| {
            let repository = __runner__::Repository::get()?.borrow();

            let inner = repository.inner().read().unwrap();

            let modules = inner.get_modules()?;

            Ok(modules
                .values()
                .flat_map(|module| {
                    module.values().flat_map(move |tool| {
                        let tool = tool.borrow(py);
                        if tool.private.unwrap_or_default() {
                            return None;
                        }

                        let module = tool.module.as_ref();

                        // 获取工具定义并克隆
                        let tool_definition = tool.tool_definition.as_ref().clone();

                        Some(Arc::new(ScriptTool {
                            name: tool.name.clone(),
                            description: tool_definition.description,
                            params_schema: tool_definition.params,
                            result_schema: tool_definition.result,
                            language: ScriptLanguage::Python,
                            alias: tool_definition.alias.unwrap_or_default(),
                            catalog: tool_definition.catalog,
                            features: ScriptToolFeatures {
                                verbose_summary: tool_definition.verbose,
                                ignore_converter_cache: tool_definition.ignore_converter_cache,
                                skip_summary: tool_definition.skip_summary,
                            },
                            module_name: module.name.clone(),
                            module_version: module.version.clone(),
                            module_alias: module.alias.clone().unwrap_or_default(),
                            dependency_version: tool.dependency_version.parse().unwrap_or_default(),
                            code_source: tool.source.clone(),
                            module_config: module.config.clone(),
                        }))
                    })
                })
                .collect())
        })
    }

    #[tracing::instrument(skip(self, temp_view_sender))]
    async fn run_tool(
        &self,
        context_id: ContextId,
        module_name: &str,
        tool_name: &str,
        params: &serde_json::Value,
        temp_view_sender: Option<tokio::sync::mpsc::Sender<serde_json::Value>>,
    ) -> error::Result<serde_json::Value> {
        tracing::debug!("Running tool: {}/{}", module_name, tool_name);

        let future = with_python(|py| -> error::Result<_> {
            let repository = __runner__::Repository::get()?;

            let (module_definition, function) = repository
                .borrow()
                .inner()
                .read()
                .unwrap()
                .get_tool(module_name, tool_name)
                .inspect_err(|e| {
                    tracing::debug!(
                        "Failed to get tool {:?}/{}: {:?}",
                        module_name,
                        tool_name,
                        e
                    )
                })?
                .map(|tool| {
                    let tool = tool.borrow(py);

                    (tool.module.clone(), tool.function.clone_ref(py))
                })
                .ok_or_else(|| {
                    tracing::debug!("Tool not found: {:?}/{}", module_name, tool_name);
                    PyKeyError::new_err(format!("{module_name:?} {tool_name} not found"))
                })?;

            let context = __runner__::Context::new(
                self.runner.clone(),
                context_id.clone(),
                module_definition.clone(),
                temp_view_sender,
            )?;

            Ok(run_function_with_context(function, context, params, &None))
        })?;

        future.await
    }

    #[tracing::instrument(skip(self))]
    async fn run_validator(
        &self,
        context_id: ContextId,
        module_name: &str,
        validator_name: &str,
        params: &serde_json::Value,
        payload: &Option<serde_json::Value>,
    ) -> error::Result<serde_json::Value> {
        tracing::debug!("Running validator: {}/{}", module_name, validator_name);

        let future = with_python(|py| -> error::Result<_> {
            let repository = __runner__::Repository::get()?;

            let (module_definition, function) = repository
                .borrow()
                .inner()
                .read()
                .unwrap()
                .get_validator(module_name, validator_name)
                .inspect_err(|e| {
                    tracing::debug!(
                        "Failed to get validator {:?}/{}: {:?}",
                        module_name,
                        validator_name,
                        e
                    )
                })?
                .map(|validator| {
                    let validator = validator.borrow(py);

                    (validator.module.clone(), validator.function.clone_ref(py))
                })
                .ok_or_else(|| {
                    tracing::debug!("Validator not found: {:?}/{}", module_name, validator_name);
                    PyKeyError::new_err(format!("{module_name:?} {validator_name} not found"))
                })?;

            let context = __runner__::Context::new(
                self.runner.clone(),
                context_id.clone(),
                module_definition.clone(),
                None,
            )?;

            Ok(run_function_with_context(
                function, context, params, payload,
            ))
        })?;

        future.await
    }

    #[tracing::instrument(skip(self))]
    async fn run_code_function(
        &self,
        code: &str,
        function_name: &str,
        params: &serde_json::Value,
    ) -> error::Result<serde_json::Value> {
        with_python(|py| {
            let code = CString::new(code)?;
            let path = CString::new("<code>")?;
            let module_name = CString::new("__temp_code__")?;

            let module = PyModule::from_code(py, &code, &path, &module_name)?;
            let function = module.getattr(function_name)?;

            let py_params = pythonize::pythonize(py, &params)?;

            let result = function.call1((py_params,))?;

            let result = pythonize::depythonize(&result)?;

            Ok(result)
        })
    }
}

struct AutoAbortHandle<T> {
    handle: std::pin::Pin<Box<tokio::task::JoinHandle<T>>>,
    cancel_token: tokio_util::sync::CancellationToken,
}

impl<T> AutoAbortHandle<T> {
    pub fn new(
        handle: tokio::task::JoinHandle<T>,
        cancel_token: tokio_util::sync::CancellationToken,
    ) -> Self {
        Self {
            handle: Box::pin(handle),
            cancel_token,
        }
    }
}

impl<T> Drop for AutoAbortHandle<T> {
    fn drop(&mut self) {
        self.cancel_token.cancel();
        self.handle.abort();
    }
}

impl<T> Future for AutoAbortHandle<T> {
    type Output = Result<T, JoinError>;

    fn poll(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Self::Output> {
        let handle = self.handle.as_mut();
        handle.poll(cx)
    }
}

/// Execute capability function
#[tracing::instrument(skip(function, context, params))]
async fn run_function_with_context(
    function: Py<PyAny>,
    context: __runner__::Context,
    params: &serde_json::Value,
    payload: &Option<serde_json::Value>,
) -> error::Result<serde_json::Value> {
    let params = params.clone();
    let payload = payload.clone();

    enum ResultType<F> {
        Sync(PyResult<Py<PyAny>>),
        Async(F),
    }

    let cancel_token = context.clone_cancel_token();

    let result = with_python(|py| -> PyResult<_> {
        let asyncio = py
            .import("asyncio")
            .inspect_err(|e| tracing::debug!("Failed to import asyncio module: {:?}", e))?;

        let is_async = asyncio
            .call_method1("iscoroutinefunction", (&function,))
            .inspect_err(|e| tracing::debug!("Failed to check coroutine function: {:?}", e))?
            .is_truthy()
            .inspect_err(|e| tracing::debug!("Failed to check truthiness: {:?}", e))?;

        let py_params = pythonize(py, &params)
            .inspect_err(|e| tracing::debug!("Failed to serialize parameters: {:?}", e))?;

        // 获取函数的参数数量，决定是否序列化 payload
        let inspect = py.import("inspect")?;
        let sig = inspect.call_method1("signature", (function.clone_ref(py),))?;
        let parameters = sig.getattr("parameters")?;
        let num_params = parameters.call_method0("__len__")?.extract::<usize>()?;

        let result = if num_params > 2 {
            let py_payload = match payload.map(|p| pythonize(py, &p)) {
                Some(Ok(v)) => Some(v),
                Some(Err(e)) => return Err(e.into()),
                None => None,
            };

            function.bind(py).call1((context, py_params, py_payload))
        } else {
            function.bind(py).call1((context, py_params))
        };

        let result = result
            .inspect_err(|e| tracing::debug!("Function call failed: {:?}", e))
            .map(|result| result.unbind());

        if is_async {
            match result {
                Ok(result) => Ok(ResultType::Async(tokio::task::spawn_blocking(move || {
                    with_python(|py| -> PyResult<_> {
                        let asyncio = py.import("asyncio").inspect_err(|e| {
                            tracing::debug!("Failed to import asyncio module: {:?}", e)
                        })?;

                        tracing::debug!("Waiting for async result");
                        let result = asyncio.call_method1("run", (&result,)).inspect_err(|e| {
                            tracing::debug!("Failed to run async function: {:?}", e)
                        })?;

                        Ok(result.unbind())
                    })
                }))),
                Err(e) => Err(e),
            }
        } else {
            Ok(ResultType::Sync(result))
        }
    })?;

    let result: Result<Py<PyAny>, PyErr> = match result {
        ResultType::Sync(result) => result,
        ResultType::Async(future) => AutoAbortHandle::new(future, cancel_token).await?,
    };

    with_python(|py| {
        tracing::debug!("Deserializing result");
        match result {
            Ok(result) => Ok(depythonize(result.bind(py))
                .inspect_err(|e| tracing::debug!("Failed to deserialize result: {:?}", e))?),
            Err(e) => {
                if !e.is_instance_of::<__runner__::InteractionError>(py) {
                    return Err(e.into());
                }

                let error = e
                    .value(py)
                    .getattr("args")
                    .unwrap()
                    .downcast::<PyTuple>()
                    .unwrap()
                    .get_item(0)
                    .unwrap();

                let result: Interaction = depythonize(&error)?;

                Err(error::Error::Interaction(serde_json::to_value(result)?))
            }
        }
    })
}
