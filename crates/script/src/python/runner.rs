#[pyo3::prelude::pymodule]
pub mod __runner__ {
    use std::{collections::HashMap, path::PathBuf, sync::Arc};

    use bytes::Bytes;
    use interaction::Interaction;
    use pyo3::{
        IntoPyObjectExt, PyTypeInfo, create_exception,
        exceptions::{PyBaseException, PyKeyError, PyValueError},
        ffi,
        prelude::*,
        types::{PyAny, PyCFunction, PyDict, PyString},
    };
    use pyo3_bytes::PyBytes;
    use pythonize::pythonize;
    use serde::{Deserialize, Serialize};

    use crate::{
        ScriptRunner,
        config::{ScriptModuleDefinition, ScriptToolDefinition},
        manager::{ContextId, FileTarget},
        python::with_python,
    };

    use super::super::py;

    create_exception!(__runner__, InteractionError, PyBaseException);

    #[pyfunction]
    #[pyo3(signature = (version=None, private=None))]
    fn tool(version: Option<&str>, private: Option<bool>) -> PyResult<Bound<'_, PyCFunction>> {
        let dependency_version = version.unwrap_or("*").to_string();

        PyCFunction::new_closure(
            py(),
            Some(ffi::c_str!("wrapper")),
            None,
            move |args, _kwargs| -> PyResult<Py<PyAny>> {
                let py = py();

                let function = args.get_item(0)?.downcast_into::<PyAny>()?;

                // add tool to repository
                let repository = Repository::get()?;

                let code = function.getattr("__code__")?;

                let name = code.getattr("co_name")?.extract::<String>()?;

                let filename = code.getattr("co_filename")?.extract::<String>()?;
                // check if filename is correct

                let source: std::path::PathBuf = filename
                    .parse()
                    .map_err(|_| PyValueError::new_err("Invalid filename"))?;

                let module = repository
                    .borrow()
                    .inner()
                    .read()
                    .unwrap()
                    .current_loading_module
                    .clone()
                    .ok_or_else(|| PyValueError::new_err("Current loading module not set"))?;

                let tool_definition = match module.tools_definition.iter().find(|t| t.name == name)
                {
                    Some(tool_definition) => tool_definition.clone(),
                    None if private.unwrap_or_default() => Arc::new(ScriptToolDefinition {
                        name: name.clone(),
                        ..Default::default()
                    }),
                    None => {
                        return Err(PyValueError::new_err(format!(
                            "Tool {name} definition not found"
                        )));
                    }
                };

                repository
                    .borrow()
                    .inner()
                    .write()
                    .unwrap()
                    .add_tool(Bound::new(
                        py,
                        PythonTool {
                            name,
                            source,
                            dependency_version: dependency_version.clone(),
                            function: function.clone().into(),
                            module: module.clone(),
                            private,
                            tool_definition: tool_definition.clone(),
                        },
                    )?)?;

                Ok(function.into())
            },
        )
    }

    #[pyfunction]
    #[pyo3(signature = (version=None))]
    fn validator(version: Option<&str>) -> PyResult<Bound<'_, PyCFunction>> {
        let dependency_version = version.unwrap_or("*").to_string();

        PyCFunction::new_closure(
            py(),
            Some(ffi::c_str!("wrapper")),
            None,
            move |args, _kwargs| -> PyResult<Py<PyAny>> {
                let py = py();

                let function = args.get_item(0)?.downcast_into::<PyAny>()?;

                // add tool to repository
                let repository = Repository::get()?;

                let code = function.getattr("__code__")?;

                let name = code.getattr("co_name")?.extract::<String>()?;

                let filename = code.getattr("co_filename")?.extract::<String>()?;
                // check if filename is correct

                let source: std::path::PathBuf = filename
                    .parse()
                    .map_err(|_| PyValueError::new_err("Invalid filename"))?;

                let module = repository
                    .borrow()
                    .inner()
                    .read()
                    .unwrap()
                    .current_loading_module
                    .clone()
                    .ok_or_else(|| PyValueError::new_err("Current loading module not set"))?;

                repository
                    .borrow()
                    .inner()
                    .write()
                    .unwrap()
                    .add_validator(Bound::new(
                        py,
                        PythonValidator {
                            name,
                            source,
                            dependency_version: dependency_version.clone(),
                            function: function.clone().into(),
                            module: module.clone(),
                        },
                    )?)?;

                Ok(function.into())
            },
        )
    }

    /// Context class, provides runtime context for capability functions
    #[pyclass]
    #[allow(dead_code)]
    #[derive(Clone)]
    pub struct Context {
        context_id: ContextId,
        module_definition: Arc<ScriptModuleDefinition>,

        runner: Arc<dyn ScriptRunner + Send + Sync>,
        cancel_token: tokio_util::sync::CancellationToken,
        temp_view_sender: Option<tokio::sync::mpsc::Sender<serde_json::Value>>,
    }

    impl Context {
        pub fn new(
            runner: Arc<dyn ScriptRunner + Send + Sync>,
            context_id: ContextId,
            module_definition: Arc<ScriptModuleDefinition>,
            temp_view_sender: Option<tokio::sync::mpsc::Sender<serde_json::Value>>,
        ) -> PyResult<Self> {
            Ok(Self {
                context_id,
                runner,
                module_definition,
                temp_view_sender,
                cancel_token: tokio_util::sync::CancellationToken::new(),
            })
        }

        async fn add_view_inner(&self, view: serde_json::Value) -> PyResult<()> {
            let view = serde_json::json!({
                "type": "view",
                "view": view,
            });

            self.runner
                .add_view(&self.context_id.session_id, view.clone())
                .await;

            if let Some(sender) = self.temp_view_sender.as_ref() {
                let _ = sender.send(view).await;
            }

            Ok(())
        }

        fn clone_with_tool(&self, tool: Py<PythonTool>) -> PyResult<Self> {
            let mut new_self = self.clone();

            new_self.module_definition = tool.borrow(py()).module.clone();

            Ok(new_self)
        }

        pub(crate) fn clone_cancel_token(&self) -> tokio_util::sync::CancellationToken {
            self.cancel_token.clone()
        }

        fn get_log_level(&self) -> LogLevel {
            LogLevel::from_value(
                self.module_definition
                    .config
                    .get("$log_level")
                    .unwrap_or(&serde_json::Value::String("info".to_string())),
            )
        }

        async fn log_inner(&self, level: LogLevel, message: String) -> PyResult<()> {
            if level >= self.get_log_level() {
                const FORMAT: time::format_description::well_known::iso8601::Config = time::format_description::well_known::iso8601::Config::DEFAULT
                    .set_formatted_components(
                    time::format_description::well_known::iso8601::FormattedComponents::DateTime,
                )
                .set_time_precision(time::format_description::well_known::iso8601::TimePrecision::Second {
                    decimal_digits: std::num::NonZeroU8::new(3),
                });

                let time = time::OffsetDateTime::now_local()
                    .unwrap_or_else(|_| time::OffsetDateTime::now_utc())
                    .format(&time::format_description::well_known::Iso8601::<{ FORMAT.encode() }>)
                    .unwrap_or_default();

                // 截断 message 最大长度为 4096 字符，超过则截断，并添加省略号以及长度
                const MAX_LENGTH: usize = 4096;
                let chars = message.chars().collect::<Vec<_>>();
                let message = if chars.len() > MAX_LENGTH {
                    format!(
                        "{}...({})",
                        &chars[..MAX_LENGTH].iter().collect::<String>(),
                        chars.len()
                    )
                } else {
                    message
                };

                let view = serde_json::json!({
                    "format": "card",
                    "content": {
                        "title": "日志",
                        "type": "log",
                        "description": format!("[{}] {}", level.to_string(), time),
                        "details": message,
                        "hide": true,
                    },
                });

                self.add_view_inner(view).await
            } else {
                Ok(())
            }
        }
    }

    #[derive(Debug, PartialEq, Eq, PartialOrd, Ord)]
    pub(crate) enum LogLevel {
        Debug = 1,
        Info = 2,
        Warn = 3,
        Error = 4,
        Off = 100,
    }

    impl LogLevel {
        pub fn from_str(s: &str) -> Self {
            match s {
                "debug" => Self::Debug,
                "info" => Self::Info,
                "warn" => Self::Warn,
                "error" => Self::Error,
                "off" => Self::Off,
                _ => Self::Warn,
            }
        }

        pub fn from_value(value: &serde_json::Value) -> Self {
            match value {
                serde_json::Value::String(s) => Self::from_str(s),
                _ => Self::Info,
            }
        }
    }

    // Display impl for LogLevel
    impl std::fmt::Display for LogLevel {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            write!(
                f,
                "{}",
                match self {
                    Self::Debug => "debug".to_string(),
                    Self::Info => "info".to_string(),
                    Self::Warn => "warn".to_string(),
                    Self::Error => "error".to_string(),
                    Self::Off => "off".to_string(),
                }
            )
        }
    }

    #[pymethods]
    impl Context {
        #[getter]
        fn session_id(&self) -> PyResult<&str> {
            Ok(&self.context_id.session_id)
        }

        #[getter]
        fn tenant_id(&self) -> PyResult<&str> {
            Ok(&self.context_id.tenant_id)
        }

        #[getter]
        fn user_id(&self) -> PyResult<&str> {
            Ok(&self.context_id.user_id)
        }

        #[getter]
        fn config(&self) -> PyResult<Py<PyAny>> {
            let py = py();

            let config = pythonize(py, &self.module_definition.config)?.unbind();

            Ok(config)
        }

        #[getter]
        fn is_cancelled(&self) -> PyResult<bool> {
            Ok(self.cancel_token.is_cancelled())
        }

        #[pyo3(signature = (tool_name, context=None, params=None, module_name=None))]
        fn call_tool(
            &self,
            tool_name: String,
            context: Option<Bound<'_, Context>>,
            params: Option<Bound<'_, PyAny>>,
            module_name: Option<String>,
        ) -> PyResult<Py<PyAny>> {
            let py = py();

            let repository = Repository::get()?;
            let module_name = module_name.as_ref().unwrap_or(&self.module_definition.name);
            let tool = repository
                .borrow()
                .inner()
                .read()
                .unwrap()
                .get_tool(module_name, tool_name.as_str())?;

            let tool = match &tool {
                Some(tool) => tool.clone_ref(py),
                None => {
                    return Err(PyKeyError::new_err(format!(
                        "Tool {module_name}/{tool_name} not found",
                    )));
                }
            };

            let tool_function = tool.borrow(py).function.clone_ref(py);

            let context = match context {
                Some(context) => context.borrow().clone_with_tool(tool)?,
                None => self.clone_with_tool(tool)?,
            };

            let params: Bound<'_, PyDict> = match params {
                Some(params) => params.downcast_into()?,
                None => PyDict::new(py),
            };

            let result = tool_function.bind(py).call1((context, params))?;

            Ok(result.unbind())
        }

        pub(super) async fn append_view(&self, delta: Py<PyAny>) -> PyResult<()> {
            let delta: serde_json::Value = pythonize::depythonize(delta.bind(py()))?;
            let delta = serde_json::json!({
                "type": "delta",
                "delta": delta,
            });

            self.runner
                .add_view(&self.context_id.session_id, delta.clone())
                .await;

            if let Some(sender) = self.temp_view_sender.as_ref() {
                let _ = sender.send(delta).await;
            }

            Ok(())
        }

        pub(super) async fn add_view(&self, view: Py<PyAny>) -> PyResult<()> {
            let view: serde_json::Value = pythonize::depythonize(view.bind(py()))?;
            self.add_view_inner(view).await
        }

        pub(super) async fn log(&self, level: String, message: String) -> PyResult<()> {
            self.log_inner(LogLevel::from_str(&level), message).await
        }

        pub(super) async fn log_info(&self, message: String) -> PyResult<()> {
            self.log("info".to_string(), message).await
        }

        pub(super) async fn log_debug(&self, message: String) -> PyResult<()> {
            self.log("debug".to_string(), message).await
        }

        pub(super) async fn log_warn(&self, message: String) -> PyResult<()> {
            self.log("warn".to_string(), message).await
        }

        pub(super) async fn log_error(&self, message: String) -> PyResult<()> {
            self.log("error".to_string(), message).await
        }

        pub fn require_interaction(&self, interaction: Bound<'_, PyAny>) -> PyResult<()> {
            let _: Interaction = pythonize::depythonize(&interaction)?;
            Err(InteractionError::new_err(interaction.unbind()))
        }

        pub(super) async fn get_interaction(&self, id: String) -> PyResult<Py<PyAny>> {
            let interaction = self
                .runner
                .get_interaction(&self.context_id.session_id, &id)
                .await
                .and_then(|interaction| interaction.content.get_result());

            let interaction = pythonize(py(), &interaction)?;

            Ok(interaction.unbind())
        }

        pub(super) async fn set_cache(&self, key: String, data: Py<PyAny>) -> PyResult<()> {
            let data: serde_json::Value = pythonize::depythonize(data.bind(py()))?;
            self.runner
                .set_cache(&self.context_id.session_id, &key, data)
                .await;
            Ok(())
        }

        pub(super) async fn get_cache(&self, key: String) -> PyResult<Py<PyAny>> {
            let data = self
                .runner
                .get_cache(&self.context_id.session_id, &key)
                .await;
            let data = pythonize(py(), &data)?;
            Ok(data.unbind())
        }

        pub(super) async fn add_file(
            &self,
            target: Py<PyAny>,
            data: Py<PyAny>,
        ) -> PyResult<Py<PyAny>> {
            let target: FileTargetOrPath = pythonize::depythonize(target.bind(py()))?;

            let target = target.into_target(self.context_id.clone());

            let data: Bytes = if let Ok(data) = data.extract::<PyBytes>(py()) {
                data.into_inner()
            } else {
                let data = data.extract::<String>(py())?;
                Bytes::from(data)
            };

            let info = self.runner.add_file(target.clone(), data).await?;

            Ok(pythonize(
                py(),
                &FileTarget {
                    bucket: Some(info.bucket),
                    object: info.object,
                    version_id: info.version_id,
                    context_id: self.context_id.clone(),
                    scope: None,
                    name: None,
                },
            )?
            .unbind())
        }

        #[pyo3(signature = (target, format=None))]
        pub(super) async fn get_file(
            &self,
            target: Py<PyAny>,
            format: Option<String>,
        ) -> PyResult<Py<PyAny>> {
            let target: FileTargetOrPath = pythonize::depythonize(target.bind(py()))?;
            let format = format.as_deref().unwrap_or("string");

            let target = target.into_target(self.context_id.clone());

            let data = self.runner.get_file(target).await?;

            match format {
                "string" => {
                    let data = String::from_utf8_lossy(&data);
                    PyString::new(py(), &data).into_py_any(py())
                }
                _ => PyBytes::new(data).into_py_any(py()),
            }
        }
    }

    #[derive(Debug, Serialize, Deserialize)]
    #[serde(untagged)]
    pub enum FileTargetOrPath {
        Details {
            scope: Option<String>,
            bucket: Option<String>,
            name: Option<String>,
            object: String,
            version_id: Option<String>,
        },
        Path(String),
    }

    impl FileTargetOrPath {
        pub fn into_target(self, context_id: ContextId) -> FileTarget {
            match self {
                FileTargetOrPath::Details {
                    scope,
                    bucket,
                    name,
                    object,
                    version_id,
                } => FileTarget {
                    context_id,
                    scope,
                    bucket,
                    object,
                    version_id,
                    name,
                },
                FileTargetOrPath::Path(object) => FileTarget {
                    context_id,
                    scope: Some("session".to_string()),
                    bucket: None,
                    object,
                    version_id: None,
                    name: None,
                },
            }
        }
    }

    /// Python capability class, stores capability implementation information
    #[pyclass]
    pub struct PythonTool {
        pub source: PathBuf,
        pub name: String,
        pub dependency_version: String,
        pub function: Py<PyAny>,
        pub module: Arc<ScriptModuleDefinition>,
        pub private: Option<bool>,
        pub tool_definition: Arc<ScriptToolDefinition>,
    }

    #[pyclass]
    pub struct PythonValidator {
        pub source: PathBuf,
        pub name: String,
        pub dependency_version: String,
        pub function: Py<PyAny>,
        pub module: Arc<ScriptModuleDefinition>,
    }

    /// Capability registry, manages all registered Python capabilities
    #[pyclass]
    #[derive(Default)]
    pub struct Repository {
        inner: std::sync::RwLock<RepositoryInner>,
    }

    impl Repository {
        pub fn get<'a>() -> PyResult<Bound<'a, Self>> {
            with_python(|py| {
                let repository = py
                    .import("__runner__")
                    .inspect_err(|e| {
                        tracing::debug!("Failed to import __runner__ module: {:?}", e)
                    })?
                    .getattr("__repository__")
                    .inspect_err(|e| {
                        tracing::debug!("Failed to get __repository__ attribute: {:?}", e)
                    })?
                    .downcast_into::<Self>()
                    .inspect_err(|e| {
                        tracing::debug!("Failed to convert to Repository type: {:?}", e)
                    })?;

                Ok(repository)
            })
        }

        pub fn inner(&self) -> &std::sync::RwLock<RepositoryInner> {
            &self.inner
        }
    }

    #[derive(Default)]
    pub struct RepositoryInner {
        // id -> function
        pub tools: HashMap<String, HashMap<String, Py<PythonTool>>>,
        pub validators: HashMap<String, HashMap<String, Py<PythonValidator>>>,
        pub current_loading_module: Option<Arc<ScriptModuleDefinition>>,
    }

    impl RepositoryInner {
        /// Get specified capability
        pub fn get_tool(
            &self,
            module_name: &str,
            tool_name: &str,
        ) -> PyResult<Option<Py<PythonTool>>> {
            let py = py();

            Ok(self.tools.values().find_map(|tools| {
                tools
                    .values()
                    .find(|tool| {
                        let t = tool.borrow(py);

                        (t.module.name == module_name
                            || t.module
                                .alias
                                .as_ref()
                                .unwrap_or(&vec![])
                                .contains(&module_name.to_string()))
                            && t.tool_definition.name == tool_name
                    })
                    .map(|tool| tool.clone_ref(py))
            }))
        }

        pub fn get_validator(
            &self,
            module_name: &str,
            validator_name: &str,
        ) -> PyResult<Option<&Py<PythonValidator>>> {
            Ok(self
                .validators
                .get(module_name)
                .and_then(|module| module.get(validator_name)))
        }

        /// Get all registered capabilities
        pub fn get_modules(&self) -> PyResult<&HashMap<String, HashMap<String, Py<PythonTool>>>> {
            Ok(&self.tools)
        }

        /// Add new capability to registry
        pub(super) fn add_tool(&mut self, tool: Bound<'_, PythonTool>) -> PyResult<()> {
            let module_name = tool.borrow().module.name.clone();

            let tool_name = tool.borrow().name.clone();

            tracing::debug!("add_tool {}/{}", module_name, tool_name);

            self.tools
                .entry(module_name)
                .or_default()
                .insert(tool_name, tool.into());

            Ok(())
        }

        pub(super) fn add_validator(
            &mut self,
            validator: Bound<'_, PythonValidator>,
        ) -> PyResult<()> {
            let module_name = validator.borrow().module.name.clone();

            let validator_name = validator.borrow().name.clone();

            tracing::debug!("add_validator {}/{}", module_name, validator_name);

            self.validators
                .entry(module_name)
                .or_default()
                .insert(validator_name, validator.into());

            Ok(())
        }
    }

    #[pymodule_init]
    fn init(module: &Bound<'_, PyModule>) -> PyResult<()> {
        let py = module.py();
        module.add("__repository__", Repository::default())?;
        module.add(InteractionError::NAME, py.get_type::<InteractionError>())?;

        Ok(())
    }
}
