//! Python Script Support Module
//! Implements loading, capability registration, and execution of Python scripts

use pyo3::{append_to_inittab, ffi, prelude::*};
use runner::__runner__;
use std::{env, path::PathBuf};

pub use manager::PythonScriptManager;

mod manager;
mod runner;

/// Initialize Python environment
#[tracing::instrument]
pub(super) fn on_initialize() {
    unsafe {
        let _venv = env::current_dir().unwrap().join(".venv/lib");
        let venv = PathBuf::from("D:\\data\\agent-runner\\.runtime\\tools\\.venv\\Lib");
        if venv.exists() {
            if let Some(e) = venv.read_dir().unwrap().find(|entry| {
                entry
                    .as_ref()
                    .unwrap()
                    .path()
                    .join("site-packages")
                    .exists()
            }) {
                env::set_var(
                    "PYTHONPATH",
                    e.as_ref()
                        .unwrap()
                        .path()
                        .join("site-packages")
                        .to_string_lossy()
                        .to_string(),
                );
                tracing::debug!("Found venv: {}", env::var("PYTHONPATH").unwrap());
            }
        } else {
            tracing::debug!("No venv found");
        }

        env::set_var(
            "PYTHONPATH",
            "C:\\data\\agent-runner\\.runtime\\tools\\.venv\\Lib\\site-packages",
        );

        append_to_inittab!(__runner__);
        ffi::Py_InitializeEx(0);
        let py = Python::assume_gil_acquired();

        let signal = py.import("signal").unwrap();
        signal
            .call_method1(
                "signal",
                (
                    signal.getattr("SIGINT").unwrap(),
                    signal.getattr("SIG_DFL").unwrap(),
                ),
            )
            .unwrap();

        py.import("threading").unwrap();

        py.import("sys")
            .unwrap()
            .getattr("path")
            .unwrap()
            .call_method1("append", (".runtime",))
            .unwrap();

        py.import("__runner__").unwrap();
    };

    unsafe { ffi::PyThreadState_Swap(std::ptr::null_mut()) };

    tracing::debug!("Python environment initialized");
}

#[tracing::instrument]
pub(super) fn on_finalize() {
    unsafe {
        ffi::Py_Finalize();
    }
    tracing::debug!("Python environment cleaned up");
}

// #[tracing::instrument]
pub(super) fn on_thread_stop() {
    // tracing::debug!("Python thread ended");
}

fn py() -> Python<'static> {
    unsafe { Python::assume_gil_acquired() }
}

#[tracing::instrument(skip(f))]
fn with_python<F, R>(f: F) -> R
where
    F: FnOnce(Python<'static>) -> R,
{
    let py: Python<'_> = py();

    let state = unsafe { ffi::PyGILState_Ensure() };

    let result = f(py);

    unsafe {
        ffi::PyGILState_Release(state);
    }

    result
}

// tests
#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use interaction::{Interaction, InteractionContent};
    use pyo3::types::PyTuple;
    use pythonize::{depythonize, pythonize};

    use crate::{config::ScriptModuleDefinition, manager::ContextId, runner::MockScriptRunner};

    use super::*;

    static ONCE_INIT: std::sync::Once = std::sync::Once::new();

    #[test]
    fn test_error() {
        ONCE_INIT.call_once(|| {
            on_initialize();
        });

        with_python(|py| {
            let context = __runner__::Context::new(
                Arc::new(MockScriptRunner::new()),
                ContextId {
                    session_id: "<session_id>".to_string(),
                    tenant_id: "<tenant_id>".to_string(),
                    user_id: "<user_id>".to_string(),
                },
                Arc::new(ScriptModuleDefinition::default()),
                None,
            )
            .unwrap();

            let data = Interaction {
                id: "test".to_string(),
                title: "title".to_string(),
                content: InteractionContent::Confirm {
                    confirm: "".to_string(),
                    result: None,
                },
            };

            let interaction = pythonize(py, &data).unwrap();

            let err = context
                .require_interaction(interaction.clone())
                .unwrap_err();

            assert!(err.is_instance_of::<__runner__::InteractionError>(py));

            let error = err
                .value(py)
                .getattr("args")
                .unwrap()
                .downcast::<PyTuple>()
                .unwrap()
                .get_item(0)
                .unwrap();

            let result: Result<Interaction, _> = depythonize(&error);

            assert!(result.is_ok(), "{result:?}");

            assert_eq!(result.unwrap(), data);
        });
    }
}
