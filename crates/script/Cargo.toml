[package]
name = "script"
version = "0.1.0"
edition = "2024"
publish = false

[lib]
name = "script"
path = "src/lib.rs"

[dependencies]
async-trait = { workspace = true }
bytes = { workspace = true }
error = { workspace = true }
interaction = { workspace = true }
mlua = { workspace = true }
mockall = { workspace = true }
pyo3 = { workspace = true }
pyo3-async-runtimes = { workspace = true }
pyo3-bytes = { workspace = true }
pythonize = { workspace = true }
reqwest = { workspace = true }
semver = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
time = { workspace = true }
tokio = { workspace = true }
tokio-util = { workspace = true }
tracing = { workspace = true }

