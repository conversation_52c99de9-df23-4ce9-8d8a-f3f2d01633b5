# ToolHub

ToolHub 是一个基于 Rust 构建的 AI 流程/工作流系统，通过灵活的模块化设计和脚本扩展（Lua 与 Python）机制，实现复杂任务的动态调度和执行。项目同时集成了 MCP 接口，用于支持与外部服务的协同工作。

---

## 项目结构

### src 目录

- **src/config.rs**  
  项目的配置信息和初始化逻辑。

- **src/main.rs**  
  程序入口，负责系统启动和主流程控制。

- **src/mcp.rs**  
  实现 MCP 相关功能，用于与外部系统和服务进行交互。

- **src/pool.rs**  
  管理资源池或连接池，支持高并发任务的处理。

- **src/resolver.rs**  
  负责任务或依赖的解析，决定任务的执行方式。

- **src/service.rs**  
  提供核心业务逻辑与服务功能，包括流程(flow)和视图(view)的 API。

- **src/tool.rs**  
  管理系统中的"能力（Tool）"，负责加载、调度和执行各项能力模块。

- **src/script/**  
  脚本支持模块：
  - **lua.rs**：Lua 脚本支持。
  - **python.rs**：Python 脚本支持。
  - **mod.rs**：脚本模块的统一管理。

### .runtime 目录

- 存放所有系统配置文件。

- **.runtime/modules.json**  
  定义模块信息，包含以下字段：

  - **name**：模块名称。
  - **base**：基础模块名称。
  - **description**：模块用途描述。
  - **version**：模块所依赖的目标能力版本。
  - **config**：模块特定的配置信息（例如 `base_url`）。

- **.runtime/tools/**  
  存放能力（Tool）的相关文件，按照模块及能力组版本进行组织：

  - 每个 **能力组**（tool_group）的版本由文件夹路径决定，例如 `demo/1.0.0` 与 `demo/v2`，组内所有能力共享该版本。
  - 在能力配置文件（如 `tools.json`）中，可指定每个能力依赖的目标版本，从而确保能力与模块间的兼容性。
  - JSON 配置参考 .runtime/schema 下的 JSON Schema 文件。

- **.runtime/flow/**
  存放流程定义文件，使用 YAML 格式描述流程的执行步骤和逻辑。

- **.runtime/view/**
  存放视图定义文件，使用 YAML 格式描述界面和交互元素。

---

## 模块与能力管理

### 模块（Module）

- **定义与配置**  
  模块的相关信息在 **.runtime/modules.json** 中配置。每个模块声明了自身的名称、基础模块、版本、描述及其它配置信息（例如 API 的 base_url）。

- **版本管理**  
  模块中声明的版本代表模块所依赖的目标能力版本。系统加载模块时，会根据版本信息选择与之匹配的能力。

**示例：通过声明 Module 来启用一个能力**  
在 **.runtime/modules.json** 中新增一个模块记录，例如：

```json
{
  "modules": [
    {
      "name": "demo-2",
      "base": "demo",
      "description": "描述用途",
      "version": "2.0.0",
      "config": {
        "base_url": "http://********:9096/"
      }
    }
  ]
}
```

此示例表示模块 "demo-2" 依赖的能力版本为 "2.0.0"，因此它应加载位于对应能力组（例如 **.runtime/tools/demo/v2/**）中的能力。如果系统中只有 **.runtime/tools/demo/1.0.0/** 的能力，则需要更新模块声明或部署新版能力组。

### 能力（Tool）

- **组织结构与版本**

  - 各能力文件存储在 **.runtime/tools/** 下，按照模块和能力组版本进行组织（例如：`demo/1.0.0` 与 `demo/v2`）。
  - 每个能力组的版本由其所在文件夹路径决定，组内所有能力共享该版本。
  - 在能力配置文件（例如 `tools.json`）中，可以指定每个能力依赖的目标版本，从而确保能力与模块间的兼容性。

- **版本选择机制**

  - 模块加载时会检查自身在 **.runtime/modules.json** 中声明的版本，并筛选相应能力组中的能力，确保版本匹配。
  - 系统支持多种版本格式，包括标准的语义化版本（如 "1.0.0"）和前缀版本（如 "v2"）。
  - 如果能力所在组的版本与模块所需的版本不一致，则需要升级或调整版本以确保依赖关系一致。

- **如何定义一个能力**

  **示例：定义问候能力**

  1. **编写能力脚本文件**  
     参考实际文件 **.runtime/tools/demo/v2/greet.py**，内容如下：

     ```python
     from __runner__ import tool, Context

     @tool(version="*")
     async def greet(context: Context, params: any):
         return {
             "output": "hello, " + params["name"]
         }
     ```

  2. **更新能力配置**  
     参考实际文件 **.runtime/tools/demo/v2/tools.json**，其中关于问候能力的配置如下：

     ```json
     {
       "$schema": "../../../schema/tools.schema.json",
       "tools": [
         {
           "name": "greet",
           "description": "问候能力",
           "params": {
             "type": "object",
             "description": "问候能力的输入参数",
             "properties": {
               "name": {
                 "type": "string",
                 "description": "接收问候的人员姓名，将在问候消息中使用"
               }
             },
             "required": ["name"]
           },
           "result": {
             "type": "object",
             "description": "问候能力的输出结果",
             "properties": {
               "output": {
                 "type": "string",
                 "description": "包含针对指定人员的问候消息"
               }
             },
             "required": ["output"]
           }
         },
         {
           "name": "echo",
           "description": "回声能力",
           "params": {
             "type": "object",
             "description": "回声能力的输入参数",
             "properties": {
               "input": {
                 "type": "string",
                 "description": "需要被回显的输入文本"
               }
             },
             "required": ["input"]
           },
           "result": {
             "type": "object",
             "description": "回声能力的输出结果",
             "properties": {
               "output": {
                 "type": "string",
                 "description": "原样返回的输入文本内容"
               }
             },
             "required": ["output"]
           }
         }
       ]
     }
     ```

  3. **能力可用条件**
     - 所在模块必须正确配置；
     - 对应的外部服务（如果有）必须处于可用状态；
     - 能力的版本（如问候能力要求能力组版本为 2.0.0）必须与模块所依赖的版本匹配。

## 配置文件与 JSON Schema 支持

系统的配置文件位于 `.runtime` 目录，主要包括以下几种类型：

- **modules.json**: 定义模块信息和依赖关系
- **tools.json**: 定义各能力组内的能力和参数
- **server.json**: 服务器配置信息

### JSON Schema 支持

项目中的配置文件都采用了 JSON Schema 进行规范，这为开发和配置提供了以下优势：

- **配置自动校验**：能够在编辑时即时发现配置错误
- **自动补全提示**：根据 Schema 提供字段和值的智能提示
- **字段文档提示**：悬停在字段上可查看其用途说明
- **减少配置错误**：防止因配置错误导致的运行时问题

### 推荐使用支持 JSON Schema 的编辑器

强烈推荐使用支持 JSON Schema 校验的编辑器来编辑配置文件，例如：

- **VSCode**：内置支持 JSON Schema，自动提供校验和补全
- **JetBrains IDE**（如 WebStorm、IntelliJ IDEA）：也提供了很好的 JSON Schema 支持
- **其他支持 JSON Schema 的编辑器**

使用这些编辑器编辑 `.runtime` 目录下的配置文件时，它们会自动读取文件中的 `$schema` 属性指向的 Schema 定义，提供实时校验和智能提示，大大降低配置错误的几率。

**示例：在 VSCode 中编辑 `modules.json`**

当你在 VSCode 中编辑 `modules.json` 文件时，编辑器会：

- 自动识别 `"$schema": "./schema/modules.schema.json"` 属性
- 读取对应的 Schema 定义
- 提供字段补全和类型检查
- 在配置错误时显示警告或错误提示

这极大地简化了配置过程，特别是对于不熟悉系统的新开发人员，能够减少学习成本和错误率。

---

## 流程与视图

### 流程（Flow）

- **流程定义**
  - 流程文件存储在 **.runtime/flow/** 目录，使用 YAML 格式定义。
  - 每个流程文件定义了一系列步骤及其执行逻辑，可以调用系统中的各种能力。
  - 通过 `/api/flow` 端点访问流程相关功能。

### 视图（View）

- **视图定义**
  - 视图文件存储在 **.runtime/view/** 目录，使用 YAML 格式定义。
  - 视图用于描述用户界面元素和交互方式。
  - 通过 `/api/view` 端点访问视图相关功能。

---

## 系统工作流程图解

### 1. Tool 如何变成可用的 Module 中的 Tool

```mermaid
graph TD
    A[定义 Tool 在 .runtime/tools 目录下] -->|使用 @tool 装饰器| B[装饰器注册 Tool 到 Repository]
    B --> C[系统启动时加载配置文件]
    C --> D[ScriptEngine.load 方法加载所有代码文件]
    D --> E1[Python: 收集 __runner__ Repository 中的所有 tools]
    D --> E2[Lua: 从 __repository__ 表中收集 tools]
    E1 --> F[创建 SourceTool 对象列表]
    E2 --> F
    F --> G[Resolver.new 方法接收 SourceTool 列表]
    G --> H[解析 tool_groups 和 modules 配置文件]
    H --> I[创建 Tool 对象并分配给对应 Module]
    I --> J[匹配 Tool 的版本与 Module 配置中的版本要求]
    J --> K[通过版本匹配成功的 Tool 被激活]
    K --> L[Module 中现在包含可调用的 Tool]
```

关键流程:

1. 开发者在 `.runtime/tools/{group}/{version}/` 目录下创建 Python 或 Lua 脚本
2. 使用 `@tool(version="*")` 装饰器标记函数为技能
3. 系统启动时，`ScriptEngine.load` 加载所有脚本文件
4. 从脚本中收集 `SourceTool` 信息（路径、名称、类型和版本依赖）
5. `Resolver` 解析 `modules.json` 和收集到的 tools
6. 将 tool 与 module 关联，比较版本要求是否匹配
7. 匹配成功的 tool 被注册到对应 module

### 2. MCP 输入的工具名或 JSON RPC 如何命中对应的 Tool

```mermaid
graph TD
    A[接收 MCP 请求] --> B{请求类型}
    B -->|工具调用| C[ModuleRouter.call_tool]
    B -->|列出工具| D[ModuleRouter.list_tools]

    C --> E{路由类型}
    E -->|Global路由| F["resolve_tool(tool_name)"]
    E -->|Module路由| G["module.tools.get(tool_name)"]

    F --> H{解析工具名格式}
    H -->|"module/tool"| I["resolve_tool_with_module(module, tool)"]
    H -->|"group@version/tool"| J["resolve_tool_with_group_version(group, tool, version)"]

    I --> K[获取 Tool 对象]
    J --> K
    G --> K

    K --> L[ScriptEngine.run_tool 执行 Tool]
```

关键流程:

1. MCP 服务接收工具调用请求
2. 根据请求路径判断是全局路由还是特定模块路由
3. 解析工具名（两种格式：`module/tool` 或 `group@version/tool`）
4. 调用相应的解析方法查找匹配的 Tool
5. 如果找到匹配的 Tool，则通过 ScriptEngine 执行

### 3. MCP 工具或 JSON RPC 调用时的数据流

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 服务器
    participant MCP as MCP 协议层
    participant Resolver as 解析器
    participant ScriptEngine as 脚本引擎
    participant Python as Python 引擎
    participant Lua as Lua 引擎
    participant App as 应用

    Client->>API: 发送请求 (POST /api/mcp/{module})
    API->>MCP: 路由请求到 MCP 处理器
    MCP->>Resolver: 解析工具名找到对应 Tool
    Resolver->>MCP: 返回匹配的 Tool 对象
    MCP->>ScriptEngine: 调用 run_tool(tool, arguments)

    alt Python Tool
        ScriptEngine->>Python: 调用 python::run_tool
        Python->>Python: 获取函数引用 (get_tool_function)
        Python->>Python: 创建运行时上下文 (Context)
        Python->>Python: 异步执行函数 (run_tool_function)
        Python->>App: 调用应用 API
        App->>Python: 返回结果
        Python->>ScriptEngine: 返回结果
    else Lua Tool
        ScriptEngine->>Lua: 调用 lua::run_tool
        Lua->>Lua: 获取函数引用 (get_tool_function)
        Lua->>Lua: 设置执行上下文
        Lua->>Lua: 异步调用函数
        Lua->>App: 调用应用 API
        App->>Lua: 返回结果
        Lua->>ScriptEngine: 返回结果
    end

    ScriptEngine->>MCP: 返回执行结果
    MCP->>API: 格式化响应
    API->>Client: 返回结果
```

关键数据流:

1. 客户端发送请求到 API 服务器（`/api/mcp/` 或 `/api/mcp/{module}`）
2. MCP 模块接收请求并解析工具名
3. Resolver 根据工具名查找对应的 Tool
4. ScriptEngine 根据 Tool 类型选择 Python 或 Lua 引擎执行
5. 创建执行上下文，将参数转换为脚本环境可用的格式
6. 执行脚本函数并收集结果
7. 结果经过转换后返回给客户端

## 服务 API 与生成规则

### 服务 API

- 项目通过 MCP 或 HTTP 接口暴露以下功能：
  - **API 根路径**: `/api`
    - **RPC API**: `/api/rpc` - 用于执行能力调用
    - **MCP API**: `/api/mcp` - 实现 MCP 协议，支持工具集成
    - **View API**: `/api/view` - 用于获取视图定义
    - **Flow API**: `/api/flow` - 用于获取和执行流程

### MCP 服务集成

- 项目实现了 Model Context Protocol (MCP) 服务，支持工具集成。
- 可以作为 MCP 服务器提供能力给其他系统调用。
- 支持全局能力路由 `/api/mcp/` 和模块特定路由 `/api/mcp/{module_name}`。

### 生成规则

- 系统读取 **.runtime** 目录下的配置文件和能力脚本，根据模块和能力组中的版本信息自动加载和注册能力。
- 自动化生成流程确保：
  - 模块与能力的版本匹配；
  - 根据配置文件动态注册并调度能力；
  - 若存在版本不匹配，系统将提示进行版本更新或调整配置，确保依赖关系明确。

**示例：版本管理说明**

- 模块（module）在 `.runtime/modules.json` 中声明的版本（如 "2.0.0"）是一个**目标版本**，表示该模块希望使用的能力版本。
- 能力（tool）在代码中通过 `@tool(version="*")` 装饰器声明自己的**版本兼容范围**，这个声明采用语义化版本比较器（semver）格式。
  - 例如，`"*"` 表示兼容任何版本
  - `"^2.0.0"` 表示兼容 2.x.x 的所有版本
  - `"~1.2.0"` 表示兼容 1.2.x 的所有版本
- 系统会将**模块的目标版本**与**能力的版本兼容范围**进行匹配：
  - 只有当模块的目标版本满足能力声明的兼容范围时，该能力才会被激活并可用于该模块
  - 例如，如果模块声明版本为 "2.0.0"，而能力声明 `@tool(version="^1.0.0")`，则不匹配
  - 反之，如果模块版本为 "2.0.0"，能力声明 `@tool(version="*")` 或 `@tool(version="^2.0.0")`，则匹配成功
- 在匹配多个版本的能力组时，系统会**优先选择更高版本**的能力组：
  - 当存在 `.runtime/tools/demo/1.0.0/` 和 `.runtime/tools/demo/v2/` 两个能力组时
  - 系统会按照版本号从高到低（降序）尝试匹配，即先检查 v2 组，再检查 1.0.0 组
  - 这确保了总是使用符合兼容性要求的最新版本能力

通过这种版本匹配机制，系统保证了模块只会使用与其声明的版本兼容的能力，同时优先使用符合条件的最高版本，从而实现了能力的平滑升级和向后兼容。

---

## 编译与运行

- **编译项目**  
  使用 Cargo 命令进行编译：

  ```bash
  cargo build
  ```

- **运行项目**  
  启动项目：

  ```bash
  cargo run
  ```

- 请确保所有必要的环境变量和依赖均已正确配置，以便项目能够顺利运行。

---

## 测试

### 能力测试

系统提供了一套工具，可以在不启动完整服务的情况下，直接测试各个能力的正确性。这对于开发和调试能力特别有用。

#### 测试工具

- **`__runner__.py`**: 实现了 Context 类和 tool 装饰器的核心功能，与服务中的实现保持一致。
- **`__tester__.py`**: 提供测试辅助函数，用于加载模块和执行能力。
  - `load_module(module_path, module_name=None)`: 加载指定路径的能力模块
  - `run_async(func, context=None, params=None)`: 执行异步能力函数
  - `run(func, context=None, params=None)`: 执行同步能力函数

#### 编写测试脚本

测试脚本可以直接放在 `.runtime/tools` 目录下，通常以 `test_` 为前缀。

**示例: 测试问候能力**

```python
from __tester__ import load_module, run_async
from __runner__ import Context

# 加载要测试的能力模块
greet_module = load_module("demo/1.0.0/greet.py")

# 准备测试数据
context = Context()
context.config = {"key": "value"}
params = {"name": "ronbb"}
expected_result = {"output": "hello, ronbb"}

# 执行能力函数
result = run_async(greet_module.greet, context, params)
assert result == expected_result

# 测试不传context参数的情况
params = {"name": "world"}
expected_result = {"output": "hello, world"}
result = run_async(greet_module.greet, params=params)
assert result == expected_result

print("测试通过!")
```

#### 运行测试

可以直接使用 Python 解释器运行测试脚本：

```bash
python .runtime/tools/test_echo.py
```

#### 测试的优势

1. **独立性**: 无需启动完整服务即可测试能力
2. **快速迭代**: 修改能力后可立即测试效果
3. **模拟环境**: 可以模拟各种上下文和参数情况
4. **断言验证**: 使用断言自动验证输出结果是否符合预期

这种测试机制确保了能力在集成到系统前已经过充分验证，提高了开发效率和代码质量。

---

## MCP 客户端测试

除了能力的单元测试外，系统还提供了 MCP 客户端测试脚本，用于测试整个系统的 MCP 接口功能。这对于验证系统与外部 AI 服务的集成特别有用。

### MCP 客户端测试工具

在 `demo` 目录下提供了一个 `mcp_client.py` 客户端脚本，用于连接到系统的 MCP 服务并使用大语言模型与系统能力进行交互。

### 环境配置

在使用 MCP 客户端前，需要先配置环境变量。可以通过 `.env` 文件或直接设置系统环境变量来完成：

1. 在 `demo` 目录下创建 `.env` 文件（或修改现有文件），添加以下配置：

```properties
# OpenAI 兼容接口配置
OPENAI_BASE_URL=https://api.deepseek.com  # 或其他兼容 OpenAI API 的服务地址
OPENAI_API_KEY=your-api-key-here          # 替换为你的 API 密钥
OPENAI_MODEL=deepseek-chat                # 或其他支持的模型名称
OPENAI_AGENTS_DISABLE_TRACING=1           # 禁用追踪功能
```

### 运行 MCP 客户端测试

1. 确保系统服务已启动：

```bash
cargo run
```

2. 在另一个终端窗口中，运行 MCP 客户端测试脚本：

```bash
cd demo
python mcp_client.py
```

### 客户端工作流程

MCP 客户端测试脚本执行以下操作：

1. 加载环境变量配置
2. 连接到系统的 MCP 服务端（默认为 `http://127.0.0.1:15336/api/mcp`）
3. 初始化会话并获取可用工具列表
4. 将系统能力作为工具提供给大语言模型
5. 发送用户输入到大语言模型
6. 处理大语言模型返回的工具调用请求
7. 将工具调用转发到系统的 MCP 服务
8. 将工具执行结果返回给大语言模型
9. 循环处理直到完成对话

### 示例：测试问候能力

通过 MCP 客户端，可以让大语言模型调用系统中的问候能力：

```python
from dotenv import load_dotenv
load_dotenv()

import anyio
from mcp.client.session import ClientSession
from mcp.client.sse import sse_client
from openai import AsyncOpenAI

async def main(input: str):
    # 连接 MCP 服务
    async with sse_client("http://127.0.0.1:15336/api/mcp") as (read_stream, write_stream):
        async with ClientSession(read_stream, write_stream) as session:
            # 初始化会话并获取工具列表
            await session.initialize()
            tools = await session.list_tools()

            # 将系统能力转换为 OpenAI 工具格式
            openai_tools = []
            for tool in tools.tools:
                openai_tool = {
                    "type": "function",
                    "function": {
                        "name": tool.name.replace("/", "____"),
                        "description": tool.description,
                        "parameters": tool.inputSchema
                    }
                }
                openai_tools.append(openai_tool)

            # 创建对话
            messages = [
                {"role": "system", "content": "你是一个只会使用工具的机器人，哔哔哔。"},
                {"role": "user", "content": input},
            ]

            # 调用大语言模型
            async with AsyncOpenAI() as openai_client:
                # ... 处理对话循环 ...

# 运行测试
anyio.run(main, "hello 早上好呀, 我是 ronbb")
```

这个例子展示了如何通过 MCP 客户端让大语言模型调用系统的 "greet" 能力。

### 注意事项

- 确保已安装所需的 Python 依赖：`pip install dotenv anyio openai colorama mcp`
- 保护好你的 API 密钥，避免将含有密钥的配置文件提交到代码仓库
- 如果遇到连接问题，请检查系统服务是否正常运行，以及网络连接是否畅通
- 不同的 OpenAI 兼容服务可能有细微差异，请根据实际使用的服务调整配置

---

## 注意事项

- 所有配置均存储于 **.runtime** 目录，便于集中管理。
- 模块与能力的版本管理严格：
  - 模块声明的版本代表依赖目标版本；
  - 能力组（通过文件夹路径指定）的版本必须与模块依赖的版本匹配，否则需要升级或调整配置。
- 定义新能力或模块时，请确保满足能力可用条件，包括外部服务状态、权限要求及环境配置等。
- 项目支持异步执行，尤其适合处理需要长时间等待的任务。

---

本 README.md 详细描述了项目结构、模块与能力管理机制、版本依赖关系，以及如何定义和启用能力的具体示例，为后续扩展和维护提供明确指导。
